﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using static McLaser.EditViewerSk.Base.Command;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SkiaSharp;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 删除命令
    /// </summary>
    public class DeleteCmd : ModifyCmd
    {
        /// <summary>
        /// 要删除的图元
        /// </summary>
        private List<EntityBase> _entitiesToDelete = new List<EntityBase>();

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            Step1_SelectObjects = 1,
        }
        private Step _step = Step.Step1_SelectObjects;

        public override void Initialize()
        {
            base.Initialize();

            // 检查是否已有选中的对象
            if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
            {
                _entitiesToDelete.AddRange(doc.SelectedEntities);
                _mgr.FinishCurrentCommand();
            }
            else
            {
                this.pointer.Mode = IndicatorMode.Select;
                this.pointer.Document.Prompt = "选择要删除的对象:";
            }
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            try
            {
                if (_entitiesToDelete != null && _entitiesToDelete.Count > 0)
                {
                    doc.Action.ActEntityDelete(_entitiesToDelete);
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"删除对象时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"DeleteCmd.Commit error: {ex}");
            }
        }

        /// <summary>
        /// 回滚撤销
        /// </summary>
        protected override void Rollback()
        {
            if (_entitiesToDelete != null && _entitiesToDelete.Count > 0)
            {
                foreach (EntityBase entity in _entitiesToDelete)
                {
                    doc.Action.ActEntityAdd(entity);
                }
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            // 选择对象的逻辑由基类处理
            return EventResult.Unhandled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
                {
                    _entitiesToDelete.AddRange(doc.SelectedEntities);
                    _mgr.FinishCurrentCommand();
                }
                else
                {
                    _mgr.CancelCurrentCommand();
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }
            else if (e.KeyCode == System.Windows.Forms.Keys.Delete)
            {
                if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
                {
                    _entitiesToDelete.AddRange(doc.SelectedEntities);
                    _mgr.FinishCurrentCommand();
                }
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }

        public override void OnPaint(SKCanvas canvas)
        {
            // 删除命令不需要特殊的绘制
        }
    }
}
