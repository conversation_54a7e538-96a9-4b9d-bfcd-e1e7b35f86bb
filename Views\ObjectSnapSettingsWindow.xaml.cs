using McLaser.EditViewerSk.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 对象捕捉设置窗口
    /// </summary>
    public partial class ObjectSnapSettingsWindow : Window
    {
        private ViewBase _viewer;
        private ObjectSnapMode _currentSnapModes;
        private bool _isUpdating = false;
        
        private Dictionary<CheckBox, ObjectSnapMode> _snapModeCheckBoxes;

        public ObjectSnapSettingsWindow(ViewBase viewer = null)
        {
            InitializeComponent();
            _viewer = viewer;
            InitializeSnapModeMapping();
            LoadCurrentSettings();
        }

        private void InitializeSnapModeMapping()
        {
            _snapModeCheckBoxes = new Dictionary<CheckBox, ObjectSnapMode>
            {
                { EndSnapCheckBox, ObjectSnapMode.End },
                { MidSnapCheckBox, ObjectSnapMode.Mid },
                { CenterSnapCheckBox, ObjectSnapMode.Center },
                { QuadSnapCheckBox, ObjectSnapMode.Quad },
                { IntersectionSnapCheckBox, ObjectSnapMode.Intersection },
                { PerpendicularSnapCheckBox, ObjectSnapMode.Perpendicular },
                { TangentSnapCheckBox, ObjectSnapMode.Tangent },
                { NearSnapCheckBox, ObjectSnapMode.Near },
                { GridSnapCheckBox, ObjectSnapMode.Grid }
            };
        }

        private void LoadCurrentSettings()
        {
            _isUpdating = true;
            try
            {
                // 获取当前捕捉模式
                if (_viewer?.GetPhase2Manager() != null)
                {
                    _currentSnapModes = _viewer.GetPhase2Manager().GetObjectSnapSettings();
                }
                else if (_viewer?._inputManager?.snapMgr != null)
                {
                    _currentSnapModes = _viewer._inputManager.snapMgr.RunningSnapModes;
                }
                else
                {
                    // 默认捕捉模式
                    _currentSnapModes = ObjectSnapMode.End | ObjectSnapMode.Mid |
                                       ObjectSnapMode.Center | ObjectSnapMode.Intersection |
                                       ObjectSnapMode.Grid;
                }

                // 基本设置
                EnableObjectSnapCheckBox.IsChecked = _currentSnapModes != ObjectSnapMode.Undefined;
                SnapToleranceTextBox.Text = "10"; // TODO: 从设置中获取
                ShowSnapMarkersCheckBox.IsChecked = true; // TODO: 从设置中获取
                ShowSnapTooltipsCheckBox.IsChecked = true; // TODO: 从设置中获取
                AutoAcquireTrackingPointsCheckBox.IsChecked = true; // TODO: 从设置中获取

                // 视觉设置
                MarkerSizeTextBox.Text = "8"; // TODO: 从设置中获取
                TooltipFontSizeTextBox.Text = "12"; // TODO: 从设置中获取

                // 更新捕捉模式复选框
                UpdateSnapModeCheckBoxes();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void UpdateSnapModeCheckBoxes()
        {
            foreach (var kvp in _snapModeCheckBoxes)
            {
                kvp.Key.IsChecked = (_currentSnapModes & kvp.Value) == kvp.Value;
            }
        }

        private void SaveCurrentSettings()
        {
            if (_isUpdating) return;

            try
            {
                // 收集选中的捕捉模式
                _currentSnapModes = ObjectSnapMode.Undefined;
                
                if (EnableObjectSnapCheckBox.IsChecked == true)
                {
                    foreach (var kvp in _snapModeCheckBoxes)
                    {
                        if (kvp.Key.IsChecked == true)
                        {
                            _currentSnapModes |= kvp.Value;
                        }
                    }
                }

                // 应用到系统
                ApplySettingsToSystem();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplySettingsToSystem()
        {
            if (_viewer?.GetPhase2Manager() != null)
            {
                _viewer.GetPhase2Manager().ApplyObjectSnapSettings(_currentSnapModes);
            }
            else if (_viewer?._inputManager?.snapMgr != null)
            {
                _viewer._inputManager.snapMgr.RunningSnapModes = _currentSnapModes;
            }
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            _isUpdating = true;
            try
            {
                foreach (var checkBox in _snapModeCheckBoxes.Keys)
                {
                    checkBox.IsChecked = true;
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void SelectNone_Click(object sender, RoutedEventArgs e)
        {
            _isUpdating = true;
            try
            {
                foreach (var checkBox in _snapModeCheckBoxes.Keys)
                {
                    checkBox.IsChecked = false;
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void InvertSelection_Click(object sender, RoutedEventArgs e)
        {
            _isUpdating = true;
            try
            {
                foreach (var checkBox in _snapModeCheckBoxes.Keys)
                {
                    checkBox.IsChecked = !checkBox.IsChecked;
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void RestoreDefaults_Click(object sender, RoutedEventArgs e)
        {
            _isUpdating = true;
            try
            {
                // 恢复默认设置
                EnableObjectSnapCheckBox.IsChecked = true;
                SnapToleranceTextBox.Text = "10";
                ShowSnapMarkersCheckBox.IsChecked = true;
                ShowSnapTooltipsCheckBox.IsChecked = true;
                AutoAcquireTrackingPointsCheckBox.IsChecked = true;
                MarkerSizeTextBox.Text = "8";
                TooltipFontSizeTextBox.Text = "12";

                // 默认捕捉模式
                _currentSnapModes = ObjectSnapMode.End | ObjectSnapMode.Mid | 
                                   ObjectSnapMode.Center | ObjectSnapMode.Intersection | 
                                   ObjectSnapMode.Grid;
                
                UpdateSnapModeCheckBoxes();
                
                MessageBox.Show("已恢复默认设置", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentSettings();
            MessageBox.Show("设置已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentSettings();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
