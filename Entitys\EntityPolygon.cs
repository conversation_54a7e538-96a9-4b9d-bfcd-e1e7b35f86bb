using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 正多边形图元类
    /// 基于CAD行业标准实现，支持内接圆、外接圆等多种定义方式
    /// </summary>
    public class EntityPolygon : EntityBase
    {
        #region 枚举
        /// <summary>
        /// 多边形类型
        /// </summary>
        public enum PolygonType
        {
            /// <summary>内接圆（多边形顶点在圆上）</summary>
            Inscribed,
            /// <summary>外切圆（多边形边与圆相切）</summary>
            Circumscribed
        }
        #endregion

        #region 私有字段
        private Vector2 _center = Vector2.Zero;
        private double _radius = 10.0;
        private int _sides = 6; // 默认六边形
        private double _rotation = 0.0; // 旋转角度（度）
        private PolygonType _polygonType = PolygonType.Inscribed;
        
        [JsonIgnore]
        private Vector2[] _vertices; // 缓存的顶点数组
        private bool _verticesNeedUpdate = true; // 顶点是否需要更新
        
        [JsonIgnore]
        private SKPath _polygonPath; // 缓存的多边形路径
        private bool _pathNeedsUpdate = true; // 路径是否需要更新
        #endregion

        #region 公共属性
        /// <summary>
        /// 多边形中心点
        /// </summary>
        [Category("几何"), DisplayName("中心点"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public Vector2 Center
        {
            get => _center;
            set
            {
                if (_center != value)
                {
                    _center = value;
                    IsNeedToRegen = true;
                    _verticesNeedUpdate = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Center));
                }
            }
        }

        /// <summary>
        /// 半径（内接圆或外切圆的半径）
        /// </summary>
        [Category("几何"), DisplayName("半径"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double Radius
        {
            get => _radius;
            set
            {
                if (value > 0 && Math.Abs(_radius - value) > double.Epsilon)
                {
                    _radius = value;
                    IsNeedToRegen = true;
                    _verticesNeedUpdate = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Radius));
                    OnPropertyChanged(nameof(SideLength));
                    OnPropertyChanged(nameof(Area));
                    OnPropertyChanged(nameof(Perimeter));
                }
            }
        }

        /// <summary>
        /// 边数（3-1000）
        /// </summary>
        [Category("几何"), DisplayName("边数"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public int Sides
        {
            get => _sides;
            set
            {
                var clampedValue = Math.Max(3, Math.Min(1000, value)); // 限制在合理范围内
                if (_sides != clampedValue)
                {
                    _sides = clampedValue;
                    IsNeedToRegen = true;
                    _verticesNeedUpdate = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Sides));
                    OnPropertyChanged(nameof(SideLength));
                    OnPropertyChanged(nameof(Area));
                    OnPropertyChanged(nameof(Perimeter));
                    OnPropertyChanged(nameof(InteriorAngle));
                    OnPropertyChanged(nameof(CentralAngle));
                }
            }
        }

        /// <summary>
        /// 旋转角度（度）
        /// </summary>
        [Category("几何"), DisplayName("旋转角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double Rotation
        {
            get => _rotation;
            set
            {
                var normalizedAngle = MathHelper.NormalizeAngle(value);
                if (Math.Abs(_rotation - normalizedAngle) > double.Epsilon)
                {
                    _rotation = normalizedAngle;
                    IsNeedToRegen = true;
                    _verticesNeedUpdate = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Rotation));
                }
            }
        }

        /// <summary>
        /// 多边形类型（内接或外切）
        /// </summary>
        [Category("几何"), DisplayName("类型"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public PolygonType PolygonTypeValue
        {
            get => _polygonType;
            set
            {
                if (_polygonType != value)
                {
                    _polygonType = value;
                    IsNeedToRegen = true;
                    _verticesNeedUpdate = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(PolygonTypeValue));
                    OnPropertyChanged(nameof(SideLength));
                    OnPropertyChanged(nameof(Area));
                    OnPropertyChanged(nameof(Perimeter));
                }
            }
        }

        /// <summary>
        /// 边长（只读计算属性）
        /// </summary>
        [Category("计算属性"), DisplayName("边长"), Browsable(true), ReadOnly(true)]
        public double SideLength
        {
            get
            {
                if (_sides <= 0) return 0;
                
                var centralAngle = 2.0 * Math.PI / _sides;
                
                if (_polygonType == PolygonType.Inscribed)
                {
                    // 内接多边形：边长 = 2 * R * sin(π/n)
                    return 2.0 * _radius * Math.Sin(Math.PI / _sides);
                }
                else
                {
                    // 外切多边形：边长 = 2 * R * tan(π/n)
                    return 2.0 * _radius * Math.Tan(Math.PI / _sides);
                }
            }
        }

        /// <summary>
        /// 内角（度）
        /// </summary>
        [Category("计算属性"), DisplayName("内角"), Browsable(true), ReadOnly(true)]
        public double InteriorAngle
        {
            get
            {
                if (_sides <= 2) return 0;
                return (double)(_sides - 2) * 180.0 / _sides;
            }
        }

        /// <summary>
        /// 中心角（度）
        /// </summary>
        [Category("计算属性"), DisplayName("中心角"), Browsable(true), ReadOnly(true)]
        public double CentralAngle => _sides > 0 ? 360.0 / _sides : 0;

        /// <summary>
        /// 周长
        /// </summary>
        [Category("计算属性"), DisplayName("周长"), Browsable(true), ReadOnly(true)]
        public double Perimeter => SideLength * _sides;

        /// <summary>
        /// 面积
        /// </summary>
        [Category("计算属性"), DisplayName("面积"), Browsable(true), ReadOnly(true)]
        public double Area
        {
            get
            {
                if (_sides <= 2) return 0;
                
                if (_polygonType == PolygonType.Inscribed)
                {
                    // 内接多边形面积 = (n * R²/2) * sin(2π/n)
                    return (_sides * _radius * _radius / 2.0) * Math.Sin(2.0 * Math.PI / _sides);
                }
                else
                {
                    // 外切多边形面积 = n * R² * tan(π/n)
                    return _sides * _radius * _radius * Math.Tan(Math.PI / _sides);
                }
            }
        }

        /// <summary>
        /// 顶点数组（只读）
        /// </summary>
        [JsonIgnore, Browsable(false)]
        public Vector2[] Vertices
        {
            get
            {
                if (_verticesNeedUpdate)
                {
                    UpdateVertices();
                }
                return _vertices?.ToArray() ?? new Vector2[0];
            }
        }
        #endregion

        #region 构造函数
        public EntityPolygon()
        {
            Name = "Polygon";
            _polygonPath = new SKPath();
            // Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/polygon.png"));
        }

        public EntityPolygon(Vector2 center, double radius, int sides, PolygonType polygonType = PolygonType.Inscribed, double rotation = 0) 
            : this()
        {
            _center = center;
            _radius = Math.Max(radius, 0.001);
            _sides = Math.Max(3, Math.Min(1000, sides));
            _polygonType = polygonType;
            _rotation = MathHelper.NormalizeAngle(rotation);
        }
        #endregion

        #region 重写方法
        [JsonIgnore]
        public override BoundingBox BoundingBox { get; set; } = BoundingBox.Empty;

        /// <summary>
        /// 重新生成多边形数据
        /// </summary>
        public override void Regen()
        {
            UpdateVertices();
            UpdatePolygonPath();
            RegenBoundRect();
            IsNeedToRegen = false;
        }

        /// <summary>
        /// 渲染多边形
        /// </summary>
        public override void Render(IView view)
        {
            if (view == null || !IsRenderable) return;
            if (IsNeedToRegen) Regen();

            // 使用新的集成渲染系统
            this.IntegratePolygonRendering(view);
        }

        /// <summary>
        /// 平移变换
        /// </summary>
        public override void Translate(Vector2 delta)
        {
            if (IsLocked || delta == Vector2.Zero) return;
            
            Center += delta;
            BoundingBox?.Transit(delta);
        }

        /// <summary>
        /// 旋转变换
        /// </summary>
        public override void Rotate(double angle)
        {
            if (IsLocked || MathHelper.IsZero(angle)) return;
            
            Rotation += angle;
        }

        /// <summary>
        /// 围绕指定点旋转
        /// </summary>
        public override void Rotate(double angle, Vector2 rotateCenter)
        {
            if (IsLocked || MathHelper.IsZero(angle)) return;
            
            // 旋转中心点
            var matrix = Matrix3.CreateRotation((float)(angle * Math.PI / 180.0), rotateCenter);
            Center = Vector2.Transform(Center, matrix);
            
            // 旋转多边形本身
            Rotation += angle;
        }

        /// <summary>
        /// 缩放变换
        /// </summary>
        public override void Scale(Vector2 scale, Vector2 scaleCenter)
        {
            if (IsLocked || scale == Vector2.Zero || scale == Vector2.One) return;
            
            // 缩放中心点
            Center = (Center - scaleCenter) * scale + scaleCenter;
            
            // 缩放半径（使用平均缩放因子）
            var scaleFactor = (Math.Abs(scale.X) + Math.Abs(scale.Y)) * 0.5;
            Radius *= scaleFactor;
        }

        /// <summary>
        /// 点击测试
        /// </summary>
        public override bool HitTest(double x, double y, double threshold)
        {
            if (!BoundingBox.HitTest(x, y, threshold)) return false;
            
            return IsPointInOrOnPolygon(new Vector2((float)x, (float)y), threshold);
        }

        /// <summary>
        /// 矩形区域碰撞测试
        /// </summary>
        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!BoundingBox.HitTest(br, threshold)) return false;
            
            // 检查多边形是否与矩形相交
            return IsPolygonIntersectRect(br);
        }

        /// <summary>
        /// 获取对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            var snapPoints = new List<ObjectSnapPoint>();
            
            // 中心点
            snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Center, _center));
            
            // 顶点
            if (_vertices != null)
            {
                foreach (var vertex in _vertices)
                {
                    snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.End, vertex));
                }
                
                // 边的中点
                for (int i = 0; i < _vertices.Length; i++)
                {
                    var nextIndex = (i + 1) % _vertices.Length;
                    var midPoint = (_vertices[i] + _vertices[nextIndex]) * 0.5f;
                    snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Mid, midPoint));
                }
            }
            
            return snapPoints;
        }

        /// <summary>
        /// 克隆对象
        /// </summary>
        public override object Clone()
        {
            return new EntityPolygon
            {
                Name = Name,
                Description = Description,
                Parent = Parent,
                IsSelected = IsSelected,
                IsVisible = IsVisible,
                IsRenderable = IsRenderable,
                IsMarkerable = IsMarkerable,
                IsLocked = IsLocked,
                Color = Color,
                Center = _center,
                Radius = _radius,
                Sides = _sides,
                Rotation = _rotation,
                PolygonTypeValue = _polygonType,
                BoundingBox = BoundingBox?.Clone(),
                Tag = Tag,
                Index = Index,
                IsNeedToRegen = true
            };
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 更新顶点数组
        /// </summary>
        private void UpdateVertices()
        {
            if (!_verticesNeedUpdate || _sides < 3) return;
            
            _vertices = new Vector2[_sides];
            var centralAngle = 2.0 * Math.PI / _sides;
            var startAngle = _rotation * Math.PI / 180.0;
            
            // 计算实际绘制半径
            var drawRadius = _radius;
            if (_polygonType == PolygonType.Circumscribed)
            {
                // 外切多边形：绘制半径 = 外切圆半径 / cos(π/n)
                drawRadius = _radius / Math.Cos(Math.PI / _sides);
            }
            
            // 生成顶点
            for (int i = 0; i < _sides; i++)
            {
                var angle = startAngle + i * centralAngle;
                _vertices[i] = new Vector2(
                    (float)(_center.X + drawRadius * Math.Cos(angle)),
                    (float)(_center.Y + drawRadius * Math.Sin(angle))
                );
            }
            
            _verticesNeedUpdate = false;
        }

        /// <summary>
        /// 更新多边形路径
        /// </summary>
        private void UpdatePolygonPath()
        {
            if (!_pathNeedsUpdate || _vertices == null || _vertices.Length < 3) return;
            
            _polygonPath?.Reset();
            
            // 创建多边形路径
            _polygonPath.MoveTo(_vertices[0].X, _vertices[0].Y);
            for (int i = 1; i < _vertices.Length; i++)
            {
                _polygonPath.LineTo(_vertices[i].X, _vertices[i].Y);
            }
            _polygonPath.Close();
            
            _pathNeedsUpdate = false;
        }

        /// <summary>
        /// 重新计算包围盒
        /// </summary>
        private void RegenBoundRect()
        {
            if (_vertices == null || _vertices.Length == 0)
            {
                BoundingBox = BoundingBox.Empty;
                return;
            }
            
            var minX = _vertices.Min(v => v.X);
            var maxX = _vertices.Max(v => v.X);
            var minY = _vertices.Min(v => v.Y);
            var maxY = _vertices.Max(v => v.Y);
            
            BoundingBox = new BoundingBox(minX, maxY, maxX, minY);
        }

        /// <summary>
        /// 基础方法渲染（降级方案）
        /// </summary>
        private void RenderWithBasicMethod(ViewBase viewBase)
        {
            if (_vertices == null || _vertices.Length < 3) return;
            
            // 绘制多边形边
            for (int i = 0; i < _vertices.Length; i++)
            {
                var nextIndex = (i + 1) % _vertices.Length;
                viewBase.DrawLine(_vertices[i], _vertices[nextIndex], Pen);
            }
        }

        /// <summary>
        /// 渲染控制点
        /// </summary>
        private void RenderControlPoints(ViewBase viewBase)
        {
            var controlPaint = new SKPaint
            {
                Color = SKColors.Blue,
                StrokeWidth = 1.0f,
                Style = SKPaintStyle.Fill
            };

            // 中心点
            viewBase.DrawCircle(_center, 3, controlPaint);
            
            // 顶点
            if (_vertices != null)
            {
                foreach (var vertex in _vertices)
                {
                    viewBase.DrawRectangle(vertex - new Vector2(2, 2), 4, 4, controlPaint);
                }
            }
        }

        /// <summary>
        /// 判断点是否在多边形内或边上
        /// </summary>
        private bool IsPointInOrOnPolygon(Vector2 point, double threshold)
        {
            if (_vertices == null || _vertices.Length < 3) return false;
            
            // 使用射线法判断点是否在多边形内
            var isInside = IsPointInPolygon(point);
            if (isInside) return true;
            
            // 检查点是否在多边形边上
            for (int i = 0; i < _vertices.Length; i++)
            {
                var nextIndex = (i + 1) % _vertices.Length;
                if (MathHelper.IntersectPointInLine(
                    _vertices[i].X, _vertices[i].Y,
                    _vertices[nextIndex].X, _vertices[nextIndex].Y,
                    point.X, point.Y, threshold))
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// 射线法判断点是否在多边形内
        /// </summary>
        private bool IsPointInPolygon(Vector2 point)
        {
            if (_vertices == null || _vertices.Length < 3) return false;
            
            bool inside = false;
            int j = _vertices.Length - 1;
            
            for (int i = 0; i < _vertices.Length; j = i++)
            {
                var vi = _vertices[i];
                var vj = _vertices[j];
                
                if (((vi.Y > point.Y) != (vj.Y > point.Y)) &&
                    (point.X < (vj.X - vi.X) * (point.Y - vi.Y) / (vj.Y - vi.Y) + vi.X))
                {
                    inside = !inside;
                }
            }
            
            return inside;
        }

        /// <summary>
        /// 多边形与矩形相交测试
        /// </summary>
        private bool IsPolygonIntersectRect(BoundingBox rect)
        {
            // 简化实现：检查多边形的包围盒是否与给定矩形相交
            return BoundingBox.HitTest(rect, 0);
        }
        #endregion

        #region IDisposable
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _polygonPath?.Dispose();
                _polygonPath = null;
                _vertices = null;
            }
            base.Dispose(disposing);
        }
        #endregion
    }
} 