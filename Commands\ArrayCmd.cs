using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 阵列命令基类
    /// 实现专业CAD级别的阵列功能，支持矩形、极轴和路径阵列
    /// </summary>
    public abstract class ArrayCmd : Command
    {
        protected ArrayState _currentState = ArrayState.SelectEntities;
        protected List<EntityBase> _selectedEntities = new List<EntityBase>();
        protected List<EntityBase> _previewEntities = new List<EntityBase>();
        protected ArrayType _arrayType;
        protected ArrayOptions _options;
        
        // 视觉样式
        protected SKPaint _previewPaint;
        protected SKPaint _selectedPaint;
        protected SKPaint _gridPaint;
        
        public override string Name => "ARRAY";
        public override string Description => "创建对象的阵列";
        
        protected ArrayCmd(ArrayType arrayType)
        {
            _arrayType = arrayType;
            _options = new ArrayOptions();
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(120, 0, 255, 0), // 半透明绿色
                IsAntialias = true
            };
            
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _gridPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 0.5f,
                Color = SKColor.FromArgb(100, 128, 128, 128),
                PathEffect = SKPathEffect.CreateDash(new float[] { 2, 2 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = ArrayState.SelectEntities;
            _selectedEntities.Clear();
            _previewEntities.Clear();
            
            _viewer.Document.Prompt = "选择要阵列的对象：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case ArrayState.SelectEntities:
                    HandleEntitySelection(currentPoint);
                    break;
                    
                default:
                    HandleSpecificArrayInput(currentPoint, e);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            UpdateArrayPreview(currentPoint);
            _viewer.RepaintCanvas();
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState != ArrayState.SelectEntities && _previewEntities.Count > 0)
                    {
                        CompleteArray();
                    }
                    else if (_currentState == ArrayState.SelectEntities && _selectedEntities.Count > 0)
                    {
                        StartArrayDefinition();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.A:
                    // 全选
                    if (_currentState == ArrayState.SelectEntities)
                    {
                        SelectAllEntities();
                    }
                    break;
            }
            
            HandleSpecificArrayKeys(e);
        }
        
        protected virtual void HandleEntitySelection(Vector2 point)
        {
            var hitEntity = FindSelectableEntity(point);
            
            if (hitEntity != null)
            {
                if (!_selectedEntities.Contains(hitEntity))
                {
                    _selectedEntities.Add(hitEntity);
                    _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，继续选择或按Enter确认：";
                }
            }
        }
        
        protected virtual void StartArrayDefinition()
        {
            if (_selectedEntities.Count == 0)
            {
                _viewer.Document.Prompt = "未选择任何对象";
                return;
            }
            
            // 由子类实现具体的阵列定义逻辑
            OnStartArrayDefinition();
        }
        
        protected abstract void OnStartArrayDefinition();
        protected abstract void HandleSpecificArrayInput(Vector2 point, MouseEventArgs e);
        protected abstract void HandleSpecificArrayKeys(KeyEventArgs e);
        protected abstract void UpdateArrayPreview(Vector2 currentPoint);
        protected abstract List<EntityBase> GenerateArrayEntities();
        
        protected void CompleteArray()
        {
            if (_previewEntities.Count == 0) return;
            
            try
            {
                // 添加阵列后的实体到文档
                foreach (var entity in _previewEntities)
                {
                    _viewer.Document.ActiveLayer.Children.Add(entity);
                }
                
                // 创建撤销记录
                CreateUndoRecord();
                
                Finish();
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"阵列操作失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Array completion error: {ex.Message}");
            }
        }
        
        protected void CreateUndoRecord()
        {
            // 这里应该创建撤销记录
        }
        
        protected EntityBase FindSelectableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        protected bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        protected void SelectAllEntities()
        {
            _selectedEntities.Clear();
            _selectedEntities.AddRange(_viewer.Document.ActiveLayer.Children);
            _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，按Enter确认：";
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制选中的实体
            foreach (var entity in _selectedEntities)
            {
                RenderSelectedEntity(canvas, entity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制阵列特定的辅助图形
            RenderArraySpecificGraphics(canvas);
        }
        
        protected virtual void RenderArraySpecificGraphics(SKCanvas canvas)
        {
            // 由子类实现特定的辅助图形绘制
        }
        
        protected void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _selectedPaint);
            }
        }
        
        protected void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            // 根据实体类型绘制预览
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityCircle circle:
                    canvas.DrawCircle(circle.Center.X, circle.Center.Y, circle.Radius, _previewPaint);
                    break;
                    
                case EntityRectangle rect:
                    canvas.DrawRect(rect.Corner1.X, rect.Corner1.Y,
                                  rect.Width, rect.Height, _previewPaint);
                    break;
            }
        }
        
        protected EntityBase CloneEntity(EntityBase original, Matrix3x2 transform)
        {
            EntityBase clone = null;
            
            switch (original)
            {
                case EntityLine line:
                    clone = new EntityLine
                    {
                        StartPoint = Vector2.Transform(line.StartPoint, transform),
                        EndPoint = Vector2.Transform(line.EndPoint, transform),
                        LineType = line.LineType,
                        LineWeight = line.LineWeight,
                        Color = line.Color
                    };
                    break;
                    
                case EntityCircle circle:
                    clone = new EntityCircle
                    {
                        Center = Vector2.Transform(circle.Center, transform),
                        Radius = circle.Radius,
                        LineType = circle.LineType,
                        LineWeight = circle.LineWeight,
                        Color = circle.Color
                    };
                    break;
                    
                case EntityRectangle rect:
                    clone = new EntityRectangle
                    {
                        Corner1 = Vector2.Transform(rect.Corner1, transform),
                        Corner2 = Vector2.Transform(rect.Corner2, transform),
                        LineType = rect.LineType,
                        LineWeight = rect.LineWeight,
                        Color = rect.Color
                    };
                    break;
                    
                case EntityArc arc:
                    clone = new EntityArc
                    {
                        Center = Vector2.Transform(arc.Center, transform),
                        Radius = arc.Radius,
                        StartAngle = arc.StartAngle,
                        EndAngle = arc.EndAngle,
                        LineType = arc.LineType,
                        LineWeight = arc.LineWeight,
                        Color = arc.Color
                    };
                    break;
                    
                default:
                    // 对于其他类型的实体，尝试通用克隆
                    clone = CloneEntityGeneric(original, transform);
                    break;
            }
            
            return clone;
        }
        
        protected virtual EntityBase CloneEntityGeneric(EntityBase original, Matrix3x2 transform)
        {
            // 通用克隆方法，可以在子类中重写
            return null;
        }
        
        public override void Cancel()
        {
            _previewEntities.Clear();
            _selectedEntities.Clear();
            _currentState = ArrayState.SelectEntities;
            base.Cancel();
        }
        
        public override void Finish()
        {
            _previewEntities.Clear();
            _selectedEntities.Clear();
            base.Finish();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _previewPaint?.Dispose();
                _selectedPaint?.Dispose();
                _gridPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    /// <summary>
    /// 矩形阵列命令
    /// </summary>
    public class RectangularArrayCmd : ArrayCmd
    {
        private int _rows = 2;
        private int _columns = 2;
        private float _rowSpacing = 100.0f;
        private float _columnSpacing = 100.0f;
        private Vector2 _basePoint;
        private Vector2 _currentPoint;
        
        public RectangularArrayCmd() : base(ArrayType.Rectangular)
        {
        }
        
        protected override void OnStartArrayDefinition()
        {
            _currentState = ArrayState.SpecifyFirstCorner;
            _viewer.Document.Prompt = "指定阵列的第一个角点：";
        }
        
        protected override void HandleSpecificArrayInput(Vector2 point, MouseEventArgs e)
        {
            switch (_currentState)
            {
                case ArrayState.SpecifyFirstCorner:
                    _basePoint = point;
                    _currentState = ArrayState.SpecifySecondCorner;
                    _viewer.Document.Prompt = "指定阵列的第二个角点：";
                    break;
                    
                case ArrayState.SpecifySecondCorner:
                    CalculateArrayParameters(point);
                    _currentState = ArrayState.PreviewArray;
                    _viewer.Document.Prompt = $"行数[{_rows}] 列数[{_columns}] 行间距[{_rowSpacing:F1}] 列间距[{_columnSpacing:F1}] 按Enter确认：";
                    break;
            }
        }
        
        protected override void HandleSpecificArrayKeys(KeyEventArgs e)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                switch (e.KeyCode)
                {
                    case Keys.R:
                        // 修改行数
                        if (_viewer._dynamicInputer != null)
                        {
                            _viewer._dynamicInputer.StartInput(_currentPoint, DynInputStatus.WaitForNumber);
                            _viewer.Document.Prompt = "输入行数：";
                        }
                        break;
                        
                    case Keys.C:
                        // 修改列数
                        if (_viewer._dynamicInputer != null)
                        {
                            _viewer._dynamicInputer.StartInput(_currentPoint, DynInputStatus.WaitForNumber);
                            _viewer.Document.Prompt = "输入列数：";
                        }
                        break;
                }
            }
        }
        
        private void CalculateArrayParameters(Vector2 secondPoint)
        {
            var delta = secondPoint - _basePoint;
            _columnSpacing = Math.Abs(delta.X);
            _rowSpacing = Math.Abs(delta.Y);
            
            // 根据距离自动计算行列数
            if (_columnSpacing > 0)
                _columns = Math.Max(2, (int)(_columnSpacing / 50) + 1);
            if (_rowSpacing > 0)
                _rows = Math.Max(2, (int)(_rowSpacing / 50) + 1);
        }
        
        protected override void UpdateArrayPreview(Vector2 currentPoint)
        {
            _currentPoint = currentPoint;
            
            if (_currentState == ArrayState.PreviewArray)
            {
                _previewEntities = GenerateArrayEntities();
            }
        }
        
        protected override List<EntityBase> GenerateArrayEntities()
        {
            var arrayEntities = new List<EntityBase>();

            try
            {
                // 性能优化：预分配容量
                var totalItems = _rows * _columns - 1; // 减去原始位置
                var totalEntities = totalItems * _selectedEntities.Count;
                arrayEntities.Capacity = totalEntities;

                // 性能优化：批量生成变换矩阵
                var transforms = new List<Matrix3x2>(totalItems);

                for (int row = 0; row < _rows; row++)
                {
                    for (int col = 0; col < _columns; col++)
                    {
                        if (row == 0 && col == 0) continue; // 跳过原始位置

                        var offset = new Vector2(col * _columnSpacing, row * _rowSpacing);
                        transforms.Add(Matrix3x2.CreateTranslation(offset));
                    }
                }

                // 克隆实体并应用变换
                foreach (var transform in transforms)
                {
                    foreach (var entity in _selectedEntities)
                    {
                        try
                        {
                            var clonedEntity = entity.Clone();
                            if (clonedEntity != null)
                            {
                                // 应用变换
                                ApplyTransform(clonedEntity, transform);
                                arrayEntities.Add(clonedEntity);
                            }
                        }
                        catch (System.Exception cloneEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"克隆实体失败: {cloneEx.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Array generation error: {ex.Message}");
            }

            return arrayEntities;
        }
        
        protected override void RenderArraySpecificGraphics(SKCanvas canvas)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                // 绘制阵列网格
                for (int row = 0; row <= _rows; row++)
                {
                    var y = _basePoint.Y + row * _rowSpacing;
                    canvas.DrawLine(_basePoint.X, y, 
                                   _basePoint.X + _columns * _columnSpacing, y, _gridPaint);
                }
                
                for (int col = 0; col <= _columns; col++)
                {
                    var x = _basePoint.X + col * _columnSpacing;
                    canvas.DrawLine(x, _basePoint.Y,
                                   x, _basePoint.Y + _rows * _rowSpacing, _gridPaint);
                }
            }
        }
    }
    
    /// <summary>
    /// 极轴阵列命令
    /// </summary>
    public class PolarArrayCmd : ArrayCmd
    {
        private Vector2 _centerPoint;
        private int _itemCount = 6;
        private float _totalAngle = 360.0f;
        private bool _rotateItems = true;
        private float _radius;
        
        public PolarArrayCmd() : base(ArrayType.Polar)
        {
        }
        
        protected override void OnStartArrayDefinition()
        {
            _currentState = ArrayState.SpecifyCenter;
            _viewer.Document.Prompt = "指定阵列的中心点：";
        }
        
        protected override void HandleSpecificArrayInput(Vector2 point, MouseEventArgs e)
        {
            switch (_currentState)
            {
                case ArrayState.SpecifyCenter:
                    _centerPoint = point;
                    CalculateRadius();
                    _currentState = ArrayState.PreviewArray;
                    _viewer.Document.Prompt = $"项目数[{_itemCount}] 角度[{_totalAngle:F1}°] 旋转[{(_rotateItems ? "是" : "否")}] 按Enter确认：";
                    break;
            }
        }
        
        protected override void HandleSpecificArrayKeys(KeyEventArgs e)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                switch (e.KeyCode)
                {
                    case Keys.I:
                        // 修改项目数
                        break;
                        
                    case Keys.A:
                        // 修改角度
                        break;
                        
                    case Keys.R:
                        // 切换旋转选项
                        _rotateItems = !_rotateItems;
                        _viewer.Document.Prompt = $"项目数[{_itemCount}] 角度[{_totalAngle:F1}°] 旋转[{(_rotateItems ? "是" : "否")}] 按Enter确认：";
                        break;
                }
            }
        }
        
        private void CalculateRadius()
        {
            if (_selectedEntities.Count > 0)
            {
                var firstEntity = _selectedEntities[0];
                var bounds = firstEntity.BoundingBox;
                if (bounds != null)
                {
                    var entityCenter = bounds.Center;
                    _radius = Vector2.Distance(_centerPoint, entityCenter);
                }
            }
        }
        
        protected override void UpdateArrayPreview(Vector2 currentPoint)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                _previewEntities = GenerateArrayEntities();
            }
        }
        
        protected override List<EntityBase> GenerateArrayEntities()
        {
            var arrayEntities = new List<EntityBase>();
            var angleStep = _totalAngle / _itemCount;
            
            for (int i = 1; i < _itemCount; i++) // 跳过第一个（原始位置）
            {
                var angle = i * angleStep * Math.PI / 180.0; // 转换为弧度
                
                Matrix3x2 transform;
                
                if (_rotateItems)
                {
                    // 先平移到原点，旋转，再平移回去
                    transform = Matrix3x2.CreateTranslation(-_centerPoint) *
                               Matrix3x2.CreateRotation((float)angle) *
                               Matrix3x2.CreateTranslation(_centerPoint);
                }
                else
                {
                    // 只做圆周平移
                    var offset = new Vector2(
                        (float)(_radius * Math.Cos(angle)) - _radius,
                        (float)(_radius * Math.Sin(angle))
                    );
                    transform = Matrix3x2.CreateTranslation(offset);
                }
                
                foreach (var entity in _selectedEntities)
                {
                    var clonedEntity = CloneEntity(entity, transform);
                    if (clonedEntity != null)
                    {
                        arrayEntities.Add(clonedEntity);
                    }
                }
            }
            
            return arrayEntities;
        }
        
        protected override void RenderArraySpecificGraphics(SKCanvas canvas)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                // 绘制圆形参考线
                canvas.DrawCircle(_centerPoint.X, _centerPoint.Y, _radius, _gridPaint);
                
                // 绘制角度分割线
                var angleStep = _totalAngle / _itemCount * Math.PI / 180.0;
                for (int i = 0; i < _itemCount; i++)
                {
                    var angle = i * angleStep;
                    var endPoint = new Vector2(
                        _centerPoint.X + (float)(_radius * Math.Cos(angle)),
                        _centerPoint.Y + (float)(_radius * Math.Sin(angle))
                    );
                    canvas.DrawLine(_centerPoint.X, _centerPoint.Y, endPoint.X, endPoint.Y, _gridPaint);
                }
            }
        }
    }
    
    /// <summary>
    /// 路径阵列命令
    /// </summary>
    public class PathArrayCmd : ArrayCmd
    {
        private EntityBase _pathEntity;
        private int _itemCount = 5;
        private bool _alignToPath = true;
        private List<Vector2> _pathPoints;
        
        public PathArrayCmd() : base(ArrayType.Path)
        {
            _pathPoints = new List<Vector2>();
        }
        
        protected override void OnStartArrayDefinition()
        {
            _currentState = ArrayState.SelectPath;
            _viewer.Document.Prompt = "选择路径对象：";
        }
        
        protected override void HandleSpecificArrayInput(Vector2 point, MouseEventArgs e)
        {
            switch (_currentState)
            {
                case ArrayState.SelectPath:
                    var pathEntity = FindSelectableEntity(point);
                    if (pathEntity != null && IsValidPathEntity(pathEntity))
                    {
                        _pathEntity = pathEntity;
                        GeneratePathPoints();
                        _currentState = ArrayState.PreviewArray;
                        _viewer.Document.Prompt = $"项目数[{_itemCount}] 对齐路径[{(_alignToPath ? "是" : "否")}] 按Enter确认：";
                    }
                    else
                    {
                        _viewer.Document.Prompt = "选择的对象不是有效的路径，请重新选择：";
                    }
                    break;
            }
        }
        
        protected override void HandleSpecificArrayKeys(KeyEventArgs e)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                switch (e.KeyCode)
                {
                    case Keys.I:
                        // 修改项目数
                        break;
                        
                    case Keys.A:
                        // 切换对齐选项
                        _alignToPath = !_alignToPath;
                        _viewer.Document.Prompt = $"项目数[{_itemCount}] 对齐路径[{(_alignToPath ? "是" : "否")}] 按Enter确认：";
                        break;
                }
            }
        }
        
        private bool IsValidPathEntity(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityArc ||
                   entity is EntityCircle ||
                   entity is EntityLwPolyline ||
                   entity is EntitySpline;
        }
        
        private void GeneratePathPoints()
        {
            _pathPoints.Clear();
            
            switch (_pathEntity)
            {
                case EntityLine line:
                    GenerateLinePathPoints(line);
                    break;
                    
                case EntityArc arc:
                    GenerateArcPathPoints(arc);
                    break;
                    
                case EntityLwPolyline polyline:
                    GeneratePolylinePathPoints(polyline);
                    break;
            }
        }
        
        private void GenerateLinePathPoints(EntityLine line)
        {
            for (int i = 0; i < _itemCount; i++)
            {
                var t = (float)i / (_itemCount - 1);
                var point = Vector2.Lerp(line.StartPoint, line.EndPoint, t);
                _pathPoints.Add(point);
            }
        }
        
        private void GenerateArcPathPoints(EntityArc arc)
        {
            var totalAngle = arc.EndAngle - arc.StartAngle;
            
            for (int i = 0; i < _itemCount; i++)
            {
                var t = (float)i / (_itemCount - 1);
                var angle = arc.StartAngle + totalAngle * t;
                var angleRad = angle * Math.PI / 180.0;
                
                var point = new Vector2(
                    arc.Center.X + (float)(arc.Radius * Math.Cos(angleRad)),
                    arc.Center.Y + (float)(arc.Radius * Math.Sin(angleRad))
                );
                
                _pathPoints.Add(point);
            }
        }
        
        private void GeneratePolylinePathPoints(EntityLwPolyline polyline)
        {
            // 简化实现：沿多段线顶点分布
            var vertices = polyline.Vertexs.Select(v => v.Position).ToList();
            
            for (int i = 0; i < _itemCount; i++)
            {
                var t = (float)i / (_itemCount - 1) * (vertices.Count - 1);
                var index = (int)t;
                var fraction = t - index;
                
                if (index >= vertices.Count - 1)
                {
                    _pathPoints.Add(vertices.Last());
                }
                else
                {
                    var point = Vector2.Lerp(vertices[index], vertices[index + 1], fraction);
                    _pathPoints.Add(point);
                }
            }
        }
        
        protected override void UpdateArrayPreview(Vector2 currentPoint)
        {
            if (_currentState == ArrayState.PreviewArray)
            {
                _previewEntities = GenerateArrayEntities();
            }
        }
        
        protected override List<EntityBase> GenerateArrayEntities()
        {
            var arrayEntities = new List<EntityBase>();
            
            for (int i = 1; i < _pathPoints.Count; i++) // 跳过第一个点（原始位置）
            {
                var targetPoint = _pathPoints[i];
                var originalPoint = _pathPoints[0];
                var offset = targetPoint - originalPoint;
                
                Matrix3x2 transform = Matrix3x2.CreateTranslation(offset);
                
                // 如果需要对齐到路径，还需要计算旋转
                if (_alignToPath && i < _pathPoints.Count - 1)
                {
                    var direction = Vector2.Normalize(_pathPoints[i + 1] - _pathPoints[i]);
                    var angle = Math.Atan2(direction.Y, direction.X);
                    
                    transform = Matrix3x2.CreateTranslation(-originalPoint) *
                               Matrix3x2.CreateRotation((float)angle) *
                               Matrix3x2.CreateTranslation(targetPoint);
                }
                
                foreach (var entity in _selectedEntities)
                {
                    var clonedEntity = CloneEntity(entity, transform);
                    if (clonedEntity != null)
                    {
                        arrayEntities.Add(clonedEntity);
                    }
                }
            }
            
            return arrayEntities;
        }
        
        protected override void RenderArraySpecificGraphics(SKCanvas canvas)
        {
            if (_currentState == ArrayState.PreviewArray && _pathPoints.Count > 1)
            {
                // 绘制路径点
                foreach (var point in _pathPoints)
                {
                    canvas.DrawCircle(point.X, point.Y, 3, _gridPaint);
                }
                
                // 绘制路径连线
                for (int i = 0; i < _pathPoints.Count - 1; i++)
                {
                    canvas.DrawLine(_pathPoints[i].X, _pathPoints[i].Y,
                                   _pathPoints[i + 1].X, _pathPoints[i + 1].Y, _gridPaint);
                }
            }
        }

        /// <summary>
        /// 应用变换到实体
        /// </summary>
        private void ApplyTransform(EntityBase entity, Matrix3x2 transform)
        {
            try
            {
                if (entity is EntityLine line)
                {
                    line.StartPoint = Vector2.Transform(line.StartPoint, transform);
                    line.EndPoint = Vector2.Transform(line.EndPoint, transform);
                }
                else if (entity is EntityCircle circle)
                {
                    circle.Center = Vector2.Transform(circle.Center, transform);
                }
                else if (entity is EntityArc arc)
                {
                    arc.Center = Vector2.Transform(arc.Center, transform);
                }
                else if (entity is EntityRectangle rect)
                {
                    rect.StartPoint = Vector2.Transform(rect.StartPoint, transform);
                    rect.EndPoint = Vector2.Transform(rect.EndPoint, transform);
                }
                else if (entity is EntityLwPolyline polyline)
                {
                    for (int i = 0; i < polyline.Vertexes.Count; i++)
                    {
                        polyline.Vertexes[i] = Vector2.Transform(polyline.Vertexes[i], transform);
                    }
                }
                else if (entity is EntityPoint point)
                {
                    point.Position = Vector2.Transform(point.Position, transform);
                }
                // 可以扩展更多实体类型的变换
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用变换失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 阵列类型
    /// </summary>
    public enum ArrayType
    {
        Rectangular,  // 矩形阵列
        Polar,        // 极轴阵列
        Path          // 路径阵列
    }
    
    /// <summary>
    /// 阵列状态
    /// </summary>
    public enum ArrayState
    {
        SelectEntities,      // 选择实体
        SpecifyFirstCorner,  // 指定第一个角点
        SpecifySecondCorner, // 指定第二个角点
        SpecifyCenter,       // 指定中心点
        SelectPath,          // 选择路径
        PreviewArray         // 预览阵列
    }
    
    /// <summary>
    /// 阵列选项
    /// </summary>
    public class ArrayOptions
    {
        public bool PreserveOriginal { get; set; } = true;
        public string Layer { get; set; } = "0";
        public bool ShowPreview { get; set; } = true;
        public bool AssociativeArray { get; set; } = false;
    }
} 