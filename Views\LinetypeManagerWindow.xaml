<Window x:Class="McLaser.EditViewerSk.Views.LinetypeManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="线型管理器" Height="500" Width="700" 
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 线型列表 -->
        <DockPanel Grid.Column="0" Margin="10">
            <TextBlock Text="线型列表" Style="{StaticResource HeaderTextStyle}" DockPanel.Dock="Top"/>
            
            <StackPanel DockPanel.Dock="Bottom" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                <Button Content="新建" Width="60" Height="30" Margin="5" Click="NewLinetype_Click"/>
                <Button Content="删除" Width="60" Height="30" Margin="5" Click="DeleteLinetype_Click"/>
                <Button Content="加载" Width="60" Height="30" Margin="5" Click="LoadLinetype_Click"/>
            </StackPanel>
            
            <ListBox Name="LinetypeListBox" SelectionChanged="LinetypeListBox_SelectionChanged" Margin="0,5">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Vertical" Margin="5">
                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                            <Canvas Height="20" Width="200" Background="White" Margin="0,2">
                                <!-- 线型预览将在这里显示 -->
                                <Line X1="5" Y1="10" X2="195" Y2="10" Stroke="Black" StrokeThickness="2"/>
                            </Canvas>
                            <TextBlock Text="{Binding Description}" FontSize="10" Foreground="Gray"/>
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </DockPanel>
        
        <!-- 线型属性编辑 -->
        <ScrollViewer Grid.Column="1" Margin="10" VerticalScrollBarVisibility="Auto">
            <StackPanel Name="PropertiesPanel">
                <TextBlock Text="线型属性" Style="{StaticResource HeaderTextStyle}"/>
                
                <!-- 基本信息 -->
                <GroupBox Header="基本信息" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="线型名称:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <TextBox Name="LinetypeNameTextBox" Grid.Row="0" Grid.Column="1" Height="25" Margin="5"/>
                        
                        <TextBlock Text="描述:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <TextBox Name="DescriptionTextBox" Grid.Row="1" Grid.Column="1" Height="25" Margin="5"/>
                        
                        <TextBlock Text="比例因子:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <TextBox Name="ScaleFactorTextBox" Grid.Row="2" Grid.Column="1" Height="25" Margin="5" Text="1.0"/>
                    </Grid>
                </GroupBox>
                
                <!-- 线型定义 -->
                <GroupBox Header="线型定义" Margin="0,10">
                    <StackPanel>
                        <TextBlock Text="线型模式 (正数表示实线长度，负数表示空白长度):" Margin="5"/>
                        <TextBox Name="PatternTextBox" Height="60" TextWrapping="Wrap" 
                                 AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="5"
                                 Text="5, -2, 1, -2"/>
                        
                        <TextBlock Text="预设模式:" Margin="5,10,5,5"/>
                        <WrapPanel Margin="5">
                            <Button Content="实线" Width="60" Height="25" Margin="2" Click="PresetPattern_Click" Tag=""/>
                            <Button Content="虚线" Width="60" Height="25" Margin="2" Click="PresetPattern_Click" Tag="5, -5"/>
                            <Button Content="点线" Width="60" Height="25" Margin="2" Click="PresetPattern_Click" Tag="5, -2, 1, -2"/>
                            <Button Content="点划线" Width="60" Height="25" Margin="2" Click="PresetPattern_Click" Tag="10, -2, 1, -2, 1, -2"/>
                            <Button Content="双点划线" Width="80" Height="25" Margin="2" Click="PresetPattern_Click" Tag="10, -2, 1, -2, 1, -2, 1, -2"/>
                        </WrapPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 预览 -->
                <GroupBox Header="预览" Margin="0,10">
                    <StackPanel>
                        <Canvas Name="PreviewCanvas" Height="60" Background="White" Margin="5">
                            <Line Name="PreviewLine" X1="10" Y1="30" X2="400" Y2="30" 
                                  Stroke="Black" StrokeThickness="2"/>
                        </Canvas>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                            <Button Content="更新预览" Width="80" Height="25" Margin="5" Click="UpdatePreview_Click"/>
                            <Button Content="应用到图层" Width="80" Height="25" Margin="5" Click="ApplyToLayer_Click"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 高级设置 -->
                <GroupBox Header="高级设置" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="线宽:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <TextBox Name="LineWidthTextBox" Grid.Row="0" Grid.Column="1" Height="25" Margin="5" Text="1.0"/>
                        
                        <TextBlock Text="端点样式:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <ComboBox Name="EndCapComboBox" Grid.Row="1" Grid.Column="1" Height="25" Margin="5">
                            <ComboBoxItem Content="平端" IsSelected="True"/>
                            <ComboBoxItem Content="圆端"/>
                            <ComboBoxItem Content="方端"/>
                        </ComboBox>
                        
                        <CheckBox Name="IsCurrentCheckBox" Content="设为当前线型" Grid.Row="2" Grid.Column="1" 
                                  Margin="5" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>
                
                <!-- 按钮区域 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20">
                    <Button Content="导入" Width="80" Height="35" Margin="5" Click="Import_Click"/>
                    <Button Content="导出" Width="80" Height="35" Margin="5" Click="Export_Click"/>
                    <Button Content="应用" Width="80" Height="35" Margin="5" Click="Apply_Click"/>
                    <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click"/>
                    <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
