using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Base
{
    /// <summary>
    /// 改进的命令基类
    /// 解决资源管理、状态管理和依赖注入问题
    /// </summary>
    public abstract class CommandBase : IDisposable
    {
        protected IView _view;
        protected IDocument _document;
        protected CommandState _state;
        protected Dictionary<string, object> _parameters;
        protected CommandConfiguration _config;
        
        // 静态资源池，避免重复创建
        private static readonly Dictionary<string, SKPaint> _paintCache = new Dictionary<string, SKPaint>();
        
        public abstract string Name { get; }
        public abstract string Description { get; }
        public virtual string Category => "Edit";
        public virtual bool RequiresSelection => false;
        
        public CommandState State => _state;
        public bool IsActive => _state != CommandState.Inactive;
        
        public event EventHandler<CommandStateChangedEventArgs> StateChanged;
        public event EventHandler<CommandCompletedEventArgs> Completed;
        public event EventHandler<CommandCancelledEventArgs> Cancelled;
        
        protected CommandBase(IView view, CommandConfiguration config = null)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _document = view.Document;
            _config = config ?? CommandConfiguration.Default;
            _parameters = new Dictionary<string, object>();
            _state = CommandState.Inactive;
        }
        
        /// <summary>
        /// 获取缓存的Paint对象
        /// </summary>
        protected SKPaint GetCachedPaint(string key, Func<SKPaint> factory)
        {
            if (!_paintCache.TryGetValue(key, out SKPaint paint))
            {
                paint = factory();
                _paintCache[key] = paint;
            }
            return paint;
        }
        
        /// <summary>
        /// 设置命令状态
        /// </summary>
        protected virtual void SetState(CommandState newState)
        {
            if (_state != newState)
            {
                var oldState = _state;
                _state = newState;
                OnStateChanged(oldState, newState);
            }
        }
        
        /// <summary>
        /// 状态改变事件
        /// </summary>
        protected virtual void OnStateChanged(CommandState oldState, CommandState newState)
        {
            StateChanged?.Invoke(this, new CommandStateChangedEventArgs
            {
                OldState = oldState,
                NewState = newState,
                Command = this
            });
        }
        
        /// <summary>
        /// 开始命令
        /// </summary>
        public virtual void Start()
        {
            if (_state != CommandState.Inactive)
                throw new InvalidOperationException("Command is already active");
            
            SetState(CommandState.Active);
            OnStart();
        }
        
        /// <summary>
        /// 完成命令
        /// </summary>
        public virtual void Complete()
        {
            if (_state != CommandState.Active)
                return;
            
            SetState(CommandState.Completed);
            OnComplete();
            
            Completed?.Invoke(this, new CommandCompletedEventArgs
            {
                Command = this,
                Result = GetCommandResult()
            });
        }
        
        /// <summary>
        /// 取消命令
        /// </summary>
        public virtual void Cancel()
        {
            if (_state == CommandState.Inactive || _state == CommandState.Completed)
                return;
            
            SetState(CommandState.Cancelled);
            OnCancel();
            
            Cancelled?.Invoke(this, new CommandCancelledEventArgs
            {
                Command = this,
                Reason = "User cancelled"
            });
        }
        
        // 抽象方法，由子类实现
        protected abstract void OnStart();
        protected abstract void OnComplete();
        protected abstract void OnCancel();
        protected abstract object GetCommandResult();
        
        // 输入处理方法
        public abstract void HandleMouseDown(MouseEventArgs e);
        public abstract void HandleMouseMove(MouseEventArgs e);
        public abstract void HandleMouseUp(MouseEventArgs e);
        public abstract void HandleKeyDown(KeyEventArgs e);
        public abstract void Render(SKCanvas canvas);
        
        /// <summary>
        /// 获取本地化字符串
        /// </summary>
        protected string GetLocalizedString(string key, params object[] args)
        {
            // TODO: 实现本地化资源管理
            return string.Format(_config.GetLocalizedString(key), args);
        }
        
        /// <summary>
        /// 验证操作有效性
        /// </summary>
        protected virtual bool ValidateOperation()
        {
            return _state == CommandState.Active;
        }
        
        /// <summary>
        /// 获取配置值
        /// </summary>
        protected T GetConfigValue<T>(string key, T defaultValue = default(T))
        {
            return _config.GetValue(key, defaultValue);
        }
        
        public virtual void Dispose()
        {
            Cancel();
            _parameters?.Clear();
            
            // 注意：不要Dispose静态缓存的Paint，它们是共享的
        }
        
        /// <summary>
        /// 清理静态资源（应用程序退出时调用）
        /// </summary>
        public static void CleanupStaticResources()
        {
            foreach (var paint in _paintCache.Values)
            {
                paint?.Dispose();
            }
            _paintCache.Clear();
        }
    }
    
    /// <summary>
    /// 命令状态
    /// </summary>
    public enum CommandState
    {
        Inactive,
        Active,
        Completed,
        Cancelled,
        Error
    }
    
    /// <summary>
    /// 命令配置
    /// </summary>
    public class CommandConfiguration
    {
        public static CommandConfiguration Default { get; } = new CommandConfiguration();
        
        public float DefaultTolerance { get; set; } = 5.0f;
        public bool EnablePreview { get; set; } = true;
        public string Language { get; set; } = "zh-CN";
        
        private Dictionary<string, object> _values = new Dictionary<string, object>();
        private Dictionary<string, string> _localizedStrings = new Dictionary<string, string>();
        
        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            return _values.TryGetValue(key, out object value) ? (T)value : defaultValue;
        }
        
        public void SetValue<T>(string key, T value)
        {
            _values[key] = value;
        }
        
        public string GetLocalizedString(string key)
        {
            return _localizedStrings.TryGetValue(key, out string value) ? value : key;
        }
    }
    
    /// <summary>
    /// 命令事件参数
    /// </summary>
    public class CommandStateChangedEventArgs : EventArgs
    {
        public CommandState OldState { get; set; }
        public CommandState NewState { get; set; }
        public CommandBase Command { get; set; }
    }
    
    public class CommandCompletedEventArgs : EventArgs
    {
        public CommandBase Command { get; set; }
        public object Result { get; set; }
    }
    
    public class CommandCancelledEventArgs : EventArgs
    {
        public CommandBase Command { get; set; }
        public string Reason { get; set; }
    }
} 