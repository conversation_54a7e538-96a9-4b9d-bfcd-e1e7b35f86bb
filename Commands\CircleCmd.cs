﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class CircleCmd : DrawCmd
    {
        private EntityCircle _circle = null;
        private DocumentBase doc;

        public CircleCmd(DocumentBase doc)
        {
            this.doc = doc;
        }

        /// <summary>
        /// 新增的图元列表
        /// </summary>
        private List<EntityBase> _newEntities = new List<EntityBase>();

        protected override IEnumerable<EntityBase> newEntities
        {
            get { return _newEntities; }
        }

        
        private Step _step = Step.Step1_SpecifyCenter;
        private enum Step
        {
            Step1_SpecifyCenter = 1,
            Step2_SpecityRadius = 2,
        }

        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyCenter;
            this.pointer.Mode = IndicatorMode.Locate;
            this.pointer.Document.Prompt = "指定圆的中心点:";
        }

        protected override void Commit()
        {
            try
            {
                if (this.newEntities != null && this.newEntities.Count() == 1)
                {
                    doc.Action.ActEntityAdd(newEntities.First());
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"保存圆形时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"CircleCmd.Commit error: {ex}");
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            try
            {
                switch (_step)
                {
                    case Step.Step1_SpecifyCenter:
                        if (e.Button == MouseButtons.Left)
                        {
                            _circle = new EntityCircle();
                            _circle.Center = this.pointer.CurrentSnapPoint;
                            _circle.Radius = 0;
                            _circle.LayerId = doc.ActiveLayer?.Id ?? 0;
                            _circle.Color = doc.ActiveLayer?.Color ?? SkiaSharp.SKColors.White;

                            _newEntities.Add(_circle);
                            _step = Step.Step2_SpecityRadius;
                            this.pointer.Document.Prompt = "指定圆的半径:";
                        }
                        else if (e.Button == MouseButtons.Right)
                        {
                            _mgr.CancelCurrentCommand();
                        }
                        break;

                    case Step.Step2_SpecityRadius:
                        if (e.Button == MouseButtons.Left)
                        {
                            Vector2 vector = this.pointer.CurrentSnapPoint;
                            _circle.Radius = (_circle.Center - this.pointer.CurrentSnapPoint).Length;
                            _mgr.FinishCurrentCommand();
                        }
                        else if (e.Button == MouseButtons.Right)
                        {
                            _mgr.CancelCurrentCommand();
                        }
                        break;
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"创建圆形时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"CircleCmd.OnMouseDown error: {ex}");
                _mgr.CancelCurrentCommand();
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            // 鼠标确认逻辑已移动到OnMouseDown中
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecityRadius)
            {
                Vector2 vector = this.pointer.CurrentSnapPoint;
                _circle.Radius = (_circle.Center - this.pointer.CurrentSnapPoint).Length;
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }

        public override void OnPaint(ViewBase _viewer)
        {

            if (_circle != null)
            {
                ViewBase viewer = _mgr.Viewer as ViewBase;
                _circle.Render(viewer);
            }
        }
    }
}
