using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Numerics;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Spatial
{
    /// <summary>
    /// 空间索引管理器
    /// 提供高性能的空间查询、选择和碰撞检测功能
    /// </summary>
    public class SpatialIndexManager : IDisposable
    {
        #region 私有字段

        private static SpatialIndexManager _instance;
        private static readonly object _lockObject = new object();

        private readonly Dictionary<DocumentBase, QuadTree> _documentTrees;
        private readonly object _treeLock = new object();
        private bool _isDisposed;
        private bool _isEnabled = true;

        // 性能统计
        private long _queryCount;
        private long _insertCount;
        private long _updateCount;
        private long _removeCount;
        private TimeSpan _totalQueryTime;

        #endregion

        #region 单例模式

        public static SpatialIndexManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new SpatialIndexManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private SpatialIndexManager()
        {
            _documentTrees = new Dictionary<DocumentBase, QuadTree>();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否启用空间索引
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => _isEnabled = value;
        }

        /// <summary>
        /// 查询次数统计
        /// </summary>
        public long QueryCount => _queryCount;

        /// <summary>
        /// 平均查询时间
        /// </summary>
        public double AverageQueryTime => _queryCount > 0 ? _totalQueryTime.TotalMilliseconds / _queryCount : 0;

        #endregion

        #region 文档管理

        /// <summary>
        /// 为文档创建空间索引
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="bounds">空间边界</param>
        /// <returns>是否成功创建</returns>
        public bool CreateIndex(DocumentBase document, BoundingBox bounds)
        {
            if (_isDisposed || document == null || bounds.IsEmpty) return false;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.ContainsKey(document))
                    {
                        _documentTrees[document].Dispose();
                    }

                    var quadTree = new QuadTree(bounds, maxDepth: 8, maxEntitiesPerNode: 10);
                    _documentTrees[document] = quadTree;

                    // 批量插入现有实体
                    var allEntities = GetAllEntities(document);
                    foreach (var entity in allEntities)
                    {
                        quadTree.Insert(entity);
                    }

                    Debug.WriteLine($"Created spatial index for document with {allEntities.Count} entities");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating spatial index: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除文档的空间索引
        /// </summary>
        /// <param name="document">文档</param>
        public void RemoveIndex(DocumentBase document)
        {
            if (_isDisposed || document == null) return;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        quadTree.Dispose();
                        _documentTrees.Remove(document);
                        Debug.WriteLine("Removed spatial index for document");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing spatial index: {ex.Message}");
            }
        }

        /// <summary>
        /// 重建文档的空间索引
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="newBounds">新的空间边界</param>
        public void RebuildIndex(DocumentBase document, BoundingBox newBounds)
        {
            if (_isDisposed || document == null) return;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        quadTree.Rebuild(newBounds);
                        Debug.WriteLine("Rebuilt spatial index for document");
                    }
                    else
                    {
                        CreateIndex(document, newBounds);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error rebuilding spatial index: {ex.Message}");
            }
        }

        #endregion

        #region 实体操作

        /// <summary>
        /// 插入实体到空间索引
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="entity">实体</param>
        /// <returns>是否成功插入</returns>
        public bool InsertEntity(DocumentBase document, EntityBase entity)
        {
            if (_isDisposed || !_isEnabled || document == null || entity == null) return false;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        var result = quadTree.Insert(entity);
                        if (result)
                        {
                            _insertCount++;
                        }
                        return result;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error inserting entity: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从空间索引移除实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="entity">实体</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveEntity(DocumentBase document, EntityBase entity)
        {
            if (_isDisposed || !_isEnabled || document == null || entity == null) return false;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        var result = quadTree.Remove(entity);
                        if (result)
                        {
                            _removeCount++;
                        }
                        return result;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing entity: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新实体在空间索引中的位置
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="entity">实体</param>
        /// <returns>是否成功更新</returns>
        public bool UpdateEntity(DocumentBase document, EntityBase entity)
        {
            if (_isDisposed || !_isEnabled || document == null || entity == null) return false;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        var result = quadTree.Update(entity);
                        if (result)
                        {
                            _updateCount++;
                        }
                        return result;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating entity: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 空间查询

        /// <summary>
        /// 查询指定区域内的实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="queryBounds">查询区域</param>
        /// <returns>实体列表</returns>
        public List<EntityBase> QueryRegion(DocumentBase document, BoundingBox queryBounds)
        {
            if (_isDisposed || document == null || queryBounds.IsEmpty)
                return new List<EntityBase>();

            var startTime = DateTime.Now;

            try
            {
                List<EntityBase> results;

                if (_isEnabled)
                {
                    lock (_treeLock)
                    {
                        if (_documentTrees.TryGetValue(document, out var quadTree))
                        {
                            results = quadTree.Query(queryBounds);
                        }
                        else
                        {
                            // 回退到线性搜索
                            results = LinearSearch(document, queryBounds);
                        }
                    }
                }
                else
                {
                    // 空间索引被禁用，使用线性搜索
                    results = LinearSearch(document, queryBounds);
                }

                // 更新统计信息
                _queryCount++;
                _totalQueryTime += DateTime.Now - startTime;

                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error querying region: {ex.Message}");
                return new List<EntityBase>();
            }
        }

        /// <summary>
        /// 查询指定点附近的实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="point">查询点</param>
        /// <param name="radius">查询半径</param>
        /// <returns>实体列表</returns>
        public List<EntityBase> QueryPoint(DocumentBase document, Vector2 point, float radius = 0.1f)
        {
            var queryBounds = new BoundingBox(
                point.X - radius, point.Y + radius,
                point.X + radius, point.Y - radius);

            return QueryRegion(document, queryBounds);
        }

        /// <summary>
        /// 查询视口内的实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="viewport">视口边界</param>
        /// <returns>实体列表</returns>
        public List<EntityBase> QueryViewport(DocumentBase document, BoundingBox viewport)
        {
            return QueryRegion(document, viewport);
        }

        /// <summary>
        /// 查询与指定实体相交的实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="entity">查询实体</param>
        /// <returns>相交的实体列表</returns>
        public List<EntityBase> QueryIntersecting(DocumentBase document, EntityBase entity)
        {
            if (_isDisposed || document == null || entity == null)
                return new List<EntityBase>();

            try
            {
                if (_isEnabled)
                {
                    lock (_treeLock)
                    {
                        if (_documentTrees.TryGetValue(document, out var quadTree))
                        {
                            return quadTree.QueryIntersecting(entity);
                        }
                    }
                }

                // 回退到线性搜索
                var entityBounds = entity.BBox;
                if (entityBounds == null || entityBounds.IsEmpty)
                    return new List<EntityBase>();

                var candidates = LinearSearch(document, entityBounds);
                candidates.Remove(entity); // 移除查询实体本身
                return candidates;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error querying intersecting entities: {ex.Message}");
                return new List<EntityBase>();
            }
        }

        #endregion

        #region 性能优化

        /// <summary>
        /// 批量插入实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="entities">实体列表</param>
        /// <returns>成功插入的数量</returns>
        public int BatchInsertEntities(DocumentBase document, IEnumerable<EntityBase> entities)
        {
            if (_isDisposed || !_isEnabled || document == null || entities == null) return 0;

            try
            {
                lock (_treeLock)
                {
                    if (_documentTrees.TryGetValue(document, out var quadTree))
                    {
                        int successCount = 0;
                        foreach (var entity in entities)
                        {
                            if (quadTree.Insert(entity))
                            {
                                successCount++;
                            }
                        }

                        _insertCount += successCount;
                        return successCount;
                    }
                }

                return 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error batch inserting entities: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 异步重建索引
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="newBounds">新边界</param>
        /// <returns>异步任务</returns>
        public Task RebuildIndexAsync(DocumentBase document, BoundingBox newBounds)
        {
            return Task.Run(() => RebuildIndex(document, newBounds));
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 线性搜索（回退方法）
        /// </summary>
        /// <param name="document">文档</param>
        /// <param name="queryBounds">查询区域</param>
        /// <returns>实体列表</returns>
        private List<EntityBase> LinearSearch(DocumentBase document, BoundingBox queryBounds)
        {
            var results = new List<EntityBase>();

            try
            {
                foreach (var layer in document.Layers)
                {
                    foreach (var entity in layer.Children)
                    {
                        if (entity?.BBox != null && entity.BBox.HitTest(queryBounds, 0.001))
                        {
                            results.Add(entity);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in linear search: {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 获取文档中的所有实体
        /// </summary>
        /// <param name="document">文档</param>
        /// <returns>实体列表</returns>
        private List<EntityBase> GetAllEntities(DocumentBase document)
        {
            var entities = new List<EntityBase>();

            try
            {
                foreach (var layer in document.Layers)
                {
                    entities.AddRange(layer.Children);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all entities: {ex.Message}");
            }

            return entities;
        }

        #endregion

        #region 统计和调试

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计</returns>
        public SpatialIndexStats GetStats()
        {
            try
            {
                lock (_treeLock)
                {
                    var stats = new SpatialIndexStats
                    {
                        IsEnabled = _isEnabled,
                        DocumentCount = _documentTrees.Count,
                        QueryCount = _queryCount,
                        InsertCount = _insertCount,
                        UpdateCount = _updateCount,
                        RemoveCount = _removeCount,
                        AverageQueryTime = AverageQueryTime,
                        TotalQueryTime = _totalQueryTime.TotalMilliseconds
                    };

                    // 收集每个文档的统计信息
                    foreach (var kvp in _documentTrees)
                    {
                        var quadTreeStats = kvp.Value.GetStats();
                        stats.TotalEntities += quadTreeStats.TotalEntities;
                        stats.TotalNodes += quadTreeStats.TotalNodes;
                        stats.LeafNodes += quadTreeStats.LeafNodes;
                    }

                    return stats;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting stats: {ex.Message}");
                return new SpatialIndexStats();
            }
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetStats()
        {
            _queryCount = 0;
            _insertCount = 0;
            _updateCount = 0;
            _removeCount = 0;
            _totalQueryTime = TimeSpan.Zero;
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                lock (_treeLock)
                {
                    foreach (var quadTree in _documentTrees.Values)
                    {
                        quadTree?.Dispose();
                    }
                    _documentTrees.Clear();
                }

                _isDisposed = true;
                Debug.WriteLine("SpatialIndexManager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error disposing SpatialIndexManager: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 空间索引统计信息
    /// </summary>
    public class SpatialIndexStats
    {
        public bool IsEnabled { get; set; }
        public int DocumentCount { get; set; }
        public int TotalEntities { get; set; }
        public int TotalNodes { get; set; }
        public int LeafNodes { get; set; }
        public long QueryCount { get; set; }
        public long InsertCount { get; set; }
        public long UpdateCount { get; set; }
        public long RemoveCount { get; set; }
        public double AverageQueryTime { get; set; }
        public double TotalQueryTime { get; set; }
        public double PerformanceGain => QueryCount > 0 ? Math.Max(1.0, TotalEntities / (AverageQueryTime + 0.001)) : 1.0;
    }
}
