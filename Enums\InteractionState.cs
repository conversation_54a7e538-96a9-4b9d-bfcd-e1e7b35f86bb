namespace McLaser.EditViewerSk.Enums
{
    /// <summary>
    /// 交互状态枚举
    /// 定义CAD软件中的基本交互状态
    /// </summary>
    public enum InteractionState
    {
        /// <summary>
        /// 空闲状态 - 无任何活动交互
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 选择状态 - 正在进行选择操作
        /// </summary>
        Selecting = 1,

        /// <summary>
        /// 拖拽状态 - 正在拖拽对象
        /// </summary>
        Dragging = 2,

        /// <summary>
        /// 平移状态 - 正在平移视图
        /// </summary>
        Panning = 3,

        /// <summary>
        /// 缩放状态 - 正在缩放视图
        /// </summary>
        Zooming = 4,

        /// <summary>
        /// 绘制状态 - 正在绘制图形
        /// </summary>
        Drawing = 5,

        /// <summary>
        /// 编辑状态 - 正在编辑对象
        /// </summary>
        Editing = 6
    }

    /// <summary>
    /// 选择模式枚举
    /// </summary>
    public enum EntitySelectionMode
    {
        /// <summary>
        /// 窗口选择 - 完全包含在选择框内的对象被选中
        /// </summary>
        Window = 1,

        /// <summary>
        /// 交叉选择 - 与选择框有交集的对象被选中
        /// </summary>
        Cross = 2,

        /// <summary>
        /// 点选 - 点击选择单个对象
        /// </summary>
        Point = 3
    }

    /// <summary>
    /// 鼠标事件类型
    /// </summary>
    public enum MouseEventType
    {
        Down,
        Up,
        Move,
        DoubleClick,
        Wheel
    }
} 