using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 系统设置窗口
    /// </summary>
    public partial class SystemSettingsWindow : Window
    {
        private bool _isUpdating = false;
        private Dictionary<string, StackPanel> _settingsPanels;

        public SystemSettingsWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // 初始化设置面板映射
            _settingsPanels = new Dictionary<string, StackPanel>
            {
                { "General", GeneralSettingsPanel },
                { "Display", DisplaySettingsPanel },
                { "Drawing", DrawingSettingsPanel },
                { "ObjectSnap", null }, // 将使用现有的对象捕捉设置窗口
                { "PolarTracking", null }, // 将使用现有的极轴追踪设置窗口
                { "Dimension", null }, // 将使用现有的标注样式管理器
                { "Files", null },
                { "Shortcuts", ShortcutsSettingsPanel },
                { "Performance", PerformanceSettingsPanel }
            };

            LoadSettings();
            LoadShortcuts();
        }

        private void LoadSettings()
        {
            _isUpdating = true;
            try
            {
                // 加载常规设置
                ShowStartupDialogCheckBox.IsChecked = true; // TODO: 从配置文件加载
                LoadLastFileCheckBox.IsChecked = false;
                CheckUpdatesCheckBox.IsChecked = true;
                
                // 加载显示设置
                ShowGridCheckBox.IsChecked = true;
                AntiAliasingCheckBox.IsChecked = true;
                HardwareAccelerationCheckBox.IsChecked = true;
                
                // 加载绘图设置
                DefaultLineWidthTextBox.Text = "1.0";
                PrecisionTextBox.Text = "0.001";
                
                // 加载性能设置
                MaxUndoStepsTextBox.Text = "50";
                AutoSaveCheckBox.IsChecked = true;
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void LoadShortcuts()
        {
            // 创建示例快捷键数据
            var shortcuts = new List<ShortcutInfo>
            {
                new ShortcutInfo { Command = "LINE", Description = "绘制直线", Shortcut = "L" },
                new ShortcutInfo { Command = "CIRCLE", Description = "绘制圆", Shortcut = "C" },
                new ShortcutInfo { Command = "RECTANGLE", Description = "绘制矩形", Shortcut = "REC" },
                new ShortcutInfo { Command = "OFFSET", Description = "偏移", Shortcut = "O" },
                new ShortcutInfo { Command = "TRIM", Description = "修剪", Shortcut = "TR" },
                new ShortcutInfo { Command = "EXTEND", Description = "延伸", Shortcut = "EX" },
                new ShortcutInfo { Command = "DIMLINEAR", Description = "线性标注", Shortcut = "DLI" },
                new ShortcutInfo { Command = "COPY", Description = "复制", Shortcut = "CO" },
                new ShortcutInfo { Command = "MOVE", Description = "移动", Shortcut = "M" },
                new ShortcutInfo { Command = "ROTATE", Description = "旋转", Shortcut = "RO" },
                new ShortcutInfo { Command = "SCALE", Description = "缩放", Shortcut = "SC" },
                new ShortcutInfo { Command = "FILLET", Description = "圆角", Shortcut = "F" },
                new ShortcutInfo { Command = "CHAMFER", Description = "倒角", Shortcut = "CHA" },
                new ShortcutInfo { Command = "ARRAY", Description = "阵列", Shortcut = "AR" },
                new ShortcutInfo { Command = "ELLIPSE", Description = "椭圆", Shortcut = "EL" }
            };
            
            ShortcutsDataGrid.ItemsSource = shortcuts;
        }

        private void CategoryListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isUpdating) return;

            var selectedItem = CategoryListBox.SelectedItem as ListBoxItem;
            var category = selectedItem?.Tag as string;

            if (!string.IsNullOrEmpty(category))
            {
                ShowSettingsPanel(category);
            }
        }

        private void ShowSettingsPanel(string category)
        {
            // 隐藏所有面板
            foreach (var panel in _settingsPanels.Values)
            {
                if (panel != null)
                {
                    panel.Visibility = Visibility.Collapsed;
                }
            }

            // 显示选中的面板
            if (_settingsPanels.ContainsKey(category) && _settingsPanels[category] != null)
            {
                _settingsPanels[category].Visibility = Visibility.Visible;
            }
            else
            {
                // 对于没有专门面板的设置，打开对应的专用窗口
                OpenSpecializedWindow(category);
            }
        }

        private void OpenSpecializedWindow(string category)
        {
            try
            {
                switch (category)
                {
                    case "ObjectSnap":
                        var objectSnapWindow = new ObjectSnapSettingsWindow();
                        objectSnapWindow.ShowDialog();
                        break;
                    case "PolarTracking":
                        var polarTrackingWindow = new PolarTrackingSettingsWindow();
                        polarTrackingWindow.ShowDialog();
                        break;
                    case "Dimension":
                        var dimensionWindow = new DimensionStyleManagerWindow();
                        dimensionWindow.ShowDialog();
                        break;
                    default:
                        MessageBox.Show($"{category} 设置功能将在后续版本中实现", "提示", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开设置窗口失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BackgroundColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                if (colorPicker.ShowDialog() == true)
                {
                    var selectedColor = colorPicker.SelectedSKColor;
                    // TODO: 应用背景颜色到系统设置
                    BackgroundColorButton.Content = $"背景色: #{selectedColor.Red:X2}{selectedColor.Green:X2}{selectedColor.Blue:X2}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择背景颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GridColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                if (colorPicker.ShowDialog() == true)
                {
                    var selectedColor = colorPicker.SelectedSKColor;
                    // TODO: 应用网格颜色到系统设置
                    GridColorButton.Content = $"网格色: #{selectedColor.Red:X2}{selectedColor.Green:X2}{selectedColor.Blue:X2}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择网格颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DefaultColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                if (colorPicker.ShowDialog() == true)
                {
                    var selectedColor = colorPicker.SelectedSKColor;
                    // TODO: 应用默认颜色到系统设置
                    DefaultColorButton.Content = $"默认色: #{selectedColor.Red:X2}{selectedColor.Green:X2}{selectedColor.Blue:X2}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择默认颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetShortcuts_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有快捷键到默认设置吗？", "确认重置", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                LoadShortcuts();
                MessageBox.Show("快捷键已重置为默认设置", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ImportShortcuts_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导入快捷键功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportShortcuts_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导出快捷键功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RestoreDefaults_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要恢复所有设置到默认值吗？", "确认恢复", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                LoadSettings();
                LoadShortcuts();
                MessageBox.Show("所有设置已恢复为默认值", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            MessageBox.Show("设置已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void SaveSettings()
        {
            try
            {
                // TODO: 保存设置到配置文件
                // 这里应该将所有设置保存到配置文件或注册表中
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // 快捷键信息类
    public class ShortcutInfo
    {
        public string Command { get; set; }
        public string Description { get; set; }
        public string Shortcut { get; set; }
    }
}
