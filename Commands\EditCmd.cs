﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SkiaSharp;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 通用编辑命令基类
    /// </summary>
    public class EditCmd : ModifyCmd
    {
        /// <summary>
        /// 要编辑的实体
        /// </summary>
        private List<EntityBase> _selectedEntities = new List<EntityBase>();
        private List<EntityBase> _originalEntities = new List<EntityBase>();

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            Step1_SelectObjects = 1,
            Step2_EditProperties = 2,
        }
        private Step _step = Step.Step1_SelectObjects;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            // 检查是否已有选中的对象
            if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
            {
                _selectedEntities.AddRange(doc.SelectedEntities);
                CreateBackupCopies();
                _step = Step.Step2_EditProperties;
                this.pointer.Mode = IndicatorMode.Locate;
                this.pointer.Document.Prompt = "编辑选中的对象 (按Enter确认，ESC取消):";

                // 打开属性编辑界面
                OpenPropertyEditor();
            }
            else
            {
                _step = Step.Step1_SelectObjects;
                this.pointer.Mode = IndicatorMode.Select;
                this.pointer.Document.Prompt = "选择要编辑的对象:";
            }
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            if (_selectedEntities != null && _selectedEntities.Count > 0)
            {
                doc.Action.ActEntityModify(_selectedEntities);
            }
        }

        /// <summary>
        /// 回滚撤销
        /// </summary>
        protected override void Rollback()
        {
            if (_selectedEntities != null && _originalEntities != null &&
                _selectedEntities.Count == _originalEntities.Count)
            {
                for (int i = 0; i < _selectedEntities.Count; i++)
                {
                    // 恢复原始属性
                    RestoreEntityProperties(_selectedEntities[i], _originalEntities[i]);
                }
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (_step == Step.Step1_SelectObjects)
            {
                // 选择对象的逻辑由基类处理
                return EventResult.Unhandled;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (_step == Step.Step1_SelectObjects)
            {
                if (e.Button == MouseButtons.Right)
                {
                    if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
                    {
                        _selectedEntities.AddRange(doc.SelectedEntities);
                        CreateBackupCopies();
                        _step = Step.Step2_EditProperties;
                        this.pointer.Mode = IndicatorMode.Locate;
                        this.pointer.Document.Prompt = "编辑选中的对象 (按Enter确认，ESC取消):";

                        // 打开属性编辑界面
                        OpenPropertyEditor();
                    }
                    else
                    {
                        _mgr.CancelCurrentCommand();
                    }
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }
            else if (e.KeyCode == System.Windows.Forms.Keys.Enter)
            {
                if (_step == Step.Step2_EditProperties)
                {
                    _mgr.FinishCurrentCommand();
                    return EventResult.Handled;
                }
            }

            return EventResult.Unhandled;
        }

        public override void OnPaint(SKCanvas canvas)
        {
            // 编辑命令不需要特殊的绘制
        }

        /// <summary>
        /// 创建备份副本
        /// </summary>
        private void CreateBackupCopies()
        {
            _originalEntities.Clear();
            if (_selectedEntities != null)
            {
                foreach (EntityBase entity in _selectedEntities)
                {
                    EntityBase backup = entity.Clone();
                    _originalEntities.Add(backup);
                }
            }
        }

        /// <summary>
        /// 恢复实体属性
        /// </summary>
        private void RestoreEntityProperties(EntityBase target, EntityBase source)
        {
            if (target != null && source != null)
            {
                // 恢复基本属性
                target.Color = source.Color;
                target.LayerId = source.LayerId;
                target.LineType = source.LineType;
                target.LineWidth = source.LineWidth;

                // 根据实体类型恢复特定属性
                // 这里可以扩展更多的属性恢复逻辑
            }
        }

        /// <summary>
        /// 打开属性编辑界面
        /// </summary>
        private void OpenPropertyEditor()
        {
            try
            {
                // 检查是否有属性面板可用
                if (TryOpenPropertyPanel())
                {
                    this.pointer.Document.Prompt = "在属性面板中编辑对象属性，完成后按Enter确认";
                    return;
                }

                // 如果没有属性面板，提供基本的命令行编辑
                if (_selectedEntities.Count == 1)
                {
                    var entity = _selectedEntities[0];
                    this.pointer.Document.Prompt = $"编辑 {entity.GetType().Name} 属性 - 颜色(C) 图层(L) 线型(T) 或Enter完成:";
                }
                else
                {
                    this.pointer.Document.Prompt = $"编辑 {_selectedEntities.Count} 个对象的属性 - 颜色(C) 图层(L) 线型(T) 或Enter完成:";
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开属性编辑界面失败: {ex.Message}");
                this.pointer.Document.Prompt = $"属性编辑失败: {ex.Message}";
                _mgr.CancelCurrentCommand();
            }
        }

        /// <summary>
        /// 尝试打开属性面板
        /// </summary>
        private bool TryOpenPropertyPanel()
        {
            try
            {
                // 检查是否有LayerPropertiesPanel可用
                var mainWindow = System.Windows.Application.Current?.MainWindow;
                if (mainWindow != null)
                {
                    // 通过反射查找属性面板
                    var layerPanel = mainWindow.GetType().GetProperty("LayerPropertiesPanel")?.GetValue(mainWindow);
                    if (layerPanel != null)
                    {
                        // 激活属性面板并设置选中的实体
                        var setSelectedMethod = layerPanel.GetType().GetMethod("SetSelectedEntities");
                        setSelectedMethod?.Invoke(layerPanel, new object[] { _selectedEntities });

                        // 显示面板
                        var showMethod = layerPanel.GetType().GetMethod("Show");
                        showMethod?.Invoke(layerPanel, null);

                        return true;
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"尝试打开属性面板失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 处理属性编辑的键盘输入
        /// </summary>
        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (_step == Step.Step2_EditProperties)
            {
                switch (e.KeyCode)
                {
                    case System.Windows.Forms.Keys.C:
                        // 编辑颜色
                        EditColor();
                        return EventResult.Handled;

                    case System.Windows.Forms.Keys.L:
                        // 编辑图层
                        EditLayer();
                        return EventResult.Handled;

                    case System.Windows.Forms.Keys.T:
                        // 编辑线型
                        EditLineType();
                        return EventResult.Handled;

                    case System.Windows.Forms.Keys.Enter:
                        _mgr.FinishCurrentCommand();
                        return EventResult.Handled;

                    case System.Windows.Forms.Keys.Escape:
                        _mgr.CancelCurrentCommand();
                        return EventResult.Handled;
                }
            }

            return base.OnKeyDown(e);
        }

        private void EditColor()
        {
            // 简化的颜色编辑
            this.pointer.Document.Prompt = "颜色编辑功能需要颜色选择器支持";
        }

        private void EditLayer()
        {
            // 简化的图层编辑
            this.pointer.Document.Prompt = "图层编辑功能需要图层管理器支持";
        }

        private void EditLineType()
        {
            // 简化的线型编辑
            this.pointer.Document.Prompt = "线型编辑功能需要线型管理器支持";
        }
    }
}
