<Window x:Class="McLaser.EditViewerSk.Views.SystemSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="系统设置" Height="700" Width="900" 
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="CategoryHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5,10,5,5"/>
            <Setter Property="Foreground" Value="DarkBlue"/>
        </Style>
        
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Width" Value="150"/>
        </Style>
        
        <Style x:Key="PropertyControlStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Height" Value="25"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 设置类别列表 -->
        <DockPanel Grid.Column="0" Background="LightGray">
            <TextBlock Text="设置类别" DockPanel.Dock="Top" Style="{StaticResource CategoryHeaderStyle}" 
                       HorizontalAlignment="Center" Margin="5,10"/>
            
            <ListBox Name="CategoryListBox" SelectionChanged="CategoryListBox_SelectionChanged" 
                     BorderThickness="0" Background="Transparent">
                <ListBoxItem Content="常规设置" Tag="General" IsSelected="True"/>
                <ListBoxItem Content="显示设置" Tag="Display"/>
                <ListBoxItem Content="绘图设置" Tag="Drawing"/>
                <ListBoxItem Content="对象捕捉" Tag="ObjectSnap"/>
                <ListBoxItem Content="极轴追踪" Tag="PolarTracking"/>
                <ListBoxItem Content="标注设置" Tag="Dimension"/>
                <ListBoxItem Content="文件设置" Tag="Files"/>
                <ListBoxItem Content="快捷键" Tag="Shortcuts"/>
                <ListBoxItem Content="性能设置" Tag="Performance"/>
            </ListBox>
        </DockPanel>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" Margin="10">
            <StackPanel Name="SettingsContentPanel">
                
                <!-- 常规设置 -->
                <StackPanel Name="GeneralSettingsPanel" Visibility="Visible">
                    <TextBlock Text="常规设置" Style="{StaticResource CategoryHeaderStyle}"/>
                    
                    <GroupBox Header="启动设置" Margin="0,10">
                        <StackPanel>
                            <CheckBox Name="ShowStartupDialogCheckBox" Content="启动时显示欢迎对话框" Margin="5" IsChecked="True"/>
                            <CheckBox Name="LoadLastFileCheckBox" Content="启动时加载上次打开的文件" Margin="5" IsChecked="False"/>
                            <CheckBox Name="CheckUpdatesCheckBox" Content="启动时检查更新" Margin="5" IsChecked="True"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="界面设置" Margin="0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="界面语言:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <ComboBox Name="LanguageComboBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}">
                                <ComboBoxItem Content="简体中文" IsSelected="True"/>
                                <ComboBoxItem Content="English"/>
                                <ComboBoxItem Content="繁體中文"/>
                            </ComboBox>
                            
                            <TextBlock Text="界面主题:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <ComboBox Name="ThemeComboBox" Grid.Row="1" Grid.Column="1" Style="{StaticResource PropertyControlStyle}">
                                <ComboBoxItem Content="默认主题" IsSelected="True"/>
                                <ComboBoxItem Content="深色主题"/>
                                <ComboBoxItem Content="高对比度"/>
                            </ComboBox>
                            
                            <TextBlock Text="工具栏大小:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <ComboBox Name="ToolbarSizeComboBox" Grid.Row="2" Grid.Column="1" Style="{StaticResource PropertyControlStyle}">
                                <ComboBoxItem Content="小"/>
                                <ComboBoxItem Content="中等" IsSelected="True"/>
                                <ComboBoxItem Content="大"/>
                            </ComboBox>
                        </Grid>
                    </GroupBox>
                </StackPanel>
                
                <!-- 显示设置 -->
                <StackPanel Name="DisplaySettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="显示设置" Style="{StaticResource CategoryHeaderStyle}"/>
                    
                    <GroupBox Header="图形显示" Margin="0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="背景颜色:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <Button Name="BackgroundColorButton" Grid.Row="0" Grid.Column="1" 
                                    Content="选择颜色" Style="{StaticResource PropertyControlStyle}" 
                                    HorizontalAlignment="Left" Width="100" Click="BackgroundColorButton_Click"/>
                            
                            <TextBlock Text="网格颜色:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <Button Name="GridColorButton" Grid.Row="1" Grid.Column="1" 
                                    Content="选择颜色" Style="{StaticResource PropertyControlStyle}" 
                                    HorizontalAlignment="Left" Width="100" Click="GridColorButton_Click"/>
                            
                            <TextBlock Text="光标大小:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBox Name="CursorSizeTextBox" Grid.Row="2" Grid.Column="1" 
                                     Style="{StaticResource PropertyControlStyle}" Text="20"/>
                            
                            <CheckBox Name="ShowGridCheckBox" Content="显示网格" Grid.Row="3" Grid.Column="1" 
                                      Margin="5" VerticalAlignment="Center" IsChecked="True"/>
                        </Grid>
                    </GroupBox>
                    
                    <GroupBox Header="渲染设置" Margin="0,10">
                        <StackPanel>
                            <CheckBox Name="AntiAliasingCheckBox" Content="启用抗锯齿" Margin="5" IsChecked="True"/>
                            <CheckBox Name="HardwareAccelerationCheckBox" Content="启用硬件加速" Margin="5" IsChecked="True"/>
                            <CheckBox Name="HighQualityRenderingCheckBox" Content="高质量渲染" Margin="5" IsChecked="False"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
                
                <!-- 绘图设置 -->
                <StackPanel Name="DrawingSettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="绘图设置" Style="{StaticResource CategoryHeaderStyle}"/>
                    
                    <GroupBox Header="默认设置" Margin="0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="默认线宽:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBox Name="DefaultLineWidthTextBox" Grid.Row="0" Grid.Column="1" 
                                     Style="{StaticResource PropertyControlStyle}" Text="1.0"/>
                            
                            <TextBlock Text="默认颜色:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <Button Name="DefaultColorButton" Grid.Row="1" Grid.Column="1" 
                                    Content="选择颜色" Style="{StaticResource PropertyControlStyle}" 
                                    HorizontalAlignment="Left" Width="100" Click="DefaultColorButton_Click"/>
                            
                            <TextBlock Text="精度:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBox Name="PrecisionTextBox" Grid.Row="2" Grid.Column="1" 
                                     Style="{StaticResource PropertyControlStyle}" Text="0.001"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>
                
                <!-- 快捷键设置 -->
                <StackPanel Name="ShortcutsSettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="快捷键设置" Style="{StaticResource CategoryHeaderStyle}"/>
                    
                    <GroupBox Header="命令快捷键" Margin="0,10">
                        <StackPanel>
                            <DataGrid Name="ShortcutsDataGrid" Height="300" AutoGenerateColumns="False" 
                                      CanUserAddRows="False" CanUserDeleteRows="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="命令" Binding="{Binding Command}" Width="150" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="200" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="快捷键" Binding="{Binding Shortcut}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                            
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                                <Button Content="重置默认" Width="80" Height="30" Margin="5" Click="ResetShortcuts_Click"/>
                                <Button Content="导入" Width="80" Height="30" Margin="5" Click="ImportShortcuts_Click"/>
                                <Button Content="导出" Width="80" Height="30" Margin="5" Click="ExportShortcuts_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
                
                <!-- 性能设置 -->
                <StackPanel Name="PerformanceSettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="性能设置" Style="{StaticResource CategoryHeaderStyle}"/>
                    
                    <GroupBox Header="内存管理" Margin="0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="最大撤销步数:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBox Name="MaxUndoStepsTextBox" Grid.Row="0" Grid.Column="1" 
                                     Style="{StaticResource PropertyControlStyle}" Text="50"/>
                            
                            <CheckBox Name="AutoSaveCheckBox" Content="启用自动保存" Grid.Row="1" Grid.Column="1" 
                                      Margin="5" VerticalAlignment="Center" IsChecked="True"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" 
                    VerticalAlignment="Bottom" Margin="10,0,10,10">
            <Button Content="恢复默认" Width="80" Height="35" Margin="5" Click="RestoreDefaults_Click"/>
            <Button Content="应用" Width="80" Height="35" Margin="5" Click="Apply_Click"/>
            <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click"/>
            <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
