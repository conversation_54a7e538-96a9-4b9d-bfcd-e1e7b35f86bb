using System;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Managers;

namespace McLaser.EditViewerSk.Commands
{
    public class LinearDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForFirstPoint,
            WaitingForSecondPoint,
            WaitingForDimensionLinePosition
        }

        private State currentState = State.WaitingForFirstPoint;
        private Vector2 firstPoint;
        private Vector2 secondPoint;
        private EntityLinearDimension previewDimension;
        private bool isHorizontal = false;
        private bool isVertical = false;

        public LinearDimensionCmd(IView view) : base(view)
        {
            CommandName = "线性标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForFirstPoint:
                    firstPoint = point;
                    currentState = State.WaitingForSecondPoint;
                    DynamicInputManager.AddInput("请指定第二点:");
                    return true;

                case State.WaitingForSecondPoint:
                    secondPoint = point;
                    
                    // 创建预览标注
                    var offset = Vector2.Distance(firstPoint, secondPoint) * 0.2f;
                    var direction = Vector2.Normalize(secondPoint - firstPoint);
                    var perpendicular = new Vector2(-direction.Y, direction.X);
                    var dimLinePos = (firstPoint + secondPoint) * 0.5f + perpendicular * offset;
                    
                    previewDimension = new EntityLinearDimension(firstPoint, secondPoint, dimLinePos);
                    previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                    
                    currentState = State.WaitingForDimensionLinePosition;
                    DynamicInputManager.AddInput("请指定标注线位置:");
                    return true;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.DimensionLinePosition = point;
                        
                        // 添加到文档
                        Document.AddEntity(previewDimension);
                        
                        // 尝试自动创建关联
                        var candidates = Document.GetEntitiesInRegion(previewDimension.BoundingBox, 5.0);
                        DimensionAssociationManager.Instance.AutoCreateAssociations(previewDimension, candidates);
                        
                        // 添加到撤销系统
                        Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                        
                        Finish();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForSecondPoint:
                    // 显示预览线
                    InvalidateView();
                    return true;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.DimensionLinePosition = point;
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            switch (key)
            {
                case System.Windows.Input.Key.H:
                    if (currentState == State.WaitingForSecondPoint || currentState == State.WaitingForDimensionLinePosition)
                    {
                        isHorizontal = true;
                        isVertical = false;
                        DynamicInputManager.AddInfo("水平标注模式");
                        return true;
                    }
                    break;

                case System.Windows.Input.Key.V:
                    if (currentState == State.WaitingForSecondPoint || currentState == State.WaitingForDimensionLinePosition)
                    {
                        isVertical = true;
                        isHorizontal = false;
                        DynamicInputManager.AddInfo("垂直标注模式");
                        return true;
                    }
                    break;

                case System.Windows.Input.Key.Escape:
                    Cancel();
                    return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForSecondPoint:
                    if (View.LastMousePosition.HasValue)
                    {
                        var secondPt = isHorizontal ? new Vector2(View.LastMousePosition.Value.X, firstPoint.Y) :
                                      isVertical ? new Vector2(firstPoint.X, View.LastMousePosition.Value.Y) :
                                      View.LastMousePosition.Value;
                        
                        renderer.DrawLine(firstPoint, secondPt, paint);
                        DrawPoint(renderer, firstPoint, paint);
                    }
                    break;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForFirstPoint:
                    return "请指定第一个延伸线原点或按 Enter 选择对象:";
                case State.WaitingForSecondPoint:
                    return "请指定第二个延伸线原点: [H]水平 [V]垂直";
                case State.WaitingForDimensionLinePosition:
                    return "请指定标注线位置或 [文字(M)/角度(A)]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            currentState = State.WaitingForFirstPoint;
            isHorizontal = false;
            isVertical = false;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            currentState = State.WaitingForFirstPoint;
            isHorizontal = false;
            isVertical = false;
            base.Finish();
        }

        private void DrawPoint(IGraphicsRenderer renderer, Vector2 point, SkiaSharp.SKPaint paint)
        {
            var size = 2.0f;
            renderer.DrawLine(
                new Vector2(point.X - size, point.Y),
                new Vector2(point.X + size, point.Y), 
                paint);
            renderer.DrawLine(
                new Vector2(point.X, point.Y - size),
                new Vector2(point.X, point.Y + size), 
                paint);
        }
    }

    public class AlignedDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForFirstPoint,
            WaitingForSecondPoint,
            WaitingForDimensionLinePosition
        }

        private State currentState = State.WaitingForFirstPoint;
        private Vector2 firstPoint;
        private Vector2 secondPoint;
        private EntityAlignedDimension previewDimension;

        public AlignedDimensionCmd(IView view) : base(view)
        {
            CommandName = "对齐标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForFirstPoint:
                    firstPoint = point;
                    currentState = State.WaitingForSecondPoint;
                    DynamicInputManager.AddInput("请指定第二点:");
                    return true;

                case State.WaitingForSecondPoint:
                    secondPoint = point;
                    
                    // 创建预览标注
                    var defaultOffset = Vector2.Distance(firstPoint, secondPoint) * 0.2f;
                    previewDimension = new EntityAlignedDimension(firstPoint, secondPoint, defaultOffset);
                    previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                    
                    currentState = State.WaitingForDimensionLinePosition;
                    DynamicInputManager.AddInput("请指定标注线位置:");
                    return true;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.SetDimensionLinePosition(point);
                        
                        // 添加到文档
                        Document.AddEntity(previewDimension);
                        
                        // 尝试自动创建关联
                        var candidates = Document.GetEntitiesInRegion(previewDimension.BoundingBox, 5.0);
                        DimensionAssociationManager.Instance.AutoCreateAssociations(previewDimension, candidates);
                        
                        // 添加到撤销系统
                        Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                        
                        Finish();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForSecondPoint:
                    InvalidateView();
                    return true;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.SetDimensionLinePosition(point);
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            if (key == System.Windows.Input.Key.Escape)
            {
                Cancel();
                return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForSecondPoint:
                    if (View.LastMousePosition.HasValue)
                    {
                        renderer.DrawLine(firstPoint, View.LastMousePosition.Value, paint);
                        DrawPoint(renderer, firstPoint, paint);
                    }
                    break;

                case State.WaitingForDimensionLinePosition:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForFirstPoint:
                    return "请指定第一个延伸线原点或按 Enter 选择对象:";
                case State.WaitingForSecondPoint:
                    return "请指定第二个延伸线原点:";
                case State.WaitingForDimensionLinePosition:
                    return "请指定标注线位置或 [文字(M)/角度(A)]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            currentState = State.WaitingForFirstPoint;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            currentState = State.WaitingForFirstPoint;
            base.Finish();
        }

        private void DrawPoint(IGraphicsRenderer renderer, Vector2 point, SkiaSharp.SKPaint paint)
        {
            var size = 2.0f;
            renderer.DrawLine(
                new Vector2(point.X - size, point.Y),
                new Vector2(point.X + size, point.Y), 
                paint);
            renderer.DrawLine(
                new Vector2(point.X, point.Y - size),
                new Vector2(point.X, point.Y + size), 
                paint);
        }
    }
} 