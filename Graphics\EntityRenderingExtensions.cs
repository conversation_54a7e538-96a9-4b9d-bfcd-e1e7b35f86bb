using System;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Selection;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 实体渲染扩展方法
    /// 提供专业的CAD实体选择高亮功能
    /// </summary>
    public static class EntityRenderingExtensions
    {
        /// <summary>
        /// 为实体渲染选择高亮效果
        /// </summary>
        public static void RenderSelectionHighlight(this EntityBase entity, ViewBase view, SelectionHighlightStyle style = null)
        {
            if (entity == null || view == null || !entity.IsSelected) return;

            style = style ?? GetDefaultHighlightStyle();
            var renderer = view.GetGraphicsRenderer();
            if (renderer == null) return;

            // 创建高亮画笔
            var highlightPaint = new SKPaint()
            {
                Color = style.SelectedColor,
                StrokeWidth = style.SelectedWidth,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true
            };

            // 根据实体类型渲染不同的高亮效果
            switch (entity)
            {
                case EntityLine line:
                    RenderLineHighlight(line, renderer, highlightPaint, style);
                    break;
                case EntityCircle circle:
                    RenderCircleHighlight(circle, renderer, highlightPaint, style);
                    break;
                case EntityRectangle rectangle:
                    RenderRectangleHighlight(rectangle, renderer, highlightPaint, style);
                    break;
                case EntityArc arc:
                    RenderArcHighlight(arc, renderer, highlightPaint, style);
                    break;
                case EntityLwPolyline polyline:
                    RenderPolylineHighlight(polyline, renderer, highlightPaint, style);
                    break;
            }

            highlightPaint.Dispose();
        }

        /// <summary>
        /// 渲染实体的握点
        /// </summary>
        public static void RenderGripPoints(this EntityBase entity, ViewBase view, SelectionHighlightStyle style = null)
        {
            if (entity == null || view == null || !entity.IsSelected) return;

            style = style ?? GetDefaultHighlightStyle();
            if (!style.ShowGrips) return;

            var renderer = view.GetGraphicsRenderer();
            if (renderer == null) return;

            var gripPaint = new SKPaint()
            {
                Color = style.GripColor,
                Style = SKPaintStyle.Fill,
                IsAntialias = true
            };

            var gripPoints = GetEntityGripPoints(entity);
            foreach (var point in gripPoints)
            {
                renderer.DrawCircle(point, style.GripSize, gripPaint, CoordinateSpace.Model);
            }

            gripPaint.Dispose();
        }

        #region 私有辅助方法

        private static SelectionHighlightStyle GetDefaultHighlightStyle()
        {
            return new SelectionHighlightStyle()
            {
                SelectedColor = SKColors.Red,
                SelectedWidth = 3.0f,
                ShowGrips = true,
                GripSize = 4.0f,
                GripColor = SKColors.Cyan
            };
        }

        private static void RenderLineHighlight(EntityLine line, IGraphicsRenderer renderer, SKPaint paint, SelectionHighlightStyle style)
        {
            // 绘制高亮线条
            renderer.DrawLine(line.StartPoint, line.EndPoint, paint, CoordinateSpace.Model);

            // 如果启用闪烁效果
            if (style.IsBlinking)
            {
                var blinkPaint = paint.Clone();
                blinkPaint.Color = blinkPaint.Color.WithAlpha((byte)(128 + 127 * Math.Sin(DateTime.Now.Millisecond * Math.PI / style.BlinkInterval)));
                renderer.DrawLine(line.StartPoint, line.EndPoint, blinkPaint, CoordinateSpace.Model);
                blinkPaint.Dispose();
            }
        }

        private static void RenderCircleHighlight(EntityCircle circle, IGraphicsRenderer renderer, SKPaint paint, SelectionHighlightStyle style)
        {
            // 绘制高亮圆形
            renderer.DrawCircle(circle.Center, circle.Radius, paint, CoordinateSpace.Model);
        }

        private static void RenderRectangleHighlight(EntityRectangle rectangle, IGraphicsRenderer renderer, SKPaint paint, SelectionHighlightStyle style)
        {
            // 计算矩形的宽高
            var width = Math.Abs(rectangle.EndPoint.X - rectangle.StartPoint.X);
            var height = Math.Abs(rectangle.EndPoint.Y - rectangle.StartPoint.Y);
            
            // 确定左上角位置
            var topLeft = new Vector2(
                Math.Min(rectangle.StartPoint.X, rectangle.EndPoint.X),
                Math.Min(rectangle.StartPoint.Y, rectangle.EndPoint.Y)
            );

            renderer.DrawRectangle(topLeft, width, height, paint, CoordinateSpace.Model);
        }

        private static void RenderArcHighlight(EntityArc arc, IGraphicsRenderer renderer, SKPaint paint, SelectionHighlightStyle style)
        {
            // 绘制高亮圆弧
            renderer.DrawArc(arc.Center, arc.Radius, arc.StartAngle, arc.SweepAngle, paint, CoordinateSpace.Model);
        }

        private static void RenderPolylineHighlight(EntityLwPolyline polyline, IGraphicsRenderer renderer, SKPaint paint, SelectionHighlightStyle style)
        {
            // 绘制折线的每一段
            int numOfVertices = polyline.NumOfVertices;
            for (int i = 0; i < numOfVertices - 1; i++)
            {
                renderer.DrawLine(polyline.GetPointAt(i), polyline.GetPointAt(i + 1), paint, CoordinateSpace.Model);
            }

            // 如果是闭合折线，连接最后一点和第一点
            if (polyline.IsClosed && numOfVertices > 2)
            {
                renderer.DrawLine(polyline.GetPointAt(numOfVertices - 1), polyline.GetPointAt(0), paint, CoordinateSpace.Model);
            }
        }

        private static Vector2[] GetEntityGripPoints(EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    return new[] { line.StartPoint, line.EndPoint };

                case EntityCircle circle:
                    // 圆形的握点：上下左右四个点
                    return new[]
                    {
                        new Vector2(circle.Center.X, circle.Center.Y + (float)circle.Radius), // 上
                        new Vector2(circle.Center.X + (float)circle.Radius, circle.Center.Y), // 右
                        new Vector2(circle.Center.X, circle.Center.Y - (float)circle.Radius), // 下
                        new Vector2(circle.Center.X - (float)circle.Radius, circle.Center.Y)  // 左
                    };

                case EntityRectangle rectangle:
                    // 矩形的握点：四个角和四条边的中点
                    var minX = Math.Min(rectangle.StartPoint.X, rectangle.EndPoint.X);
                    var maxX = Math.Max(rectangle.StartPoint.X, rectangle.EndPoint.X);
                    var minY = Math.Min(rectangle.StartPoint.Y, rectangle.EndPoint.Y);
                    var maxY = Math.Max(rectangle.StartPoint.Y, rectangle.EndPoint.Y);
                    var midX = (minX + maxX) / 2;
                    var midY = (minY + maxY) / 2;

                    return new[]
                    {
                        new Vector2(minX, minY), // 左下
                        new Vector2(midX, minY), // 下中
                        new Vector2(maxX, minY), // 右下
                        new Vector2(maxX, midY), // 右中
                        new Vector2(maxX, maxY), // 右上
                        new Vector2(midX, maxY), // 上中
                        new Vector2(minX, maxY), // 左上
                        new Vector2(minX, midY)  // 左中
                    };

                case EntityArc arc:
                    // 圆弧的握点：起点、终点、中点
                    var startAngleRad = arc.StartAngle * Math.PI / 180;
                    var endAngleRad = (arc.StartAngle + arc.SweepAngle) * Math.PI / 180;
                    var midAngleRad = (startAngleRad + endAngleRad) / 2;

                    return new[]
                    {
                        new Vector2(
                            arc.Center.X + (float)(arc.Radius * Math.Cos(startAngleRad)),
                            arc.Center.Y + (float)(arc.Radius * Math.Sin(startAngleRad))
                        ),
                        new Vector2(
                            arc.Center.X + (float)(arc.Radius * Math.Cos(midAngleRad)),
                            arc.Center.Y + (float)(arc.Radius * Math.Sin(midAngleRad))
                        ),
                        new Vector2(
                            arc.Center.X + (float)(arc.Radius * Math.Cos(endAngleRad)),
                            arc.Center.Y + (float)(arc.Radius * Math.Sin(endAngleRad))
                        )
                    };

                case EntityLwPolyline polyline:
                    // 折线的握点：所有顶点
                    var points = new Vector2[polyline.NumOfVertices];
                    for (int i = 0; i < polyline.NumOfVertices; i++)
                    {
                        points[i] = polyline.GetPointAt(i);
                    }
                    return points;

                default:
                    return new Vector2[0];
            }
        }

        #endregion
    }

    /// <summary>
    /// ViewBase的扩展方法
    /// </summary>
    public static class ViewBaseExtensions
    {
        /// <summary>
        /// 获取图形渲染器（扩展方法）
        /// </summary>
        public static IGraphicsRenderer GetGraphicsRenderer(this ViewBase viewBase)
        {
            // 使用反射调用私有方法
            var method = typeof(ViewBase).GetMethod("GetGraphicsRenderer", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return method?.Invoke(viewBase, null) as IGraphicsRenderer;
        }
    }
} 