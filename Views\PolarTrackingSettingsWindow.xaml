<Window x:Class="McLaser.EditViewerSk.Views.PolarTrackingSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="极轴追踪设置" Height="500" Width="600" 
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Width" Value="120"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Text="极轴追踪设置" Style="{StaticResource HeaderTextStyle}" Grid.Row="0"/>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,10">
            <StackPanel>
                <!-- 基本设置 -->
                <GroupBox Header="基本设置" Margin="0,10">
                    <StackPanel>
                        <CheckBox Name="EnablePolarTrackingCheckBox" Content="启用极轴追踪" 
                                  Margin="5" FontWeight="Bold" IsChecked="True"/>
                        
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="角度容差:" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBox Name="AngleToleranceTextBox" Width="80" Height="25" Margin="5,2" Text="2.0"/>
                                <TextBlock Text="度" VerticalAlignment="Center" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <CheckBox Name="ShowAngleInfoCheckBox" Content="显示角度信息" 
                                  Margin="5" IsChecked="True"/>
                        <CheckBox Name="ShowDistanceInfoCheckBox" Content="显示距离信息" 
                                  Margin="5" IsChecked="True"/>
                        <CheckBox Name="ExtendToScreenEdgeCheckBox" Content="追踪线延伸到屏幕边缘" 
                                  Margin="5" IsChecked="True"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 追踪角度设置 -->
                <GroupBox Header="追踪角度设置" Margin="0,10">
                    <StackPanel>
                        <RadioButton Name="StandardAnglesRadioButton" Content="使用标准角度" 
                                     Margin="5" IsChecked="True" GroupName="AngleMode"/>
                        
                        <StackPanel Name="StandardAnglesPanel" Margin="20,5">
                            <TextBlock Text="选择要追踪的标准角度:" Margin="0,5"/>
                            <WrapPanel>
                                <CheckBox Content="0°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="15°" Margin="5,2" IsChecked="False"/>
                                <CheckBox Content="30°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="45°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="60°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="90°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="120°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="135°" Margin="5,2" IsChecked="True"/>
                                <CheckBox Content="150°" Margin="5,2" IsChecked="False"/>
                                <CheckBox Content="180°" Margin="5,2" IsChecked="True"/>
                            </WrapPanel>
                        </StackPanel>
                        
                        <RadioButton Name="CustomAnglesRadioButton" Content="使用自定义角度" 
                                     Margin="5" GroupName="AngleMode"/>
                        
                        <StackPanel Name="CustomAnglesPanel" Margin="20,5">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Text="角度增量:" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                                <TextBox Name="AngleIncrementTextBox" Grid.Column="1" Height="25" 
                                         Margin="5,2" Text="15"/>
                                <TextBlock Text="度" Grid.Column="2" VerticalAlignment="Center" Margin="5,0"/>
                            </Grid>
                            
                            <TextBlock Text="自定义角度列表 (用逗号分隔):" Margin="0,10,0,5"/>
                            <TextBox Name="CustomAnglesTextBox" Height="60" TextWrapping="Wrap" 
                                     AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                     Text="22.5, 67.5, 112.5, 157.5"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 视觉设置 -->
                <GroupBox Header="视觉设置" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="追踪线颜色:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <Button Name="TrackingLineColorButton" Grid.Row="0" Grid.Column="1" 
                                Content="选择颜色" Height="25" Width="100" 
                                HorizontalAlignment="Left" Margin="5,2" Click="TrackingLineColorButton_Click"/>
                        
                        <TextBlock Text="追踪线宽度:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                            <TextBox Name="TrackingLineWidthTextBox" Width="80" Height="25" Margin="5,2" Text="1.0"/>
                            <TextBlock Text="像素" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                        
                        <TextBlock Text="信息文本大小:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal">
                            <TextBox Name="InfoTextSizeTextBox" Width="80" Height="25" Margin="5,2" Text="12"/>
                            <TextBlock Text="像素" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
                
                <!-- 预览区域 -->
                <GroupBox Header="预览" Margin="0,10">
                    <Border Background="LightGray" Height="100" Margin="5">
                        <TextBlock Text="极轴追踪预览区域" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" Foreground="Gray"/>
                    </Border>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="恢复默认" Width="80" Height="35" Margin="5" Click="RestoreDefaults_Click"/>
            <Button Content="应用" Width="80" Height="35" Margin="5" Click="Apply_Click"/>
            <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click"/>
            <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
