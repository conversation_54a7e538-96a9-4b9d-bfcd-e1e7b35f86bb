using System;
using System.Collections.Generic;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;

namespace McLaser.EditViewerSk.Viewport
{
    /// <summary>
    /// CAD视口管理器
    /// 基于AutoCAD等主流CAD软件的视口管理标准
    /// </summary>
    public class ViewportManager
    {
        private readonly ViewBase _viewBase;
        private ViewportState _currentState;
        
        // 视口边界
        private SKRect _viewportBounds;
        
        // 模型空间边界
        private SKRect _modelBounds;
        
        // 变换历史栈（用于撤销/重做操作）
        private readonly Stack<ViewportState> _transformHistory;
        private const int MaxHistorySize = 50;

        public ViewportManager(ViewBase viewBase)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _transformHistory = new Stack<ViewportState>();
            
            // 初始化默认视口状态
            _currentState = new ViewportState
            {
                Scale = 1.0f,
                PanOffset = Vector2.Zero,
                Rotation = 0.0f,
                ViewMatrix = SKMatrix.CreateIdentity()
            };
            
            UpdateViewportBounds();
        }

        #region 视口操作

        /// <summary>
        /// 缩放视图
        /// </summary>
        /// <param name="scaleFactor">缩放因子</param>
        /// <param name="centerPoint">缩放中心点（屏幕坐标）</param>
        public void ZoomView(float scaleFactor, Vector2? centerPoint = null)
        {
            SaveCurrentState();
            
            var center = centerPoint ?? GetViewportCenter();
            
            // 将缩放中心转换为模型坐标
            var modelCenter = ScreenToModel(center);
            
            // 应用缩放
            _currentState.Scale *= scaleFactor;
            
            // 限制缩放范围
            _currentState.Scale = Math.Max(0.001f, Math.Min(1000.0f, _currentState.Scale));
            
            // 调整平移偏移以保持缩放中心不变
            var newScreenCenter = ModelToScreen(modelCenter);
            var offset = center - newScreenCenter;
            _currentState.PanOffset += offset;
            
            UpdateViewMatrix();
            _viewBase.RepaintCanvas();
        }

        /// <summary>
        /// 平移视图
        /// </summary>
        /// <param name="deltaX">X轴平移量（像素）</param>
        /// <param name="deltaY">Y轴平移量（像素）</param>
        public void PanView(float deltaX, float deltaY)
        {
            SaveCurrentState();
            
            _currentState.PanOffset.X += deltaX;
            _currentState.PanOffset.Y += deltaY;
            
            UpdateViewMatrix();
            _viewBase.RepaintCanvas();
        }

        /// <summary>
        /// 旋转视图
        /// </summary>
        /// <param name="angleDegrees">旋转角度（度）</param>
        /// <param name="centerPoint">旋转中心点（屏幕坐标）</param>
        public void RotateView(float angleDegrees, Vector2? centerPoint = null)
        {
            SaveCurrentState();
            
            var center = centerPoint ?? GetViewportCenter();
            _currentState.Rotation += angleDegrees;
            
            // 标准化角度到0-360度
            _currentState.Rotation = _currentState.Rotation % 360.0f;
            if (_currentState.Rotation < 0) _currentState.Rotation += 360.0f;
            
            UpdateViewMatrix();
            _viewBase.RepaintCanvas();
        }

        /// <summary>
        /// 缩放到指定矩形区域
        /// </summary>
        /// <param name="modelRect">模型空间矩形</param>
        /// <param name="margin">边距百分比</param>
        public void ZoomToRectangle(SKRect modelRect, float margin = 0.1f)
        {
            SaveCurrentState();
            
            if (modelRect.Width <= 0 || modelRect.Height <= 0) return;
            
            var viewportSize = GetViewportSize();
            var scaleX = viewportSize.Width / (modelRect.Width * (1 + margin));
            var scaleY = viewportSize.Height / (modelRect.Height * (1 + margin));
            
            _currentState.Scale = Math.Min(scaleX, scaleY);
            
            // 计算居中偏移
            var modelCenter = new Vector2(modelRect.MidX, modelRect.MidY);
            var viewportCenter = GetViewportCenter();
            var screenCenter = ModelToScreen(modelCenter);
            
            _currentState.PanOffset = viewportCenter - screenCenter;
            
            UpdateViewMatrix();
            _viewBase.RepaintCanvas();
        }

        /// <summary>
        /// 缩放到适应所有对象
        /// </summary>
        public void ZoomToExtents()
        {
            var extents = CalculateModelExtents();
            if (extents.HasValue)
            {
                ZoomToRectangle(extents.Value, 0.1f);
            }
        }

        /// <summary>
        /// 重置视图到默认状态
        /// </summary>
        public void ResetView()
        {
            SaveCurrentState();
            
            _currentState.Scale = 1.0f;
            _currentState.PanOffset = Vector2.Zero;
            _currentState.Rotation = 0.0f;
            
            UpdateViewMatrix();
            _viewBase.RepaintCanvas();
        }

        #endregion

        #region 坐标转换

        /// <summary>
        /// 屏幕坐标转模型坐标
        /// </summary>
        public Vector2 ScreenToModel(Vector2 screenPoint)
        {
            var inverseMatrix = _currentState.ViewMatrix.Invert();
            var transformedPoint = inverseMatrix.MapPoint((float)screenPoint.X, (float)screenPoint.Y);
            return new Vector2(transformedPoint.X, transformedPoint.Y);
        }

        /// <summary>
        /// 模型坐标转屏幕坐标
        /// </summary>
        public Vector2 ModelToScreen(Vector2 modelPoint)
        {
            var transformedPoint = _currentState.ViewMatrix.MapPoint((float)modelPoint.X, (float)modelPoint.Y);
            return new Vector2(transformedPoint.X, transformedPoint.Y);
        }

        /// <summary>
        /// 屏幕距离转模型距离
        /// </summary>
        public float ScreenToModelDistance(float screenDistance)
        {
            return screenDistance / _currentState.Scale;
        }

        /// <summary>
        /// 模型距离转屏幕距离
        /// </summary>
        public float ModelToScreenDistance(float modelDistance)
        {
            return modelDistance * _currentState.Scale;
        }

        #endregion

        #region 视口状态管理

        /// <summary>
        /// 保存当前状态到历史栈
        /// </summary>
        private void SaveCurrentState()
        {
            _transformHistory.Push(_currentState.Clone());
            
            // 限制历史栈大小
            if (_transformHistory.Count > MaxHistorySize)
            {
                var tempStack = new Stack<ViewportState>();
                for (int i = 0; i < MaxHistorySize - 1; i++)
                {
                    tempStack.Push(_transformHistory.Pop());
                }
                _transformHistory.Clear();
                while (tempStack.Count > 0)
                {
                    _transformHistory.Push(tempStack.Pop());
                }
            }
        }

        /// <summary>
        /// 撤销到上一个状态
        /// </summary>
        public bool UndoTransform()
        {
            if (_transformHistory.Count > 0)
            {
                _currentState = _transformHistory.Pop();
                UpdateViewMatrix();
                _viewBase.RepaintCanvas();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 更新视图变换矩阵
        /// </summary>
        private void UpdateViewMatrix()
        {
            var center = GetViewportCenter();
            
            // 创建复合变换矩阵
            var matrix = SKMatrix.CreateIdentity();
            
            // 1. 平移到原点
            matrix = matrix.PostConcat(SKMatrix.CreateTranslation(-(float)center.X, -(float)center.Y));
            
            // 2. 应用旋转
            if (_currentState.Rotation != 0)
            {
                matrix = matrix.PostConcat(SKMatrix.CreateRotationDegrees(_currentState.Rotation));
            }
            
            // 3. 应用缩放
            matrix = matrix.PostConcat(SKMatrix.CreateScale(_currentState.Scale, -_currentState.Scale)); // Y轴翻转
            
            // 4. 应用平移
            matrix = matrix.PostConcat(SKMatrix.CreateTranslation(
                (float)center.X + (float)_currentState.PanOffset.X,
                (float)center.Y + (float)_currentState.PanOffset.Y));
            
            _currentState.ViewMatrix = matrix;
            
            // 通知ViewBase更新其变换矩阵
            UpdateViewBaseMatrix();
        }

        #endregion

        #region 辅助方法

        private Vector2 GetViewportCenter()
        {
            return new Vector2(_viewportBounds.MidX, _viewportBounds.MidY);
        }

        private SKSize GetViewportSize()
        {
            return new SKSize(_viewportBounds.Width, _viewportBounds.Height);
        }

        private void UpdateViewportBounds()
        {
            if (_viewBase.Viewer != null)
            {
                _viewportBounds = new SKRect(0, 0, (float)_viewBase.Viewer.Width, (float)_viewBase.Viewer.Height);
            }
            else
            {
                _viewportBounds = new SKRect(0, 0, 800, 600); // 默认大小
            }
        }

        private SKRect? CalculateModelExtents()
        {
            // 这里应该计算所有实体的边界框
            // 由于需要访问Document中的实体，暂时返回默认范围
            return new SKRect(-100, -100, 100, 100);
        }

        private void UpdateViewBaseMatrix()
        {
            // 使用反射更新ViewBase的私有变换矩阵
            var matrixField = typeof(ViewBase).GetField("_matrixTrans", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (matrixField != null)
            {
                matrixField.SetValue(_viewBase, _currentState.ViewMatrix);
                _viewBase.ResetGraphicsRenderer(); // 重置渲染器以更新坐标转换
            }
        }

        #endregion

        #region 属性

        /// <summary>
        /// 当前缩放比例
        /// </summary>
        public float CurrentScale => _currentState.Scale;

        /// <summary>
        /// 当前平移偏移
        /// </summary>
        public Vector2 CurrentPanOffset => _currentState.PanOffset;

        /// <summary>
        /// 当前旋转角度
        /// </summary>
        public float CurrentRotation => _currentState.Rotation;

        /// <summary>
        /// 当前视图矩阵
        /// </summary>
        public SKMatrix CurrentViewMatrix => _currentState.ViewMatrix;

        /// <summary>
        /// 视口边界
        /// </summary>
        public SKRect ViewportBounds => _viewportBounds;

        #endregion
    }

    /// <summary>
    /// 视口状态
    /// </summary>
    public class ViewportState
    {
        public float Scale { get; set; } = 1.0f;
        public Vector2 PanOffset { get; set; } = Vector2.Zero;
        public float Rotation { get; set; } = 0.0f;
        public SKMatrix ViewMatrix { get; set; } = SKMatrix.CreateIdentity();

        public ViewportState Clone()
        {
            return new ViewportState
            {
                Scale = this.Scale,
                PanOffset = this.PanOffset,
                Rotation = this.Rotation,
                ViewMatrix = this.ViewMatrix
            };
        }
    }
} 