using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Tracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 极轴追踪设置窗口
    /// </summary>
    public partial class PolarTrackingSettingsWindow : Window
    {
        private ViewBase _viewer;
        private PolarTrackingSettings _settings;
        private bool _isUpdating = false;

        public PolarTrackingSettingsWindow(ViewBase viewer = null)
        {
            InitializeComponent();
            _viewer = viewer;
            InitializeSettings();
            LoadSettings();
        }

        private void InitializeSettings()
        {
            // 获取当前设置或创建默认设置
            if (_viewer?.GetPhase2Manager() != null)
            {
                _settings = _viewer.GetPhase2Manager().GetPolarTrackingSettings();
            }
            else
            {
                _settings = new PolarTrackingSettings();
            }
        }

        private void LoadSettings()
        {
            _isUpdating = true;
            try
            {
                // 基本设置
                EnablePolarTrackingCheckBox.IsChecked = _settings.IsEnabled;
                AngleToleranceTextBox.Text = _settings.AngleTolerance.ToString();
                ShowAngleInfoCheckBox.IsChecked = _settings.ShowAngleInfo;
                ShowDistanceInfoCheckBox.IsChecked = _settings.ShowDistanceInfo;
                ExtendToScreenEdgeCheckBox.IsChecked = _settings.ExtendToScreenEdge;
                
                // 视觉设置
                TrackingLineWidthTextBox.Text = _settings.TrackingLineWidth.ToString();
                
                // 角度设置
                LoadTrackingAngles();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void LoadTrackingAngles()
        {
            // 检查是否使用标准角度
            var standardAngles = new float[] { 0, 30, 45, 60, 90, 120, 135, 180 };
            bool isStandardAngles = _settings.TrackingAngles.Count == standardAngles.Length &&
                                   standardAngles.All(angle => _settings.TrackingAngles.Contains(angle));
            
            if (isStandardAngles)
            {
                StandardAnglesRadioButton.IsChecked = true;
                UpdateStandardAngleCheckboxes();
            }
            else
            {
                CustomAnglesRadioButton.IsChecked = true;
                CustomAnglesTextBox.Text = string.Join(", ", _settings.TrackingAngles);
            }
        }

        private void UpdateStandardAngleCheckboxes()
        {
            var checkBoxes = StandardAnglesPanel.Children.OfType<WrapPanel>().FirstOrDefault()?.Children.OfType<CheckBox>();
            if (checkBoxes != null)
            {
                foreach (var checkBox in checkBoxes)
                {
                    if (float.TryParse(checkBox.Content.ToString().Replace("°", ""), out float angle))
                    {
                        checkBox.IsChecked = _settings.TrackingAngles.Contains(angle);
                    }
                }
            }
        }

        private void SaveSettings()
        {
            if (_isUpdating) return;
            
            try
            {
                // 基本设置
                _settings.IsEnabled = EnablePolarTrackingCheckBox.IsChecked ?? true;
                
                if (float.TryParse(AngleToleranceTextBox.Text, out float tolerance))
                    _settings.AngleTolerance = tolerance;
                
                _settings.ShowAngleInfo = ShowAngleInfoCheckBox.IsChecked ?? true;
                _settings.ShowDistanceInfo = ShowDistanceInfoCheckBox.IsChecked ?? true;
                _settings.ExtendToScreenEdge = ExtendToScreenEdgeCheckBox.IsChecked ?? true;
                
                // 视觉设置
                if (float.TryParse(TrackingLineWidthTextBox.Text, out float width))
                    _settings.TrackingLineWidth = width;
                
                // 角度设置
                SaveTrackingAngles();
                
                // 应用设置到系统
                ApplySettingsToSystem();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveTrackingAngles()
        {
            _settings.TrackingAngles.Clear();
            
            if (StandardAnglesRadioButton.IsChecked == true)
            {
                // 保存选中的标准角度
                var checkBoxes = StandardAnglesPanel.Children.OfType<WrapPanel>().FirstOrDefault()?.Children.OfType<CheckBox>();
                if (checkBoxes != null)
                {
                    foreach (var checkBox in checkBoxes)
                    {
                        if (checkBox.IsChecked == true && 
                            float.TryParse(checkBox.Content.ToString().Replace("°", ""), out float angle))
                        {
                            _settings.TrackingAngles.Add(angle);
                        }
                    }
                }
            }
            else if (CustomAnglesRadioButton.IsChecked == true)
            {
                // 保存自定义角度
                var angleStrings = CustomAnglesTextBox.Text.Split(',');
                foreach (var angleString in angleStrings)
                {
                    if (float.TryParse(angleString.Trim(), out float angle))
                    {
                        _settings.TrackingAngles.Add(angle);
                    }
                }
            }
        }

        private void ApplySettingsToSystem()
        {
            if (_viewer?.GetPhase2Manager() != null)
            {
                var phase2Manager = _viewer.GetPhase2Manager();
                phase2Manager.ApplyPolarTrackingSettings(_settings);
            }
        }

        private void RestoreDefaults_Click(object sender, RoutedEventArgs e)
        {
            _settings = new PolarTrackingSettings();
            LoadSettings();
            MessageBox.Show("已恢复默认设置", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void TrackingLineColorButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现颜色选择对话框
            MessageBox.Show("颜色选择功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            MessageBox.Show("设置已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
