using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Numerics;

namespace McLaser.EditViewerSk.Spatial
{
    /// <summary>
    /// 四叉树空间索引
    /// 用于高效的空间查询、碰撞检测和视口裁剪
    /// </summary>
    public class QuadTree : IDisposable
    {
        #region 私有字段

        private QuadTreeNode _root;
        private readonly int _maxDepth;
        private readonly int _maxEntitiesPerNode;
        private readonly Dictionary<EntityBase, QuadTreeNode> _entityToNodeMap;
        private BoundingBox _bounds;
        private int _totalEntities;
        private bool _isDisposed;

        #endregion

        #region 构造函数

        /// <summary>
        /// 创建四叉树
        /// </summary>
        /// <param name="bounds">空间边界</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="maxEntitiesPerNode">每个节点最大实体数</param>
        public QuadTree(BoundingBox bounds, int maxDepth = 8, int maxEntitiesPerNode = 10)
        {
            _bounds = bounds;
            _maxDepth = maxDepth;
            _maxEntitiesPerNode = maxEntitiesPerNode;
            _entityToNodeMap = new Dictionary<EntityBase, QuadTreeNode>();
            _root = new QuadTreeNode(bounds, 0, maxDepth, maxEntitiesPerNode);
            _totalEntities = 0;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 空间边界
        /// </summary>
        public BoundingBox Bounds => _bounds;

        /// <summary>
        /// 总实体数量
        /// </summary>
        public int TotalEntities => _totalEntities;

        /// <summary>
        /// 最大深度
        /// </summary>
        public int MaxDepth => _maxDepth;

        /// <summary>
        /// 根节点
        /// </summary>
        public QuadTreeNode Root => _root;

        #endregion

        #region 公共方法

        /// <summary>
        /// 插入实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>是否成功插入</returns>
        public bool Insert(EntityBase entity)
        {
            if (_isDisposed || entity == null) return false;

            try
            {
                // 检查实体是否已存在
                if (_entityToNodeMap.ContainsKey(entity))
                {
                    return false;
                }

                // 获取实体包围盒
                var entityBounds = GetEntityBounds(entity);
                if (entityBounds.IsEmpty) return false;

                // 插入到四叉树
                var node = _root.Insert(entity, entityBounds);
                if (node != null)
                {
                    _entityToNodeMap[entity] = node;
                    _totalEntities++;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree insert error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>是否成功移除</returns>
        public bool Remove(EntityBase entity)
        {
            if (_isDisposed || entity == null) return false;

            try
            {
                if (_entityToNodeMap.TryGetValue(entity, out var node))
                {
                    if (node.Remove(entity))
                    {
                        _entityToNodeMap.Remove(entity);
                        _totalEntities--;
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree remove error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新实体位置
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>是否成功更新</returns>
        public bool Update(EntityBase entity)
        {
            if (_isDisposed || entity == null) return false;

            try
            {
                // 先移除再插入
                if (Remove(entity))
                {
                    return Insert(entity);
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree update error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查询指定区域内的实体
        /// </summary>
        /// <param name="queryBounds">查询区域</param>
        /// <returns>实体列表</returns>
        public List<EntityBase> Query(BoundingBox queryBounds)
        {
            if (_isDisposed || queryBounds.IsEmpty) return new List<EntityBase>();

            try
            {
                var results = new List<EntityBase>();
                _root.Query(queryBounds, results);
                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree query error: {ex.Message}");
                return new List<EntityBase>();
            }
        }

        /// <summary>
        /// 查询指定点附近的实体
        /// </summary>
        /// <param name="point">查询点</param>
        /// <param name="radius">查询半径</param>
        /// <returns>实体列表</returns>
        public List<EntityBase> QueryPoint(Vector2 point, float radius = 0.1f)
        {
            var queryBounds = new BoundingBox(
                point.X - radius, point.Y + radius,
                point.X + radius, point.Y - radius);

            return Query(queryBounds);
        }

        /// <summary>
        /// 查询与指定实体相交的实体
        /// </summary>
        /// <param name="entity">查询实体</param>
        /// <returns>相交的实体列表</returns>
        public List<EntityBase> QueryIntersecting(EntityBase entity)
        {
            if (_isDisposed || entity == null) return new List<EntityBase>();

            var entityBounds = GetEntityBounds(entity);
            if (entityBounds.IsEmpty) return new List<EntityBase>();

            var candidates = Query(entityBounds);
            
            // 移除查询实体本身
            candidates.Remove(entity);
            
            return candidates;
        }

        /// <summary>
        /// 清空四叉树
        /// </summary>
        public void Clear()
        {
            if (_isDisposed) return;

            try
            {
                _root.Clear();
                _entityToNodeMap.Clear();
                _totalEntities = 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree clear error: {ex.Message}");
            }
        }

        /// <summary>
        /// 重建四叉树
        /// </summary>
        /// <param name="newBounds">新的空间边界</param>
        public void Rebuild(BoundingBox newBounds)
        {
            if (_isDisposed) return;

            try
            {
                // 收集所有实体
                var allEntities = new List<EntityBase>(_entityToNodeMap.Keys);

                // 清空当前树
                Clear();

                // 更新边界
                _bounds = newBounds;
                _root = new QuadTreeNode(newBounds, 0, _maxDepth, _maxEntitiesPerNode);

                // 重新插入所有实体
                foreach (var entity in allEntities)
                {
                    Insert(entity);
                }

                Debug.WriteLine($"QuadTree rebuilt with {allEntities.Count} entities");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree rebuild error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public QuadTreeStats GetStats()
        {
            if (_isDisposed) return new QuadTreeStats();

            try
            {
                var stats = new QuadTreeStats
                {
                    TotalEntities = _totalEntities,
                    MaxDepth = _maxDepth,
                    MaxEntitiesPerNode = _maxEntitiesPerNode
                };

                _root.CollectStats(stats);
                return stats;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree stats error: {ex.Message}");
                return new QuadTreeStats();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取实体包围盒
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>包围盒</returns>
        private BoundingBox GetEntityBounds(EntityBase entity)
        {
            try
            {
                if (entity.BBox != null && !entity.BBox.IsEmpty)
                {
                    return entity.BBox;
                }

                // 如果包围盒为空，尝试重新计算
                entity.UpdateBoundingBox();
                return entity.BBox ?? BoundingBox.Empty;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting entity bounds: {ex.Message}");
                return BoundingBox.Empty;
            }
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                Clear();
                _root?.Dispose();
                _root = null;
                _isDisposed = true;

                Debug.WriteLine("QuadTree disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTree dispose error: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 四叉树统计信息
    /// </summary>
    public class QuadTreeStats
    {
        public int TotalEntities { get; set; }
        public int TotalNodes { get; set; }
        public int LeafNodes { get; set; }
        public int MaxDepth { get; set; }
        public int MaxEntitiesPerNode { get; set; }
        public int ActualMaxDepth { get; set; }
        public double AverageEntitiesPerLeaf { get; set; }
        public int EmptyNodes { get; set; }
    }
}
