using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace McLaser.EditViewerSk.Tracking
{
    /// <summary>
    /// 对象追踪系统
    /// 实现基于对象捕捉点的智能追踪和对齐功能
    /// </summary>
    public class ObjectTrackingSystem
    {
        private ViewBase _viewer;
        private bool _isEnabled = false;
        private List<TrackingPoint> _trackingPoints;
        private List<TrackingLine> _activeTrackingLines;
        private Vector2? _currentCursor;
        private List<Vector2> _intersectionPoints;
        
        // 配置参数
        private const float TrackingActivationDistance = 150.0f; // 追踪激活距离（像素）
        private const float AlignmentTolerance = 8.0f;           // 对齐容差（像素）
        private const int MaxTrackingPoints = 12;               // 最大追踪点数量
        private const float MinTrackingLineLength = 30.0f;      // 最小追踪线长度
        
        // 视觉样式
        private SKPaint _trackingLinePaint;
        private SKPaint _alignmentLinePaint;
        private SKPaint _trackingPointPaint;
        private SKPaint _intersectionPaint;
        private SKPaint _highlightPaint;
        
        public bool IsEnabled
        {
            get { return _isEnabled; }
            set { _isEnabled = value; }
        }
        
        public List<TrackingPoint> TrackingPoints
        {
            get { return _trackingPoints; }
        }
        
        public List<Vector2> IntersectionPoints
        {
            get { return _intersectionPoints; }
        }
        
        public ObjectTrackingSystem(ViewBase viewer)
        {
            _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
            _trackingPoints = new List<TrackingPoint>();
            _activeTrackingLines = new List<TrackingLine>();
            _intersectionPoints = new List<Vector2>();
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _trackingLinePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(180, 255, 165, 0), // 半透明橙色
                PathEffect = SKPathEffect.CreateDash(new float[] { 4, 3 }, 0),
                IsAntialias = true
            };
            
            _alignmentLinePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.5f,
                Color = SKColors.Yellow,
                PathEffect = SKPathEffect.CreateDash(new float[] { 6, 3 }, 0),
                IsAntialias = true
            };
            
            _trackingPointPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColor.FromArgb(220, 255, 255, 0), // 半透明黄色
                IsAntialias = true
            };
            
            _intersectionPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Red,
                IsAntialias = true
            };
            
            _highlightPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Cyan,
                IsAntialias = true
            };
        }
        
        /// <summary>
        /// 添加追踪点
        /// </summary>
        public void AddTrackingPoint(Vector2 point, ObjectSnapMode snapMode, EntityBase sourceEntity = null)
        {
            if (!_isEnabled) return;
            
            // 检查是否已存在相近的追踪点
            var existingPoint = _trackingPoints.FirstOrDefault(tp => 
                Vector2.Distance(tp.Position, point) < 2.0f);
            
            if (existingPoint != null)
            {
                // 更新现有点的信息
                existingPoint.LastUsed = DateTime.Now;
                existingPoint.UseCount++;
                return;
            }
            
            // 添加新的追踪点
            var trackingPoint = new TrackingPoint
            {
                Position = point,
                SnapMode = snapMode,
                SourceEntity = sourceEntity,
                CreatedTime = DateTime.Now,
                LastUsed = DateTime.Now,
                UseCount = 1
            };
            
            _trackingPoints.Add(trackingPoint);
            
            // 限制追踪点数量，移除最旧且使用最少的点
            if (_trackingPoints.Count > MaxTrackingPoints)
            {
                var pointToRemove = _trackingPoints
                    .OrderBy(tp => tp.UseCount)
                    .ThenBy(tp => tp.LastUsed)
                    .First();
                _trackingPoints.Remove(pointToRemove);
            }
        }
        
        /// <summary>
        /// 清除所有追踪点
        /// </summary>
        public void ClearTrackingPoints()
        {
            _trackingPoints.Clear();
            _activeTrackingLines.Clear();
            _intersectionPoints.Clear();
        }
        
        /// <summary>
        /// 清除过期的追踪点
        /// </summary>
        public void ClearExpiredTrackingPoints(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.Now - maxAge;
            _trackingPoints.RemoveAll(tp => tp.LastUsed < cutoffTime);
        }
        
        /// <summary>
        /// 更新追踪（在鼠标移动时调用）
        /// </summary>
        public ObjectTrackingResult UpdateTracking(Vector2 cursorPosition)
        {
            _currentCursor = cursorPosition;
            _activeTrackingLines.Clear();
            _intersectionPoints.Clear();
            
            if (!_isEnabled || _trackingPoints.Count == 0)
            {
                return new ObjectTrackingResult();
            }
            
            var result = new ObjectTrackingResult();
            var cursorCanvas = _viewer.ModelToCanvas(cursorPosition);
            
            // 查找活跃的追踪线
            foreach (var trackingPoint in _trackingPoints)
            {
                var pointCanvas = _viewer.ModelToCanvas(trackingPoint.Position);
                var distance = Vector2.Distance(pointCanvas, cursorCanvas);
                
                if (distance <= TrackingActivationDistance)
                {
                    // 创建水平和垂直追踪线
                    CreateTrackingLines(trackingPoint, cursorPosition, result);
                    
                    // 更新最后使用时间
                    trackingPoint.LastUsed = DateTime.Now;
                }
            }
            
            // 计算追踪线交点
            CalculateIntersections(result);
            
            // 查找最佳对齐点
            result.BestAlignmentPoint = FindBestAlignmentPoint(cursorPosition, result);
            
            return result;
        }
        
        /// <summary>
        /// 创建追踪线
        /// </summary>
        private void CreateTrackingLines(TrackingPoint trackingPoint, Vector2 cursorPosition, ObjectTrackingResult result)
        {
            var cursorCanvas = _viewer.ModelToCanvas(cursorPosition);
            var pointCanvas = _viewer.ModelToCanvas(trackingPoint.Position);
            
            // 水平追踪线
            if (Math.Abs(cursorCanvas.Y - pointCanvas.Y) <= AlignmentTolerance)
            {
                var horizontalLine = new TrackingLine
                {
                    StartPoint = trackingPoint.Position,
                    Direction = new Vector2(1, 0),
                    TrackingPoint = trackingPoint,
                    Type = TrackingLineType.Horizontal,
                    IsActive = true
                };
                
                _activeTrackingLines.Add(horizontalLine);
                result.AlignmentLines.Add(horizontalLine);
            }
            
            // 垂直追踪线
            if (Math.Abs(cursorCanvas.X - pointCanvas.X) <= AlignmentTolerance)
            {
                var verticalLine = new TrackingLine
                {
                    StartPoint = trackingPoint.Position,
                    Direction = new Vector2(0, 1),
                    TrackingPoint = trackingPoint,
                    Type = TrackingLineType.Vertical,
                    IsActive = true
                };
                
                _activeTrackingLines.Add(verticalLine);
                result.AlignmentLines.Add(verticalLine);
            }
            
            // 45度对角线追踪
            CreateDiagonalTrackingLines(trackingPoint, cursorPosition, result);
        }
        
        /// <summary>
        /// 创建对角线追踪线
        /// </summary>
        private void CreateDiagonalTrackingLines(TrackingPoint trackingPoint, Vector2 cursorPosition, ObjectTrackingResult result)
        {
            var cursorCanvas = _viewer.ModelToCanvas(cursorPosition);
            var pointCanvas = _viewer.ModelToCanvas(trackingPoint.Position);
            
            var deltaX = cursorCanvas.X - pointCanvas.X;
            var deltaY = cursorCanvas.Y - pointCanvas.Y;
            
            // 检查45度对角线
            if (Math.Abs(Math.Abs(deltaX) - Math.Abs(deltaY)) <= AlignmentTolerance)
            {
                var direction = new Vector2(Math.Sign(deltaX), Math.Sign(deltaY));
                direction = Vector2.Normalize(direction);
                
                var diagonalLine = new TrackingLine
                {
                    StartPoint = trackingPoint.Position,
                    Direction = direction,
                    TrackingPoint = trackingPoint,
                    Type = TrackingLineType.Diagonal45,
                    IsActive = true
                };
                
                _activeTrackingLines.Add(diagonalLine);
                result.AlignmentLines.Add(diagonalLine);
            }
        }
        
        /// <summary>
        /// 计算追踪线交点
        /// </summary>
        private void CalculateIntersections(ObjectTrackingResult result)
        {
            for (int i = 0; i < _activeTrackingLines.Count; i++)
            {
                for (int j = i + 1; j < _activeTrackingLines.Count; j++)
                {
                    var line1 = _activeTrackingLines[i];
                    var line2 = _activeTrackingLines[j];
                    
                    var intersection = CalculateLineIntersection(line1, line2);
                    if (intersection.HasValue)
                    {
                        _intersectionPoints.Add(intersection.Value);
                        result.IntersectionPoints.Add(intersection.Value);
                    }
                }
            }
        }
        
        /// <summary>
        /// 计算两条追踪线的交点
        /// </summary>
        private Vector2? CalculateLineIntersection(TrackingLine line1, TrackingLine line2)
        {
            // 避免相同类型的线相交
            if (line1.Type == line2.Type) return null;
            
            var p1 = line1.StartPoint;
            var d1 = line1.Direction;
            var p2 = line2.StartPoint;
            var d2 = line2.Direction;
            
            // 计算交点参数
            var denominator = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(denominator) < 1e-6) return null; // 平行线
            
            var dp = p2 - p1;
            var t = (dp.X * d2.Y - dp.Y * d2.X) / denominator;
            
            return p1 + t * d1;
        }
        
        /// <summary>
        /// 查找最佳对齐点
        /// </summary>
        private Vector2? FindBestAlignmentPoint(Vector2 cursorPosition, ObjectTrackingResult result)
        {
            var candidates = new List<AlignmentCandidate>();
            
            // 添加交点候选
            foreach (var intersection in result.IntersectionPoints)
            {
                var distance = Vector2.Distance(cursorPosition, intersection);
                candidates.Add(new AlignmentCandidate
                {
                    Point = intersection,
                    Distance = distance,
                    Priority = 1, // 交点优先级最高
                    Type = "Intersection"
                });
            }
            
            // 添加对齐线上的投影点
            foreach (var line in result.AlignmentLines)
            {
                var projectionPoint = ProjectPointOntoLine(cursorPosition, line);
                if (projectionPoint.HasValue)
                {
                    var distance = Vector2.Distance(cursorPosition, projectionPoint.Value);
                    candidates.Add(new AlignmentCandidate
                    {
                        Point = projectionPoint.Value,
                        Distance = distance,
                        Priority = 2,
                        Type = line.Type.ToString()
                    });
                }
            }
            
            if (candidates.Count == 0) return null;
            
            // 选择优先级最高且距离最近的点
            var bestCandidate = candidates
                .OrderBy(c => c.Priority)
                .ThenBy(c => c.Distance)
                .FirstOrDefault();
            
            return bestCandidate?.Point;
        }
        
        /// <summary>
        /// 将点投影到追踪线上
        /// </summary>
        private Vector2? ProjectPointOntoLine(Vector2 point, TrackingLine line)
        {
            switch (line.Type)
            {
                case TrackingLineType.Horizontal:
                    return new Vector2(point.X, line.StartPoint.Y);
                case TrackingLineType.Vertical:
                    return new Vector2(line.StartPoint.X, point.Y);
                case TrackingLineType.Diagonal45:
                    return ProjectPointOntoDiagonal(point, line);
                default:
                    return null;
            }
        }
        
        /// <summary>
        /// 将点投影到对角线上
        /// </summary>
        private Vector2? ProjectPointOntoDiagonal(Vector2 point, TrackingLine line)
        {
            var toPoint = point - line.StartPoint;
            var projection = Vector2.Dot(toPoint, line.Direction);
            return line.StartPoint + projection * line.Direction;
        }
        
        /// <summary>
        /// 渲染对象追踪
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            if (!_isEnabled) return;
            
            // 绘制追踪点
            RenderTrackingPoints(canvas);
            
            // 绘制活跃的追踪线
            RenderActiveTrackingLines(canvas);
            
            // 绘制交点
            RenderIntersectionPoints(canvas);
        }
        
        private void RenderTrackingPoints(SKCanvas canvas)
        {
            foreach (var trackingPoint in _trackingPoints)
            {
                var pointCanvas = _viewer.ModelToCanvas(trackingPoint.Position);
                
                // 计算透明度（基于最后使用时间和使用频率）
                var timeSinceLastUse = DateTime.Now - trackingPoint.LastUsed;
                var baseAlpha = Math.Max(0.2f, 1.0f - (float)timeSinceLastUse.TotalSeconds / 60.0f);
                var frequencyBoost = Math.Min(0.3f, trackingPoint.UseCount * 0.05f);
                var alpha = Math.Min(1.0f, baseAlpha + frequencyBoost);
                
                var paint = new SKPaint
                {
                    Style = SKPaintStyle.Fill,
                    Color = SKColor.FromArgb((byte)(alpha * 255), 255, 255, 0),
                    IsAntialias = true
                };
                
                // 绘制追踪点标记
                var markerSize = 2.5f + trackingPoint.UseCount * 0.3f;
                canvas.DrawCircle(pointCanvas.X, pointCanvas.Y, markerSize, paint);
                
                // 绘制外圈
                paint.Style = SKPaintStyle.Stroke;
                paint.StrokeWidth = 0.5f;
                paint.Color = SKColor.FromArgb((byte)(alpha * 200), 255, 165, 0);
                canvas.DrawCircle(pointCanvas.X, pointCanvas.Y, markerSize + 1, paint);
                
                paint.Dispose();
            }
        }
        
        private void RenderActiveTrackingLines(SKCanvas canvas)
        {
            foreach (var line in _activeTrackingLines)
            {
                RenderTrackingLine(canvas, line);
            }
        }
        
        private void RenderTrackingLine(SKCanvas canvas, TrackingLine line)
        {
            if (!_currentCursor.HasValue) return;
            
            var startCanvas = _viewer.ModelToCanvas(line.StartPoint);
            var cursorCanvas = _viewer.ModelToCanvas(_currentCursor.Value);
            
            Vector2 endCanvas;
            
            switch (line.Type)
            {
                case TrackingLineType.Horizontal:
                    endCanvas = new Vector2(cursorCanvas.X, startCanvas.Y);
                    break;
                case TrackingLineType.Vertical:
                    endCanvas = new Vector2(startCanvas.X, cursorCanvas.Y);
                    break;
                case TrackingLineType.Diagonal45:
                    endCanvas = CalculateDiagonalEndPoint(startCanvas, cursorCanvas, line.Direction);
                    break;
                default:
                    return;
            }
            
            // 只绘制足够长的线
            if (Vector2.Distance(startCanvas, endCanvas) >= MinTrackingLineLength)
            {
                canvas.DrawLine(startCanvas.X, startCanvas.Y, endCanvas.X, endCanvas.Y, _alignmentLinePaint);
            }
        }
        
        private Vector2 CalculateDiagonalEndPoint(Vector2 startCanvas, Vector2 cursorCanvas, Vector2 direction)
        {
            var toTarget = cursorCanvas - startCanvas;
            var projection = Vector2.Dot(toTarget, direction);
            return startCanvas + projection * direction;
        }
        
        private void RenderIntersectionPoints(SKCanvas canvas)
        {
            foreach (var intersection in _intersectionPoints)
            {
                var intersectionCanvas = _viewer.ModelToCanvas(intersection);
                
                // 绘制交点标记
                canvas.DrawCircle(intersectionCanvas.X, intersectionCanvas.Y, 4, _intersectionPaint);
                
                // 绘制外圈
                var outerPaint = new SKPaint
                {
                    Style = SKPaintStyle.Stroke,
                    StrokeWidth = 1.5f,
                    Color = SKColors.White,
                    IsAntialias = true
                };
                canvas.DrawCircle(intersectionCanvas.X, intersectionCanvas.Y, 6, outerPaint);
                outerPaint.Dispose();
            }
        }
        
        /// <summary>
        /// 自动从实体获取追踪点
        /// </summary>
        public void AutoAcquireTrackingPoints()
        {
            if (!_isEnabled) return;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                var snapPoints = entity.GetSnapPoints();
                if (snapPoints != null)
                {
                    foreach (var snapPoint in snapPoints)
                    {
                        // 只添加重要的捕捉点类型
                        if (IsImportantSnapPoint(snapPoint.type))
                        {
                            AddTrackingPoint(snapPoint.position, snapPoint.type, entity);
                        }
                    }
                }
            }
        }
        
        private bool IsImportantSnapPoint(ObjectSnapMode snapMode)
        {
            return snapMode == ObjectSnapMode.End ||
                   snapMode == ObjectSnapMode.Mid ||
                   snapMode == ObjectSnapMode.Center ||
                   snapMode == ObjectSnapMode.Intersection ||
                   snapMode == ObjectSnapMode.Quad;
        }
        
        /// <summary>
        /// 获取追踪状态信息
        /// </summary>
        public ObjectTrackingInfo GetTrackingInfo()
        {
            return new ObjectTrackingInfo
            {
                IsActive = _isEnabled && _activeTrackingLines.Count > 0,
                TrackingPointCount = _trackingPoints.Count,
                ActiveTrackingLines = _activeTrackingLines.Count,
                IntersectionCount = _intersectionPoints.Count
            };
        }
        
        public void Dispose()
        {
            _trackingLinePaint?.Dispose();
            _alignmentLinePaint?.Dispose();
            _trackingPointPaint?.Dispose();
            _intersectionPaint?.Dispose();
            _highlightPaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 追踪点
    /// </summary>
    public class TrackingPoint
    {
        public Vector2 Position { get; set; }
        public ObjectSnapMode SnapMode { get; set; }
        public EntityBase SourceEntity { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime LastUsed { get; set; }
        public int UseCount { get; set; }
    }
    
    /// <summary>
    /// 追踪线
    /// </summary>
    public class TrackingLine
    {
        public Vector2 StartPoint { get; set; }
        public Vector2 Direction { get; set; }
        public TrackingPoint TrackingPoint { get; set; }
        public TrackingLineType Type { get; set; }
        public bool IsActive { get; set; }
    }
    
    /// <summary>
    /// 追踪线类型
    /// </summary>
    public enum TrackingLineType
    {
        Horizontal,
        Vertical,
        Diagonal45,
        Angular
    }
    
    /// <summary>
    /// 对象追踪结果
    /// </summary>
    public class ObjectTrackingResult
    {
        public List<TrackingLine> AlignmentLines { get; set; } = new List<TrackingLine>();
        public List<Vector2> IntersectionPoints { get; set; } = new List<Vector2>();
        public Vector2? BestAlignmentPoint { get; set; }
    }
    
    /// <summary>
    /// 对齐候选点
    /// </summary>
    internal class AlignmentCandidate
    {
        public Vector2 Point { get; set; }
        public float Distance { get; set; }
        public int Priority { get; set; }
        public string Type { get; set; }
    }
    
    /// <summary>
    /// 对象追踪信息
    /// </summary>
    public class ObjectTrackingInfo
    {
        public bool IsActive { get; set; }
        public int TrackingPointCount { get; set; }
        public int ActiveTrackingLines { get; set; }
        public int IntersectionCount { get; set; }
    }
} 