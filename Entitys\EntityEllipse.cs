using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Numerics;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 椭圆图元类
    /// 基于CAD行业标准实现，支持完整的椭圆数学模型
    /// </summary>
    public class EntityEllipse : EntityBase
    {
        #region 私有字段
        private Vector2 _center = Vector2.Zero;
        private double _radiusX = 10.0;
        private double _radiusY = 5.0;
        private double _rotation = 0.0; // 椭圆旋转角度（度）
        private double _startAngle = 0.0; // 起始角度（椭圆弧用）
        private double _endAngle = 360.0; // 结束角度（椭圆弧用）
        private bool _isEllipticalArc = false; // 是否为椭圆弧
        
        [JsonIgnore]
        private SKPath _ellipsePath; // 缓存的椭圆路径
        private bool _pathNeedsUpdate = true; // 路径是否需要更新
        #endregion

        #region 公共属性
        /// <summary>
        /// 椭圆中心点
        /// </summary>
        [Category("几何"), DisplayName("中心点"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public Vector2 Center
        {
            get => _center;
            set
            {
                if (_center != value)
                {
                    _center = value;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Center));
                }
            }
        }

        /// <summary>
        /// X轴半径（长轴半径）
        /// </summary>
        [Category("几何"), DisplayName("X轴半径"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double RadiusX
        {
            get => _radiusX;
            set
            {
                if (value > 0 && Math.Abs(_radiusX - value) > double.Epsilon)
                {
                    _radiusX = value;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(RadiusX));
                    OnPropertyChanged(nameof(MajorRadius));
                    OnPropertyChanged(nameof(MinorRadius));
                    OnPropertyChanged(nameof(Eccentricity));
                }
            }
        }

        /// <summary>
        /// Y轴半径（短轴半径）
        /// </summary>
        [Category("几何"), DisplayName("Y轴半径"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double RadiusY
        {
            get => _radiusY;
            set
            {
                if (value > 0 && Math.Abs(_radiusY - value) > double.Epsilon)
                {
                    _radiusY = value;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(RadiusY));
                    OnPropertyChanged(nameof(MajorRadius));
                    OnPropertyChanged(nameof(MinorRadius));
                    OnPropertyChanged(nameof(Eccentricity));
                }
            }
        }

        /// <summary>
        /// 椭圆旋转角度（度）
        /// </summary>
        [Category("几何"), DisplayName("旋转角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double Rotation
        {
            get => _rotation;
            set
            {
                var normalizedAngle = MathHelper.NormalizeAngle(value);
                if (Math.Abs(_rotation - normalizedAngle) > double.Epsilon)
                {
                    _rotation = normalizedAngle;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(Rotation));
                }
            }
        }

        /// <summary>
        /// 起始角度（椭圆弧用，度）
        /// </summary>
        [Category("弧段"), DisplayName("起始角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double StartAngle
        {
            get => _startAngle;
            set
            {
                var normalizedAngle = MathHelper.NormalizeAngle(value);
                if (Math.Abs(_startAngle - normalizedAngle) > double.Epsilon)
                {
                    _startAngle = normalizedAngle;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(StartAngle));
                }
            }
        }

        /// <summary>
        /// 结束角度（椭圆弧用，度）
        /// </summary>
        [Category("弧段"), DisplayName("结束角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double EndAngle
        {
            get => _endAngle;
            set
            {
                var normalizedAngle = MathHelper.NormalizeAngle(value);
                if (Math.Abs(_endAngle - normalizedAngle) > double.Epsilon)
                {
                    _endAngle = normalizedAngle;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(EndAngle));
                }
            }
        }

        /// <summary>
        /// 是否为椭圆弧
        /// </summary>
        [Category("弧段"), DisplayName("椭圆弧"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public bool IsEllipticalArc
        {
            get => _isEllipticalArc;
            set
            {
                if (_isEllipticalArc != value)
                {
                    _isEllipticalArc = value;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(IsEllipticalArc));
                }
            }
        }

        /// <summary>
        /// 长轴半径（只读）
        /// </summary>
        [Category("计算属性"), DisplayName("长轴半径"), Browsable(true), ReadOnly(true)]
        public double MajorRadius => Math.Max(_radiusX, _radiusY);

        /// <summary>
        /// 短轴半径（只读）
        /// </summary>
        [Category("计算属性"), DisplayName("短轴半径"), Browsable(true), ReadOnly(true)]
        public double MinorRadius => Math.Min(_radiusX, _radiusY);

        /// <summary>
        /// 离心率（只读）
        /// </summary>
        [Category("计算属性"), DisplayName("离心率"), Browsable(true), ReadOnly(true)]
        public double Eccentricity
        {
            get
            {
                var a = MajorRadius;
                var b = MinorRadius;
                if (a == 0) return 0;
                return Math.Sqrt(1 - (b * b) / (a * a));
            }
        }

        /// <summary>
        /// 椭圆周长近似值（使用拉马努金公式）
        /// </summary>
        [Category("计算属性"), DisplayName("周长"), Browsable(true), ReadOnly(true)]
        public double Perimeter
        {
            get
            {
                var a = _radiusX;
                var b = _radiusY;
                var h = Math.Pow((a - b) / (a + b), 2);
                return Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.Sqrt(4 - 3 * h)));
            }
        }

        /// <summary>
        /// 椭圆面积
        /// </summary>
        [Category("计算属性"), DisplayName("面积"), Browsable(true), ReadOnly(true)]
        public double Area => Math.PI * _radiusX * _radiusY;
        #endregion

        #region 构造函数
        public EntityEllipse()
        {
            Name = "Ellipse";
            _ellipsePath = new SKPath();
            // Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/ellipse.png"));
        }

        public EntityEllipse(Vector2 center, double radiusX, double radiusY, double rotation = 0) : this()
        {
            _center = center;
            _radiusX = Math.Max(radiusX, 0.001); // 防止零半径
            _radiusY = Math.Max(radiusY, 0.001);
            _rotation = MathHelper.NormalizeAngle(rotation);
        }

        public EntityEllipse(Vector2 center, double radiusX, double radiusY, double startAngle, double endAngle, double rotation = 0)
            : this(center, radiusX, radiusY, rotation)
        {
            _startAngle = MathHelper.NormalizeAngle(startAngle);
            _endAngle = MathHelper.NormalizeAngle(endAngle);
            _isEllipticalArc = true;
        }
        #endregion

        #region 重写方法
        [JsonIgnore]
        public override BoundingBox BoundingBox { get; set; } = BoundingBox.Empty;

        /// <summary>
        /// 重新生成椭圆数据
        /// </summary>
        public override void Regen()
        {
            RegenBoundRect();
            UpdateEllipsePath();
            IsNeedToRegen = false;
        }

        /// <summary>
        /// 渲染椭圆
        /// </summary>
        public override void Render(IView view)
        {
            if (view == null || !IsRenderable) return;
            if (IsNeedToRegen) Regen();

            // 使用新的集成渲染系统
            this.IntegrateEllipseRendering(view);
        }

        /// <summary>
        /// 平移变换
        /// </summary>
        public override void Translate(Vector2 delta)
        {
            if (IsLocked || delta == Vector2.Zero) return;
            
            Center += delta;
            BoundingBox?.Transit(delta);
        }

        /// <summary>
        /// 旋转变换
        /// </summary>
        public override void Rotate(double angle)
        {
            if (IsLocked || MathHelper.IsZero(angle)) return;
            
            Rotation += angle;
        }

        /// <summary>
        /// 围绕指定点旋转
        /// </summary>
        public override void Rotate(double angle, Vector2 rotateCenter)
        {
            if (IsLocked || MathHelper.IsZero(angle)) return;
            
            // 旋转中心点
            var matrix = Matrix3.CreateRotation((float)(angle * Math.PI / 180.0), rotateCenter);
            Center = Vector2.Transform(Center, matrix);
            
            // 旋转椭圆本身
            Rotation += angle;
        }

        /// <summary>
        /// 缩放变换
        /// </summary>
        public override void Scale(Vector2 scale, Vector2 scaleCenter)
        {
            if (IsLocked || scale == Vector2.Zero || scale == Vector2.One) return;
            
            // 缩放中心点
            Center = (Center - scaleCenter) * scale + scaleCenter;
            
            // 缩放半径
            RadiusX *= Math.Abs(scale.X);
            RadiusY *= Math.Abs(scale.Y);
        }

        /// <summary>
        /// 点击测试
        /// </summary>
        public override bool HitTest(double x, double y, double threshold)
        {
            if (!BoundingBox.HitTest(x, y, threshold)) return false;
            
            return IsPointOnEllipse(new Vector2((float)x, (float)y), threshold);
        }

        /// <summary>
        /// 矩形区域碰撞测试
        /// </summary>
        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!BoundingBox.HitTest(br, threshold)) return false;
            
            // 简化：检查椭圆是否与矩形相交
            return IsEllipseIntersectRect(br);
        }

        /// <summary>
        /// 获取对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            var snapPoints = new List<ObjectSnapPoint>();
            
            // 中心点
            snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Center, _center));
            
            if (_isEllipticalArc)
            {
                // 椭圆弧端点
                snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.End, GetPointAtAngle(_startAngle)));
                snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.End, GetPointAtAngle(_endAngle)));
            }
            else
            {
                // 完整椭圆的象限点
                for (int i = 0; i < 4; i++)
                {
                    var angle = i * 90.0;
                    snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Quad, GetPointAtAngle(angle)));
                }
            }
            
            return snapPoints;
        }

        /// <summary>
        /// 克隆对象
        /// </summary>
        public override object Clone()
        {
            return new EntityEllipse
            {
                Name = Name,
                Description = Description,
                Parent = Parent,
                IsSelected = IsSelected,
                IsVisible = IsVisible,
                IsRenderable = IsRenderable,
                IsMarkerable = IsMarkerable,
                IsLocked = IsLocked,
                Color = Color,
                Center = _center,
                RadiusX = _radiusX,
                RadiusY = _radiusY,
                Rotation = _rotation,
                StartAngle = _startAngle,
                EndAngle = _endAngle,
                IsEllipticalArc = _isEllipticalArc,
                BoundingBox = BoundingBox?.Clone(),
                Tag = Tag,
                Index = Index,
                IsNeedToRegen = true
            };
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 重新计算包围盒
        /// </summary>
        private void RegenBoundRect()
        {
            if (_radiusX <= 0 || _radiusY <= 0)
            {
                BoundingBox = BoundingBox.Empty;
                return;
            }

            // 考虑椭圆旋转的包围盒计算
            var cos = Math.Cos(_rotation * Math.PI / 180.0);
            var sin = Math.Sin(_rotation * Math.PI / 180.0);
            
            var ux = _radiusX * cos;
            var uy = _radiusX * sin;
            var vx = _radiusY * -sin;
            var vy = _radiusY * cos;
            
            var halfWidth = Math.Sqrt(ux * ux + vx * vx);
            var halfHeight = Math.Sqrt(uy * uy + vy * vy);
            
            var left = _center.X - halfWidth;
            var right = _center.X + halfWidth;
            var top = _center.Y + halfHeight;
            var bottom = _center.Y - halfHeight;
            
            BoundingBox = new BoundingBox(left, top, right, bottom);
        }

        /// <summary>
        /// 更新椭圆路径
        /// </summary>
        private void UpdateEllipsePath()
        {
            if (!_pathNeedsUpdate) return;
            
            _ellipsePath?.Reset();
            _ellipsePath = CreateEllipsePath();
            _pathNeedsUpdate = false;
        }

        /// <summary>
        /// 创建椭圆路径
        /// </summary>
        private SKPath CreateEllipsePath()
        {
            var path = new SKPath();
            var rect = new SKRect(
                (float)(_center.X - _radiusX),
                (float)(_center.Y - _radiusY),
                (float)(_center.X + _radiusX),
                (float)(_center.Y + _radiusY)
            );

            if (_isEllipticalArc)
            {
                var sweepAngle = _endAngle - _startAngle;
                if (sweepAngle < 0) sweepAngle += 360;
                
                path.AddArc(rect, (float)_startAngle, (float)sweepAngle);
            }
            else
            {
                path.AddOval(rect);
            }

            // 应用旋转变换
            if (Math.Abs(_rotation) > double.Epsilon)
            {
                var matrix = SKMatrix.CreateRotationDegrees((float)_rotation, _center.X, _center.Y);
                path.Transform(matrix);
            }

            return path;
        }

        /// <summary>
        /// 椭圆弧渲染
        /// </summary>
        private void RenderEllipticalArc(Graphics.IGraphicsRenderer renderer)
        {
            // 使用路径渲染椭圆弧
            if (_ellipsePath != null)
            {
                renderer.DrawPath(_ellipsePath, Pen);
            }
        }

        /// <summary>
        /// 基础方法渲染（降级方案）
        /// </summary>
        private void RenderWithBasicMethod(ViewBase viewBase)
        {
            // 使用分段直线逼近椭圆
            var points = GetEllipsePoints(60); // 60个点的精度
            for (int i = 0; i < points.Count - 1; i++)
            {
                viewBase.DrawLine(points[i], points[i + 1], Pen);
            }
            
            if (!_isEllipticalArc && points.Count > 0)
            {
                viewBase.DrawLine(points[points.Count - 1], points[0], Pen);
            }
        }

        /// <summary>
        /// 渲染控制点
        /// </summary>
        private void RenderControlPoints(ViewBase viewBase)
        {
            var controlPaint = new SKPaint
            {
                Color = SKColors.Blue,
                StrokeWidth = 1.0f,
                Style = SKPaintStyle.Fill
            };

            // 中心点
            viewBase.DrawCircle(_center, 3, controlPaint);
            
            // 长轴端点
            var majorPoints = GetMajorAxisPoints();
            foreach (var point in majorPoints)
            {
                viewBase.DrawRectangle(point - new Vector2(2, 2), 4, 4, controlPaint);
            }
            
            // 短轴端点
            var minorPoints = GetMinorAxisPoints();
            foreach (var point in minorPoints)
            {
                viewBase.DrawRectangle(point - new Vector2(2, 2), 4, 4, controlPaint);
            }
        }

        /// <summary>
        /// 获取椭圆上的点列表
        /// </summary>
        private List<Vector2> GetEllipsePoints(int segments)
        {
            var points = new List<Vector2>();
            var startAngle = _isEllipticalArc ? _startAngle : 0;
            var endAngle = _isEllipticalArc ? _endAngle : 360;
            
            if (endAngle < startAngle) endAngle += 360;
            
            var angleStep = (endAngle - startAngle) / segments;
            
            for (int i = 0; i <= segments; i++)
            {
                var angle = startAngle + i * angleStep;
                points.Add(GetPointAtAngle(angle));
            }
            
            return points;
        }

        /// <summary>
        /// 获取指定角度上的椭圆点
        /// </summary>
        private Vector2 GetPointAtAngle(double angleDegrees)
        {
            var angleRad = angleDegrees * Math.PI / 180.0;
            var rotationRad = _rotation * Math.PI / 180.0;
            
            // 椭圆参数方程
            var x = _radiusX * Math.Cos(angleRad);
            var y = _radiusY * Math.Sin(angleRad);
            
            // 应用旋转
            var cosRot = Math.Cos(rotationRad);
            var sinRot = Math.Sin(rotationRad);
            
            var rotatedX = x * cosRot - y * sinRot;
            var rotatedY = x * sinRot + y * cosRot;
            
            return new Vector2((float)(_center.X + rotatedX), (float)(_center.Y + rotatedY));
        }

        /// <summary>
        /// 获取长轴端点
        /// </summary>
        private Vector2[] GetMajorAxisPoints()
        {
            if (_radiusX >= _radiusY)
            {
                return new[] { GetPointAtAngle(0), GetPointAtAngle(180) };
            }
            else
            {
                return new[] { GetPointAtAngle(90), GetPointAtAngle(270) };
            }
        }

        /// <summary>
        /// 获取短轴端点
        /// </summary>
        private Vector2[] GetMinorAxisPoints()
        {
            if (_radiusX >= _radiusY)
            {
                return new[] { GetPointAtAngle(90), GetPointAtAngle(270) };
            }
            else
            {
                return new[] { GetPointAtAngle(0), GetPointAtAngle(180) };
            }
        }

        /// <summary>
        /// 判断点是否在椭圆上
        /// </summary>
        private bool IsPointOnEllipse(Vector2 point, double threshold)
        {
            // 将点转换到椭圆的局部坐标系
            var localPoint = TransformToLocal(point);
            
            // 椭圆方程: (x/a)² + (y/b)² = 1
            var normalizedX = localPoint.X / _radiusX;
            var normalizedY = localPoint.Y / _radiusY;
            var distanceFromEllipse = Math.Abs(normalizedX * normalizedX + normalizedY * normalizedY - 1.0);
            
            return distanceFromEllipse <= threshold / Math.Min(_radiusX, _radiusY);
        }

        /// <summary>
        /// 椭圆与矩形相交测试
        /// </summary>
        private bool IsEllipseIntersectRect(BoundingBox rect)
        {
            // 简化实现：检查椭圆的包围盒是否与给定矩形相交
            return BoundingBox.HitTest(rect, 0);
        }

        /// <summary>
        /// 将世界坐标转换到椭圆的局部坐标系
        /// </summary>
        private Vector2 TransformToLocal(Vector2 worldPoint)
        {
            var relativePoint = worldPoint - _center;
            
            if (Math.Abs(_rotation) < double.Epsilon)
                return relativePoint;
            
            var rotationRad = -_rotation * Math.PI / 180.0; // 反向旋转
            var cos = Math.Cos(rotationRad);
            var sin = Math.Sin(rotationRad);
            
            return new Vector2(
                (float)(relativePoint.X * cos - relativePoint.Y * sin),
                (float)(relativePoint.X * sin + relativePoint.Y * cos)
            );
        }
        #endregion

        #region IDisposable
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _ellipsePath?.Dispose();
                _ellipsePath = null;
            }
            base.Dispose(disposing);
        }
        #endregion
    }
} 