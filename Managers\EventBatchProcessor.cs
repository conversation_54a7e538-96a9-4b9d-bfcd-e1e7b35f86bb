using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 事件批处理管理器
    /// 提供高性能的事件批处理、去重、优先级管理和异步处理功能
    /// </summary>
    public class EventBatchProcessor : IDisposable
    {
        #region 私有字段

        private static EventBatchProcessor _instance;
        private static readonly object _lockObject = new object();

        private readonly ConcurrentQueue<BatchEvent> _eventQueue;
        private readonly Dictionary<EventType, List<BatchEvent>> _eventBatches;
        private readonly Timer _batchTimer;
        private readonly object _batchLock = new object();
        
        private readonly int _batchInterval = 50; // 50ms批处理间隔
        private readonly int _maxBatchSize = 100; // 最大批处理大小
        private readonly int _maxQueueSize = 1000; // 最大队列大小
        
        private bool _isProcessing;
        private bool _isDisposed;
        private long _processedEventCount;
        private long _droppedEventCount;

        #endregion

        #region 单例模式

        public static EventBatchProcessor Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new EventBatchProcessor();
                        }
                    }
                }
                return _instance;
            }
        }

        private EventBatchProcessor()
        {
            _eventQueue = new ConcurrentQueue<BatchEvent>();
            _eventBatches = new Dictionary<EventType, List<BatchEvent>>();
            
            // 初始化批处理字典
            foreach (EventType eventType in Enum.GetValues<EventType>())
            {
                _eventBatches[eventType] = new List<BatchEvent>();
            }

            // 创建批处理定时器
            _batchTimer = new Timer(ProcessBatch, null, _batchInterval, _batchInterval);
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 队列中的事件数量
        /// </summary>
        public int QueuedEventCount => _eventQueue.Count;

        /// <summary>
        /// 已处理的事件总数
        /// </summary>
        public long ProcessedEventCount => _processedEventCount;

        /// <summary>
        /// 被丢弃的事件总数
        /// </summary>
        public long DroppedEventCount => _droppedEventCount;

        /// <summary>
        /// 是否正在处理事件
        /// </summary>
        public bool IsProcessing => _isProcessing;

        #endregion

        #region 事件

        /// <summary>
        /// 批处理完成事件
        /// </summary>
        public event EventHandler<BatchProcessedEventArgs> BatchProcessed;

        /// <summary>
        /// 事件被丢弃事件
        /// </summary>
        public event EventHandler<EventDroppedEventArgs> EventDropped;

        #endregion

        #region 公共方法

        /// <summary>
        /// 排队事件进行批处理
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="source">事件源</param>
        /// <param name="data">事件数据</param>
        /// <param name="priority">事件优先级</param>
        /// <returns>是否成功排队</returns>
        public bool QueueEvent(EventType eventType, object source, object data, EventPriority priority = EventPriority.Normal)
        {
            if (_isDisposed) return false;

            // 检查队列大小限制
            if (_eventQueue.Count >= _maxQueueSize)
            {
                Interlocked.Increment(ref _droppedEventCount);
                OnEventDropped(eventType, source, "Queue full");
                return false;
            }

            var batchEvent = new BatchEvent
            {
                EventType = eventType,
                Source = source,
                Data = data,
                Priority = priority,
                Timestamp = DateTime.Now,
                Id = Guid.NewGuid()
            };

            _eventQueue.Enqueue(batchEvent);
            return true;
        }

        /// <summary>
        /// 立即处理所有排队的事件
        /// </summary>
        public void FlushEvents()
        {
            if (_isDisposed || _isProcessing) return;

            ProcessBatch(null);
        }

        /// <summary>
        /// 设置批处理间隔
        /// </summary>
        /// <param name="intervalMs">间隔毫秒数</param>
        public void SetBatchInterval(int intervalMs)
        {
            if (intervalMs > 0 && intervalMs <= 1000)
            {
                _batchTimer?.Change(intervalMs, intervalMs);
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计</returns>
        public EventProcessorStats GetStats()
        {
            return new EventProcessorStats
            {
                QueuedEvents = _eventQueue.Count,
                ProcessedEvents = _processedEventCount,
                DroppedEvents = _droppedEventCount,
                IsProcessing = _isProcessing
            };
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理批次事件
        /// </summary>
        /// <param name="state">定时器状态</param>
        private void ProcessBatch(object state)
        {
            if (_isDisposed || _isProcessing || _eventQueue.IsEmpty) return;

            _isProcessing = true;
            var startTime = DateTime.Now;

            try
            {
                lock (_batchLock)
                {
                    // 清空批处理字典
                    foreach (var batch in _eventBatches.Values)
                    {
                        batch.Clear();
                    }

                    // 从队列中取出事件并分类
                    var processedCount = 0;
                    while (_eventQueue.TryDequeue(out var batchEvent) && processedCount < _maxBatchSize)
                    {
                        _eventBatches[batchEvent.EventType].Add(batchEvent);
                        processedCount++;
                    }

                    if (processedCount == 0) return;

                    // 按优先级处理事件
                    ProcessEventsByPriority();

                    Interlocked.Add(ref _processedEventCount, processedCount);

                    // 触发批处理完成事件
                    OnBatchProcessed(processedCount, DateTime.Now - startTime);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Event batch processing error: {ex.Message}");
            }
            finally
            {
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 按优先级处理事件
        /// </summary>
        private void ProcessEventsByPriority()
        {
            // 高优先级事件
            ProcessEventsOfPriority(EventPriority.High);
            
            // 普通优先级事件
            ProcessEventsOfPriority(EventPriority.Normal);
            
            // 低优先级事件
            ProcessEventsOfPriority(EventPriority.Low);
        }

        /// <summary>
        /// 处理指定优先级的事件
        /// </summary>
        /// <param name="priority">优先级</param>
        private void ProcessEventsOfPriority(EventPriority priority)
        {
            foreach (var eventType in Enum.GetValues<EventType>())
            {
                var events = _eventBatches[eventType]
                    .Where(e => e.Priority == priority)
                    .ToList();

                if (events.Count == 0) continue;

                try
                {
                    switch (eventType)
                    {
                        case EventType.PropertyChanged:
                            ProcessPropertyChangedEvents(events);
                            break;
                        case EventType.DimensionUpdate:
                            ProcessDimensionUpdateEvents(events);
                            break;
                        case EventType.LayerChanged:
                            ProcessLayerChangedEvents(events);
                            break;
                        case EventType.SelectionChanged:
                            ProcessSelectionChangedEvents(events);
                            break;
                        case EventType.EntityAdded:
                            ProcessEntityAddedEvents(events);
                            break;
                        case EventType.EntityRemoved:
                            ProcessEntityRemovedEvents(events);
                            break;
                        default:
                            ProcessGenericEvents(events);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error processing {eventType} events: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理属性变化事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessPropertyChangedEvents(List<BatchEvent> events)
        {
            // 按源对象分组，去重相同属性的变化
            var groupedEvents = events
                .GroupBy(e => e.Source)
                .ToList();

            foreach (var group in groupedEvents)
            {
                var latestEvents = group
                    .GroupBy(e => ((PropertyChangedEventData)e.Data).PropertyName)
                    .Select(g => g.OrderByDescending(e => e.Timestamp).First())
                    .ToList();

                // 批量处理同一对象的属性变化
                ProcessEntityPropertyChanges(group.Key, latestEvents);
            }
        }

        /// <summary>
        /// 处理标注更新事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessDimensionUpdateEvents(List<BatchEvent> events)
        {
            // 收集所有需要更新的实体
            var entitiesToUpdate = events
                .Select(e => e.Source)
                .Distinct()
                .ToList();

            // 批量更新标注
            if (entitiesToUpdate.Count > 0)
            {
                DimensionAssociationManager.Instance.BatchUpdateAssociatedDimensions(
                    entitiesToUpdate.Cast<McLaser.EditViewerSk.Base.EntityBase>());
            }
        }

        /// <summary>
        /// 处理图层变化事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessLayerChangedEvents(List<BatchEvent> events)
        {
            // 按图层分组处理
            var layerGroups = events
                .GroupBy(e => ((LayerChangedEventData)e.Data).LayerName)
                .ToList();

            foreach (var group in layerGroups)
            {
                var latestEvent = group.OrderByDescending(e => e.Timestamp).First();
                ProcessLayerChange(latestEvent);
            }
        }

        /// <summary>
        /// 处理选择变化事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessSelectionChangedEvents(List<BatchEvent> events)
        {
            // 只处理最新的选择变化事件
            var latestEvent = events.OrderByDescending(e => e.Timestamp).First();
            ProcessSelectionChange(latestEvent);
        }

        /// <summary>
        /// 处理实体添加事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessEntityAddedEvents(List<BatchEvent> events)
        {
            var entities = events.Select(e => e.Source).ToList();
            ProcessEntitiesAdded(entities);
        }

        /// <summary>
        /// 处理实体删除事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessEntityRemovedEvents(List<BatchEvent> events)
        {
            var entities = events.Select(e => e.Source).ToList();
            ProcessEntitiesRemoved(entities);
        }

        /// <summary>
        /// 处理通用事件
        /// </summary>
        /// <param name="events">事件列表</param>
        private void ProcessGenericEvents(List<BatchEvent> events)
        {
            foreach (var evt in events)
            {
                Debug.WriteLine($"Processing generic event: {evt.EventType}");
            }
        }

        #endregion

        #region 具体事件处理方法

        /// <summary>
        /// 处理实体属性变化
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <param name="events">属性变化事件列表</param>
        private void ProcessEntityPropertyChanges(object entity, List<BatchEvent> events)
        {
            try
            {
                if (entity is McLaser.EditViewerSk.Base.EntityBase entityBase)
                {
                    // 标记实体需要重新生成
                    entityBase.IsNeedToRegen = true;

                    // 触发关联更新
                    QueueEvent(EventType.DimensionUpdate, entity, null, EventPriority.Normal);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing entity property changes: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理图层变化
        /// </summary>
        /// <param name="evt">图层变化事件</param>
        private void ProcessLayerChange(BatchEvent evt)
        {
            try
            {
                var data = (LayerChangedEventData)evt.Data;
                Debug.WriteLine($"Processing layer change: {data.LayerName}");

                // 这里可以添加图层变化的具体处理逻辑
                // 例如：更新图层显示状态、重新渲染等
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing layer change: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理选择变化
        /// </summary>
        /// <param name="evt">选择变化事件</param>
        private void ProcessSelectionChange(BatchEvent evt)
        {
            try
            {
                Debug.WriteLine("Processing selection change");

                // 这里可以添加选择变化的具体处理逻辑
                // 例如：更新选择高亮、属性面板等
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing selection change: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理实体添加
        /// </summary>
        /// <param name="entities">添加的实体列表</param>
        private void ProcessEntitiesAdded(List<object> entities)
        {
            try
            {
                Debug.WriteLine($"Processing {entities.Count} entities added");

                // 这里可以添加实体添加的具体处理逻辑
                // 例如：更新空间索引、触发重新渲染等
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing entities added: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理实体删除
        /// </summary>
        /// <param name="entities">删除的实体列表</param>
        private void ProcessEntitiesRemoved(List<object> entities)
        {
            try
            {
                Debug.WriteLine($"Processing {entities.Count} entities removed");

                // 这里可以添加实体删除的具体处理逻辑
                // 例如：清理关联、更新索引等
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing entities removed: {ex.Message}");
            }
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发批处理完成事件
        /// </summary>
        /// <param name="processedCount">处理的事件数量</param>
        /// <param name="duration">处理耗时</param>
        private void OnBatchProcessed(int processedCount, TimeSpan duration)
        {
            BatchProcessed?.Invoke(this, new BatchProcessedEventArgs
            {
                ProcessedCount = processedCount,
                Duration = duration,
                Timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 触发事件丢弃事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="source">事件源</param>
        /// <param name="reason">丢弃原因</param>
        private void OnEventDropped(EventType eventType, object source, string reason)
        {
            EventDropped?.Invoke(this, new EventDroppedEventArgs
            {
                EventType = eventType,
                Source = source,
                Reason = reason,
                Timestamp = DateTime.Now
            });
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;

            try
            {
                // 停止定时器
                _batchTimer?.Dispose();

                // 处理剩余事件
                FlushEvents();

                // 清空队列
                while (_eventQueue.TryDequeue(out _)) { }

                // 清空批处理字典
                lock (_batchLock)
                {
                    foreach (var batch in _eventBatches.Values)
                    {
                        batch.Clear();
                    }
                    _eventBatches.Clear();
                }

                Debug.WriteLine("EventBatchProcessor disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error disposing EventBatchProcessor: {ex.Message}");
            }
        }

        #endregion
    }

    #region 辅助类和枚举

    /// <summary>
    /// 事件类型枚举
    /// </summary>
    public enum EventType
    {
        PropertyChanged,
        DimensionUpdate,
        LayerChanged,
        SelectionChanged,
        EntityAdded,
        EntityRemoved,
        ViewportChanged,
        DocumentChanged
    }

    /// <summary>
    /// 事件优先级枚举
    /// </summary>
    public enum EventPriority
    {
        Low = 0,
        Normal = 1,
        High = 2
    }

    /// <summary>
    /// 批处理事件
    /// </summary>
    public class BatchEvent
    {
        public Guid Id { get; set; }
        public EventType EventType { get; set; }
        public object Source { get; set; }
        public object Data { get; set; }
        public EventPriority Priority { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 属性变化事件数据
    /// </summary>
    public class PropertyChangedEventData
    {
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }

    /// <summary>
    /// 图层变化事件数据
    /// </summary>
    public class LayerChangedEventData
    {
        public string LayerName { get; set; }
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }

    /// <summary>
    /// 批处理完成事件参数
    /// </summary>
    public class BatchProcessedEventArgs : EventArgs
    {
        public int ProcessedCount { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 事件丢弃事件参数
    /// </summary>
    public class EventDroppedEventArgs : EventArgs
    {
        public EventType EventType { get; set; }
        public object Source { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 事件处理器性能统计
    /// </summary>
    public class EventProcessorStats
    {
        public int QueuedEvents { get; set; }
        public long ProcessedEvents { get; set; }
        public long DroppedEvents { get; set; }
        public bool IsProcessing { get; set; }
        public double ProcessingRate => ProcessedEvents > 0 ? (double)ProcessedEvents / (ProcessedEvents + DroppedEvents) : 0;
    }

    #endregion
}
