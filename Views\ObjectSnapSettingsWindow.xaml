<Window x:Class="McLaser.EditViewerSk.Views.ObjectSnapSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="对象捕捉设置" Height="600" Width="700" 
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="SnapModeCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="SnapModeDescriptionStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="Gray"/>
            <Setter Property="FontSize" Value="11"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Text="对象捕捉设置" Style="{StaticResource HeaderTextStyle}" Grid.Row="0"/>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,10">
            <StackPanel>
                <!-- 基本设置 -->
                <GroupBox Header="基本设置" Margin="0,10">
                    <StackPanel>
                        <CheckBox Name="EnableObjectSnapCheckBox" Content="启用对象捕捉" 
                                  Margin="5" FontWeight="Bold" IsChecked="True"/>
                        
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="捕捉容差:" Grid.Column="0" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBox Name="SnapToleranceTextBox" Width="80" Height="25" Margin="5,2" Text="10"/>
                                <TextBlock Text="像素" VerticalAlignment="Center" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <CheckBox Name="ShowSnapMarkersCheckBox" Content="显示捕捉标记" 
                                  Margin="5" IsChecked="True"/>
                        <CheckBox Name="ShowSnapTooltipsCheckBox" Content="显示捕捉工具提示" 
                                  Margin="5" IsChecked="True"/>
                        <CheckBox Name="AutoAcquireTrackingPointsCheckBox" Content="自动获取追踪点" 
                                  Margin="5" IsChecked="True"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 捕捉模式设置 -->
                <GroupBox Header="捕捉模式" Margin="0,10">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Content="全选" Grid.Column="0" Width="60" Height="25" 
                                    Margin="5" Click="SelectAll_Click"/>
                            <Button Content="全不选" Grid.Column="0" Width="60" Height="25" 
                                    Margin="70,5,5,5" Click="SelectNone_Click"/>
                            <Button Content="反选" Grid.Column="0" Width="60" Height="25" 
                                    Margin="135,5,5,5" Click="InvertSelection_Click"/>
                        </Grid>
                        
                        <Separator Margin="5"/>
                        
                        <!-- 基础捕捉模式 -->
                        <TextBlock Text="基础捕捉模式" FontWeight="Bold" Margin="5,10,5,5"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="EndSnapCheckBox" Content="端点 (END)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}" IsChecked="True"/>
                            <TextBlock Text="捕捉到直线、弧线的端点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="MidSnapCheckBox" Content="中点 (MID)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}" IsChecked="True"/>
                            <TextBlock Text="捕捉到直线、弧线的中点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="CenterSnapCheckBox" Content="中心点 (CEN)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}" IsChecked="True"/>
                            <TextBlock Text="捕捉到圆、弧的中心点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="QuadSnapCheckBox" Content="象限点 (QUA)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}"/>
                            <TextBlock Text="捕捉到圆、弧的象限点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="IntersectionSnapCheckBox" Content="交点 (INT)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}" IsChecked="True"/>
                            <TextBlock Text="捕捉到对象的交点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <!-- 高级捕捉模式 -->
                        <TextBlock Text="高级捕捉模式" FontWeight="Bold" Margin="5,15,5,5"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="PerpendicularSnapCheckBox" Content="垂足 (PER)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}"/>
                            <TextBlock Text="捕捉到对象的垂足点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="TangentSnapCheckBox" Content="切点 (TAN)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}"/>
                            <TextBlock Text="捕捉到圆、弧的切点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="NearSnapCheckBox" Content="最近点 (NEA)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}"/>
                            <TextBlock Text="捕捉到对象上的最近点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Name="GridSnapCheckBox" Content="网格点 (GRID)" Grid.Column="0" 
                                      Style="{StaticResource SnapModeCheckBoxStyle}" IsChecked="True"/>
                            <TextBlock Text="捕捉到网格交点" Grid.Column="1" 
                                       Style="{StaticResource SnapModeDescriptionStyle}"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- 视觉设置 -->
                <GroupBox Header="视觉设置" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="标记大小:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                            <TextBox Name="MarkerSizeTextBox" Width="80" Height="25" Margin="5,2" Text="8"/>
                            <TextBlock Text="像素" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                        
                        <TextBlock Text="工具提示字体大小:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="5"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                            <TextBox Name="TooltipFontSizeTextBox" Width="80" Height="25" Margin="5,2" Text="12"/>
                            <TextBlock Text="像素" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="恢复默认" Width="80" Height="35" Margin="5" Click="RestoreDefaults_Click"/>
            <Button Content="应用" Width="80" Height="35" Margin="5" Click="Apply_Click"/>
            <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click"/>
            <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
