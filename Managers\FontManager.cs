using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 字体管理器
    /// 负责管理系统字体、CAD字体文件、文本样式等
    /// </summary>
    public class FontManager : ObservableObject, IDisposable
    {
        #region 私有字段

        private ObservableCollection<FontInfo> _systemFonts;
        private ObservableCollection<FontInfo> _cadFonts;
        private ObservableCollection<TextStyleInfo> _textStyles;
        private FontInfo _currentFont;
        private TextStyleInfo _currentTextStyle;
        private DocumentBase _document;
        private readonly Dictionary<string, SKTypeface> _fontCache;
        private readonly Dictionary<string, DateTime> _fontCacheAccessTime;
        private readonly string _cadFontsPath;
        private readonly int _maxCacheSize = 100;
        private readonly TimeSpan _cacheTimeout = TimeSpan.FromMinutes(30);

        #endregion

        #region 事件

        /// <summary>
        /// 字体添加事件
        /// </summary>
        public event EventHandler<FontEventArgs> FontAdded;

        /// <summary>
        /// 文本样式添加事件
        /// </summary>
        public event EventHandler<TextStyleEventArgs> TextStyleAdded;

        /// <summary>
        /// 当前字体改变事件
        /// </summary>
        public event EventHandler<FontEventArgs> CurrentFontChanged;

        /// <summary>
        /// 当前文本样式改变事件
        /// </summary>
        public event EventHandler<TextStyleEventArgs> CurrentTextStyleChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化字体管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public FontManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _systemFonts = new ObservableCollection<FontInfo>();
            _cadFonts = new ObservableCollection<FontInfo>();
            _textStyles = new ObservableCollection<TextStyleInfo>();
            _fontCache = new Dictionary<string, SKTypeface>();
            _fontCacheAccessTime = new Dictionary<string, DateTime>();
            _cadFontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");

            LoadSystemFonts();
            LoadCADFonts();
            CreateDefaultTextStyles();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 系统字体集合
        /// </summary>
        public ObservableCollection<FontInfo> SystemFonts
        {
            get => _systemFonts;
            private set => SetProperty(ref _systemFonts, value);
        }

        /// <summary>
        /// CAD字体集合
        /// </summary>
        public ObservableCollection<FontInfo> CADFonts
        {
            get => _cadFonts;
            private set => SetProperty(ref _cadFonts, value);
        }

        /// <summary>
        /// 文本样式集合
        /// </summary>
        public ObservableCollection<TextStyleInfo> TextStyles
        {
            get => _textStyles;
            private set => SetProperty(ref _textStyles, value);
        }

        /// <summary>
        /// 当前字体
        /// </summary>
        public FontInfo CurrentFont
        {
            get => _currentFont;
            set
            {
                if (SetProperty(ref _currentFont, value))
                {
                    OnCurrentFontChanged(value);
                }
            }
        }

        /// <summary>
        /// 当前文本样式
        /// </summary>
        public TextStyleInfo CurrentTextStyle
        {
            get => _currentTextStyle;
            set
            {
                if (SetProperty(ref _currentTextStyle, value))
                {
                    OnCurrentTextStyleChanged(value);
                }
            }
        }

        /// <summary>
        /// 所有字体（系统 + CAD）
        /// </summary>
        public IEnumerable<FontInfo> AllFonts => _systemFonts.Concat(_cadFonts);

        #endregion

        #region 字体操作方法

        /// <summary>
        /// 添加CAD字体文件
        /// </summary>
        /// <param name="fontFilePath">字体文件路径</param>
        /// <returns>是否添加成功</returns>
        public bool AddCADFont(string fontFilePath)
        {
            if (!File.Exists(fontFilePath))
                return false;

            try
            {
                var fontInfo = LoadFontFromFile(fontFilePath);
                if (fontInfo != null && !FontExists(fontInfo.Name))
                {
                    _cadFonts.Add(fontInfo);
                    OnFontAdded(fontInfo);
                    return true;
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                System.Diagnostics.Debug.WriteLine($"添加CAD字体失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 创建文本样式
        /// </summary>
        /// <param name="name">样式名称</param>
        /// <param name="fontInfo">字体信息</param>
        /// <param name="fontSize">字体大小</param>
        /// <param name="isBold">是否粗体</param>
        /// <param name="isItalic">是否斜体</param>
        /// <param name="widthFactor">宽度因子</param>
        /// <param name="obliqueAngle">倾斜角度</param>
        /// <returns>创建的文本样式</returns>
        public TextStyleInfo CreateTextStyle(string name, FontInfo fontInfo, float fontSize = 12.0f, 
            bool isBold = false, bool isItalic = false, float widthFactor = 1.0f, float obliqueAngle = 0.0f)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("文本样式名称不能为空", nameof(name));

            if (TextStyleExists(name))
                throw new InvalidOperationException($"文本样式 '{name}' 已存在");

            var textStyle = new TextStyleInfo
            {
                Name = name,
                Font = fontInfo,
                FontSize = fontSize,
                IsBold = isBold,
                IsItalic = isItalic,
                WidthFactor = widthFactor,
                ObliqueAngle = obliqueAngle,
                IsCustom = true
            };

            _textStyles.Add(textStyle);
            OnTextStyleAdded(textStyle);

            return textStyle;
        }

        /// <summary>
        /// 删除文本样式
        /// </summary>
        /// <param name="textStyle">要删除的文本样式</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteTextStyle(TextStyleInfo textStyle)
        {
            if (textStyle == null || textStyle.IsDefault || !textStyle.IsCustom)
                return false;

            return _textStyles.Remove(textStyle);
        }

        /// <summary>
        /// 修改文本样式
        /// </summary>
        /// <param name="textStyle">要修改的文本样式</param>
        /// <param name="fontInfo">新字体</param>
        /// <param name="fontSize">新字体大小</param>
        /// <param name="isBold">是否粗体</param>
        /// <param name="isItalic">是否斜体</param>
        /// <param name="widthFactor">宽度因子</param>
        /// <param name="obliqueAngle">倾斜角度</param>
        /// <returns>是否修改成功</returns>
        public bool ModifyTextStyle(TextStyleInfo textStyle, FontInfo fontInfo = null, 
            float? fontSize = null, bool? isBold = null, bool? isItalic = null, 
            float? widthFactor = null, float? obliqueAngle = null)
        {
            if (textStyle == null || !textStyle.IsCustom)
                return false;

            if (fontInfo != null) textStyle.Font = fontInfo;
            if (fontSize.HasValue) textStyle.FontSize = fontSize.Value;
            if (isBold.HasValue) textStyle.IsBold = isBold.Value;
            if (isItalic.HasValue) textStyle.IsItalic = isItalic.Value;
            if (widthFactor.HasValue) textStyle.WidthFactor = widthFactor.Value;
            if (obliqueAngle.HasValue) textStyle.ObliqueAngle = obliqueAngle.Value;

            return true;
        }

        /// <summary>
        /// 获取字体的SKTypeface
        /// </summary>
        /// <param name="fontInfo">字体信息</param>
        /// <returns>SKTypeface对象</returns>
        public SKTypeface GetTypeface(FontInfo fontInfo)
        {
            if (fontInfo == null) return SKTypeface.Default;

            var key = $"{fontInfo.Name}_{fontInfo.Style}";

            // 检查缓存
            if (_fontCache.TryGetValue(key, out var cachedTypeface))
            {
                // 更新访问时间
                _fontCacheAccessTime[key] = DateTime.Now;
                return cachedTypeface;
            }

            // 清理过期缓存
            CleanupExpiredCache();

            SKTypeface typeface;
            try
            {
                if (fontInfo.IsCADFont)
                {
                    // 加载CAD字体文件
                    typeface = SKTypeface.FromFile(fontInfo.FilePath);
                }
                else
                {
                    // 使用系统字体
                    var fontStyle = SKFontStyle.Normal;
                    if (fontInfo.Style.HasFlag(FontStyle.Bold))
                        fontStyle = fontStyle.WithWeight(SKFontStyleWeight.Bold);
                    if (fontInfo.Style.HasFlag(FontStyle.Italic))
                        fontStyle = fontStyle.WithSlant(SKFontStyleSlant.Italic);

                    typeface = SKTypeface.FromFamilyName(fontInfo.Name, fontStyle);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"字体加载失败 {fontInfo.Name}: {ex.Message}");
                typeface = SKTypeface.Default;
            }

            // 添加到缓存
            _fontCache[key] = typeface ?? SKTypeface.Default;
            _fontCacheAccessTime[key] = DateTime.Now;

            return _fontCache[key];
        }

        /// <summary>
        /// 清理过期的字体缓存
        /// </summary>
        private void CleanupExpiredCache()
        {
            try
            {
                var now = DateTime.Now;
                var expiredKeys = new List<string>();

                // 查找过期的缓存项
                foreach (var kvp in _fontCacheAccessTime)
                {
                    if (now - kvp.Value > _cacheTimeout)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }

                // 移除过期项
                foreach (var key in expiredKeys)
                {
                    if (_fontCache.TryGetValue(key, out var typeface))
                    {
                        typeface?.Dispose();
                        _fontCache.Remove(key);
                    }
                    _fontCacheAccessTime.Remove(key);
                }

                // 如果缓存仍然过大，移除最旧的项
                if (_fontCache.Count > _maxCacheSize)
                {
                    var oldestKeys = _fontCacheAccessTime
                        .OrderBy(kvp => kvp.Value)
                        .Take(_fontCache.Count - _maxCacheSize)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    foreach (var key in oldestKeys)
                    {
                        if (_fontCache.TryGetValue(key, out var typeface))
                        {
                            typeface?.Dispose();
                            _fontCache.Remove(key);
                        }
                        _fontCacheAccessTime.Remove(key);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"字体缓存清理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空字体缓存
        /// </summary>
        public void ClearFontCache()
        {
            try
            {
                foreach (var typeface in _fontCache.Values)
                {
                    typeface?.Dispose();
                }
                _fontCache.Clear();
                _fontCacheAccessTime.Clear();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空字体缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测量文本尺寸
        /// </summary>
        /// <param name="text">文本内容</param>
        /// <param name="textStyle">文本样式</param>
        /// <returns>文本尺寸</returns>
        public SKSize MeasureText(string text, TextStyleInfo textStyle)
        {
            if (string.IsNullOrEmpty(text) || textStyle?.Font == null)
                return SKSize.Empty;

            using (var paint = CreateTextPaint(textStyle))
            {
                var bounds = new SKRect();
                paint.MeasureText(text, ref bounds);
                return new SKSize(bounds.Width, bounds.Height);
            }
        }

        /// <summary>
        /// 创建文本绘制画笔
        /// </summary>
        /// <param name="textStyle">文本样式</param>
        /// <returns>SKPaint对象</returns>
        public SKPaint CreateTextPaint(TextStyleInfo textStyle)
        {
            var typeface = GetTypeface(textStyle.Font);
            var paint = new SKPaint
            {
                Typeface = typeface,
                TextSize = textStyle.FontSize,
                IsAntialias = true,
                Style = SKPaintStyle.Fill
            };

            // 应用宽度因子
            if (Math.Abs(textStyle.WidthFactor - 1.0f) > 0.001f)
            {
                paint.TextScaleX = textStyle.WidthFactor;
            }

            // 应用倾斜角度
            if (Math.Abs(textStyle.ObliqueAngle) > 0.001f)
            {
                paint.TextSkewX = (float)Math.Tan(textStyle.ObliqueAngle * Math.PI / 180.0);
            }

            return paint;
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取字体
        /// </summary>
        /// <param name="name">字体名称</param>
        /// <returns>字体信息</returns>
        public FontInfo GetFont(string name)
        {
            return AllFonts.FirstOrDefault(f => string.Equals(f.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查字体是否存在
        /// </summary>
        /// <param name="name">字体名称</param>
        /// <returns>是否存在</returns>
        public bool FontExists(string name)
        {
            return AllFonts.Any(f => string.Equals(f.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取文本样式
        /// </summary>
        /// <param name="name">样式名称</param>
        /// <returns>文本样式</returns>
        public TextStyleInfo GetTextStyle(string name)
        {
            return _textStyles.FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查文本样式是否存在
        /// </summary>
        /// <param name="name">样式名称</param>
        /// <returns>是否存在</returns>
        public bool TextStyleExists(string name)
        {
            return _textStyles.Any(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取默认文本样式
        /// </summary>
        /// <returns>默认文本样式</returns>
        public TextStyleInfo GetDefaultTextStyle()
        {
            return _textStyles.FirstOrDefault(s => s.IsDefault);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载系统字体
        /// </summary>
        private void LoadSystemFonts()
        {
            try
            {
                using (var installedFonts = new InstalledFontCollection())
                {
                    foreach (var fontFamily in installedFonts.Families)
                    {
                        var fontInfo = new FontInfo
                        {
                            Name = fontFamily.Name,
                            DisplayName = fontFamily.Name,
                            IsCADFont = false,
                            Style = FontStyle.Regular,
                            Family = fontFamily.Name
                        };

                        _systemFonts.Add(fontInfo);
                    }
                }

                // 设置默认字体
                CurrentFont = _systemFonts.FirstOrDefault(f => f.Name == "Arial") 
                            ?? _systemFonts.FirstOrDefault();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载系统字体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载CAD字体
        /// </summary>
        private void LoadCADFonts()
        {
            if (!Directory.Exists(_cadFontsPath))
            {
                Directory.CreateDirectory(_cadFontsPath);
                return;
            }

            try
            {
                var fontFiles = Directory.GetFiles(_cadFontsPath, "*.ttf")
                    .Concat(Directory.GetFiles(_cadFontsPath, "*.otf"))
                    .Concat(Directory.GetFiles(_cadFontsPath, "*.shx")); // CAD形状字体

                foreach (var fontFile in fontFiles)
                {
                    var fontInfo = LoadFontFromFile(fontFile);
                    if (fontInfo != null)
                    {
                        _cadFonts.Add(fontInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载CAD字体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从文件加载字体
        /// </summary>
        /// <param name="filePath">字体文件路径</param>
        /// <returns>字体信息</returns>
        private FontInfo LoadFontFromFile(string filePath)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath).ToLower();

                var fontInfo = new FontInfo
                {
                    Name = fileName,
                    DisplayName = fileName,
                    FilePath = filePath,
                    IsCADFont = true,
                    Style = FontStyle.Regular,
                    Family = fileName
                };

                // 特殊处理SHX字体文件
                if (extension == ".shx")
                {
                    fontInfo.IsShapeFont = true;
                }

                return fontInfo;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建默认文本样式
        /// </summary>
        private void CreateDefaultTextStyles()
        {
            // 标准样式
            var standardStyle = new TextStyleInfo
            {
                Name = "Standard",
                Font = CurrentFont ?? _systemFonts.FirstOrDefault(),
                FontSize = 12.0f,
                IsBold = false,
                IsItalic = false,
                WidthFactor = 1.0f,
                ObliqueAngle = 0.0f,
                IsDefault = true,
                IsCustom = false
            };
            _textStyles.Add(standardStyle);

            // 注释样式
            var annotationStyle = new TextStyleInfo
            {
                Name = "Annotation",
                Font = CurrentFont ?? _systemFonts.FirstOrDefault(),
                FontSize = 8.0f,
                IsBold = false,
                IsItalic = false,
                WidthFactor = 1.0f,
                ObliqueAngle = 0.0f,
                IsDefault = false,
                IsCustom = false
            };
            _textStyles.Add(annotationStyle);

            // 标题样式
            var titleStyle = new TextStyleInfo
            {
                Name = "Title",
                Font = CurrentFont ?? _systemFonts.FirstOrDefault(),
                FontSize = 18.0f,
                IsBold = true,
                IsItalic = false,
                WidthFactor = 1.0f,
                ObliqueAngle = 0.0f,
                IsDefault = false,
                IsCustom = false
            };
            _textStyles.Add(titleStyle);

            CurrentTextStyle = standardStyle;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发字体添加事件
        /// </summary>
        protected virtual void OnFontAdded(FontInfo font)
        {
            FontAdded?.Invoke(this, new FontEventArgs(font));
        }

        /// <summary>
        /// 触发文本样式添加事件
        /// </summary>
        protected virtual void OnTextStyleAdded(TextStyleInfo textStyle)
        {
            TextStyleAdded?.Invoke(this, new TextStyleEventArgs(textStyle));
        }

        /// <summary>
        /// 触发当前字体改变事件
        /// </summary>
        protected virtual void OnCurrentFontChanged(FontInfo font)
        {
            CurrentFontChanged?.Invoke(this, new FontEventArgs(font));
        }

        /// <summary>
        /// 触发当前文本样式改变事件
        /// </summary>
        protected virtual void OnCurrentTextStyleChanged(TextStyleInfo textStyle)
        {
            CurrentTextStyleChanged?.Invoke(this, new TextStyleEventArgs(textStyle));
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            foreach (var typeface in _fontCache.Values)
            {
                typeface?.Dispose();
            }
            _fontCache.Clear();
        }

        #endregion
    }

    #region 辅助类和结构

    /// <summary>
    /// 字体信息
    /// </summary>
    public class FontInfo : ObservableObject
    {
        private string _name;
        private string _displayName;
        private string _filePath;
        private string _family;
        private FontStyle _style;
        private bool _isCADFont;
        private bool _isShapeFont;

        /// <summary>字体名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>显示名称</summary>
        public string DisplayName
        {
            get => _displayName;
            set => SetProperty(ref _displayName, value);
        }

        /// <summary>字体文件路径</summary>
        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        /// <summary>字体族</summary>
        public string Family
        {
            get => _family;
            set => SetProperty(ref _family, value);
        }

        /// <summary>字体样式</summary>
        public FontStyle Style
        {
            get => _style;
            set => SetProperty(ref _style, value);
        }

        /// <summary>是否为CAD字体</summary>
        public bool IsCADFont
        {
            get => _isCADFont;
            set => SetProperty(ref _isCADFont, value);
        }

        /// <summary>是否为形状字体（SHX）</summary>
        public bool IsShapeFont
        {
            get => _isShapeFont;
            set => SetProperty(ref _isShapeFont, value);
        }
    }

    /// <summary>
    /// 文本样式信息
    /// </summary>
    public class TextStyleInfo : ObservableObject
    {
        private string _name;
        private FontInfo _font;
        private float _fontSize;
        private bool _isBold;
        private bool _isItalic;
        private float _widthFactor;
        private float _obliqueAngle;
        private bool _isDefault;
        private bool _isCustom;

        /// <summary>样式名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>字体</summary>
        public FontInfo Font
        {
            get => _font;
            set => SetProperty(ref _font, value);
        }

        /// <summary>字体大小</summary>
        public float FontSize
        {
            get => _fontSize;
            set => SetProperty(ref _fontSize, value);
        }

        /// <summary>是否粗体</summary>
        public bool IsBold
        {
            get => _isBold;
            set => SetProperty(ref _isBold, value);
        }

        /// <summary>是否斜体</summary>
        public bool IsItalic
        {
            get => _isItalic;
            set => SetProperty(ref _isItalic, value);
        }

        /// <summary>宽度因子</summary>
        public float WidthFactor
        {
            get => _widthFactor;
            set => SetProperty(ref _widthFactor, value);
        }

        /// <summary>倾斜角度</summary>
        public float ObliqueAngle
        {
            get => _obliqueAngle;
            set => SetProperty(ref _obliqueAngle, value);
        }

        /// <summary>是否为默认样式</summary>
        public bool IsDefault
        {
            get => _isDefault;
            set => SetProperty(ref _isDefault, value);
        }

        /// <summary>是否为自定义样式</summary>
        public bool IsCustom
        {
            get => _isCustom;
            set => SetProperty(ref _isCustom, value);
        }
    }

    /// <summary>
    /// 字体事件参数
    /// </summary>
    public class FontEventArgs : EventArgs
    {
        public FontInfo Font { get; }

        public FontEventArgs(FontInfo font)
        {
            Font = font;
        }
    }

    /// <summary>
    /// 文本样式事件参数
    /// </summary>
    public class TextStyleEventArgs : EventArgs
    {
        public TextStyleInfo TextStyle { get; }

        public TextStyleEventArgs(TextStyleInfo textStyle)
        {
            TextStyle = textStyle;
        }
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        ClearFontCache();
    }

    #endregion
}