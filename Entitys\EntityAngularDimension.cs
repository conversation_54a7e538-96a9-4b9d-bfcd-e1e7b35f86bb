using System;
using System.ComponentModel;
using System.Numerics;
using PropertyTools;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 角度标注实体
    /// </summary>
    [Browsable(true)]
    [DisplayName("角度标注")]
    public class EntityAngularDimension : EntityDimension
    {
        private Vector2 vertex;
        private Vector2 firstPoint;
        private Vector2 secondPoint;
        private double arcRadius = 15.0;
        private bool useDegreesUnit = true;

        #region 构造函数

        public EntityAngularDimension() : base(DimensionType.Angular)
        {
            vertex = Vector2.Zero;
            firstPoint = new Vector2(10, 0);
            secondPoint = new Vector2(0, 10);
            arcRadius = 15.0;
            Update();
        }

        public EntityAngularDimension(Vector2 vertexPoint, Vector2 first, Vector2 second) : base(DimensionType.Angular)
        {
            vertex = vertexPoint;
            firstPoint = first;
            secondPoint = second;
            arcRadius = 15.0;
            Update();
        }

        public EntityAngularDimension(Vector2 vertexPoint, Vector2 first, Vector2 second, double radius) : base(DimensionType.Angular)
        {
            vertex = vertexPoint;
            firstPoint = first;
            secondPoint = second;
            arcRadius = radius;
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("顶点")]
        public Vector2 Vertex
        {
            get => vertex;
            set
            {
                if (vertex != value)
                {
                    vertex = value;
                    OnPropertyChanged(nameof(Vertex));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("第一点")]
        public Vector2 FirstPoint
        {
            get => firstPoint;
            set
            {
                if (firstPoint != value)
                {
                    firstPoint = value;
                    OnPropertyChanged(nameof(FirstPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("第二点")]
        public Vector2 SecondPoint
        {
            get => secondPoint;
            set
            {
                if (secondPoint != value)
                {
                    secondPoint = value;
                    OnPropertyChanged(nameof(SecondPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("弧半径")]
        public double ArcRadius
        {
            get => arcRadius;
            set
            {
                if (Math.Abs(arcRadius - value) > 1e-10)
                {
                    arcRadius = Math.Max(1.0, value);
                    OnPropertyChanged(nameof(ArcRadius));
                    Update();
                }
            }
        }

        [Category("显示")]
        [DisplayName("使用度数单位")]
        public bool UseDegreesUnit
        {
            get => useDegreesUnit;
            set
            {
                if (useDegreesUnit != value)
                {
                    useDegreesUnit = value;
                    OnPropertyChanged(nameof(UseDegreesUnit));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("角度值(度)")]
        [ReadOnly(true)]
        public double AngleDegrees => ActualMeasurement * 180.0 / Math.PI;

        [Category("几何")]
        [DisplayName("角度值(弧度)")]
        [ReadOnly(true)]
        public double AngleRadians => ActualMeasurement;

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            try
            {
                // 高精度角度计算
                var vector1 = firstPoint - vertex;
                var vector2 = secondPoint - vertex;

                // 检查零向量
                var length1 = vector1.Length();
                var length2 = vector2.Length();

                if (length1 < 1e-10 || length2 < 1e-10)
                {
                    ActualMeasurement = 0.0;
                    return;
                }

                // 标准化向量
                vector1 /= length1;
                vector2 /= length2;

                // 使用atan2方法计算角度，比acos更精确
                var angle1 = Math.Atan2(vector1.Y, vector1.X);
                var angle2 = Math.Atan2(vector2.Y, vector2.X);

                // 计算角度差
                var angleDiff = angle2 - angle1;

                // 标准化到[0, 2π]范围
                while (angleDiff < 0) angleDiff += 2 * Math.PI;
                while (angleDiff >= 2 * Math.PI) angleDiff -= 2 * Math.PI;

                // 根据测量方向调整角度
                if (angleDiff > Math.PI)
                {
                    angleDiff = 2 * Math.PI - angleDiff;
                }

                ActualMeasurement = angleDiff;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Angular dimension calculation error: {ex.Message}");
                ActualMeasurement = 0.0;
            }
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            // 计算角度起始和结束方向
            var direction1 = Vector2.Normalize(firstPoint - vertex);
            var direction2 = Vector2.Normalize(secondPoint - vertex);
            
            var startAngle = Math.Atan2(direction1.Y, direction1.X);
            var endAngle = Math.Atan2(direction2.Y, direction2.X);
            
            // 确保角度范围正确
            if (endAngle < startAngle)
            {
                endAngle += 2 * Math.PI;
            }

            // 创建圆弧线段
            var arcPoints = GenerateArcPoints(vertex, arcRadius, startAngle, endAngle, 16);
            for (int i = 0; i < arcPoints.Length - 1; i++)
            {
                geometry.ExtensionLines.Add(new LineGeometry(arcPoints[i], arcPoints[i + 1]));
            }

            // 添加延伸线
            if (Style.ShowExtensionLines)
            {
                var extendLength = arcRadius + Style.ExtensionLineExtend;
                var line1End = vertex + direction1 * (float)extendLength;
                var line2End = vertex + direction2 * (float)extendLength;
                
                geometry.ExtensionLines.Add(new LineGeometry(vertex, line1End));
                geometry.ExtensionLines.Add(new LineGeometry(vertex, line2End));
            }

            // 添加箭头
            if (Style.ShowArrows)
            {
                var arcStart = vertex + direction1 * (float)arcRadius;
                var arcEnd = vertex + direction2 * (float)arcRadius;
                
                // 计算圆弧上箭头的切线方向
                var tangent1 = new Vector2(-direction1.Y, direction1.X);
                var tangent2 = new Vector2(direction2.Y, -direction2.X);
                
                geometry.Arrows.Add(new ArrowGeometry(arcStart, tangent1, Style.ArrowSize));
                geometry.Arrows.Add(new ArrowGeometry(arcEnd, tangent2, Style.ArrowSize));
            }

            // 计算文本位置（角度平分线上）
            var midAngle = (startAngle + endAngle) * 0.5;
            var textDirection = new Vector2((float)Math.Cos(midAngle), (float)Math.Sin(midAngle));
            geometry.TextPosition = vertex + textDirection * (float)(arcRadius + Style.TextHeight);

            return geometry;
        }

        protected override string GetDisplayText()
        {
            var value = useDegreesUnit ? AngleDegrees : AngleRadians;
            var unit = useDegreesUnit ? "°" : "rad";
            return $"{Style.Prefix}{value.ToString($"F{Style.DecimalPlaces}")}{unit}{Style.Suffix}";
        }

        public override EntityBase Clone()
        {
            var clone = new EntityAngularDimension(vertex, firstPoint, secondPoint, arcRadius)
            {
                UseDegreesUnit = useDegreesUnit
            };
            CopyPropertiesTo(clone);
            return clone;
        }

        #endregion

        #region 私有方法

        private Vector2[] GenerateArcPoints(Vector2 center, double radius, double startAngle, double endAngle, int segments)
        {
            var points = new Vector2[segments + 1];
            var angleStep = (endAngle - startAngle) / segments;
            
            for (int i = 0; i <= segments; i++)
            {
                var angle = startAngle + i * angleStep;
                points[i] = center + new Vector2(
                    (float)(radius * Math.Cos(angle)),
                    (float)(radius * Math.Sin(angle))
                );
            }
            
            return points;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 通过三点创建角度标注
        /// </summary>
        public static EntityAngularDimension CreateFromThreePoints(Vector2 point1, Vector2 vertexPoint, Vector2 point2)
        {
            return new EntityAngularDimension(vertexPoint, point1, point2);
        }

        /// <summary>
        /// 通过两条直线创建角度标注
        /// </summary>
        public static EntityAngularDimension CreateFromTwoLines(EntityLine line1, EntityLine line2)
        {
            // 找到两线的交点作为顶点
            var intersection = FindLinesIntersection(line1, line2);
            if (intersection.HasValue)
            {
                return new EntityAngularDimension(intersection.Value, line1.EndPoint, line2.EndPoint);
            }
            
            // 如果平行，使用中点创建
            var midPoint = (line1.StartPoint + line2.StartPoint) * 0.5f;
            return new EntityAngularDimension(midPoint, line1.EndPoint, line2.EndPoint);
        }

        private static Vector2? FindLinesIntersection(EntityLine line1, EntityLine line2)
        {
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;

            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-10) return null; // 平行线

            var t = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            return line1.StartPoint + t * d1;
        }

        #endregion
    }
} 