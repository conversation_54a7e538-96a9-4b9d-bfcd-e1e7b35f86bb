﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 偏移命令
    /// 实现专业CAD级别的偏移功能，支持直线、圆弧、多段线等的精确偏移
    /// </summary>
    public class OffsetCmd : Command
    {
        private OffsetState _currentState = OffsetState.SelectEntity;
        private EntityBase _selectedEntity;
        private float _offsetDistance = 10.0f;
        private Vector2 _offsetDirection;
        private List<EntityBase> _previewEntities;
        private OffsetOptions _options;
        
        // 视觉样式
        private SKPaint _previewPaint;
        private SKPaint _directionIndicatorPaint;
        
        public override string Name => "OFFSET";
        public override string Description => "偏移选定的对象";
        
        public OffsetCmd()
        {
            _previewEntities = new List<EntityBase>();
            _options = new OffsetOptions();
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(180, 0, 255, 0), // 半透明绿色
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0),
                IsAntialias = true
            };
            
            _directionIndicatorPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Orange,
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = OffsetState.SelectEntity;
            _selectedEntity = null;
            _previewEntities.Clear();
            
            _viewer.Document.Prompt = "选择要偏移的对象：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case OffsetState.SelectEntity:
                    HandleEntitySelection(currentPoint);
                    break;
                    
                case OffsetState.SpecifyDistance:
                    HandleDistanceInput(currentPoint);
                    break;
                    
                case OffsetState.SpecifyDirection:
                    HandleDirectionSelection(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case OffsetState.SpecifyDirection:
                    UpdateOffsetPreview(currentPoint);
                    break;
                    
                case OffsetState.SpecifyDistance:
                    if (_selectedEntity != null)
                    {
                        var distance = Vector2.Distance(_offsetDirection, currentPoint);
                        _offsetDistance = distance;
                        UpdateOffsetPreview(currentPoint);
                    }
                    break;
            }
            
            _viewer.RepaintCanvas();
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == OffsetState.SpecifyDirection && _previewEntities.Count > 0)
                    {
                        CompleteOffset();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.E:
                    // 切换是否删除原始对象
                    _options.EraseSource = !_options.EraseSource;
                    _viewer.Document.Prompt = $"删除原始对象: {(_options.EraseSource ? "是" : "否")} (E切换)";
                    break;
                    
                case Keys.M:
                    // 切换多重偏移模式
                    _options.Multiple = !_options.Multiple;
                    _viewer.Document.Prompt = $"多重偏移: {(_options.Multiple ? "是" : "否")} (M切换)";
                    break;
                    
                case Keys.U:
                    // 撤销上一次偏移
                    UndoLastOffset();
                    break;
            }
        }
        
        private void HandleEntitySelection(Vector2 point)
        {
            var hitEntity = FindOffsetableEntity(point);
            
            if (hitEntity != null)
            {
                _selectedEntity = hitEntity;
                _currentState = OffsetState.SpecifyDistance;
                _viewer.Document.Prompt = $"指定偏移距离 [{_offsetDistance}] 或点击确定偏移方向：";
                
                // 启动动态输入
                if (_viewer._dynamicInputer != null)
                {
                    _viewer._dynamicInputer.StartInput(point, DynInputStatus.WaitForNumber);
                }
            }
            else
            {
                _viewer.Document.Prompt = "未找到可偏移的对象，请重新选择：";
            }
        }
        
        private void HandleDistanceInput(Vector2 point)
        {
            // 如果动态输入器有值，使用输入的距离
            if (_viewer._dynamicInputer?.GetResultNumber() != null)
            {
                _offsetDistance = (float)_viewer._dynamicInputer.GetResultNumber().Value;
                _viewer._dynamicInputer.EndInput();
            }
            
            _offsetDirection = point;
            _currentState = OffsetState.SpecifyDirection;
            _viewer.Document.Prompt = "指定偏移方向或点击要偏移的一侧：";
        }
        
        private void HandleDirectionSelection(Vector2 point)
        {
            if (_selectedEntity != null && _previewEntities.Count > 0)
            {
                CompleteOffset();
            }
        }
        
        private void UpdateOffsetPreview(Vector2 currentPoint)
        {
            _previewEntities.Clear();
            
            if (_selectedEntity == null) return;
            
            try
            {
                // 根据实体类型计算偏移方向
                var offsetSide = DetermineOffsetSide(_selectedEntity, currentPoint);
                var offsetEntities = CalculateOffset(_selectedEntity, _offsetDistance, offsetSide);
                
                _previewEntities.AddRange(offsetEntities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Offset preview error: {ex.Message}");
            }
        }
        
        private OffsetSide DetermineOffsetSide(EntityBase entity, Vector2 point)
        {
            switch (entity)
            {
                case EntityLine line:
                    return DetermineLineSide(line, point);
                    
                case EntityCircle circle:
                    return DetermineCircleSide(circle, point);
                    
                case EntityArc arc:
                    return DetermineArcSide(arc, point);
                    
                case EntityLwPolyline polyline:
                    return DeterminePolylineSide(polyline, point);
                    
                default:
                    return OffsetSide.Right;
            }
        }
        
        private OffsetSide DetermineLineSide(EntityLine line, Vector2 point)
        {
            // 使用叉积判断点在直线的哪一侧
            var lineVec = line.EndPoint - line.StartPoint;
            var pointVec = point - line.StartPoint;
            var cross = lineVec.X * pointVec.Y - lineVec.Y * pointVec.X;
            
            return cross > 0 ? OffsetSide.Left : OffsetSide.Right;
        }
        
        private OffsetSide DetermineCircleSide(EntityCircle circle, Vector2 point)
        {
            var distance = Vector2.Distance(circle.Center, point);
            return distance > circle.Radius ? OffsetSide.Outside : OffsetSide.Inside;
        }
        
        private OffsetSide DetermineArcSide(EntityArc arc, Vector2 point)
        {
            var distance = Vector2.Distance(arc.Center, point);
            return distance > arc.Radius ? OffsetSide.Outside : OffsetSide.Inside;
        }
        
        private OffsetSide DeterminePolylineSide(EntityLwPolyline polyline, Vector2 point)
        {
            // 使用射线法判断点是否在多边形内部
            bool inside = IsPointInsidePolygon(point, polyline);
            return inside ? OffsetSide.Inside : OffsetSide.Outside;
        }
        
        private bool IsPointInsidePolygon(Vector2 point, EntityLwPolyline polyline)
        {
            bool inside = false;
            var vertices = polyline.Vertexs;
            
            int j = vertices.Count - 1;
            for (int i = 0; i < vertices.Count; i++)
            {
                var vi = vertices[i].Position;
                var vj = vertices[j].Position;
                
                if (((vi.Y > point.Y) != (vj.Y > point.Y)) &&
                    (point.X < (vj.X - vi.X) * (point.Y - vi.Y) / (vj.Y - vi.Y) + vi.X))
                {
                    inside = !inside;
                }
                j = i;
            }
            
            return inside;
        }
        
        private List<EntityBase> CalculateOffset(EntityBase entity, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();
            
            try
            {
                switch (entity)
                {
                    case EntityLine line:
                        result.AddRange(OffsetLine(line, distance, side));
                        break;
                        
                    case EntityCircle circle:
                        result.AddRange(OffsetCircle(circle, distance, side));
                        break;
                        
                    case EntityArc arc:
                        result.AddRange(OffsetArc(arc, distance, side));
                        break;
                        
                    case EntityLwPolyline polyline:
                        result.AddRange(OffsetPolyline(polyline, distance, side));
                        break;
                        
                    case EntityEllipse ellipse:
                        result.AddRange(OffsetEllipse(ellipse, distance, side));
                        break;
                        
                    default:
                        // 对于不支持的实体类型，尝试通用偏移
                        result.AddRange(GenericOffset(entity, distance, side));
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Offset calculation error for {entity.GetType().Name}: {ex.Message}");
            }
            
            return result;
        }
        
        private List<EntityBase> OffsetLine(EntityLine line, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();
            
            // 计算直线的法向量
            var lineVec = Vector2.Normalize(line.EndPoint - line.StartPoint);
            var normal = new Vector2(-lineVec.Y, lineVec.X);
            
            if (side == OffsetSide.Right)
                normal = -normal;
            
            var offsetVec = normal * distance;
            
            var offsetLine = new EntityLine
            {
                StartPoint = line.StartPoint + offsetVec,
                EndPoint = line.EndPoint + offsetVec,
                LineType = line.LineType,
                LineWeight = line.LineWeight,
                Color = line.Color
            };
            
            result.Add(offsetLine);
            return result;
        }
        
        private List<EntityBase> OffsetCircle(EntityCircle circle, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();
            
            var newRadius = side == OffsetSide.Outside ? 
                circle.Radius + distance : 
                circle.Radius - distance;
            
            if (newRadius > 0)
            {
                var offsetCircle = new EntityCircle
                {
                    Center = circle.Center,
                    Radius = newRadius,
                    LineType = circle.LineType,
                    LineWeight = circle.LineWeight,
                    Color = circle.Color
                };
                
                result.Add(offsetCircle);
            }
            
            return result;
        }
        
        private List<EntityBase> OffsetArc(EntityArc arc, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();
            
            var newRadius = side == OffsetSide.Outside ? 
                arc.Radius + distance : 
                arc.Radius - distance;
            
            if (newRadius > 0)
            {
                var offsetArc = new EntityArc
                {
                    Center = arc.Center,
                    Radius = newRadius,
                    StartAngle = arc.StartAngle,
                    EndAngle = arc.EndAngle,
                    LineType = arc.LineType,
                    LineWeight = arc.LineWeight,
                    Color = arc.Color
                };
                
                result.Add(offsetArc);
            }
            
            return result;
        }
        
        private List<EntityBase> OffsetPolyline(EntityLwPolyline polyline, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();
            
            try
            {
                var offsetVertices = CalculatePolylineOffset(polyline, distance, side);
                
                if (offsetVertices.Count >= 2)
                {
                    var offsetPolyline = new EntityLwPolyline();
                    
                    foreach (var vertex in offsetVertices)
                    {
                        offsetPolyline.Vertexs.Add(new LwPolyLineVertex { Position = vertex });
                    }
                    
                    offsetPolyline.IsClosed = polyline.IsClosed;
                    offsetPolyline.LineType = polyline.LineType;
                    offsetPolyline.LineWeight = polyline.LineWeight;
                    offsetPolyline.Color = polyline.Color;
                    
                    result.Add(offsetPolyline);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Polyline offset error: {ex.Message}");
            }
            
            return result;
        }
        
        private List<Vector2> CalculatePolylineOffset(EntityLwPolyline polyline, float distance, OffsetSide side)
        {
            var vertices = polyline.Vertexs.Select(v => v.Position).ToList();
            var offsetVertices = new List<Vector2>();
            
            if (vertices.Count < 2) return offsetVertices;
            
            // 计算每个顶点的偏移
            for (int i = 0; i < vertices.Count; i++)
            {
                var offsetPoint = CalculateVertexOffset(vertices, i, distance, side, polyline.IsClosed);
                if (offsetPoint.HasValue)
                {
                    offsetVertices.Add(offsetPoint.Value);
                }
            }
            
            return offsetVertices;
        }
        
        private Vector2? CalculateVertexOffset(List<Vector2> vertices, int index, float distance, OffsetSide side, bool isClosed)
        {
            var count = vertices.Count;
            var current = vertices[index];
            
            Vector2 prevDir, nextDir;
            
            if (index == 0 && !isClosed)
            {
                // 起始点
                nextDir = Vector2.Normalize(vertices[1] - current);
                var normal = new Vector2(-nextDir.Y, nextDir.X);
                if (side == OffsetSide.Right) normal = -normal;
                return current + normal * distance;
            }
            else if (index == count - 1 && !isClosed)
            {
                // 终止点
                prevDir = Vector2.Normalize(current - vertices[index - 1]);
                var normal = new Vector2(-prevDir.Y, prevDir.X);
                if (side == OffsetSide.Right) normal = -normal;
                return current + normal * distance;
            }
            else
            {
                // 中间点或闭合多段线的点
                var prevIndex = (index - 1 + count) % count;
                var nextIndex = (index + 1) % count;
                
                prevDir = Vector2.Normalize(current - vertices[prevIndex]);
                nextDir = Vector2.Normalize(vertices[nextIndex] - current);
                
                // 计算角平分线方向
                var bisector = Vector2.Normalize(prevDir + nextDir);
                
                // 计算偏移方向
                var normal1 = new Vector2(-prevDir.Y, prevDir.X);
                var normal2 = new Vector2(-nextDir.Y, nextDir.X);
                
                if (side == OffsetSide.Right)
                {
                    normal1 = -normal1;
                    normal2 = -normal2;
                }
                
                // 计算偏移距离
                var angle = Math.Acos(Math.Max(-1, Math.Min(1, Vector2.Dot(-prevDir, nextDir))));
                var offsetDistance = distance / Math.Sin(angle / 2);
                
                // 防止过度偏移
                if (Math.Abs(offsetDistance) > distance * 10)
                {
                    offsetDistance = Math.Sign(offsetDistance) * distance * 10;
                }
                
                var offsetDir = Vector2.Dot(bisector, normal1) > 0 ? bisector : -bisector;
                return current + offsetDir * (float)offsetDistance;
            }
        }
        
        private List<EntityBase> OffsetEllipse(EntityEllipse ellipse, float distance, OffsetSide side)
        {
            var result = new List<EntityBase>();

            try
            {
                // 精确的椭圆偏移算法
                // 椭圆偏移不是简单的半径调整，需要考虑椭圆的几何特性

                var offsetDistance = side == OffsetSide.Outside ? distance : -distance;

                // 对于椭圆，偏移后的形状不再是椭圆，而是复杂曲线
                // 这里使用近似方法：将椭圆转换为多段线进行偏移
                var approximatePolyline = ConvertEllipseToPolyline(ellipse, 64); // 64个分段

                if (approximatePolyline != null)
                {
                    var offsetPolylines = OffsetPolyline(approximatePolyline, distance, side);
                    result.AddRange(offsetPolylines);
                }
                else
                {
                    // 如果转换失败，使用简化算法作为后备
                    var scaleX = ellipse.RadiusX + offsetDistance;
                    var scaleY = ellipse.RadiusY + offsetDistance;

                    if (scaleX > 0 && scaleY > 0)
                    {
                        var offsetEllipse = new EntityEllipse
                        {
                            Center = ellipse.Center,
                            RadiusX = scaleX,
                            RadiusY = scaleY,
                            Rotation = ellipse.Rotation,
                            StartAngle = ellipse.StartAngle,
                            EndAngle = ellipse.EndAngle,
                            LineType = ellipse.LineType,
                            LineWeight = ellipse.LineWeight,
                            Color = ellipse.Color
                        };

                        result.Add(offsetEllipse);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ellipse offset error: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 将椭圆转换为近似的多段线
        /// </summary>
        private EntityLwPolyline ConvertEllipseToPolyline(EntityEllipse ellipse, int segments)
        {
            try
            {
                var polyline = new EntityLwPolyline();
                var angleStep = 2 * Math.PI / segments;

                for (int i = 0; i < segments; i++)
                {
                    var angle = i * angleStep;

                    // 椭圆参数方程
                    var x = ellipse.RadiusX * Math.Cos(angle);
                    var y = ellipse.RadiusY * Math.Sin(angle);

                    // 应用旋转
                    var rotatedX = x * Math.Cos(ellipse.Rotation) - y * Math.Sin(ellipse.Rotation);
                    var rotatedY = x * Math.Sin(ellipse.Rotation) + y * Math.Cos(ellipse.Rotation);

                    // 平移到椭圆中心
                    var point = new Vector2(
                        (float)(ellipse.Center.X + rotatedX),
                        (float)(ellipse.Center.Y + rotatedY)
                    );

                    polyline.Vertexs.Add(new LwPolyLineVertex { Position = point });
                }

                polyline.IsClosed = true;
                polyline.LineType = ellipse.LineType;
                polyline.LineWeight = ellipse.LineWeight;
                polyline.Color = ellipse.Color;

                return polyline;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ellipse to polyline conversion error: {ex.Message}");
                return null;
            }
        }
        
        private List<EntityBase> GenericOffset(EntityBase entity, float distance, OffsetSide side)
        {
            // 对于不直接支持的实体类型，返回空列表
            // 可以在这里实现更通用的偏移算法
            return new List<EntityBase>();
        }
        
        private EntityBase FindOffsetableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityOffsetable(entity) && IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsEntityOffsetable(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityCircle ||
                   entity is EntityArc ||
                   entity is EntityLwPolyline ||
                   entity is EntityEllipse ||
                   entity is EntityPolyline2D;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            // 简化的距离检测，实际应用中可能需要更精确的算法
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        private void CompleteOffset()
        {
            if (_previewEntities.Count == 0) return;
            
            try
            {
                // 添加偏移后的实体到文档
                foreach (var entity in _previewEntities)
                {
                    _viewer.Document.ActiveLayer.Children.Add(entity);
                }
                
                // 如果设置了删除原始对象
                if (_options.EraseSource && _selectedEntity != null)
                {
                    _viewer.Document.ActiveLayer.Children.Remove(_selectedEntity);
                }
                
                // 创建撤销记录
                CreateUndoRecord();
                
                if (_options.Multiple)
                {
                    // 多重偏移模式，继续选择偏移方向
                    _selectedEntity = _previewEntities.FirstOrDefault();
                    _previewEntities.Clear();
                    _currentState = OffsetState.SpecifyDirection;
                    _viewer.Document.Prompt = "指定下一个偏移方向或按Escape结束：";
                }
                else
                {
                    // 完成命令
                    Finish();
                }
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"偏移操作失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Offset completion error: {ex.Message}");
            }
        }
        
        private void CreateUndoRecord()
        {
            // 这里应该创建撤销记录
            // 具体实现依赖于撤销系统的设计
        }
        
        private void UndoLastOffset()
        {
            // 撤销上一次偏移操作
            // 具体实现依赖于撤销系统
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderEntityPreview(canvas, entity);
            }
            
            // 绘制选中实体的高亮
            if (_selectedEntity != null)
            {
                RenderSelectedEntityHighlight(canvas, _selectedEntity);
            }
            
            // 绘制方向指示器
            if (_currentState == OffsetState.SpecifyDirection)
            {
                RenderDirectionIndicator(canvas);
            }
        }
        
        private void RenderEntityPreview(SKCanvas canvas, EntityBase entity)
        {
            // 根据实体类型渲染预览
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y, 
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityCircle circle:
                    canvas.DrawCircle(circle.Center.X, circle.Center.Y, circle.Radius, _previewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _previewPaint);
                    break;
                    
                case EntityLwPolyline polyline:
                    RenderPolylinePreview(canvas, polyline);
                    break;
            }
        }
        
        private void RenderPolylinePreview(SKCanvas canvas, EntityLwPolyline polyline)
        {
            if (polyline.Vertexs.Count < 2) return;
            
            using (var path = new SKPath())
            {
                var firstVertex = polyline.Vertexs[0];
                path.MoveTo(firstVertex.Position.X, firstVertex.Position.Y);
                
                for (int i = 1; i < polyline.Vertexs.Count; i++)
                {
                    var vertex = polyline.Vertexs[i];
                    path.LineTo(vertex.Position.X, vertex.Position.Y);
                }
                
                if (polyline.IsClosed)
                {
                    path.Close();
                }
                
                canvas.DrawPath(path, _previewPaint);
            }
        }
        
        private void RenderSelectedEntityHighlight(SKCanvas canvas, EntityBase entity)
        {
            var highlightPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 3.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            // 绘制高亮边框
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, 
                              bounds.Width, bounds.Height, highlightPaint);
            }
            
            highlightPaint.Dispose();
        }
        
        private void RenderDirectionIndicator(SKCanvas canvas)
        {
            // 绘制偏移距离指示器
            if (_selectedEntity != null)
            {
                var center = _selectedEntity.BoundingBox?.Center ?? Vector2.Zero;
                var radius = 20.0f;
                
                // 绘制圆形指示器
                canvas.DrawCircle(center.X, center.Y, radius, _directionIndicatorPaint);
                
                // 绘制距离文本
                var distanceText = $"D={_offsetDistance:F1}";
                var textPaint = new SKPaint
                {
                    Style = SKPaintStyle.Fill,
                    TextSize = 12,
                    Color = SKColors.Black,
                    IsAntialias = true
                };
                
                canvas.DrawText(distanceText, center.X + radius + 5, center.Y, textPaint);
                textPaint.Dispose();
            }
        }
        
        public override void Cancel()
        {
            _previewEntities.Clear();
            _selectedEntity = null;
            _currentState = OffsetState.SelectEntity;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Cancel();
        }
        
        public override void Finish()
        {
            _previewEntities.Clear();
            _selectedEntity = null;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Finish();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _previewPaint?.Dispose();
                _directionIndicatorPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    /// <summary>
    /// 偏移状态
    /// </summary>
    public enum OffsetState
    {
        SelectEntity,      // 选择实体
        SpecifyDistance,   // 指定距离
        SpecifyDirection   // 指定方向
    }
    
    /// <summary>
    /// 偏移方向
    /// </summary>
    public enum OffsetSide
    {
        Left,      // 左侧
        Right,     // 右侧
        Inside,    // 内侧（用于圆、椭圆等封闭图形）
        Outside    // 外侧（用于圆、椭圆等封闭图形）
    }
    
    /// <summary>
    /// 偏移选项
    /// </summary>
    public class OffsetOptions
    {
        /// <summary>
        /// 是否删除原始对象
        /// </summary>
        public bool EraseSource { get; set; } = false;
        
        /// <summary>
        /// 多重偏移模式
        /// </summary>
        public bool Multiple { get; set; } = false;
        
        /// <summary>
        /// 偏移图层
        /// </summary>
        public string Layer { get; set; } = "0";
        
        /// <summary>
        /// 偏移距离精度
        /// </summary>
        public float Precision { get; set; } = 0.001f;
        
        /// <summary>
        /// 是否显示预览
        /// </summary>
        public bool ShowPreview { get; set; } = true;
    }
}
