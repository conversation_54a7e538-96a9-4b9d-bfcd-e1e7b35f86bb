﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class MirrorCmd : ModifyCmd
    {
        /// <summary>
        /// 源图元
        /// </summary>
        private List<EntityBase> _entities = new List<EntityBase>();

        /// <summary>
        /// 结果图元
        /// </summary>
        private List<EntityBase> _resultEntities = new List<EntityBase>();

        /// <summary>
        /// 源图元是否被删除
        /// </summary>
        private bool _isSrcDeleted = false;

        /// <summary>
        /// 镜像线
        /// </summary>
        private EntityLine _mirrorLine = null;

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            // 选择对象
            Step1_SelectObject = 1,
            // 指定镜像线第一点
            Step2_SpecifyMirrorLinePoint1st = 2,
            // 指定镜像线第二点
            Step3_SpecifyMirrorLinePoint2nd = 3,
            // 是否删除源对象
            Step4_WhetherDelSrc = 4,
        }
        private Step _step = Step.Step1_SelectObject;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            // 检查是否已有选中的对象
            if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
            {
                _entities.AddRange(doc.SelectedEntities);
            }
            else if (this._viewer.Selections != null && this._viewer.Selections.Count > 0)
            {
                foreach (EntityBase sel in this._viewer.Selections)
                {
                    if (sel != null)
                    {
                        _entities.Add(sel);
                    }
                }
            }

            if (_entities.Count > 0)
            {
                this.pointer.Mode = IndicatorMode.Locate;
                _step = Step.Step2_SpecifyMirrorLinePoint1st;
                this.pointer.Document.Prompt = "指定镜像线的第一点:";
            }
            else
            {
                if (this._viewer.Selections != null)
                {
                    this._viewer.Selections.Clear();
                }
                _step = Step.Step1_SelectObject;
                this.pointer.Mode = IndicatorMode.Select;
                this.pointer.Document.Prompt = "选择要镜像的对象:";
            }
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            if (_resultEntities != null && _resultEntities.Count > 0)
            {
                foreach (EntityBase item in _resultEntities)
                {
                    if (item != null)
                    {
                        doc.Action.ActEntityAdd(item);
                    }
                }
            }

            // 如果需要删除源对象
            if (_isSrcDeleted && _entities != null && _entities.Count > 0)
            {
                doc.Action.ActEntityDelete(_entities);
            }
        }

        /// <summary>
        /// 回滚撤销
        /// </summary>
        protected override void Rollback()
        {
            if (_resultEntities != null && _resultEntities.Count > 0)
            {
                foreach (EntityBase item in _resultEntities)
                {
                    // 镜像的实体需要从文档中移除
                    // 这里暂时不实现，因为需要更复杂的撤销机制
                }
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SelectObject:
                    break;

                case Step.Step2_SpecifyMirrorLinePoint1st:
                    if (e.Button == MouseButtons.Left)
                    {
                        _mirrorLine = new EntityLine();
                        _mirrorLine.StartPoint = this.pointer.CurrentSnapPoint;
                        _mirrorLine.EndPoint = _mirrorLine.StartPoint;

                        _step = Step.Step3_SpecifyMirrorLinePoint2nd;
                        this.pointer.Document.Prompt = "指定镜像线的第二点:";
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;

                case Step.Step3_SpecifyMirrorLinePoint2nd:
                    if (e.Button == MouseButtons.Left)
                    {
                        _mirrorLine.EndPoint = this.pointer.CurrentSnapPoint;
                        this.UpdateResultEntities();

                        _step = Step.Step4_WhetherDelSrc;
                        this.pointer.Document.Prompt = "是否删除源对象? (Y)是 (N)否 或 Enter确认:";
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;

                case Step.Step4_WhetherDelSrc:
                    if (e.Button == MouseButtons.Left)
                    {
                        // 左键默认不删除源对象
                        _isSrcDeleted = false;
                        _mgr.FinishCurrentCommand();
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;

                default:
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SelectObject:
                    if (e.Button == MouseButtons.Right)
                    {
                        if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
                        {
                            _entities.Clear();
                            _entities.AddRange(doc.SelectedEntities);

                            this.pointer.Mode = IndicatorMode.Locate;
                            _step = Step.Step2_SpecifyMirrorLinePoint1st;
                            this.pointer.Document.Prompt = "指定镜像线的第一点:";
                        }
                        else
                        {
                            _mgr.CancelCurrentCommand();
                        }
                    }
                    break;

                case Step.Step2_SpecifyMirrorLinePoint1st:
                    break;

                case Step.Step3_SpecifyMirrorLinePoint2nd:
                    break;

                case Step.Step4_WhetherDelSrc:
                    break;

                default:
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step3_SpecifyMirrorLinePoint2nd)
            {
                _mirrorLine.EndPoint = this.pointer.CurrentSnapPoint;
                this.UpdateResultEntities();
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            if (_step == Step.Step4_WhetherDelSrc)
            {
                switch (e.KeyCode)
                {
                    case Keys.Y:
                        // Y键：删除源对象
                        _isSrcDeleted = true;
                        this.pointer.Document.Prompt = "将删除源对象并创建镜像";
                        _mgr.FinishCurrentCommand();
                        return EventResult.Handled;

                    case Keys.N:
                        // N键：保留源对象
                        _isSrcDeleted = false;
                        this.pointer.Document.Prompt = "将保留源对象并创建镜像";
                        _mgr.FinishCurrentCommand();
                        return EventResult.Handled;

                    case Keys.Enter:
                        // Enter键：默认保留源对象
                        _isSrcDeleted = false;
                        _mgr.FinishCurrentCommand();
                        return EventResult.Handled;
                }
            }

            return EventResult.Unhandled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            //if (_step == Step.Step3_SpecifyMirrorLinePoint2nd)
            //{
            //    this.presenter.DrawEntity(g, _mirrorLine);
            //}

            //if (_step == Step.Step3_SpecifyMirrorLinePoint2nd
            //    || _step == Step.Step4_WhetherDelSrc)
            //{
            //    foreach (Entity entity in _resultEntities)
            //    {
            //        this.presenter.DrawEntity(g, entity);
            //    }
            //}
        }

        /// <summary>
        /// 刷新结果图元
        /// </summary>
        private void UpdateResultEntities()
        {
            if (_mirrorLine == null || _entities == null || _entities.Count == 0)
                return;

            _resultEntities.Clear();

            // 计算镜像变换
            var mirrorVector = _mirrorLine.EndPoint - _mirrorLine.StartPoint;
            if (mirrorVector.LengthSquared() < 0.0001f) // 避免除零
                return;

            foreach (EntityBase entity in _entities)
            {
                if (entity != null)
                {
                    EntityBase copy = entity.Clone();
                    if (copy != null)
                    {
                        // 执行镜像变换
                        MirrorEntity(copy, _mirrorLine.StartPoint, _mirrorLine.EndPoint);
                        _resultEntities.Add(copy);
                    }
                }
            }
        }

        /// <summary>
        /// 对实体执行镜像变换
        /// </summary>
        private void MirrorEntity(EntityBase entity, System.Numerics.Vector2 lineStart, System.Numerics.Vector2 lineEnd)
        {
            // 简化的镜像实现 - 针对直线的镜像
            var lineVector = lineEnd - lineStart;
            var lineLength = lineVector.Length();

            if (lineLength < 0.0001f) return;

            var unitVector = lineVector / lineLength;
            var normalVector = new System.Numerics.Vector2(-unitVector.Y, unitVector.X);

            // 对于不同类型的实体，需要不同的镜像处理
            // 这里提供基本的点镜像功能
            if (entity is EntityLine line)
            {
                line.StartPoint = MirrorPoint(line.StartPoint, lineStart, normalVector);
                line.EndPoint = MirrorPoint(line.EndPoint, lineStart, normalVector);
            }
            else if (entity is EntityCircle circle)
            {
                circle.Center = MirrorPoint(circle.Center, lineStart, normalVector);
            }
            // 可以扩展更多实体类型的镜像处理
        }

        /// <summary>
        /// 镜像一个点
        /// </summary>
        private System.Numerics.Vector2 MirrorPoint(System.Numerics.Vector2 point, System.Numerics.Vector2 linePoint, System.Numerics.Vector2 normal)
        {
            var toPoint = point - linePoint;
            var distance = System.Numerics.Vector2.Dot(toPoint, normal);
            return point - 2 * distance * normal;
        }
    }
}
