# 标注系统使用指南

## 概述

标注系统提供了专业的CAD标注功能，包括线性、对齐、半径、直径、角度和引线标注。系统支持样式管理、关联更新和高质量的图形渲染。

## 功能特性

### 标注类型
- **线性标注**: 水平、垂直和旋转标注
- **对齐标注**: 平行于测量对象的标注
- **半径标注**: 圆和圆弧的半径标注
- **直径标注**: 圆和圆弧的直径标注
- **角度标注**: 两条线之间的角度标注
- **引线标注**: 带注释文本的引线

### 标注样式
- 文本高度、字体和颜色
- 箭头大小和样式
- 延伸线偏移和延伸长度
- 标注线间距
- 精度和单位设置

### 关联更新
- 自动关联到几何实体
- 实体修改时自动更新标注
- 关联验证和管理

## 使用示例

### 1. 创建线性标注

```csharp
// 基本线性标注
var linear = new EntityLinearDimension(
    new Vector2(0, 0),      // 第一点
    new Vector2(10, 0),     // 第二点
    new Vector2(5, 5)       // 标注线位置
);

// 水平标注
var horizontal = EntityLinearDimension.CreateHorizontal(
    new Vector2(0, 0), 
    new Vector2(10, 5), 
    3.0  // Y偏移
);

// 垂直标注
var vertical = EntityLinearDimension.CreateVertical(
    new Vector2(0, 0), 
    new Vector2(5, 10), 
    3.0  // X偏移
);
```

### 2. 创建对齐标注

```csharp
// 对齐标注（平行于两点连线）
var aligned = new EntityAlignedDimension(
    new Vector2(0, 0),      // 第一点
    new Vector2(3, 4),      // 第二点
    2.0                     // 偏移距离
);

// 从现有直线创建
var line = new EntityLine(new Vector2(0, 0), new Vector2(10, 5));
var alignedFromLine = EntityAlignedDimension.CreateFromLine(line, 3.0);
```

### 3. 创建半径标注

```csharp
// 基本半径标注
var radius = new EntityRadiusDimension(
    new Vector2(0, 0),      // 圆心
    10.0,                   // 半径
    new Vector2(10, 0)      // 引线终点
);

// 从圆形实体创建
var circle = new EntityCircle(new Vector2(0, 0), 10.0);
var radiusFromCircle = EntityRadiusDimension.CreateFromCircle(
    circle, 
    new Vector2(10, 0)
);
```

### 4. 创建直径标注

```csharp
// 基本直径标注
var diameter = new EntityDiameterDimension(
    new Vector2(0, 0),      // 圆心
    20.0,                   // 直径
    new Vector2(10, 0)      // 引线终点
);

// 设置是否显示中心标记
diameter.ShowCenterMark = true;
```

### 5. 创建角度标注

```csharp
// 通过三点创建
var angular = EntityAngularDimension.CreateFromThreePoints(
    new Vector2(10, 0),     // 第一条线上的点
    new Vector2(0, 0),      // 顶点
    new Vector2(0, 10)      // 第二条线上的点
);

// 设置角度单位
angular.UseDegreesUnit = true; // 使用度数，默认为true

// 调整弧线半径
angular.ArcRadius = 15.0;
```

### 6. 创建引线标注

```csharp
// 简单引线（两点）
var leader = EntityLeaderDimension.CreateSimple(
    new Vector2(0, 0),      // 起点
    new Vector2(10, 5),     // 终点
    "注释文本"
);

// 带肘部的引线
var leaderWithElbow = EntityLeaderDimension.CreateWithElbow(
    new Vector2(0, 0),      // 起点
    new Vector2(5, 5),      // 肘部点
    new Vector2(10, 5),     // 终点
    "带肘部的注释"
);

// 多点引线
var points = new List<Vector2> 
{
    new Vector2(0, 0),
    new Vector2(5, 5),
    new Vector2(10, 5),
    new Vector2(15, 10)
};
var multiPointLeader = new EntityLeaderDimension(points, "多点引线");

// 设置样条线模式
multiPointLeader.UseSpline = true;
```

## 标注样式管理

### 获取样式管理器

```csharp
var styleManager = DimensionStyleManager.Instance;
```

### 使用预定义样式

```csharp
// 标准样式
var standard = styleManager.FindStyle("Standard");

// ISO样式
var iso = styleManager.FindStyle("ISO");

// 机械制图样式
var mechanical = styleManager.FindStyle("Mechanical");

// 设置当前样式
styleManager.CurrentStyle = mechanical;
```

### 创建自定义样式

```csharp
var customStyle = new DimensionStyle
{
    Name = "我的样式",
    TextHeight = 3.0,
    ArrowSize = 3.0,
    ExtensionLineOffset = 1.0,
    ExtensionLineExtend = 1.5,
    DimensionLineSpacing = 5.0,
    TextColor = Colors.Blue,
    LineColor = Colors.Red,
    TextFont = "Arial",
    DecimalPlaces = 2,
    Prefix = "L=",
    Suffix = "mm",
    ShowExtensionLines = true,
    ShowArrows = true
};

styleManager.AddStyle(customStyle);
```

### 应用样式到标注

```csharp
var dimension = new EntityLinearDimension(
    new Vector2(0, 0), 
    new Vector2(10, 0), 
    new Vector2(5, 5)
);

// 设置样式
dimension.Style = customStyle;

// 或使用当前默认样式
dimension.Style = styleManager.CurrentStyle;
```

## 关联更新系统

### 创建关联

```csharp
var associationManager = DimensionAssociationManager.Instance;

// 手动创建关联
var line = new EntityLine(new Vector2(0, 0), new Vector2(10, 0));
var dimension = new EntityLinearDimension(
    line.StartPoint, 
    line.EndPoint, 
    new Vector2(5, 5)
);

associationManager.CreateAssociation(dimension, line);
```

### 自动创建关联

```csharp
// 在创建标注时自动寻找关联实体
var candidates = document.GetAllEntities();
associationManager.AutoCreateAssociations(dimension, candidates, 1.0);
```

### 关联更新

```csharp
// 当实体属性改变时，关联的标注会自动更新
line.EndPoint = new Vector2(20, 0); // 标注会自动更新测量值

// 手动触发更新
associationManager.UpdateAssociatedDimensions(line);
```

## 标注命令使用

### 在命令系统中使用

```csharp
// 创建标注命令
var linearCmd = new LinearDimensionCmd(view);
var alignedCmd = new AlignedDimensionCmd(view);
var radiusCmd = new RadiusDimensionCmd(view);
var diameterCmd = new DiameterDimensionCmd(view);
var angularCmd = new AngularDimensionCmd(view);
var leaderCmd = new LeaderDimensionCmd(view);

// 激活命令
commandManager.SetActiveCommand(linearCmd);
```

### 命令交互流程

1. **线性标注命令**:
   - 指定第一点 → 指定第二点 → 指定标注线位置
   - 支持 H（水平）、V（垂直）约束

2. **对齐标注命令**:
   - 指定第一点 → 指定第二点 → 指定标注线位置

3. **半径/直径标注命令**:
   - 选择圆或圆弧 → 指定引线终点位置

4. **角度标注命令**:
   - 选择第一条线 → 选择第二条线 → 指定弧线位置

5. **引线标注命令**:
   - 指定起点 → 添加中间点 → Enter结束 → 输入注释文本

## 渲染集成

标注系统完全集成到图形渲染系统中：

```csharp
// 标注实体实现IRenderable接口
public void Render(IGraphicsRenderer renderer, CoordinateSpace coordinateSpace)
{
    // 自动渲染标注的所有组件
    var geometry = GetDimensionGeometry();
    RenderDimensionGeometry(renderer, geometry, paint, coordinateSpace);
}
```

## 性能优化

### 大量标注处理

```csharp
// 批量创建标注时禁用自动更新
associationManager.DisableAutoUpdate();

for (int i = 0; i < 1000; i++)
{
    var dimension = CreateDimension(i);
    document.AddEntity(dimension);
}

associationManager.EnableAutoUpdate();
associationManager.UpdateAllAssociations();
```

### 渲染优化

```csharp
// 使用视口裁剪
public void Render(IGraphicsRenderer renderer, CoordinateSpace coordinateSpace)
{
    if (!IsInViewport(renderer.ViewBounds)) return;
    
    // 渲染标注
    base.Render(renderer, coordinateSpace);
}
```

## 样式保存和加载

```csharp
// 保存样式到文件
styleManager.SaveStyles();

// 导出特定样式
var stylesToExport = new[] { customStyle, iso };
styleManager.ExportStyles("my_styles.json", stylesToExport);

// 导入样式
styleManager.ImportStyles("external_styles.json", false);

// 重置为默认样式
styleManager.ResetToDefaults();
```

## 最佳实践

1. **样式一致性**: 为不同的图纸类型创建标准样式
2. **关联管理**: 使用关联更新保持标注与几何的同步
3. **性能考虑**: 对于大量标注使用批量操作
4. **用户体验**: 提供直观的标注命令交互
5. **数据完整性**: 定期验证标注关联的有效性

## 故障排除

### 常见问题

1. **标注不显示**: 检查样式设置和渲染顺序
2. **关联不更新**: 验证关联是否正确创建
3. **性能问题**: 使用视口裁剪和批量操作
4. **文本显示异常**: 检查字体设置和文本渲染

### 调试方法

```csharp
// 验证标注几何
var geometry = dimension.GetDimensionGeometry();
Debug.WriteLine($"Dimension line: {geometry.DimensionLine}");
Debug.WriteLine($"Extension lines: {geometry.ExtensionLines.Count}");

// 检查关联
var associations = associationManager.GetAssociatedEntities(dimension);
Debug.WriteLine($"Associated entities: {associations.Count()}");

// 验证样式
Debug.WriteLine($"Style: {dimension.Style.Name}");
Debug.WriteLine($"Text height: {dimension.Style.TextHeight}");
```

通过这个全面的标注系统，您可以实现专业级的CAD标注功能，满足工程制图的各种需求。 