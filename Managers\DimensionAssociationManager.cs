using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 标注关联更新管理器
    /// </summary>
    public class DimensionAssociationManager : ObservableObject
    {
        private static DimensionAssociationManager instance;
        private readonly Dictionary<EntityBase, List<EntityDimension>> entityToDimensions = new Dictionary<EntityBase, List<EntityDimension>>();
        private readonly Dictionary<EntityDimension, List<EntityBase>> dimensionToEntities = new Dictionary<EntityDimension, List<EntityBase>>();
        private bool isUpdatingAssociations = false;
        private bool isAutoUpdateEnabled = true;

        #region 单例模式

        public static DimensionAssociationManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new DimensionAssociationManager();
                }
                return instance;
            }
        }

        private DimensionAssociationManager()
        {
        }

        #endregion

        #region 事件

        public event EventHandler<AssociationEventArgs> AssociationCreated;
        public event EventHandler<AssociationEventArgs> AssociationRemoved;
        public event EventHandler<DimensionUpdateEventArgs> DimensionUpdated;

        #endregion

        #region 公共方法

        /// <summary>
        /// 创建关联关系
        /// </summary>
        public void CreateAssociation(EntityDimension dimension, EntityBase entity)
        {
            if (dimension == null || entity == null || dimension == entity) return;

            // 添加实体到标注的关联
            if (!dimensionToEntities.ContainsKey(dimension))
            {
                dimensionToEntities[dimension] = new List<EntityBase>();
            }
            
            if (!dimensionToEntities[dimension].Contains(entity))
            {
                dimensionToEntities[dimension].Add(entity);
                entity.PropertyChanged += OnEntityPropertyChanged;
            }

            // 添加标注到实体的关联
            if (!entityToDimensions.ContainsKey(entity))
            {
                entityToDimensions[entity] = new List<EntityDimension>();
            }
            
            if (!entityToDimensions[entity].Contains(dimension))
            {
                entityToDimensions[entity].Add(dimension);
            }

            // 添加到标注的关联实体列表
            dimension.AddAssociatedEntity(entity);

            AssociationCreated?.Invoke(this, new AssociationEventArgs(dimension, entity));
        }

        /// <summary>
        /// 移除关联关系
        /// </summary>
        public void RemoveAssociation(EntityDimension dimension, EntityBase entity)
        {
            if (dimension == null || entity == null) return;

            // 从标注到实体的关联中移除
            if (dimensionToEntities.ContainsKey(dimension))
            {
                if (dimensionToEntities[dimension].Remove(entity))
                {
                    entity.PropertyChanged -= OnEntityPropertyChanged;
                }
                
                if (dimensionToEntities[dimension].Count == 0)
                {
                    dimensionToEntities.Remove(dimension);
                }
            }

            // 从实体到标注的关联中移除
            if (entityToDimensions.ContainsKey(entity))
            {
                entityToDimensions[entity].Remove(dimension);
                
                if (entityToDimensions[entity].Count == 0)
                {
                    entityToDimensions.Remove(entity);
                }
            }

            // 从标注的关联实体列表中移除
            dimension.RemoveAssociatedEntity(entity);

            AssociationRemoved?.Invoke(this, new AssociationEventArgs(dimension, entity));
        }

        /// <summary>
        /// 移除所有与指定标注相关的关联
        /// </summary>
        public void RemoveAllAssociations(EntityDimension dimension)
        {
            if (dimension == null || !dimensionToEntities.ContainsKey(dimension)) return;

            var entities = dimensionToEntities[dimension].ToList();
            foreach (var entity in entities)
            {
                RemoveAssociation(dimension, entity);
            }
        }

        /// <summary>
        /// 移除所有与指定实体相关的关联
        /// </summary>
        public void RemoveAllAssociations(EntityBase entity)
        {
            if (entity == null || !entityToDimensions.ContainsKey(entity)) return;

            var dimensions = entityToDimensions[entity].ToList();
            foreach (var dimension in dimensions)
            {
                RemoveAssociation(dimension, entity);
            }
        }

        /// <summary>
        /// 获取与实体关联的所有标注
        /// </summary>
        public IEnumerable<EntityDimension> GetAssociatedDimensions(EntityBase entity)
        {
            if (entity != null && entityToDimensions.ContainsKey(entity))
            {
                return entityToDimensions[entity].ToList();
            }
            return Enumerable.Empty<EntityDimension>();
        }

        /// <summary>
        /// 获取标注关联的所有实体
        /// </summary>
        public IEnumerable<EntityBase> GetAssociatedEntities(EntityDimension dimension)
        {
            if (dimension != null && dimensionToEntities.ContainsKey(dimension))
            {
                return dimensionToEntities[dimension].ToList();
            }
            return Enumerable.Empty<EntityBase>();
        }

        /// <summary>
        /// 自动创建标注关联
        /// </summary>
        public void AutoCreateAssociations(EntityDimension dimension, IEnumerable<EntityBase> candidates, double tolerance = 1.0)
        {
            if (dimension == null || candidates == null) return;

            switch (dimension)
            {
                case EntityLinearDimension linear:
                    AutoCreateLinearAssociations(linear, candidates, tolerance);
                    break;
                case EntityAlignedDimension aligned:
                    AutoCreateAlignedAssociations(aligned, candidates, tolerance);
                    break;
                case EntityRadiusDimension radius:
                    AutoCreateRadiusAssociations(radius, candidates, tolerance);
                    break;
                case EntityDiameterDimension diameter:
                    AutoCreateDiameterAssociations(diameter, candidates, tolerance);
                    break;
                case EntityAngularDimension angular:
                    AutoCreateAngularAssociations(angular, candidates, tolerance);
                    break;
            }
        }

        /// <summary>
        /// 更新所有关联的标注
        /// </summary>
        public void UpdateAssociatedDimensions(EntityBase entity)
        {
            if (isUpdatingAssociations || entity == null || !entityToDimensions.ContainsKey(entity)) return;

            isUpdatingAssociations = true;
            try
            {
                var dimensions = entityToDimensions[entity].ToList();
                foreach (var dimension in dimensions)
                {
                    UpdateDimensionFromAssociatedEntity(dimension, entity);
                }
            }
            finally
            {
                isUpdatingAssociations = false;
            }
        }

        /// <summary>
        /// 批量更新多个实体的关联标注（性能优化版本）
        /// </summary>
        public void BatchUpdateAssociatedDimensions(IEnumerable<EntityBase> entities)
        {
            if (isUpdatingAssociations || entities == null) return;

            isUpdatingAssociations = true;
            try
            {
                // 收集所有需要更新的标注，避免重复更新
                var dimensionsToUpdate = new HashSet<EntityDimension>();

                foreach (var entity in entities)
                {
                    if (entityToDimensions.ContainsKey(entity))
                    {
                        foreach (var dimension in entityToDimensions[entity])
                        {
                            dimensionsToUpdate.Add(dimension);
                        }
                    }
                }

                // 批量更新标注
                foreach (var dimension in dimensionsToUpdate)
                {
                    dimension.UpdateMeasurement();
                }
            }
            finally
            {
                isUpdatingAssociations = false;
            }
        }

        /// <summary>
        /// 禁用自动更新（用于批量操作）
        /// </summary>
        public void DisableAutoUpdate()
        {
            isAutoUpdateEnabled = false;
        }

        /// <summary>
        /// 启用自动更新
        /// </summary>
        public void EnableAutoUpdate()
        {
            isAutoUpdateEnabled = true;
        }

        /// <summary>
        /// 更新所有关联
        /// </summary>
        public void UpdateAllAssociations()
        {
            if (isUpdatingAssociations) return;

            isUpdatingAssociations = true;
            try
            {
                var allDimensions = dimensionToEntities.Keys.ToList();
                foreach (var dimension in allDimensions)
                {
                    dimension.UpdateMeasurement();
                }
            }
            finally
            {
                isUpdatingAssociations = false;
            }
        }

        /// <summary>
        /// 检查关联的有效性
        /// </summary>
        public bool ValidateAssociations(EntityDimension dimension)
        {
            if (dimension == null) return true;

            var entities = GetAssociatedEntities(dimension).ToList();
            bool isValid = true;

            foreach (var entity in entities)
            {
                if (!IsAssociationValid(dimension, entity))
                {
                    RemoveAssociation(dimension, entity);
                    isValid = false;
                }
            }

            return isValid;
        }

        #endregion

        #region 私有方法

        private void OnEntityPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (isUpdatingAssociations || !isAutoUpdateEnabled || !(sender is EntityBase entity)) return;

            // 只在关键属性改变时更新
            if (IsRelevantProperty(e.PropertyName))
            {
                UpdateAssociatedDimensions(entity);
            }
        }

        private bool IsRelevantProperty(string propertyName)
        {
            var relevantProperties = new[]
            {
                nameof(EntityLine.StartPoint),
                nameof(EntityLine.EndPoint),
                nameof(EntityCircle.Center),
                nameof(EntityCircle.Radius),
                nameof(EntityArc.Center),
                nameof(EntityArc.Radius),
                nameof(EntityArc.StartAngle),
                nameof(EntityArc.EndAngle),
                nameof(EntityRectangle.Location),
                nameof(EntityRectangle.Width),
                nameof(EntityRectangle.Height)
            };

            return relevantProperties.Contains(propertyName);
        }

        private void UpdateDimensionFromAssociatedEntity(EntityDimension dimension, EntityBase entity)
        {
            try
            {
                switch (dimension)
                {
                    case EntityLinearDimension linear when entity is EntityLine line:
                        UpdateLinearFromLine(linear, line);
                        break;
                    case EntityAlignedDimension aligned when entity is EntityLine line:
                        UpdateAlignedFromLine(aligned, line);
                        break;
                    case EntityRadiusDimension radius when entity is EntityCircle circle:
                        UpdateRadiusFromCircle(radius, circle);
                        break;
                    case EntityRadiusDimension radius when entity is EntityArc arc:
                        UpdateRadiusFromArc(radius, arc);
                        break;
                    case EntityDiameterDimension diameter when entity is EntityCircle circle:
                        UpdateDiameterFromCircle(diameter, circle);
                        break;
                    case EntityDiameterDimension diameter when entity is EntityArc arc:
                        UpdateDiameterFromArc(diameter, arc);
                        break;
                }

                dimension.Update();
                DimensionUpdated?.Invoke(this, new DimensionUpdateEventArgs(dimension, entity));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新标注失败: {ex.Message}");
            }
        }

        private void UpdateLinearFromLine(EntityLinearDimension linear, EntityLine line)
        {
            linear.FirstPoint = line.StartPoint;
            linear.SecondPoint = line.EndPoint;
        }

        private void UpdateAlignedFromLine(EntityAlignedDimension aligned, EntityLine line)
        {
            aligned.FirstPoint = line.StartPoint;
            aligned.SecondPoint = line.EndPoint;
        }

        private void UpdateRadiusFromCircle(EntityRadiusDimension radius, EntityCircle circle)
        {
            radius.Center = circle.Center;
            radius.Radius = circle.Radius;
        }

        private void UpdateRadiusFromArc(EntityRadiusDimension radius, EntityArc arc)
        {
            radius.Center = arc.Center;
            radius.Radius = arc.Radius;
        }

        private void UpdateDiameterFromCircle(EntityDiameterDimension diameter, EntityCircle circle)
        {
            diameter.Center = circle.Center;
            diameter.Diameter = circle.Radius * 2;
        }

        private void UpdateDiameterFromArc(EntityDiameterDimension diameter, EntityArc arc)
        {
            diameter.Center = arc.Center;
            diameter.Diameter = arc.Radius * 2;
        }

        private void AutoCreateLinearAssociations(EntityLinearDimension linear, IEnumerable<EntityBase> candidates, double tolerance)
        {
            foreach (var candidate in candidates.OfType<EntityLine>())
            {
                if (IsPointNear(linear.FirstPoint, candidate.StartPoint, tolerance) &&
                    IsPointNear(linear.SecondPoint, candidate.EndPoint, tolerance))
                {
                    CreateAssociation(linear, candidate);
                    break;
                }
            }
        }

        private void AutoCreateAlignedAssociations(EntityAlignedDimension aligned, IEnumerable<EntityBase> candidates, double tolerance)
        {
            foreach (var candidate in candidates.OfType<EntityLine>())
            {
                if (IsPointNear(aligned.FirstPoint, candidate.StartPoint, tolerance) &&
                    IsPointNear(aligned.SecondPoint, candidate.EndPoint, tolerance))
                {
                    CreateAssociation(aligned, candidate);
                    break;
                }
            }
        }

        private void AutoCreateRadiusAssociations(EntityRadiusDimension radius, IEnumerable<EntityBase> candidates, double tolerance)
        {
            foreach (var candidate in candidates)
            {
                switch (candidate)
                {
                    case EntityCircle circle when IsPointNear(radius.Center, circle.Center, tolerance) &&
                                                Math.Abs(radius.Radius - circle.Radius) < tolerance:
                        CreateAssociation(radius, candidate);
                        return;
                    case EntityArc arc when IsPointNear(radius.Center, arc.Center, tolerance) &&
                                          Math.Abs(radius.Radius - arc.Radius) < tolerance:
                        CreateAssociation(radius, candidate);
                        return;
                }
            }
        }

        private void AutoCreateDiameterAssociations(EntityDiameterDimension diameter, IEnumerable<EntityBase> candidates, double tolerance)
        {
            foreach (var candidate in candidates)
            {
                switch (candidate)
                {
                    case EntityCircle circle when IsPointNear(diameter.Center, circle.Center, tolerance) &&
                                                Math.Abs(diameter.Diameter - circle.Radius * 2) < tolerance:
                        CreateAssociation(diameter, candidate);
                        return;
                    case EntityArc arc when IsPointNear(diameter.Center, arc.Center, tolerance) &&
                                          Math.Abs(diameter.Diameter - arc.Radius * 2) < tolerance:
                        CreateAssociation(diameter, candidate);
                        return;
                }
            }
        }

        private void AutoCreateAngularAssociations(EntityAngularDimension angular, IEnumerable<EntityBase> candidates, double tolerance)
        {
            var lines = candidates.OfType<EntityLine>().ToList();
            
            // 查找共享顶点的两条线
            foreach (var line1 in lines)
            {
                foreach (var line2 in lines)
                {
                    if (line1 == line2) continue;

                    var intersection = FindLinesIntersection(line1, line2);
                    if (intersection.HasValue && IsPointNear(angular.Vertex, intersection.Value, tolerance))
                    {
                        CreateAssociation(angular, line1);
                        CreateAssociation(angular, line2);
                        return;
                    }
                }
            }
        }

        private bool IsAssociationValid(EntityDimension dimension, EntityBase entity)
        {
            // 检查实体是否仍然存在且未被删除
            if (entity == null) return false;

            // 检查几何关系是否仍然有效
            switch (dimension)
            {
                case EntityLinearDimension linear when entity is EntityLine line:
                    return IsPointNear(linear.FirstPoint, line.StartPoint, 1.0) &&
                           IsPointNear(linear.SecondPoint, line.EndPoint, 1.0);
                    
                case EntityRadiusDimension radius when entity is EntityCircle circle:
                    return IsPointNear(radius.Center, circle.Center, 1.0) &&
                           Math.Abs(radius.Radius - circle.Radius) < 1.0;
                    
                // 添加其他类型的验证...
                default:
                    return true;
            }
        }

        private bool IsPointNear(Vector2 point1, Vector2 point2, double tolerance)
        {
            return Vector2.Distance(point1, point2) <= tolerance;
        }

        private Vector2? FindLinesIntersection(EntityLine line1, EntityLine line2)
        {
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;

            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-10) return null; // 平行线

            var t = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            return line1.StartPoint + t * d1;
        }

        #endregion
    }

    #region 事件参数类

    public class AssociationEventArgs : EventArgs
    {
        public EntityDimension Dimension { get; }
        public EntityBase Entity { get; }

        public AssociationEventArgs(EntityDimension dimension, EntityBase entity)
        {
            Dimension = dimension;
            Entity = entity;
        }
    }

    public class DimensionUpdateEventArgs : EventArgs
    {
        public EntityDimension Dimension { get; }
        public EntityBase TriggerEntity { get; }

        public DimensionUpdateEventArgs(EntityDimension dimension, EntityBase triggerEntity)
        {
            Dimension = dimension;
            TriggerEntity = triggerEntity;
        }
    }

    #endregion
} 