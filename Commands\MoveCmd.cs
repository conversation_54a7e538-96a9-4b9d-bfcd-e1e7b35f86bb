﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    public class MoveCmd : ModifyCmd
    {
        /// <summary>
        /// 文档引用
        /// </summary>
        private DocumentBase doc => _mgr?.Viewer?.Document as DocumentBase;

        /// <summary>
        /// 操作的图元
        /// </summary>
        private List<EntityBase> _selectedEntities = new List<EntityBase>();
        private List<EntityBase> _previewEntities = new List<EntityBase>();

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            Step1_SelectObjects = 1,
            Step2_SpecifyBasePoint = 2,
            Step3_SpecifyTargetPoint = 3,
        }
        private Step _step = Step.Step1_SelectObjects;

        /// <summary>
        /// 基点
        /// </summary>
        private Vector2 _basePoint;

        /// <summary>
        /// 当前移动向量
        /// </summary>
        private Vector2 _moveVector;

        public override void Initialize()
        {
            base.Initialize();

            // 检查是否已有选中的对象
            if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
            {
                _selectedEntities.AddRange(doc.SelectedEntities);
                _step = Step.Step2_SpecifyBasePoint;
                this.pointer.Mode = IndicatorMode.Locate;
                this.pointer.Document.Prompt = "指定基点:";
            }
            else
            {
                _step = Step.Step1_SelectObjects;
                this.pointer.Mode = IndicatorMode.Select;
                this.pointer.Document.Prompt = "选择要移动的对象:";
            }
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            try
            {
                if (_selectedEntities != null && _selectedEntities.Count > 0)
                {
                    foreach (EntityBase entity in _selectedEntities)
                    {
                        entity.Translate(_moveVector);
                    }
                    doc.Action.ActEntityModify(_selectedEntities);
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"移动对象时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"MoveCmd.Commit error: {ex}");

                // 尝试回滚
                try
                {
                    Rollback();
                }
                catch (System.Exception rollbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"MoveCmd.Rollback error: {rollbackEx}");
                }
            }
        }

        /// <summary>
        /// 回滚撤销
        /// </summary>
        protected override void Rollback()
        {
            if (_selectedEntities != null && _selectedEntities.Count > 0)
            {
                foreach (EntityBase entity in _selectedEntities)
                {
                    entity.Translate(-_moveVector);
                }
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (_step == Step.Step1_SelectObjects)
            {
                // 选择对象的逻辑由基类处理
                return EventResult.Unhandled;
            }
            else if (_step == Step.Step2_SpecifyBasePoint)
            {
                if (e.Button == MouseButtons.Left)
                {
                    _basePoint = this.pointer.CurrentSnapPoint;
                    _step = Step.Step3_SpecifyTargetPoint;
                    this.pointer.Document.Prompt = "指定目标点:";

                    // 创建预览实体
                    CreatePreviewEntities();
                }
                else if (e.Button == MouseButtons.Right)
                {
                    _mgr.CancelCurrentCommand();
                }
            }
            else if (_step == Step.Step3_SpecifyTargetPoint)
            {
                if (e.Button == MouseButtons.Left)
                {
                    Vector2 targetPoint = this.pointer.CurrentSnapPoint;
                    _moveVector = targetPoint - _basePoint;
                    _mgr.FinishCurrentCommand();
                }
                else if (e.Button == MouseButtons.Right)
                {
                    _mgr.CancelCurrentCommand();
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (_step == Step.Step1_SelectObjects)
            {
                if (e.Button == MouseButtons.Right)
                {
                    if (doc.SelectedEntities != null && doc.SelectedEntities.Count > 0)
                    {
                        _selectedEntities.AddRange(doc.SelectedEntities);
                        _step = Step.Step2_SpecifyBasePoint;
                        this.pointer.Mode = IndicatorMode.Locate;
                        this.pointer.Document.Prompt = "指定基点:";
                    }
                    else
                    {
                        _mgr.CancelCurrentCommand();
                    }
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step3_SpecifyTargetPoint)
            {
                Vector2 currentPoint = this.pointer.CurrentSnapPoint;
                Vector2 newMoveVector = currentPoint - _basePoint;
                Vector2 offset = newMoveVector - _moveVector;
                _moveVector = newMoveVector;

                // 更新预览实体
                if (_previewEntities != null)
                {
                    foreach (EntityBase entity in _previewEntities)
                    {
                        entity.Translate(offset);
                    }
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }

        public override void OnPaint(SKCanvas canvas)
        {
            if (_step == Step.Step3_SpecifyTargetPoint)
            {
                // 绘制移动向量线
                using (var paint = new SKPaint())
                {
                    paint.Color = SKColors.White;
                    paint.StrokeWidth = 1;
                    paint.Style = SKPaintStyle.Stroke;
                    paint.PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0);

                    var startPoint = doc.View.ModelToCanvas(_basePoint);
                    var endPoint = doc.View.ModelToCanvas(_basePoint + _moveVector);

                    canvas.DrawLine(startPoint.X, startPoint.Y, endPoint.X, endPoint.Y, paint);
                }

                // 绘制预览实体
                if (_previewEntities != null)
                {
                    foreach (EntityBase entity in _previewEntities)
                    {
                        entity.Draw(doc.View, canvas);
                    }
                }
            }
        }

        /// <summary>
        /// 创建预览实体
        /// </summary>
        private void CreatePreviewEntities()
        {
            _previewEntities.Clear();
            if (_selectedEntities != null)
            {
                foreach (EntityBase entity in _selectedEntities)
                {
                    EntityBase preview = entity.Clone();
                    _previewEntities.Add(preview);
                }
            }
        }

        /// <summary>
        /// 更新预览实体
        /// </summary>
        private void UpdatePreviewEntities()
        {
            if (_previewEntities != null && _selectedEntities != null)
            {
                for (int i = 0; i < _previewEntities.Count && i < _selectedEntities.Count; i++)
                {
                    _previewEntities[i] = _selectedEntities[i].Clone();
                    _previewEntities[i].Translate(_moveVector);
                }
            }
        }
    }
}
