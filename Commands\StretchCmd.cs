using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 拉伸命令
    /// 实现专业CAD级别的拉伸功能，支持多种选择模式和精确的几何变换
    /// </summary>
    public class StretchCmd : Command
    {
        private StretchState _currentState = StretchState.SelectEntities;
        private List<EntityBase> _selectedEntities = new List<EntityBase>();
        private List<Vector2> _stretchPoints = new List<Vector2>();
        private Vector2 _stretchBoxStart;
        private Vector2 _stretchBoxEnd;
        private Vector2 _basePoint;
        private Vector2 _displacementVector;
        private List<EntityBase> _previewEntities = new List<EntityBase>();
        private StretchOptions _options = new StretchOptions();
        private SelectionBox _selectionBox;
        
        // 视觉样式
        private SKPaint _selectedPaint;
        private SKPaint _previewPaint;
        private SKPaint _stretchBoxPaint;
        private SKPaint _basePointPaint;
        private SKPaint _displacementPaint;
        
        public override string Name => "STRETCH";
        public override string Description => "拉伸选定的对象";
        
        public StretchCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 255, 0, 255), // 半透明紫色
                IsAntialias = true
            };
            
            _stretchBoxPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.5f,
                Color = SKColors.Green,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0),
                IsAntialias = true
            };
            
            _basePointPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Red,
                IsAntialias = true
            };
            
            _displacementPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Orange,
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = StretchState.SelectEntities;
            _selectedEntities.Clear();
            _stretchPoints.Clear();
            _previewEntities.Clear();
            _selectionBox = null;
            
            _viewer.Document.Prompt = "选择要拉伸的对象（使用窗口选择或交叉选择）：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case StretchState.SelectEntities:
                    HandleEntitySelectionStart(currentPoint);
                    break;
                    
                case StretchState.DefineStretchBox:
                    HandleStretchBoxEnd(currentPoint);
                    break;
                    
                case StretchState.SpecifyBasePoint:
                    HandleBasePointSelection(currentPoint);
                    break;
                    
                case StretchState.SpecifyDisplacement:
                    HandleDisplacementInput(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case StretchState.DefineStretchBox:
                    UpdateStretchBoxPreview(currentPoint);
                    break;
                    
                case StretchState.SpecifyDisplacement:
                    UpdateStretchPreview(currentPoint);
                    break;
            }
            
            _viewer.RepaintCanvas();
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == StretchState.SelectEntities && _selectedEntities.Count > 0)
                    {
                        StartBasePointSelection();
                    }
                    else if (_currentState == StretchState.SpecifyDisplacement)
                    {
                        CompleteStretch();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.C:
                    // 切换交叉选择模式
                    _options.CrossingMode = !_options.CrossingMode;
                    _viewer.Document.Prompt += $" [交叉选择: {(_options.CrossingMode ? "开" : "关")}]";
                    break;
                    
                case Keys.P:
                    // 切换多边形选择
                    _options.PolygonMode = !_options.PolygonMode;
                    _viewer.Document.Prompt += $" [多边形选择: {(_options.PolygonMode ? "开" : "关")}]";
                    break;
                    
                case Keys.D:
                    // 动态拉伸
                    _options.DynamicStretch = !_options.DynamicStretch;
                    _viewer.Document.Prompt += $" [动态拉伸: {(_options.DynamicStretch ? "开" : "关")}]";
                    break;
            }
        }
        
        private void HandleEntitySelectionStart(Vector2 point)
        {
            _stretchBoxStart = point;
            _currentState = StretchState.DefineStretchBox;
            _viewer.Document.Prompt = "指定拉伸框的另一个角点：";
        }
        
        private void HandleStretchBoxEnd(Vector2 point)
        {
            _stretchBoxEnd = point;
            _selectionBox = new SelectionBox
            {
                Start = _stretchBoxStart,
                End = _stretchBoxEnd
            };
            
            // 选择在拉伸框内的实体
            SelectEntitiesInStretchBox();
            
            if (_selectedEntities.Count > 0)
            {
                _currentState = StretchState.SpecifyBasePoint;
                _viewer.Document.Prompt = "指定基点：";
            }
            else
            {
                _viewer.Document.Prompt = "未选择到任何对象，请重新定义拉伸框：";
                _currentState = StretchState.SelectEntities;
            }
        }
        
        private void HandleBasePointSelection(Vector2 point)
        {
            _basePoint = point;
            _currentState = StretchState.SpecifyDisplacement;
            _viewer.Document.Prompt = "指定位移点或输入位移距离：";
        }
        
        private void HandleDisplacementInput(Vector2 point)
        {
            _displacementVector = point - _basePoint;
            CompleteStretch();
        }
        
        private void UpdateStretchBoxPreview(Vector2 currentPoint)
        {
            _stretchBoxEnd = currentPoint;
            
            // 预览选择在拉伸框内的实体
            _selectedEntities.Clear();
            var tempBox = new SelectionBox
            {
                Start = _stretchBoxStart,
                End = currentPoint
            };
            
            SelectEntitiesInBox(tempBox);
        }
        
        private void UpdateStretchPreview(Vector2 currentPoint)
        {
            _displacementVector = currentPoint - _basePoint;
            _previewEntities = GenerateStretchPreview();
        }
        
        private void SelectEntitiesInStretchBox()
        {
            _selectedEntities.Clear();
            _stretchPoints.Clear();
            
            SelectEntitiesInBox(_selectionBox);
            
            // 确定拉伸点
            CalculateStretchPoints();
        }
        
        private void SelectEntitiesInBox(SelectionBox box)
        {
            var boxRect = GetSelectionRect(box);
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (ShouldSelectEntity(entity, boxRect))
                {
                    _selectedEntities.Add(entity);
                }
            }
        }
        
        private bool ShouldSelectEntity(EntityBase entity, SKRect selectionRect)
        {
            var entityBounds = GetEntityBounds(entity);
            if (entityBounds.IsEmpty) return false;
            
            if (_options.CrossingMode)
            {
                // 交叉选择：部分包含也选中
                return selectionRect.IntersectsWith(entityBounds);
            }
            else
            {
                // 窗口选择：完全包含才选中
                return selectionRect.Contains(entityBounds);
            }
        }
        
        private void CalculateStretchPoints()
        {
            if (_selectionBox == null) return;
            
            var boxRect = GetSelectionRect(_selectionBox);
            
            foreach (var entity in _selectedEntities)
            {
                var stretchPoints = GetEntityStretchPoints(entity, boxRect);
                _stretchPoints.AddRange(stretchPoints);
            }
        }
        
        private List<Vector2> GetEntityStretchPoints(EntityBase entity, SKRect stretchBox)
        {
            var points = new List<Vector2>();
            
            switch (entity)
            {
                case EntityLine line:
                    // 检查端点是否在拉伸框内
                    if (IsPointInRect(line.StartPoint, stretchBox))
                        points.Add(line.StartPoint);
                    if (IsPointInRect(line.EndPoint, stretchBox))
                        points.Add(line.EndPoint);
                    break;
                    
                case EntityLwPolyline polyline:
                    // 检查多段线顶点
                    foreach (var vertex in polyline.Vertexs)
                    {
                        if (IsPointInRect(vertex.Position, stretchBox))
                            points.Add(vertex.Position);
                    }
                    break;
                    
                case EntityRectangle rect:
                    // 检查矩形角点
                    if (IsPointInRect(rect.Corner1, stretchBox))
                        points.Add(rect.Corner1);
                    if (IsPointInRect(rect.Corner2, stretchBox))
                        points.Add(rect.Corner2);
                    break;
                    
                case EntityCircle circle:
                    // 圆心在拉伸框内则整个圆移动
                    if (IsPointInRect(circle.Center, stretchBox))
                        points.Add(circle.Center);
                    break;
                    
                case EntityArc arc:
                    // 弧心在拉伸框内则整个弧移动
                    if (IsPointInRect(arc.Center, stretchBox))
                        points.Add(arc.Center);
                    break;
            }
            
            return points;
        }
        
        private List<EntityBase> GenerateStretchPreview()
        {
            var stretchedEntities = new List<EntityBase>();
            
            foreach (var entity in _selectedEntities)
            {
                var stretchedEntity = ApplyStretchToEntity(entity);
                if (stretchedEntity != null)
                {
                    stretchedEntities.Add(stretchedEntity);
                }
            }
            
            return stretchedEntities;
        }
        
        private EntityBase ApplyStretchToEntity(EntityBase entity)
        {
            if (_selectionBox == null) return null;
            
            var boxRect = GetSelectionRect(_selectionBox);
            
            switch (entity)
            {
                case EntityLine line:
                    return StretchLine(line, boxRect);
                    
                case EntityLwPolyline polyline:
                    return StretchPolyline(polyline, boxRect);
                    
                case EntityRectangle rect:
                    return StretchRectangle(rect, boxRect);
                    
                case EntityCircle circle:
                    return StretchCircle(circle, boxRect);
                    
                case EntityArc arc:
                    return StretchArc(arc, boxRect);
                    
                default:
                    return null;
            }
        }
        
        private EntityBase StretchLine(EntityLine line, SKRect stretchBox)
        {
            var newStartPoint = line.StartPoint;
            var newEndPoint = line.EndPoint;
            
            // 只拉伸在拉伸框内的端点
            if (IsPointInRect(line.StartPoint, stretchBox))
            {
                newStartPoint += _displacementVector;
            }
            
            if (IsPointInRect(line.EndPoint, stretchBox))
            {
                newEndPoint += _displacementVector;
            }
            
            return new EntityLine
            {
                StartPoint = newStartPoint,
                EndPoint = newEndPoint,
                LineType = line.LineType,
                LineWeight = line.LineWeight,
                Color = line.Color
            };
        }
        
        private EntityBase StretchPolyline(EntityLwPolyline polyline, SKRect stretchBox)
        {
            var newPolyline = new EntityLwPolyline
            {
                IsClosed = polyline.IsClosed,
                LineType = polyline.LineType,
                LineWeight = polyline.LineWeight,
                Color = polyline.Color
            };
            
            foreach (var vertex in polyline.Vertexs)
            {
                var newPosition = vertex.Position;
                
                if (IsPointInRect(vertex.Position, stretchBox))
                {
                    newPosition += _displacementVector;
                }
                
                newPolyline.Vertexs.Add(new LwPolyLineVertex
                {
                    Position = newPosition,
                    Bulge = vertex.Bulge,
                    StartWidth = vertex.StartWidth,
                    EndWidth = vertex.EndWidth
                });
            }
            
            return newPolyline;
        }
        
        private EntityBase StretchRectangle(EntityRectangle rect, SKRect stretchBox)
        {
            var newCorner1 = rect.Corner1;
            var newCorner2 = rect.Corner2;
            
            if (IsPointInRect(rect.Corner1, stretchBox))
            {
                newCorner1 += _displacementVector;
            }
            
            if (IsPointInRect(rect.Corner2, stretchBox))
            {
                newCorner2 += _displacementVector;
            }
            
            return new EntityRectangle
            {
                Corner1 = newCorner1,
                Corner2 = newCorner2,
                LineType = rect.LineType,
                LineWeight = rect.LineWeight,
                Color = rect.Color
            };
        }
        
        private EntityBase StretchCircle(EntityCircle circle, SKRect stretchBox)
        {
            var newCenter = circle.Center;
            
            // 如果圆心在拉伸框内，整个圆移动
            if (IsPointInRect(circle.Center, stretchBox))
            {
                newCenter += _displacementVector;
            }
            
            return new EntityCircle
            {
                Center = newCenter,
                Radius = circle.Radius,
                LineType = circle.LineType,
                LineWeight = circle.LineWeight,
                Color = circle.Color
            };
        }
        
        private EntityBase StretchArc(EntityArc arc, SKRect stretchBox)
        {
            var newCenter = arc.Center;
            
            // 如果弧心在拉伸框内，整个弧移动
            if (IsPointInRect(arc.Center, stretchBox))
            {
                newCenter += _displacementVector;
            }
            
            return new EntityArc
            {
                Center = newCenter,
                Radius = arc.Radius,
                StartAngle = arc.StartAngle,
                EndAngle = arc.EndAngle,
                LineType = arc.LineType,
                LineWeight = arc.LineWeight,
                Color = arc.Color
            };
        }
        
        private void CompleteStretch()
        {
            if (_selectedEntities.Count == 0 || _displacementVector.LengthSquared() < 0.001f) return;
            
            try
            {
                var stretchedEntities = GenerateStretchPreview();
                
                // 替换原实体
                for (int i = 0; i < _selectedEntities.Count; i++)
                {
                    var originalEntity = _selectedEntities[i];
                    var stretchedEntity = stretchedEntities[i];
                    
                    if (stretchedEntity != null)
                    {
                        var index = _viewer.Document.ActiveLayer.Children.IndexOf(originalEntity);
                        if (index >= 0)
                        {
                            _viewer.Document.ActiveLayer.Children[index] = stretchedEntity;
                        }
                    }
                }
                
                _viewer.RepaintCanvas();
                _viewer.Document.Prompt = $"拉伸完成，位移: ({_displacementVector.X:F1}, {_displacementVector.Y:F1})";
                Finish();
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"拉伸失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Stretch error: {ex.Message}");
            }
        }
        
        private void StartBasePointSelection()
        {
            _currentState = StretchState.SpecifyBasePoint;
            _viewer.Document.Prompt = "指定基点：";
        }
        
        private SKRect GetSelectionRect(SelectionBox box)
        {
            var left = Math.Min(box.Start.X, box.End.X);
            var top = Math.Min(box.Start.Y, box.End.Y);
            var right = Math.Max(box.Start.X, box.End.X);
            var bottom = Math.Max(box.Start.Y, box.End.Y);
            
            return new SKRect(left, top, right, bottom);
        }
        
        private SKRect GetEntityBounds(EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null || bounds.IsEmpty)
                return SKRect.Empty;
            
            return new SKRect(bounds.MinX, bounds.MinY, bounds.MaxX, bounds.MaxY);
        }
        
        private bool IsPointInRect(Vector2 point, SKRect rect)
        {
            return rect.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制拉伸框
            if (_selectionBox != null || _currentState == StretchState.DefineStretchBox)
            {
                RenderStretchBox(canvas);
            }
            
            // 绘制选中实体
            foreach (var entity in _selectedEntities)
            {
                RenderSelectedEntity(canvas, entity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制基点和位移向量
            if (_currentState == StretchState.SpecifyDisplacement)
            {
                RenderBasePointAndDisplacement(canvas);
            }
            
            // 绘制拉伸点
            RenderStretchPoints(canvas);
        }
        
        private void RenderStretchBox(SKCanvas canvas)
        {
            Vector2 start, end;
            
            if (_selectionBox != null)
            {
                start = _selectionBox.Start;
                end = _selectionBox.End;
            }
            else
            {
                start = _stretchBoxStart;
                end = _stretchBoxEnd;
            }
            
            var rect = new SKRect(
                Math.Min(start.X, end.X),
                Math.Min(start.Y, end.Y),
                Math.Max(start.X, end.X),
                Math.Max(start.Y, end.Y)
            );
            
            canvas.DrawRect(rect, _stretchBoxPaint);
            
            // 绘制填充
            var fillPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColor.FromArgb(30, 0, 255, 0),
                IsAntialias = true
            };
            canvas.DrawRect(rect, fillPaint);
            fillPaint.Dispose();
        }
        
        private void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = GetEntityBounds(entity);
            if (!bounds.IsEmpty)
            {
                canvas.DrawRect(bounds, _selectedPaint);
            }
        }
        
        private void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityCircle circle:
                    canvas.DrawCircle(circle.Center.X, circle.Center.Y, circle.Radius, _previewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _previewPaint);
                    break;
                    
                case EntityRectangle rectangle:
                    canvas.DrawRect(rectangle.Corner1.X, rectangle.Corner1.Y,
                                  rectangle.Width, rectangle.Height, _previewPaint);
                    break;
                    
                case EntityLwPolyline polyline:
                    RenderPolylinePreview(canvas, polyline);
                    break;
            }
        }
        
        private void RenderPolylinePreview(SKCanvas canvas, EntityLwPolyline polyline)
        {
            if (polyline.Vertexs.Count < 2) return;
            
            using (var path = new SKPath())
            {
                var firstVertex = polyline.Vertexs[0];
                path.MoveTo(firstVertex.Position.X, firstVertex.Position.Y);
                
                for (int i = 1; i < polyline.Vertexs.Count; i++)
                {
                    var vertex = polyline.Vertexs[i];
                    path.LineTo(vertex.Position.X, vertex.Position.Y);
                }
                
                if (polyline.IsClosed)
                {
                    path.Close();
                }
                
                canvas.DrawPath(path, _previewPaint);
            }
        }
        
        private void RenderBasePointAndDisplacement(SKCanvas canvas)
        {
            // 绘制基点
            canvas.DrawCircle(_basePoint.X, _basePoint.Y, 4, _basePointPaint);
            
            // 绘制位移向量
            var endPoint = _basePoint + _displacementVector;
            canvas.DrawLine(_basePoint.X, _basePoint.Y, endPoint.X, endPoint.Y, _displacementPaint);
            
            // 绘制箭头
            RenderArrow(canvas, _basePoint, endPoint);
            
            // 绘制位移信息
            var displacementText = $"ΔX:{_displacementVector.X:F1} ΔY:{_displacementVector.Y:F1}";
            var textPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = 12,
                Color = SKColors.Orange,
                IsAntialias = true
            };
            
            canvas.DrawText(displacementText, endPoint.X + 10, endPoint.Y, textPaint);
            textPaint.Dispose();
        }
        
        private void RenderArrow(SKCanvas canvas, Vector2 start, Vector2 end)
        {
            var direction = Vector2.Normalize(end - start);
            var arrowLength = 10.0f;
            var arrowAngle = 0.5f; // 弧度
            
            var arrowPoint1 = end - arrowLength * (direction * (float)Math.Cos(arrowAngle) + 
                                                  new Vector2(-direction.Y, direction.X) * (float)Math.Sin(arrowAngle));
            var arrowPoint2 = end - arrowLength * (direction * (float)Math.Cos(arrowAngle) - 
                                                  new Vector2(-direction.Y, direction.X) * (float)Math.Sin(arrowAngle));
            
            canvas.DrawLine(end.X, end.Y, arrowPoint1.X, arrowPoint1.Y, _displacementPaint);
            canvas.DrawLine(end.X, end.Y, arrowPoint2.X, arrowPoint2.Y, _displacementPaint);
        }
        
        private void RenderStretchPoints(SKCanvas canvas)
        {
            var pointPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Lime,
                IsAntialias = true
            };
            
            foreach (var point in _stretchPoints)
            {
                canvas.DrawCircle(point.X, point.Y, 3, pointPaint);
            }
            
            pointPaint.Dispose();
        }
        
        public override void Cancel()
        {
            _selectedEntities.Clear();
            _stretchPoints.Clear();
            _previewEntities.Clear();
            _selectionBox = null;
            _currentState = StretchState.SelectEntities;
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _selectedPaint?.Dispose();
                _previewPaint?.Dispose();
                _stretchBoxPaint?.Dispose();
                _basePointPaint?.Dispose();
                _displacementPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    #region 枚举和数据结构
    
    public enum StretchState
    {
        SelectEntities,
        DefineStretchBox,
        SpecifyBasePoint,
        SpecifyDisplacement
    }
    
    public class StretchOptions
    {
        public bool CrossingMode { get; set; } = false;
        public bool PolygonMode { get; set; } = false;
        public bool DynamicStretch { get; set; } = true;
        public bool PreserveProperties { get; set; } = true;
    }
    
    public class SelectionBox
    {
        public Vector2 Start { get; set; }
        public Vector2 End { get; set; }
    }
    
    #endregion
} 