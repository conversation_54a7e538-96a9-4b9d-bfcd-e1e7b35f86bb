using McLaser.EditViewerSk.Base;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Numerics;

namespace McLaser.EditViewerSk.Tracking
{
    /// <summary>
    /// 极轴追踪系统
    /// 实现专业CAD软件的极轴追踪功能，提供精确的角度追踪和视觉反馈
    /// </summary>
    public class PolarTrackingSystem
    {
        private ViewBase _viewer;
        private bool _isEnabled = false;
        private List<float> _trackingAngles;
        private Vector2? _basePoint;
        private float _currentAngle;
        private Vector2? _currentTrackingPoint;
        private float _trackingDistance;
        
        // 默认追踪角度 (度)
        private readonly float[] _defaultAngles = { 0, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330 };
        
        // 配置参数
        private const float AngleTolerance = 2.0f;     // 角度容差（度）
        private const float MinTrackingDistance = 20.0f; // 最小追踪距离（像素）
        private const float MaxTrackingDistance = 2000.0f; // 最大追踪距离（像素）
        
        // 视觉样式
        private SKPaint _trackingLinePaint;
        private SKPaint _angleTextPaint;
        private SKPaint _angleBackgroundPaint;
        private SKPaint _distanceTextPaint;
        
        public bool IsEnabled
        {
            get { return _isEnabled; }
            set { _isEnabled = value; }
        }
        
        public List<float> TrackingAngles
        {
            get { return _trackingAngles; }
            set { _trackingAngles = value ?? new List<float>(); }
        }
        
        public Vector2? CurrentTrackingPoint
        {
            get { return _currentTrackingPoint; }
        }
        
        public float CurrentAngle
        {
            get { return _currentAngle; }
        }
        
        public float TrackingDistance
        {
            get { return _trackingDistance; }
        }
        
        public PolarTrackingSystem(ViewBase viewer)
        {
            _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
            _trackingAngles = new List<float>(_defaultAngles);
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _trackingLinePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColors.Green,
                PathEffect = SKPathEffect.CreateDash(new float[] { 8, 4 }, 0),
                IsAntialias = true
            };
            
            _angleTextPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = 11.0f,
                Color = SKColors.White,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal)
            };
            
            _angleBackgroundPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColor.FromArgb(200, 0, 128, 0), // 半透明深绿色
                IsAntialias = true
            };
            
            _distanceTextPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = 10.0f,
                Color = SKColors.LightGray,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal)
            };
        }
        
        /// <summary>
        /// 设置基点（追踪的起始点）
        /// </summary>
        public void SetBasePoint(Vector2 point)
        {
            _basePoint = point;
            _currentTrackingPoint = null;
        }
        
        /// <summary>
        /// 清除基点
        /// </summary>
        public void ClearBasePoint()
        {
            _basePoint = null;
            _currentTrackingPoint = null;
            _trackingDistance = 0;
        }
        
        /// <summary>
        /// 更新追踪（在鼠标移动时调用）
        /// </summary>
        public Vector2? UpdateTracking(Vector2 currentPoint)
        {
            if (!_isEnabled || !_basePoint.HasValue)
            {
                _currentTrackingPoint = null;
                return null;
            }
            
            var basePoint = _basePoint.Value;
            var deltaX = currentPoint.X - basePoint.X;
            var deltaY = currentPoint.Y - basePoint.Y;
            
            // 计算当前角度（以度为单位）
            var currentAngleRad = Math.Atan2(deltaY, deltaX);
            var currentAngleDeg = (float)(currentAngleRad * 180.0 / Math.PI);
            if (currentAngleDeg < 0) currentAngleDeg += 360;
            
            // 查找最近的追踪角度
            var closestAngle = FindClosestTrackingAngle(currentAngleDeg);
            
            if (closestAngle.HasValue)
            {
                _currentAngle = closestAngle.Value;
                
                // 计算实际距离
                var distance = Vector2.Distance(basePoint, currentPoint);
                
                // 检查距离是否在有效范围内
                if (distance >= MinTrackingDistance && distance <= MaxTrackingDistance)
                {
                    _trackingDistance = distance;
                    
                    // 计算追踪点
                    var angleRad = _currentAngle * Math.PI / 180.0;
                    _currentTrackingPoint = new Vector2(
                        basePoint.X + (float)(distance * Math.Cos(angleRad)),
                        basePoint.Y + (float)(distance * Math.Sin(angleRad))
                    );
                    
                    return _currentTrackingPoint;
                }
            }
            
            _currentTrackingPoint = null;
            return null;
        }
        
        /// <summary>
        /// 查找最近的追踪角度
        /// </summary>
        private float? FindClosestTrackingAngle(float currentAngle)
        {
            float minDifference = float.MaxValue;
            float? closestAngle = null;
            
            foreach (var trackingAngle in _trackingAngles)
            {
                var difference = CalculateAngleDifference(currentAngle, trackingAngle);
                
                if (difference <= AngleTolerance && difference < minDifference)
                {
                    minDifference = difference;
                    closestAngle = trackingAngle;
                }
            }
            
            return closestAngle;
        }
        
        /// <summary>
        /// 计算两个角度之间的最小差值
        /// </summary>
        private float CalculateAngleDifference(float angle1, float angle2)
        {
            var diff = Math.Abs(angle1 - angle2);
            return Math.Min(diff, 360 - diff);
        }
        
        /// <summary>
        /// 渲染极轴追踪
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            if (!_isEnabled || !_basePoint.HasValue || !_currentTrackingPoint.HasValue)
                return;
            
            var basePointCanvas = _viewer.ModelToCanvas(_basePoint.Value);
            var trackingPointCanvas = _viewer.ModelToCanvas(_currentTrackingPoint.Value);
            
            // 绘制追踪线
            RenderTrackingLine(canvas, basePointCanvas, trackingPointCanvas);
            
            // 绘制角度和距离信息
            RenderTrackingInfo(canvas, basePointCanvas, trackingPointCanvas);
        }
        
        private void RenderTrackingLine(SKCanvas canvas, Vector2 basePoint, Vector2 trackingPoint)
        {
            // 计算延长线的终点（延伸到屏幕边缘）
            var direction = Vector2.Normalize(trackingPoint - basePoint);
            var screenBounds = new SKRect(0, 0, (float)_viewer.Width, (float)_viewer.Height);
            var extendedEnd = CalculateLineScreenIntersection(basePoint, direction, screenBounds);
            
            Vector2 lineEnd = extendedEnd ?? trackingPoint;
            
            // 绘制从基点到屏幕边缘的追踪线
            canvas.DrawLine(basePoint.X, basePoint.Y, lineEnd.X, lineEnd.Y, _trackingLinePaint);
            
            // 在追踪点处绘制一个小标记
            var markerSize = 3.0f;
            canvas.DrawCircle(trackingPoint.X, trackingPoint.Y, markerSize, _trackingLinePaint);
        }
        
        private void RenderTrackingInfo(SKCanvas canvas, Vector2 basePoint, Vector2 trackingPoint)
        {
            // 格式化角度和距离信息
            var angleText = $"∠{_currentAngle:F0}°";
            var distanceText = $"L={_trackingDistance:F1}";
            var infoText = $"{angleText} {distanceText}";
            
            // 计算文本位置（在追踪线旁边，避免遮挡）
            var direction = Vector2.Normalize(trackingPoint - basePoint);
            var perpendicular = new Vector2(-direction.Y, direction.X); // 垂直方向
            var textOffset = perpendicular * 20 + direction * 30; // 偏移位置
            var textPosition = basePoint + textOffset;
            
            // 确保文本不会超出屏幕
            textPosition = ClampTextPosition(textPosition, infoText);
            
            // 绘制背景
            var textBounds = new SKRect();
            _angleTextPaint.MeasureText(infoText, ref textBounds);
            
            var backgroundRect = new SKRect(
                textPosition.X - 4,
                textPosition.Y + textBounds.Top - 2,
                textPosition.X + textBounds.Width + 4,
                textPosition.Y + textBounds.Bottom + 2
            );
            
            canvas.DrawRoundRect(backgroundRect, 3, 3, _angleBackgroundPaint);
            
            // 绘制文本
            canvas.DrawText(infoText, textPosition.X, textPosition.Y, _angleTextPaint);
        }
        
        /// <summary>
        /// 确保文本位置在屏幕范围内
        /// </summary>
        private Vector2 ClampTextPosition(Vector2 position, string text)
        {
            var textBounds = new SKRect();
            _angleTextPaint.MeasureText(text, ref textBounds);
            
            var clampedX = Math.Max(10, Math.Min(position.X, _viewer.Width - textBounds.Width - 10));
            var clampedY = Math.Max(20, Math.Min(position.Y, _viewer.Height - 10));
            
            return new Vector2(clampedX, clampedY);
        }
        
        /// <summary>
        /// 计算直线与屏幕边界的交点
        /// </summary>
        private Vector2? CalculateLineScreenIntersection(Vector2 startPoint, Vector2 direction, SKRect screenBounds)
        {
            var intersections = new List<Vector2>();
            
            // 检查与各边界的交点
            AddIntersectionIfValid(intersections, startPoint, direction, screenBounds.Left, true, screenBounds);
            AddIntersectionIfValid(intersections, startPoint, direction, screenBounds.Right, true, screenBounds);
            AddIntersectionIfValid(intersections, startPoint, direction, screenBounds.Top, false, screenBounds);
            AddIntersectionIfValid(intersections, startPoint, direction, screenBounds.Bottom, false, screenBounds);
            
            // 返回距离起点最远的交点
            if (intersections.Count == 0) return null;
            
            var maxDistance = 0f;
            Vector2? farthestIntersection = null;
            
            foreach (var intersection in intersections)
            {
                var distance = Vector2.Distance(startPoint, intersection);
                if (distance > maxDistance)
                {
                    maxDistance = distance;
                    farthestIntersection = intersection;
                }
            }
            
            return farthestIntersection;
        }
        
        private void AddIntersectionIfValid(List<Vector2> intersections, Vector2 startPoint, Vector2 direction, 
            float boundary, bool isVertical, SKRect screenBounds)
        {
            if (isVertical && Math.Abs(direction.X) > 1e-6)
            {
                var t = (boundary - startPoint.X) / direction.X;
                if (t > 0)
                {
                    var y = startPoint.Y + t * direction.Y;
                    if (y >= screenBounds.Top && y <= screenBounds.Bottom)
                    {
                        intersections.Add(new Vector2(boundary, y));
                    }
                }
            }
            else if (!isVertical && Math.Abs(direction.Y) > 1e-6)
            {
                var t = (boundary - startPoint.Y) / direction.Y;
                if (t > 0)
                {
                    var x = startPoint.X + t * direction.X;
                    if (x >= screenBounds.Left && x <= screenBounds.Right)
                    {
                        intersections.Add(new Vector2(x, boundary));
                    }
                }
            }
        }
        
        /// <summary>
        /// 添加自定义追踪角度
        /// </summary>
        public void AddTrackingAngle(float angle)
        {
            angle = NormalizeAngle(angle);
            
            if (!_trackingAngles.Contains(angle))
            {
                _trackingAngles.Add(angle);
                _trackingAngles.Sort();
            }
        }
        
        /// <summary>
        /// 移除追踪角度
        /// </summary>
        public void RemoveTrackingAngle(float angle)
        {
            angle = NormalizeAngle(angle);
            _trackingAngles.Remove(angle);
        }
        
        /// <summary>
        /// 重置为默认角度
        /// </summary>
        public void ResetToDefaultAngles()
        {
            _trackingAngles = new List<float>(_defaultAngles);
        }
        
        /// <summary>
        /// 设置增量角度模式（如每15度、30度等）
        /// </summary>
        public void SetIncrementalAngles(float increment)
        {
            if (increment <= 0 || increment > 180)
                throw new ArgumentOutOfRangeException(nameof(increment), "Increment must be between 0 and 180 degrees");
                
            _trackingAngles.Clear();
            for (float angle = 0; angle < 360; angle += increment)
            {
                _trackingAngles.Add(angle);
            }
        }
        
        /// <summary>
        /// 标准化角度到0-360范围
        /// </summary>
        private float NormalizeAngle(float angle)
        {
            angle = angle % 360;
            if (angle < 0) angle += 360;
            return angle;
        }
        
        /// <summary>
        /// 获取当前追踪状态信息
        /// </summary>
        public PolarTrackingInfo GetTrackingInfo()
        {
            return new PolarTrackingInfo
            {
                IsActive = _isEnabled && _currentTrackingPoint.HasValue,
                BasePoint = _basePoint,
                TrackingPoint = _currentTrackingPoint,
                Angle = _currentAngle,
                Distance = _trackingDistance,
                ActiveAngles = new List<float>(_trackingAngles)
            };
        }
        
        public void Dispose()
        {
            _trackingLinePaint?.Dispose();
            _angleTextPaint?.Dispose();
            _angleBackgroundPaint?.Dispose();
            _distanceTextPaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 极轴追踪信息
    /// </summary>
    public class PolarTrackingInfo
    {
        public bool IsActive { get; set; }
        public Vector2? BasePoint { get; set; }
        public Vector2? TrackingPoint { get; set; }
        public float Angle { get; set; }
        public float Distance { get; set; }
        public List<float> ActiveAngles { get; set; } = new List<float>();
    }
    
    /// <summary>
    /// 极轴追踪配置
    /// </summary>
    public class PolarTrackingSettings
    {
        public bool IsEnabled { get; set; } = true;
        public List<float> TrackingAngles { get; set; } = new List<float>();
        public float AngleTolerance { get; set; } = 2.0f;
        public bool ShowAngleInfo { get; set; } = true;
        public bool ExtendToScreenEdge { get; set; } = true;
        public SKColor TrackingLineColor { get; set; } = SKColors.Green;
        public float TrackingLineWidth { get; set; } = 1.0f;
        public bool ShowDistanceInfo { get; set; } = true;
    }
} 