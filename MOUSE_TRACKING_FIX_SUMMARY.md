# 鼠标跟踪修复总结

## 🎯 问题描述

用户报告："方向对了，但是还是不跟鼠标，比如说我绘制矩形框选框时，矩形的右下角应该跟随鼠标移动"

## 🔍 问题分析过程

### 第一步：方向问题
- **问题**: 框选框方向与鼠标移动方向相反
- **原因**: Y轴翻转的坐标系处理错误
- **解决**: 正确处理Y轴翻转，计算矩形左上角和尺寸

### 第二步：跟踪精度问题  
- **问题**: 框选框角点不精确跟随鼠标
- **原因**: 坐标系不一致 - Model坐标用Canvas方式绘制
- **解决**: 统一使用Canvas坐标系绘制

## 🔧 关键发现

### 原有系统的坐标混用

原有代码中存在坐标系不一致的问题：

```csharp
// MgrIndicator.OnPaint (第498行)
// 问题：Model坐标(_selRect.startPoint)用Canvas坐标系(CSYS.Canvas)绘制
viewer.DrawRectangle(_selRect.startPoint, 
                    _selRect.endPoint.X - _selRect.startPoint.X, 
                    _selRect.endPoint.Y - _selRect.startPoint.Y, 
                    CSYS.Canvas);
```

这种不一致在某些情况下可能看起来"正常工作"，但实际上是错误的。

### 变换矩阵的复杂性

ViewBase中的`_matrixTrans`不只是简单的Y轴翻转：
```csharp
private SKMatrix _matrixTrans = SKMatrix.CreateScale(1, -1);  // 初始Y轴翻转

// 运行时还会添加平移和缩放
_matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateTranslation(offsetX, offsetY));  // 平移
_matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateScale(zoomFactor, zoomFactor));  // 缩放
```

## ✅ 最终解决方案

### 统一坐标系策略

采用Canvas坐标系作为绘制的统一标准：

1. **存储**: 在SelectRectangle中存储Model坐标（保持不变）
2. **转换**: 绘制前将Model坐标转换为Canvas坐标
3. **绘制**: 使用Canvas坐标系进行绘制

```csharp
// 新的绘制逻辑
var startCanvas = view.ModelToCanvas(startPoint);     // Model → Canvas
var endCanvas = view.ModelToCanvas(endPoint);         // Model → Canvas
var canvasWidth = endCanvas.X - startCanvas.X;        // Canvas尺寸
var canvasHeight = endCanvas.Y - startCanvas.Y;       // Canvas尺寸

view.DrawRectangle(startCanvas, canvasWidth, canvasHeight, paint, CSYS.Canvas);
```

### 修复位置

1. **SelectionRenderer.cs**: 新系统的选择框渲染器
2. **MgrIndicator.cs**: 原有系统的Paint方法

## 🚀 修复效果

### 修复前的问题
- ✗ 框选框方向错误
- ✗ 框选框角点不跟随鼠标
- ✗ 坐标系不一致

### 修复后的效果  
- ✅ 框选框方向正确
- ✅ 框选框精确跟随鼠标移动
- ✅ 新旧系统坐标处理一致
- ✅ 支持缩放和平移状态下的准确跟踪

## 🎯 技术要点

### 1. 坐标系理解
- **屏幕坐标**: 鼠标事件的原始坐标
- **Canvas坐标**: 绘制画布的坐标（经过DPI等处理）
- **Model坐标**: 业务逻辑的坐标（经过缩放、平移、翻转等变换）

### 2. 变换流程
```
鼠标屏幕坐标 (e.Location)
        ↓
    Canvas坐标 (ScreenToCanvas)
        ↓  
    Model坐标 (CanvasToModel) ← 存储在SelectRectangle中
        ↓
    Canvas坐标 (ModelToCanvas) ← 绘制前转换
        ↓
    绘制 (CSYS.Canvas)
```

### 3. 关键方法
- `ViewBase.CanvasToModel()`: 使用`_matrixTrans.Invert().MapPoint()`
- `ViewBase.ModelToCanvas()`: 使用`_matrixTrans.MapPoint()`
- `ViewBase.DrawRectangle()`: 根据CSYS参数选择坐标处理方式

## 📚 相关文件

- `Renderers/SelectionRenderer.cs` - 新系统选择框渲染
- `Common/MgrIndicator.cs` - 原有系统指示器（Paint方法）
- `Base/ViewBase.cs` - 坐标转换和绘制基础
- `Services/CoordinateService.cs` - 坐标服务封装

## 🔮 经验总结

1. **坐标系一致性**: 在CAD应用中，坐标系的一致性至关重要
2. **变换矩阵**: 理解变换矩阵的组成和影响（翻转、缩放、平移）
3. **调试方法**: 通过坐标日志追踪变换过程
4. **向后兼容**: 修复时要考虑与原有系统的兼容性
5. **测试场景**: 在不同缩放和平移状态下测试鼠标跟踪

这次修复解决了CAD应用中最核心的用户交互问题，确保了框选功能在各种视图状态下的准确性和响应性。 