using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Enums;
using McLaser.EditViewerSk.Interfaces;
using System;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Handlers
{
    /// <summary>
    /// 鼠标事件上下文
    /// 包含处理鼠标事件所需的所有信息
    /// </summary>
    public class MouseEventContext
    {
        public MouseEventArgs EventArgs { get; set; }
        public MouseEventType EventType { get; set; }
        public InteractionState CurrentState { get; set; }
        public Vector2 Position { get; set; }
        public Vector2 ModelPosition { get; set; }
        public ViewBase View { get; set; }
        public ICoordinateService CoordinateService { get; set; }
        public bool IsHandled { get; set; }
    }

    /// <summary>
    /// 鼠标事件处理结果
    /// </summary>
    public class MouseEventResult
    {
        public bool Handled { get; set; }
        public InteractionState? NewState { get; set; }
        public Command Command { get; set; }

        public static MouseEventResult Unhandled => new MouseEventResult { Handled = false };
        public static MouseEventResult HandledResult => new MouseEventResult { Handled = true };
        
        public static MouseEventResult WithStateChange(InteractionState newState)
        {
            return new MouseEventResult { Handled = true, NewState = newState };
        }

        public static MouseEventResult WithCommand(Command command)
        {
            return new MouseEventResult { Handled = true, Command = command };
        }
    }

    /// <summary>
    /// 鼠标事件处理器基类
    /// 实现职责链模式，允许多个处理器依次处理事件
    /// </summary>
    public abstract class MouseInputHandler
    {
        protected MouseInputHandler _nextHandler;
        public int Priority { get; protected set; } = 0;

        /// <summary>
        /// 设置链中的下一个处理器
        /// </summary>
        public void SetNext(MouseInputHandler handler)
        {
            _nextHandler = handler;
        }

        /// <summary>
        /// 处理鼠标事件
        /// </summary>
        public virtual MouseEventResult Handle(MouseEventContext context)
        {
            // 检查是否可以处理该事件
            if (!CanHandle(context))
            {
                return _nextHandler?.Handle(context) ?? MouseEventResult.Unhandled;
            }

            // 处理事件
            var result = DoHandle(context);
            
            // 如果已处理，返回结果
            if (result.Handled)
            {
                context.IsHandled = true;
                return result;
            }

            // 否则传递给下一个处理器
            return _nextHandler?.Handle(context) ?? MouseEventResult.Unhandled;
        }

        /// <summary>
        /// 检查是否可以处理该事件
        /// </summary>
        protected abstract bool CanHandle(MouseEventContext context);

        /// <summary>
        /// 执行实际的事件处理逻辑
        /// </summary>
        protected abstract MouseEventResult DoHandle(MouseEventContext context);
    }
} 