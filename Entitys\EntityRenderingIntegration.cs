using System;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;
using McLaser.EditViewerSk.Interfaces;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 新图元与专业渲染系统的深度集成
    /// 确保所有新图元都能充分利用最新的渲染架构
    /// </summary>
    public static class EntityRenderingIntegration
    {
        /// <summary>
        /// 为EntityEllipse集成专业渲染器
        /// </summary>
        public static void IntegrateEllipseRendering(this EntityEllipse ellipse, IView view)
        {
            if (!(view is ViewBase viewBase)) return;
            
            var renderer = viewBase.GetGraphicsRenderer();
            if (renderer == null)
            {
                // 降级到基础渲染
                ellipse.RenderBasic(viewBase);
                return;
            }

            // 使用专业渲染器
            var properties = ellipse.GetRenderingProperties();
            var strokePaint = properties.GetStrokePaint();
            var fillPaint = properties.GetFillPaint();

            if (ellipse.IsEllipticalArc)
            {
                RenderEllipticalArcProfessional(ellipse, renderer, strokePaint, fillPaint);
            }
            else
            {
                RenderEllipseProfessional(ellipse, renderer, strokePaint, fillPaint);
            }

            // 渲染选择状态
            if (ellipse.IsSelected)
            {
                RenderSelectionHighlight(ellipse, renderer);
            }
        }

        /// <summary>
        /// 为EntityPolygon集成专业渲染器
        /// </summary>
        public static void IntegratePolygonRendering(this EntityPolygon polygon, IView view)
        {
            if (!(view is ViewBase viewBase)) return;
            
            var renderer = viewBase.GetGraphicsRenderer();
            if (renderer == null)
            {
                polygon.RenderBasic(viewBase);
                return;
            }

            var properties = polygon.GetRenderingProperties();
            var strokePaint = properties.GetStrokePaint();
            var fillPaint = properties.GetFillPaint();

            // 使用专业多边形渲染
            RenderPolygonProfessional(polygon, renderer, strokePaint, fillPaint);

            if (polygon.IsSelected)
            {
                RenderPolygonSelection(polygon, renderer);
            }
        }

        /// <summary>
        /// 为EntityPolylineAdvanced集成专业渲染器
        /// </summary>
        public static void IntegratePolylineRendering(this EntityPolylineAdvanced polyline, IView view)
        {
            if (!(view is ViewBase viewBase)) return;
            
            var renderer = viewBase.GetGraphicsRenderer();
            if (renderer == null)
            {
                polyline.RenderBasic(viewBase);
                return;
            }

            var properties = polyline.GetRenderingProperties();
            var strokePaint = properties.GetStrokePaint();

            // 专业多段线渲染（支持变宽度）
            RenderPolylineProfessional(polyline, renderer, strokePaint);

            if (polyline.IsSelected)
            {
                RenderPolylineSelection(polyline, renderer);
            }
        }

        #region 专业渲染实现方法
        
        /// <summary>
        /// 专业椭圆渲染
        /// </summary>
        private static void RenderEllipseProfessional(EntityEllipse ellipse, IGraphicsRenderer renderer, 
            SKPaint strokePaint, SKPaint fillPaint)
        {
            // 先填充后描边
            if (fillPaint != null)
            {
                renderer.DrawEllipse(ellipse.Center, ellipse.RadiusX, ellipse.RadiusY, fillPaint, CoordinateSpace.Model);
            }
            
            if (strokePaint != null)
            {
                // 检查是否需要旋转
                if (Math.Abs(ellipse.Rotation) > double.Epsilon)
                {
                    // 使用变换矩阵渲染旋转椭圆
                    var matrix = SKMatrix.CreateRotationDegrees((float)ellipse.Rotation, ellipse.Center.X, ellipse.Center.Y);
                    renderer.PushMatrix(matrix);
                    renderer.DrawEllipse(ellipse.Center, ellipse.RadiusX, ellipse.RadiusY, strokePaint, CoordinateSpace.Model);
                    renderer.PopMatrix();
                }
                else
                {
                    renderer.DrawEllipse(ellipse.Center, ellipse.RadiusX, ellipse.RadiusY, strokePaint, CoordinateSpace.Model);
                }
            }
        }

        /// <summary>
        /// 专业椭圆弧渲染
        /// </summary>
        private static void RenderEllipticalArcProfessional(EntityEllipse ellipse, IGraphicsRenderer renderer,
            SKPaint strokePaint, SKPaint fillPaint)
        {
            // 创建椭圆弧路径
            var path = CreateEllipticalArcPath(ellipse);
            
            if (fillPaint != null && ellipse.IsClosed)
            {
                renderer.DrawPath(path, fillPaint, CoordinateSpace.Model);
            }
            
            if (strokePaint != null)
            {
                renderer.DrawPath(path, strokePaint, CoordinateSpace.Model);
            }
            
            path.Dispose();
        }

        /// <summary>
        /// 专业多边形渲染
        /// </summary>
        private static void RenderPolygonProfessional(EntityPolygon polygon, IGraphicsRenderer renderer,
            SKPaint strokePaint, SKPaint fillPaint)
        {
            var vertices = polygon.Vertices;
            if (vertices.Length < 3) return;

            if (fillPaint != null)
            {
                renderer.DrawPolygon(vertices, fillPaint, CoordinateSpace.Model);
            }
            
            if (strokePaint != null)
            {
                renderer.DrawPolygon(vertices, strokePaint, CoordinateSpace.Model);
            }
        }

        /// <summary>
        /// 专业多段线渲染
        /// </summary>
        private static void RenderPolylineProfessional(EntityPolylineAdvanced polyline, IGraphicsRenderer renderer,
            SKPaint strokePaint)
        {
            if (strokePaint == null || polyline.Vertices.Count < 2) return;

            // 构建复合路径（包含直线和弧段）
            var path = CreatePolylinePath(polyline);
            
            // 检查是否需要变宽度渲染
            if (polyline.UseGlobalWidth && polyline.GlobalWidth > 0)
            {
                strokePaint.StrokeWidth = (float)polyline.GlobalWidth;
            }
            
            renderer.DrawPath(path, strokePaint, CoordinateSpace.Model);
            path.Dispose();
        }

        /// <summary>
        /// 渲染选择高亮
        /// </summary>
        private static void RenderSelectionHighlight(EntityBase entity, IGraphicsRenderer renderer)
        {
            var highlightPaint = new SKPaint
            {
                Color = SKColors.Cyan,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0)
            };

            // 渲染选择框
            var bounds = entity.BoundingBox;
            if (!bounds.IsEmpty)
            {
                renderer.DrawRectangle(
                    new Vector2((float)bounds.Left, (float)bounds.Top), 
                    bounds.Width, 
                    bounds.Height, 
                    highlightPaint, 
                    CoordinateSpace.Model
                );
            }

            highlightPaint.Dispose();
        }

        /// <summary>
        /// 渲染多边形选择控制点
        /// </summary>
        private static void RenderPolygonSelection(EntityPolygon polygon, IGraphicsRenderer renderer)
        {
            var controlPaint = new SKPaint
            {
                Color = SKColors.Blue,
                Style = SKPaintStyle.Fill
            };

            // 中心点
            renderer.DrawCircle(polygon.Center, 3, controlPaint, CoordinateSpace.Model);
            
            // 顶点控制点
            foreach (var vertex in polygon.Vertices)
            {
                renderer.DrawRectangle(vertex - new Vector2(2, 2), 4, 4, controlPaint, CoordinateSpace.Model);
            }

            controlPaint.Dispose();
        }

        /// <summary>
        /// 渲染多段线选择控制点
        /// </summary>
        private static void RenderPolylineSelection(EntityPolylineAdvanced polyline, IGraphicsRenderer renderer)
        {
            var vertexPaint = new SKPaint
            {
                Color = SKColors.Green,
                Style = SKPaintStyle.Fill
            };

            var arcPaint = new SKPaint
            {
                Color = SKColors.Orange,
                Style = SKPaintStyle.Fill
            };

            // 渲染顶点控制点
            foreach (var vertex in polyline.Vertices)
            {
                var paint = vertex.IsArc ? arcPaint : vertexPaint;
                renderer.DrawCircle(vertex.Position, 3, paint, CoordinateSpace.Model);
            }

            vertexPaint.Dispose();
            arcPaint.Dispose();
        }
        #endregion

        #region 路径创建辅助方法

        /// <summary>
        /// 创建椭圆弧路径
        /// </summary>
        private static SKPath CreateEllipticalArcPath(EntityEllipse ellipse)
        {
            var path = new SKPath();
            var rect = new SKRect(
                (float)(ellipse.Center.X - ellipse.RadiusX),
                (float)(ellipse.Center.Y - ellipse.RadiusY),
                (float)(ellipse.Center.X + ellipse.RadiusX),
                (float)(ellipse.Center.Y + ellipse.RadiusY)
            );

            var sweepAngle = ellipse.EndAngle - ellipse.StartAngle;
            if (sweepAngle < 0) sweepAngle += 360;

            path.AddArc(rect, (float)ellipse.StartAngle, (float)sweepAngle);

            // 应用旋转
            if (Math.Abs(ellipse.Rotation) > double.Epsilon)
            {
                var matrix = SKMatrix.CreateRotationDegrees((float)ellipse.Rotation, ellipse.Center.X, ellipse.Center.Y);
                path.Transform(matrix);
            }

            return path;
        }

        /// <summary>
        /// 创建多段线路径
        /// </summary>
        private static SKPath CreatePolylinePath(EntityPolylineAdvanced polyline)
        {
            var path = new SKPath();
            var vertices = polyline.Vertices;
            
            if (vertices.Count < 2) return path;

            path.MoveTo(vertices[0].Position.X, vertices[0].Position.Y);

            for (int i = 0; i < vertices.Count - 1; i++)
            {
                var current = vertices[i];
                var next = vertices[i + 1];

                if (current.IsArc)
                {
                    // 添加弧段
                    AddArcSegmentToPath(path, current, next);
                }
                else
                {
                    // 添加直线段
                    path.LineTo(next.Position.X, next.Position.Y);
                }
            }

            // 闭合路径
            if (polyline.IsClosed && vertices.Count > 2)
            {
                var lastVertex = vertices[vertices.Count - 1];
                var firstVertex = vertices[0];

                if (lastVertex.IsArc)
                {
                    AddArcSegmentToPath(path, lastVertex, firstVertex);
                }
                else
                {
                    path.LineTo(firstVertex.Position.X, firstVertex.Position.Y);
                }
                path.Close();
            }

            return path;
        }

        /// <summary>
        /// 向路径添加弧段
        /// </summary>
        private static void AddArcSegmentToPath(SKPath path, PolylineVertex startVertex, PolylineVertex endVertex)
        {
            var bulge = startVertex.Bulge;
            if (Math.Abs(bulge) < 1e-10)
            {
                // 实际是直线
                path.LineTo(endVertex.Position.X, endVertex.Position.Y);
                return;
            }

            // 计算弧段参数
            var p1 = startVertex.Position;
            var p2 = endVertex.Position;
            var chordLength = Vector2.Distance(p1, p2);
            var sagitta = Math.Abs(bulge) * chordLength * 0.5;
            var radius = (chordLength * chordLength * 0.25 + sagitta * sagitta) / (2.0 * sagitta);

            // 计算圆心
            var midPoint = (p1 + p2) * 0.5f;
            var chordVector = p2 - p1;
            var chordAngle = Math.Atan2(chordVector.Y, chordVector.X);
            var perpAngle = chordAngle + (bulge > 0 ? Math.PI * 0.5 : -Math.PI * 0.5);
            var distanceToCenter = radius - sagitta;
            var center = midPoint + (float)distanceToCenter * new Vector2((float)Math.Cos(perpAngle), (float)Math.Sin(perpAngle));

            // 计算角度
            var startAngle = Math.Atan2(p1.Y - center.Y, p1.X - center.X) * 180.0 / Math.PI;
            var endAngle = Math.Atan2(p2.Y - center.Y, p2.X - center.X) * 180.0 / Math.PI;
            var sweepAngle = endAngle - startAngle;

            if (bulge < 0)
            {
                if (sweepAngle > 0) sweepAngle -= 360.0;
            }
            else
            {
                if (sweepAngle < 0) sweepAngle += 360.0;
            }

            // 添加弧到路径
            var rect = new SKRect(
                (float)(center.X - radius),
                (float)(center.Y - radius),
                (float)(center.X + radius),
                (float)(center.Y + radius)
            );

            path.ArcTo(rect, (float)startAngle, (float)sweepAngle, false);
        }
        #endregion
    }

    /// <summary>
    /// 图元渲染属性扩展方法
    /// </summary>
    public static class EntityRenderingExtensions
    {
        /// <summary>
        /// 获取图元的渲染属性
        /// </summary>
        public static EntityProperties GetRenderingProperties(this EntityBase entity)
        {
            // 如果图元已有属性系统，返回它；否则创建默认属性
            if (entity.Tag is EntityProperties properties)
            {
                return properties;
            }

            // 创建基于当前图元状态的默认属性
            var defaultProps = new EntityProperties
            {
                Color = entity.Color,
                LineType = LineTypeStyle.Continuous,
                LineWeight = LineWeightValue.Default,
                FillType = FillType.None,
                Visible = entity.IsVisible
            };

            // 缓存属性到Tag中
            entity.Tag = defaultProps;
            return defaultProps;
        }

        /// <summary>
        /// 设置图元的渲染属性
        /// </summary>
        public static void SetRenderingProperties(this EntityBase entity, EntityProperties properties)
        {
            entity.Tag = properties;
            entity.Color = properties.Color;
            entity.IsVisible = properties.Visible;
        }

        /// <summary>
        /// 基础渲染方法（降级方案）
        /// </summary>
        public static void RenderBasic(this EntityBase entity, ViewBase viewBase)
        {
            // 调用原有的基础渲染方法
            entity.Render(viewBase);
        }
    }
} 