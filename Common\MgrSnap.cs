﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace McLaser.EditViewerSk.Common
{
    /// <summary>
    /// 高级对象捕捉管理器
    /// 实现专业CAD软件级别的对象捕捉功能
    /// </summary>
    public class MgrSnap
    {
        private ViewBase _viewer = null;
        private ObjectSnapPoint _currObjectSnapPoint = null;
        private float _threshold = 10.0f; // 捕捉阈值
        
        // 运行中的捕捉模式
        private ObjectSnapMode _runningSnapModes = ObjectSnapMode.Basic;
        
        // 临时捕捉模式（一次性使用）
        private ObjectSnapMode _temporarySnapMode = ObjectSnapMode.Undefined;
        
        // 追踪点缓存
        private List<Vector2> _trackingPoints = new List<Vector2>();
        
        public ObjectSnapPoint currentObjectSnapPoint
        {
            get { return _currObjectSnapPoint; }
        }
        
        public ObjectSnapMode RunningSnapModes
        {
            get { return _runningSnapModes; }
            set { _runningSnapModes = value; }
        }

        public MgrSnap(ViewBase viewer)
        {
            _viewer = viewer;
        }

        /// <summary>
        /// 设置临时捕捉模式（用于一次性捕捉）
        /// </summary>
        public void SetTemporarySnapMode(ObjectSnapMode mode)
        {
            _temporarySnapMode = mode;
        }

        public Vector2 Snap(float x, float y)
        {
            return this.Snap(new Vector2(x, y));
        }

        /// <summary>
        /// 主要的捕捉方法，支持所有高级捕捉模式
        /// </summary>
        public Vector2 Snap(Vector2 posInCanvas)
        {
            Vector2 posInModel = _viewer.CanvasToModel(posInCanvas);
            
            // 确定当前要使用的捕捉模式
            ObjectSnapMode currentModes = _temporarySnapMode != ObjectSnapMode.Undefined ? 
                _temporarySnapMode : _runningSnapModes;
            
            _currObjectSnapPoint = null;
            
            // 按优先级顺序检查各种捕捉模式
            if (HasSnapMode(currentModes, ObjectSnapMode.Intersection) && 
                TrySnapToIntersection(posInModel))
            {
                return _currObjectSnapPoint.position;
            }
            
            if (HasSnapMode(currentModes, ObjectSnapMode.Extension) && 
                TrySnapToExtension(posInModel))
            {
                return _currObjectSnapPoint.position;
            }
            
            if (HasSnapMode(currentModes, ObjectSnapMode.GeometricCenter) && 
                TrySnapToGeometricCenter(posInModel))
            {
                return _currObjectSnapPoint.position;
            }
            
            if (HasSnapMode(currentModes, ObjectSnapMode.ApparentIntersection) && 
                TrySnapToApparentIntersection(posInModel))
            {
                return _currObjectSnapPoint.position;
            }
            
            if (HasSnapMode(currentModes, ObjectSnapMode.Parallel) && 
                TrySnapToParallel(posInModel))
            {
                return _currObjectSnapPoint.position;
            }
            
            // 检查基础捕捉点（原有逻辑）
            if (TrySnapToBasicPoints(posInModel, currentModes))
            {
                return _currObjectSnapPoint.position;
            }
            
            // 网格捕捉
            if (HasSnapMode(currentModes, ObjectSnapMode.Grid))
            {
                return SnapToGrid(posInModel);
            }
            
            // 清除临时捕捉模式
            _temporarySnapMode = ObjectSnapMode.Undefined;
            
            return posInModel;
        }

        /// <summary>
        /// 检查是否启用了指定的捕捉模式
        /// </summary>
        private bool HasSnapMode(ObjectSnapMode modes, ObjectSnapMode targetMode)
        {
            return (modes & targetMode) == targetMode;
        }

        /// <summary>
        /// 捕捉到交点
        /// </summary>
        private bool TrySnapToIntersection(Vector2 posInModel)
        {
            var entities = _viewer.Document.ActiveLayer.Children.ToList();
            
            for (int i = 0; i < entities.Count; i++)
            {
                for (int j = i + 1; j < entities.Count; j++)
                {
                    var intersections = CalculateIntersections(entities[i], entities[j]);
                    foreach (var intersection in intersections)
                    {
                        if (IsWithinThreshold(intersection, posInModel))
                        {
                            _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.Intersection, intersection);
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 捕捉到几何中心
        /// </summary>
        private bool TrySnapToGeometricCenter(Vector2 posInModel)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                if (entity is EntityPolygon polygon)
                {
                    var center = CalculateGeometricCenter(polygon);
                    if (IsWithinThreshold(center, posInModel))
                    {
                        _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.GeometricCenter, center);
                        return true;
                    }
                }
                else if (entity is EntityRectangle rectangle)
                {
                    var center = CalculateGeometricCenter(rectangle);
                    if (IsWithinThreshold(center, posInModel))
                    {
                        _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.GeometricCenter, center);
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 捕捉到延伸线
        /// </summary>
        private bool TrySnapToExtension(Vector2 posInModel)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                if (entity is EntityLine line)
                {
                    var extendedPoint = CalculateExtensionPoint(line, posInModel);
                    if (extendedPoint.HasValue && IsWithinThreshold(extendedPoint.Value, posInModel))
                    {
                        _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.Extension, extendedPoint.Value);
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 捕捉到虚交点
        /// </summary>
        private bool TrySnapToApparentIntersection(Vector2 posInModel)
        {
            var entities = _viewer.Document.ActiveLayer.Children.ToList();
            
            for (int i = 0; i < entities.Count; i++)
            {
                for (int j = i + 1; j < entities.Count; j++)
                {
                    var apparentIntersection = CalculateApparentIntersection(entities[i], entities[j]);
                    if (apparentIntersection.HasValue && IsWithinThreshold(apparentIntersection.Value, posInModel))
                    {
                        _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.ApparentIntersection, apparentIntersection.Value);
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 捕捉到平行线
        /// </summary>
        private bool TrySnapToParallel(Vector2 posInModel)
        {
            var cmd = _viewer._cmdsMgr.CurrentCmd;
            if (cmd is LineCmd lineCmd && lineCmd.CurLine?.StartPoint != null)
            {
                var startPoint = lineCmd.CurLine.StartPoint.Value;
                
                foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
                {
                    if (entity is EntityLine referenceLine)
                    {
                        var parallelPoint = CalculateParallelPoint(startPoint, referenceLine, posInModel);
                        if (parallelPoint.HasValue && IsWithinThreshold(parallelPoint.Value, posInModel))
                        {
                            _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.Parallel, parallelPoint.Value);
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 基础捕捉点检查（重构原有逻辑）
        /// </summary>
        private bool TrySnapToBasicPoints(Vector2 posInModel, ObjectSnapMode currentModes)
        {
            var cmd = _viewer._cmdsMgr.CurrentCmd;

            if (cmd != null)
            {
                if (cmd is LineCmd lineCmd)
                {
                    if (TrySnapToEntities(lineCmd.CurLine?.StartPoint ?? Vector2.Zero, posInModel, currentModes))
                    {
                        return true;
                    }
                }
                else if (cmd is PolylineCmd polylineCmd)
                {
                    if (TrySnapToEntities(polylineCmd.CurLine?.StartPoint ?? Vector2.Zero, posInModel, currentModes))
                    {
                        return true;
                    }
                }
                else
                {
                    if (TrySnapToEntityPoints(posInModel, currentModes))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        // 处理直线和圆的切点，以及相关的snap点
        private bool TrySnapToEntities(Vector2 startPoint, Vector2 posInModel, ObjectSnapMode currentModes)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                // 处理切点
                if (HasSnapMode(currentModes, ObjectSnapMode.Tangent) && 
                    entity is EntityCircle circle && startPoint != Vector2.Zero)
                {
                    if (CalculateTangentPoints(startPoint, circle.Center, circle.Radius, out List<Vector2> tangentPoints))
                    {
                        foreach (var point in tangentPoints)
                        {
                            if (IsWithinThreshold(point, posInModel))
                            {
                                _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.Tangent, point);
                                return true;
                            }
                        }
                    }
                }

                // 处理其他基础捕捉点
                var snapPoints = entity.GetSnapPoints();
                if (snapPoints != null && snapPoints.Count > 0)
                {
                    foreach (ObjectSnapPoint snapPnt in snapPoints)
                    {
                        // 检查捕捉模式是否启用
                        if (HasSnapMode(currentModes, snapPnt.type) && IsWithinThreshold(snapPnt.position, posInModel))
                        {
                            _currObjectSnapPoint = snapPnt;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        // 处理snap点
        private bool TrySnapToEntityPoints(Vector2 posInModel, ObjectSnapMode currentModes)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                var snapPoints = entity.GetSnapPoints();
                if (snapPoints != null && snapPoints.Count > 0)
                {
                    foreach (ObjectSnapPoint snapPnt in snapPoints)
                    {
                        if (HasSnapMode(currentModes, snapPnt.type) && IsWithinThreshold(snapPnt.position, posInModel))
                        {
                            _currObjectSnapPoint = snapPnt;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 计算两个实体的交点
        /// </summary>
        private List<Vector2> CalculateIntersections(EntityBase entity1, EntityBase entity2)
        {
            var intersections = new List<Vector2>();
            
            // 线与线的交点
            if (entity1 is EntityLine line1 && entity2 is EntityLine line2)
            {
                var intersection = CalculateLineLineIntersection(line1, line2);
                if (intersection.HasValue)
                    intersections.Add(intersection.Value);
            }
            // 线与圆的交点
            else if (entity1 is EntityLine line && entity2 is EntityCircle circle)
            {
                intersections.AddRange(CalculateLineCircleIntersections(line, circle));
            }
            else if (entity1 is EntityCircle circle2 && entity2 is EntityLine line3)
            {
                intersections.AddRange(CalculateLineCircleIntersections(line3, circle2));
            }
            // 圆与圆的交点
            else if (entity1 is EntityCircle circle3 && entity2 is EntityCircle circle4)
            {
                intersections.AddRange(CalculateCircleCircleIntersections(circle3, circle4));
            }
            
            return intersections;
        }

        /// <summary>
        /// 计算线线交点
        /// </summary>
        private Vector2? CalculateLineLineIntersection(EntityLine line1, EntityLine line2)
        {
            var p1 = line1.StartPoint;
            var p2 = line1.EndPoint;
            var p3 = line2.StartPoint;
            var p4 = line2.EndPoint;
            
            var denom = (p1.X - p2.X) * (p3.Y - p4.Y) - (p1.Y - p2.Y) * (p3.X - p4.X);
            if (Math.Abs(denom) < 1e-10) return null; // 平行线
            
            var t = ((p1.X - p3.X) * (p3.Y - p4.Y) - (p1.Y - p3.Y) * (p3.X - p4.X)) / denom;
            
            return new Vector2(
                p1.X + t * (p2.X - p1.X),
                p1.Y + t * (p2.Y - p1.Y)
            );
        }

        /// <summary>
        /// 计算线圆交点
        /// </summary>
        private List<Vector2> CalculateLineCircleIntersections(EntityLine line, EntityCircle circle)
        {
            var intersections = new List<Vector2>();
            
            var dx = line.EndPoint.X - line.StartPoint.X;
            var dy = line.EndPoint.Y - line.StartPoint.Y;
            var fx = line.StartPoint.X - circle.Center.X;
            var fy = line.StartPoint.Y - circle.Center.Y;
            
            var a = dx * dx + dy * dy;
            var b = 2 * (fx * dx + fy * dy);
            var c = fx * fx + fy * fy - circle.Radius * circle.Radius;
            
            var discriminant = b * b - 4 * a * c;
            if (discriminant >= 0)
            {
                var sqrt = Math.Sqrt(discriminant);
                var t1 = (-b - sqrt) / (2 * a);
                var t2 = (-b + sqrt) / (2 * a);
                
                if (t1 >= 0 && t1 <= 1)
                {
                    intersections.Add(new Vector2(
                        line.StartPoint.X + t1 * dx,
                        line.StartPoint.Y + t1 * dy
                    ));
                }
                
                if (t2 >= 0 && t2 <= 1 && Math.Abs(t1 - t2) > 1e-10)
                {
                    intersections.Add(new Vector2(
                        line.StartPoint.X + t2 * dx,
                        line.StartPoint.Y + t2 * dy
                    ));
                }
            }
            
            return intersections;
        }

        /// <summary>
        /// 计算圆圆交点
        /// </summary>
        private List<Vector2> CalculateCircleCircleIntersections(EntityCircle circle1, EntityCircle circle2)
        {
            var intersections = new List<Vector2>();
            
            var dx = circle2.Center.X - circle1.Center.X;
            var dy = circle2.Center.Y - circle1.Center.Y;
            var d = Math.Sqrt(dx * dx + dy * dy);
            
            if (d > circle1.Radius + circle2.Radius || d < Math.Abs(circle1.Radius - circle2.Radius) || d == 0)
                return intersections;
            
            var a = (circle1.Radius * circle1.Radius - circle2.Radius * circle2.Radius + d * d) / (2 * d);
            var h = Math.Sqrt(circle1.Radius * circle1.Radius - a * a);
            
            var px = circle1.Center.X + a * dx / d;
            var py = circle1.Center.Y + a * dy / d;
            
            intersections.Add(new Vector2(
                (float)(px + h * dy / d),
                (float)(py - h * dx / d)
            ));
            
            if (h > 1e-10)
            {
                intersections.Add(new Vector2(
                    (float)(px - h * dy / d),
                    (float)(py + h * dx / d)
                ));
            }
            
            return intersections;
        }

        /// <summary>
        /// 计算几何中心
        /// </summary>
        private Vector2 CalculateGeometricCenter(EntityPolygon polygon)
        {
            // 实现多边形几何中心计算
            var vertices = polygon.GetVertices();
            if (vertices == null || vertices.Count == 0)
                return Vector2.Zero;
                
            float sumX = 0, sumY = 0;
            foreach (var vertex in vertices)
            {
                sumX += vertex.X;
                sumY += vertex.Y;
            }
            
            return new Vector2(sumX / vertices.Count, sumY / vertices.Count);
        }

        private Vector2 CalculateGeometricCenter(EntityRectangle rectangle)
        {
            var bounds = rectangle.BoundingBox;
            return new Vector2(
                (bounds.MinX + bounds.MaxX) / 2,
                (bounds.MinY + bounds.MaxY) / 2
            );
        }

        /// <summary>
        /// 计算延伸点
        /// </summary>
        private Vector2? CalculateExtensionPoint(EntityLine line, Vector2 targetPoint)
        {
            var direction = Vector2.Normalize(line.EndPoint - line.StartPoint);
            var toTarget = targetPoint - line.EndPoint;
            var projection = Vector2.Dot(toTarget, direction);
            
            if (projection > 0) // 延伸到终点之外
            {
                return line.EndPoint + direction * projection;
            }
            
            // 检查起点延伸
            toTarget = targetPoint - line.StartPoint;
            projection = Vector2.Dot(toTarget, -direction);
            
            if (projection > 0) // 延伸到起点之外
            {
                return line.StartPoint - direction * projection;
            }
            
            return null;
        }

        /// <summary>
        /// 计算虚交点
        /// </summary>
        private Vector2? CalculateApparentIntersection(EntityBase entity1, EntityBase entity2)
        {
            // 将实体投影到当前视图平面，然后计算交点
            // 这里简化实现，实际应该考虑3D投影
            return CalculateIntersections(entity1, entity2).FirstOrDefault();
        }

        /// <summary>
        /// 计算平行点
        /// </summary>
        private Vector2? CalculateParallelPoint(Vector2 startPoint, EntityLine referenceLine, Vector2 targetPoint)
        {
            var refDirection = Vector2.Normalize(referenceLine.EndPoint - referenceLine.StartPoint);
            var toTarget = targetPoint - startPoint;
            var projection = Vector2.Dot(toTarget, refDirection);
            
            return startPoint + refDirection * projection;
        }

        private bool IsWithinThreshold(Vector2 point, Vector2 posInModel)
        {
            double distance = (point - posInModel).Length();
            return Math.Abs(_viewer.ModelToCanvas(distance)) <= _threshold;
        }

        private Vector2 SnapToGrid(Vector2 posInModel)
        {
            int index = _viewer.GridSnaps.FindIndex(item => Math.Abs(_viewer.ModelToCanvas((item.position - posInModel).Length())) <= _threshold);
            if (index != -1)
            {
                _currObjectSnapPoint = _viewer.GridSnaps[index];
                return _currObjectSnapPoint.position;
            }

            _currObjectSnapPoint = null;
            return posInModel;
        }

        public bool CalculateTangentPoints(Vector2 externalPoint, Vector2 centerPoint, double radius, out List<Vector2> tangentPoints)
        {
            tangentPoints = new List<Vector2>();

            // 1. 计算圆外一点到圆心的距离
            double distance = Math.Sqrt(Math.Pow(externalPoint.X - centerPoint.X, 2) + Math.Pow(externalPoint.Y - centerPoint.Y, 2));

            // 2. 处理点在圆内或圆上的情况
            if (distance <= radius)
            {
                return false; // 点在圆内或圆上，无切点
            }

            // 3. 计算切点
            double angle = Math.Acos(radius / distance);
            double baseAngle = Math.Atan2(externalPoint.Y - centerPoint.Y, externalPoint.X - centerPoint.X);

            // 两个切点
            var tangent1 = new Vector2(
                (float)(centerPoint.X + radius * Math.Cos(baseAngle + angle)),
                (float)(centerPoint.Y + radius * Math.Sin(baseAngle + angle))
            );
            
            var tangent2 = new Vector2(
                (float)(centerPoint.X + radius * Math.Cos(baseAngle - angle)),
                (float)(centerPoint.Y + radius * Math.Sin(baseAngle - angle))
            );

            tangentPoints.Add(tangent1);
            tangentPoints.Add(tangent2);

            return true;
        }

        public void Clear()
        {
            _currObjectSnapPoint = null;
        }

        private double _threshold = 8;
        public void OnPaint(ViewBase viewer)
        {
            if (_currObjectSnapPoint != null)
            {
                SKPaint pen = new SKPaint()
                {
                    Color = SKColors.DeepPink,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.Stroke,
                    IsAntialias = true,
                };

                SKPaint pen1 = new SKPaint()
                {
                    TextSize = 16,
                    Typeface = SKTypeface.FromFamilyName("微软雅黑"),
                    Color = SKColors.Black,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.Fill,
                    IsAntialias = true,
                };

                SKPaint rectPen = new SKPaint()
                {
                    Color = SKColors.Gold,
                    IsStroke = true,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.StrokeAndFill,
                    IsAntialias = true,
                };

                switch (_currObjectSnapPoint.type)
                {
                    case ObjectSnapMode.End:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("端点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;

                    case ObjectSnapMode.Mid:
                        {
                            Vector2 offset = new Vector2(0, (float)(_threshold * 0.6));
                            Vector2 point1 = _currObjectSnapPoint.position + offset;

                            offset = Rotate(offset, 120);
                            Vector2 point2 = _currObjectSnapPoint.position + offset;
                            offset = Rotate(offset, 120);
                            Vector2 point3 = _currObjectSnapPoint.position + offset;

                            _viewer.DrawLine(point1, point2, pen);
                            _viewer.DrawLine(point2, point3, pen);
                            _viewer.DrawLine(point3, point1, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("中点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;

                    case ObjectSnapMode.Center:
                        {
                            Vector2 offset = new Vector2(0, (float)(_threshold * 0.6));
                            Vector2 point1 = _currObjectSnapPoint.position + offset;

                            offset = Rotate(offset, 120);
                            Vector2 point2 = _currObjectSnapPoint.position + offset;
                            offset = Rotate(offset, 120);
                            Vector2 point3 = _currObjectSnapPoint.position + offset;

                            _viewer.DrawLine(point1, point2, pen);
                            _viewer.DrawLine(point2, point3, pen);
                            _viewer.DrawLine(point3, point1, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("中心点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Tangent:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("切点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Quad:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("四分点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Grid:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                        }
                        break;

                    default:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                        }
                        break;
                }
            }
        }

        Vector2 Rotate(Vector2 vector, float angle)
        {
            float radians = (float)(Math.PI / 180 * angle);
            float cos = (float)Math.Cos(radians);
            float sin = (float)Math.Sin(radians);
            return new Vector2(
                vector.X * cos - vector.Y * sin,
                vector.X * sin + vector.Y * cos
            );
        }
    }
}
