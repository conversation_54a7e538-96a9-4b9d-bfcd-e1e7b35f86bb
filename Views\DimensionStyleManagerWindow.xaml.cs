using McLaser.EditViewerSk.Managers;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SkiaSharp;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 标注样式管理器窗口
    /// </summary>
    public partial class DimensionStyleManagerWindow : Window
    {
        private DimensionStyleManager _styleManager;
        private DimensionStyle _currentStyle;
        private bool _isUpdating = false;

        public DimensionStyleManagerWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            try
            {
                _styleManager = DimensionStyleManager.Instance;
                LoadStyles();
                LoadFonts();
                
                // 选择第一个样式
                if (StyleListBox.Items.Count > 0)
                {
                    StyleListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化标注样式管理器失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadStyles()
        {
            StyleListBox.ItemsSource = _styleManager.Styles;
        }

        private void LoadFonts()
        {
            // 加载系统字体
            FontComboBox.Items.Clear();
            FontComboBox.Items.Add("Arial");
            FontComboBox.Items.Add("Times New Roman");
            FontComboBox.Items.Add("Calibri");
            FontComboBox.Items.Add("宋体");
            FontComboBox.Items.Add("黑体");
            FontComboBox.Items.Add("微软雅黑");
            FontComboBox.SelectedIndex = 0;
        }

        private void StyleListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            var selectedStyle = StyleListBox.SelectedItem as DimensionStyle;
            if (selectedStyle != null)
            {
                _currentStyle = selectedStyle;
                LoadStyleProperties();
            }
        }

        private void LoadStyleProperties()
        {
            if (_currentStyle == null) return;
            
            _isUpdating = true;
            try
            {
                // 基本信息
                StyleNameTextBox.Text = _currentStyle.Name;
                DescriptionTextBox.Text = _currentStyle.Description;
                
                // 文本属性
                TextHeightTextBox.Text = _currentStyle.TextHeight.ToString();
                FontComboBox.Text = _currentStyle.TextFont;
                DecimalPlacesTextBox.Text = _currentStyle.DecimalPlaces.ToString();
                
                // 箭头属性
                ArrowSizeTextBox.Text = _currentStyle.ArrowSize.ToString();
                ShowArrowsCheckBox.IsChecked = _currentStyle.ShowArrows;
                
                // 延伸线属性
                ExtensionLineOffsetTextBox.Text = _currentStyle.ExtensionLineOffset.ToString();
                ExtensionLineExtendTextBox.Text = _currentStyle.ExtensionLineExtend.ToString();
                ShowExtensionLinesCheckBox.IsChecked = _currentStyle.ShowExtensionLines;
                
                // 单位和格式
                PrefixTextBox.Text = _currentStyle.Prefix;
                SuffixTextBox.Text = _currentStyle.Suffix;
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void SaveStyleProperties()
        {
            if (_currentStyle == null || _isUpdating) return;
            
            try
            {
                // 基本信息
                _currentStyle.Name = StyleNameTextBox.Text;
                _currentStyle.Description = DescriptionTextBox.Text;
                
                // 文本属性
                if (double.TryParse(TextHeightTextBox.Text, out double textHeight))
                    _currentStyle.TextHeight = textHeight;
                
                _currentStyle.TextFont = FontComboBox.Text;
                
                if (int.TryParse(DecimalPlacesTextBox.Text, out int decimalPlaces))
                    _currentStyle.DecimalPlaces = decimalPlaces;
                
                // 箭头属性
                if (double.TryParse(ArrowSizeTextBox.Text, out double arrowSize))
                    _currentStyle.ArrowSize = arrowSize;
                
                _currentStyle.ShowArrows = ShowArrowsCheckBox.IsChecked ?? true;
                
                // 延伸线属性
                if (double.TryParse(ExtensionLineOffsetTextBox.Text, out double offset))
                    _currentStyle.ExtensionLineOffset = offset;
                
                if (double.TryParse(ExtensionLineExtendTextBox.Text, out double extend))
                    _currentStyle.ExtensionLineExtend = extend;
                
                _currentStyle.ShowExtensionLines = ShowExtensionLinesCheckBox.IsChecked ?? true;
                
                // 单位和格式
                _currentStyle.Prefix = PrefixTextBox.Text;
                _currentStyle.Suffix = SuffixTextBox.Text;
                
                // 刷新列表显示
                StyleListBox.Items.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存样式属性失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NewStyle_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newStyle = new DimensionStyle
                {
                    Name = "新样式",
                    Description = "新建的标注样式",
                    TextHeight = 3.0,
                    ArrowSize = 3.0,
                    DecimalPlaces = 2,
                    TextFont = "Arial",
                    Prefix = "",
                    Suffix = "",
                    ShowArrows = true,
                    ShowExtensionLines = true,
                    ExtensionLineOffset = 1.0,
                    ExtensionLineExtend = 1.5
                };
                
                _styleManager.AddStyle(newStyle);
                StyleListBox.SelectedItem = newStyle;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建新样式失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteStyle_Click(object sender, RoutedEventArgs e)
        {
            if (_currentStyle == null) return;
            
            var result = MessageBox.Show($"确定要删除样式 '{_currentStyle.Name}' 吗？", "确认删除", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _styleManager.RemoveStyle(_currentStyle);
                    
                    // 选择第一个样式
                    if (StyleListBox.Items.Count > 0)
                    {
                        StyleListBox.SelectedIndex = 0;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除样式失败: {ex.Message}", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CopyStyle_Click(object sender, RoutedEventArgs e)
        {
            if (_currentStyle == null) return;
            
            try
            {
                var copiedStyle = new DimensionStyle
                {
                    Name = _currentStyle.Name + "_副本",
                    Description = _currentStyle.Description,
                    TextHeight = _currentStyle.TextHeight,
                    ArrowSize = _currentStyle.ArrowSize,
                    DecimalPlaces = _currentStyle.DecimalPlaces,
                    TextFont = _currentStyle.TextFont,
                    Prefix = _currentStyle.Prefix,
                    Suffix = _currentStyle.Suffix,
                    ShowArrows = _currentStyle.ShowArrows,
                    ShowExtensionLines = _currentStyle.ShowExtensionLines,
                    ExtensionLineOffset = _currentStyle.ExtensionLineOffset,
                    ExtensionLineExtend = _currentStyle.ExtensionLineExtend
                };
                
                _styleManager.AddStyle(copiedStyle);
                StyleListBox.SelectedItem = copiedStyle;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制样式失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TextColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                if (colorPicker.ShowDialog() == true)
                {
                    if (_currentStyle != null)
                    {
                        // TODO: 将选中的颜色应用到当前样式
                        var selectedColor = colorPicker.SelectedSKColor;
                        // _currentStyle.TextColor = selectedColor;

                        // 更新按钮显示
                        TextColorButton.Content = $"颜色: #{selectedColor.Red:X2}{selectedColor.Green:X2}{selectedColor.Blue:X2}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            SaveStyleProperties();
            MessageBox.Show("样式已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveStyleProperties();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
