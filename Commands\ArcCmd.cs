﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    internal class ArcCmd : DrawCmd
    {
        /// <summary>
        /// 绘制的圆弧
        /// </summary>
        private EntityArc _arc = null;
        private Vector2 tmpPoint = Vector2.Zero;
        private DocumentBase doc;


        public ArcCmd(DocumentBase doc)
        {
            this.doc = doc;
        }
        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityArc[1] { _arc }; }
        }

   
        private Step _step = Step.Step1_SpecifyCenter;
        private enum Step
        {
            Step1_SpecifyCenter = 1,
            Step2_SpecityStartPoint = 2,
            Step3_SpecifyEndPoint = 3,
        }

     
        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyCenter;
            this.pointer.Mode = IndicatorMode.Locate;
            this.pointer.Document.Prompt = "指定圆弧的中心点:";
        }

        protected override void Commit()
        {
            if (this.newEntities != null && this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }


        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    if (e.Button == MouseButtons.Left)
                    {
                        _arc = new EntityArc();
                        _arc.Center = this.pointer.CurrentSnapPoint;
                        _arc.Radius = 0;
                        _arc.LayerId = doc.ActiveLayer?.Id ?? 0;
                        _arc.Color = doc.ActiveLayer?.Color ?? SkiaSharp.SKColors.White;

                        this.newEntities.Add(_arc);
                        _step = Step.Step2_SpecityStartPoint;
                        this.pointer.Document.Prompt = "指定圆弧的起点:";
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;

                case Step.Step2_SpecityStartPoint:
                    if (e.Button == MouseButtons.Left)
                    {
                        tmpPoint = this.pointer.CurrentSnapPoint;
                        _arc.Radius = (_arc.Center - tmpPoint).Length;

                        double startAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            tmpPoint - _arc.Center);
                        startAngle = MathHelper.NormalizeRadianAngle(startAngle);
                        startAngle = MathHelper.ConvertRadiansToDegrees(startAngle);
                        _arc.StartAngle = NormalizeAngle((float)startAngle);
                        _arc.SweepAngle = 0;

                        _step = Step.Step3_SpecifyEndPoint;
                        this.pointer.Document.Prompt = "指定圆弧的终点:";
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;

                case Step.Step3_SpecifyEndPoint:

                    if (e.Button == MouseButtons.Left)
                    {
                        double endAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            this.pointer.CurrentSnapPoint - _arc.Center);
                        endAngle = MathHelper.NormalizeRadianAngle(endAngle);
                        endAngle = MathHelper.ConvertRadiansToDegrees(endAngle);

                        // 计算扫描角度并确保在0-360度范围内
                        float sweepAngle = CalculateSweepAngle(_arc.StartAngle, (float)endAngle);
                        _arc.SweepAngle = sweepAngle;

                        _mgr.FinishCurrentCommand();
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        _mgr.CancelCurrentCommand();
                    }
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Middle)
            {
                return EventResult.Handled;
            }

            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    break;

                case Step.Step2_SpecityStartPoint:
                    break;
                case Step.Step3_SpecifyEndPoint:
                    double endAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            this.pointer.CurrentSnapPoint - _arc.Center);
                    endAngle = MathHelper.NormalizeRadianAngle(endAngle);
                    endAngle = MathHelper.ConvertRadiansToDegrees(endAngle);

                    // 计算扫描角度并确保在0-360度范围内
                    float sweepAngle = CalculateSweepAngle(_arc.StartAngle, (float)endAngle);
                    _arc.SweepAngle = sweepAngle;
                    break;
            }

            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            if (_arc != null)
            {
                _arc.Render(_viewer);
            }

            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    break;

                case Step.Step2_SpecityStartPoint:
                    _viewer.DrawLine(
                        _arc.Center,
                        this.pointer.CurrentSnapPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });
                    break;

                case Step.Step3_SpecifyEndPoint:
                    _viewer.DrawLine(
                       _arc.Center,
                       tmpPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });

                    _viewer.DrawLine(
                        _arc.Center,
                        this.pointer.CurrentSnapPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });

                    break;
            }
        }

        /// <summary>
        /// 计算扫描角度，确保在0-360度范围内
        /// </summary>
        private float CalculateSweepAngle(float startAngle, float endAngle)
        {
            // 标准化角度到0-360范围
            startAngle = NormalizeAngle(startAngle);
            endAngle = NormalizeAngle(endAngle);

            float sweepAngle;
            if (endAngle >= startAngle)
            {
                sweepAngle = endAngle - startAngle;
            }
            else
            {
                sweepAngle = 360.0f + endAngle - startAngle;
            }

            // 确保扫描角度在0-360度范围内
            if (sweepAngle > 360.0f)
            {
                sweepAngle = 360.0f;
            }
            else if (sweepAngle < 0.0f)
            {
                sweepAngle = 0.0f;
            }

            return sweepAngle;
        }

        /// <summary>
        /// 标准化角度到0-360度范围
        /// </summary>
        private float NormalizeAngle(float angle)
        {
            while (angle < 0.0f)
            {
                angle += 360.0f;
            }
            while (angle >= 360.0f)
            {
                angle -= 360.0f;
            }
            return angle;
        }

        /// <summary>
        /// 添加ESC键处理
        /// </summary>
        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }
    }
}
