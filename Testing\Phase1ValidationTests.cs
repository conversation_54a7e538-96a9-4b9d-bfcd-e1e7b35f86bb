using System;
using System.Collections.Generic;
using System.Numerics;
using System.Diagnostics;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;

namespace McLaser.EditViewerSk.Testing
{
    /// <summary>
    /// 阶段1功能验证测试类
    /// 验证新增图元的数学精度、渲染正确性和性能表现
    /// </summary>
    public static class Phase1ValidationTests
    {
        private const double EPSILON = 1e-10;
        private const double TOLERANCE = 1e-6;

        /// <summary>
        /// 运行所有阶段1验证测试
        /// </summary>
        public static ValidationResult RunAllTests()
        {
            var result = new ValidationResult();
            
            Console.WriteLine("🚀 开始阶段1功能验证测试...\n");

            // 椭圆测试
            result.Merge(TestEllipse());
            
            // 正多边形测试  
            result.Merge(TestPolygon());
            
            // 高级多段线测试
            result.Merge(TestAdvancedPolyline());
            
            // 属性系统测试
            result.Merge(TestEntityProperties());
            
            // 包围盒计算测试
            result.Merge(TestBoundingBoxCalculator());
            
            // 性能测试
            result.Merge(TestPerformance());

            Console.WriteLine($"\n📊 测试完成统计:");
            Console.WriteLine($"✅ 通过: {result.PassedTests}");
            Console.WriteLine($"❌ 失败: {result.FailedTests}");
            Console.WriteLine($"⚠️  警告: {result.Warnings}");
            Console.WriteLine($"📈 总体评分: {result.OverallScore:F1}/10");

            return result;
        }

        #region 椭圆测试
        private static ValidationResult TestEllipse()
        {
            var result = new ValidationResult();
            Console.WriteLine("🔵 椭圆功能测试");

            try
            {
                // 测试1: 基础椭圆创建
                var ellipse = new EntityEllipse(new Vector2(0, 0), 10, 5);
                result.Assert(ellipse.RadiusX == 10, "椭圆X半径设置");
                result.Assert(ellipse.RadiusY == 5, "椭圆Y半径设置");
                result.Assert(ellipse.MajorRadius == 10, "长轴半径计算");
                result.Assert(ellipse.MinorRadius == 5, "短轴半径计算");

                // 测试2: 椭圆数学属性
                var expectedEccentricity = Math.Sqrt(1 - (5.0 * 5.0) / (10.0 * 10.0));
                result.Assert(Math.Abs(ellipse.Eccentricity - expectedEccentricity) < TOLERANCE, "离心率计算精度");
                
                var expectedArea = Math.PI * 10 * 5;
                result.Assert(Math.Abs(ellipse.Area - expectedArea) < TOLERANCE, "椭圆面积计算精度");

                // 测试3: 椭圆旋转
                ellipse.Rotation = 45;
                result.Assert(Math.Abs(ellipse.Rotation - 45) < EPSILON, "椭圆旋转角度设置");

                // 测试4: 椭圆弧功能
                var ellipticArc = new EntityEllipse(Vector2.Zero, 10, 5, 0, 180);
                result.Assert(ellipticArc.IsEllipticalArc, "椭圆弧标志");
                result.Assert(ellipticArc.StartAngle == 0, "椭圆弧起始角度");
                result.Assert(ellipticArc.EndAngle == 180, "椭圆弧结束角度");

                // 测试5: 包围盒计算
                ellipse.Rotation = 0; // 重置旋转
                ellipse.Regen();
                var bounds = ellipse.BoundingBox;
                result.Assert(Math.Abs(bounds.Width - 20) < TOLERANCE, "椭圆包围盒宽度");
                result.Assert(Math.Abs(bounds.Height - 10) < TOLERANCE, "椭圆包围盒高度");

                Console.WriteLine("  ✅ 椭圆基础功能验证通过");
                result.PassedTests += 5;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 椭圆测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion

        #region 正多边形测试
        private static ValidationResult TestPolygon()
        {
            var result = new ValidationResult();
            Console.WriteLine("🔺 正多边形功能测试");

            try
            {
                // 测试1: 内接正六边形
                var hexagon = new EntityPolygon(Vector2.Zero, 10, 6, EntityPolygon.PolygonType.Inscribed);
                result.Assert(hexagon.Sides == 6, "六边形边数");
                result.Assert(hexagon.InteriorAngle == 120, "六边形内角");
                result.Assert(hexagon.CentralAngle == 60, "六边形中心角");

                // 测试2: 边长计算验证
                var expectedSideLength = 2.0 * 10 * Math.Sin(Math.PI / 6); // 内接六边形边长
                result.Assert(Math.Abs(hexagon.SideLength - expectedSideLength) < TOLERANCE, "内接六边形边长计算");

                // 测试3: 外切正方形
                var square = new EntityPolygon(Vector2.Zero, 5, 4, EntityPolygon.PolygonType.Circumscribed);
                var expectedSquareSideLength = 2.0 * 5 * Math.Tan(Math.PI / 4); // 外切正方形边长
                result.Assert(Math.Abs(square.SideLength - expectedSquareSideLength) < TOLERANCE, "外切正方形边长计算");

                // 测试4: 顶点生成验证
                hexagon.Regen();
                var vertices = hexagon.Vertices;
                result.Assert(vertices.Length == 6, "六边形顶点数量");

                // 验证第一个顶点位置
                var firstVertex = vertices[0];
                var expectedFirstVertex = new Vector2(10, 0); // 0度位置
                result.Assert(Vector2.Distance(firstVertex, expectedFirstVertex) < TOLERANCE, "六边形第一个顶点位置");

                // 测试5: 面积计算
                var expectedArea = (6 * 10 * 10 / 2.0) * Math.Sin(2.0 * Math.PI / 6); // 内接六边形面积
                result.Assert(Math.Abs(hexagon.Area - expectedArea) < TOLERANCE, "六边形面积计算");

                Console.WriteLine("  ✅ 正多边形基础功能验证通过");
                result.PassedTests += 5;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 正多边形测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion

        #region 高级多段线测试
        private static ValidationResult TestAdvancedPolyline()
        {
            var result = new ValidationResult();
            Console.WriteLine("📏 高级多段线功能测试");

            try
            {
                // 测试1: 直线多段线
                var polyline = new EntityPolylineAdvanced();
                polyline.AddVertex(new Vector2(0, 0));
                polyline.AddVertex(new Vector2(10, 0));
                polyline.AddVertex(new Vector2(10, 10));
                
                result.Assert(polyline.VertexCount == 3, "多段线顶点数量");
                result.Assert(polyline.SegmentCount == 2, "多段线线段数量");
                result.Assert(polyline.ArcSegmentCount == 0, "直线多段线弧段数量");

                // 测试2: 长度计算
                var expectedLength = 10 + 10; // 两条直线段
                polyline.Regen();
                result.Assert(Math.Abs(polyline.Length - expectedLength) < TOLERANCE, "多段线长度计算");

                // 测试3: 弧段多段线
                var arcPolyline = new EntityPolylineAdvanced();
                arcPolyline.AddVertex(new Vector2(0, 0));
                arcPolyline.AddArcVertex(new Vector2(10, 0), 1.0); // bulge = 1 表示半圆
                
                result.Assert(arcPolyline.ArcSegmentCount == 1, "弧段多段线弧段数量");

                // 测试4: 闭合多段线面积
                var closedPolyline = new EntityPolylineAdvanced(new[]
                {
                    new Vector2(0, 0),
                    new Vector2(10, 0),
                    new Vector2(10, 10),
                    new Vector2(0, 10)
                }, true);
                
                closedPolyline.Regen();
                result.Assert(Math.Abs(closedPolyline.Area - 100) < TOLERANCE, "闭合多段线面积计算");

                // 测试5: 顶点操作
                polyline.InsertVertex(1, new PolylineVertex(5, 5));
                result.Assert(polyline.VertexCount == 4, "顶点插入操作");
                
                polyline.RemoveVertex(1);
                result.Assert(polyline.VertexCount == 3, "顶点删除操作");

                Console.WriteLine("  ✅ 高级多段线基础功能验证通过");
                result.PassedTests += 5;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 高级多段线测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion

        #region 属性系统测试
        private static ValidationResult TestEntityProperties()
        {
            var result = new ValidationResult();
            Console.WriteLine("🎨 图元属性系统测试");

            try
            {
                // 测试1: 基础属性设置
                var properties = new EntityProperties();
                properties.Color = SKColors.Red;
                properties.LineType = LineTypeStyle.Dashed;
                properties.LineWeight = LineWeightValue.LineWeight025;
                
                result.Assert(properties.Color == SKColors.Red, "颜色属性设置");
                result.Assert(properties.LineType == LineTypeStyle.Dashed, "线型属性设置");
                result.Assert(properties.LineWidthMM == 0.25, "线宽毫米转换");

                // 测试2: 透明度处理
                properties.Transparency = 50;
                result.Assert(properties.Alpha == 127, "透明度Alpha值转换"); // 50% = 127/255

                // 测试3: 绘制画笔生成
                var strokePaint = properties.GetStrokePaint();
                result.Assert(strokePaint != null, "线条画笔生成");
                result.Assert(strokePaint.Style == SKPaintStyle.Stroke, "画笔样式设置");

                // 测试4: 填充属性
                properties.FillType = FillType.Solid;
                properties.FillColor = SKColors.Blue;
                var fillPaint = properties.GetFillPaint();
                result.Assert(fillPaint != null, "填充画笔生成");
                result.Assert(fillPaint.Style == SKPaintStyle.Fill, "填充画笔样式");

                // 测试5: 属性克隆
                var clonedProps = properties.Clone() as EntityProperties;
                result.Assert(clonedProps.Color == properties.Color, "属性克隆验证");

                Console.WriteLine("  ✅ 图元属性系统验证通过");
                result.PassedTests += 5;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 属性系统测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion

        #region 包围盒计算测试
        private static ValidationResult TestBoundingBoxCalculator()
        {
            var result = new ValidationResult();
            Console.WriteLine("📦 包围盒计算器测试");

            try
            {
                // 测试1: 圆形包围盒
                var circleBounds = BoundingBoxCalculator.CalculateCircleBounds(new Vector2(5, 5), 3);
                result.Assert(Math.Abs(circleBounds.Width - 6) < TOLERANCE, "圆形包围盒宽度");
                result.Assert(Math.Abs(circleBounds.Height - 6) < TOLERANCE, "圆形包围盒高度");

                // 测试2: 旋转椭圆包围盒
                var ellipseBounds = BoundingBoxCalculator.CalculateEllipseBounds(Vector2.Zero, 4, 2, 45);
                var expectedDiagonal = Math.Sqrt(4 * 4 + 2 * 2); // 旋转45度后的对角线
                result.Assert(ellipseBounds.Width > 4 && ellipseBounds.Height > 2, "旋转椭圆包围盒扩展");

                // 测试3: 弧段包围盒
                var arcBounds = BoundingBoxCalculator.CalculateArcBounds(Vector2.Zero, 5, 0, 90);
                result.Assert(arcBounds.Left >= -TOLERANCE, "90度弧包围盒左边界");
                result.Assert(arcBounds.Right <= 5 + TOLERANCE, "90度弧包围盒右边界");

                // 测试4: 多边形包围盒
                var polygonPoints = new[]
                {
                    new Vector2(0, 0),
                    new Vector2(10, 5),
                    new Vector2(5, 10),
                    new Vector2(-2, 3)
                };
                var polygonBounds = BoundingBoxCalculator.CalculatePolygonBounds(polygonPoints);
                result.Assert(polygonBounds.Left == -2, "多边形包围盒左边界");
                result.Assert(polygonBounds.Right == 10, "多边形包围盒右边界");

                // 测试5: 包围盒合并
                var bounds1 = new BoundingBox(0, 5, 5, 0);
                var bounds2 = new BoundingBox(3, 8, 8, 3);
                var unionBounds = BoundingBoxCalculator.UnionBounds(bounds1, bounds2);
                result.Assert(unionBounds.Left == 0 && unionBounds.Right == 8, "包围盒合并操作");

                Console.WriteLine("  ✅ 包围盒计算器验证通过");
                result.PassedTests += 5;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 包围盒计算器测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion

        #region 性能测试
        private static ValidationResult TestPerformance()
        {
            var result = new ValidationResult();
            Console.WriteLine("⚡ 性能测试");

            try
            {
                var stopwatch = new Stopwatch();

                // 测试1: 大量椭圆创建性能
                stopwatch.Start();
                for (int i = 0; i < 1000; i++)
                {
                    var ellipse = new EntityEllipse(new Vector2(i, i), 10 + i % 5, 5 + i % 3, i % 360);
                    ellipse.Regen();
                }
                stopwatch.Stop();
                var ellipseCreationTime = stopwatch.ElapsedMilliseconds;
                result.Assert(ellipseCreationTime < 1000, $"1000个椭圆创建时间: {ellipseCreationTime}ms < 1000ms");

                // 测试2: 复杂多边形性能
                stopwatch.Restart();
                for (int i = 0; i < 100; i++)
                {
                    var polygon = new EntityPolygon(Vector2.Zero, 10, 50 + i % 50); // 50-99边形
                    polygon.Regen();
                }
                stopwatch.Stop();
                var polygonCreationTime = stopwatch.ElapsedMilliseconds;
                result.Assert(polygonCreationTime < 500, $"100个复杂多边形创建时间: {polygonCreationTime}ms < 500ms");

                // 测试3: 包围盒计算性能
                stopwatch.Restart();
                for (int i = 0; i < 10000; i++)
                {
                    BoundingBoxCalculator.CalculateEllipseBounds(new Vector2(i % 100, i % 100), 10, 5, i % 360);
                }
                stopwatch.Stop();
                var boundingBoxTime = stopwatch.ElapsedMilliseconds;
                result.Assert(boundingBoxTime < 100, $"10000次包围盒计算时间: {boundingBoxTime}ms < 100ms");

                Console.WriteLine($"  📊 性能指标:");
                Console.WriteLine($"    - 椭圆创建: {ellipseCreationTime}ms/1000个");
                Console.WriteLine($"    - 复杂多边形: {polygonCreationTime}ms/100个");
                Console.WriteLine($"    - 包围盒计算: {boundingBoxTime}ms/10000次");
                Console.WriteLine("  ✅ 性能测试通过");
                
                result.PassedTests += 3;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 性能测试失败: {ex.Message}");
                result.FailedTests++;
            }

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        public int PassedTests { get; set; } = 0;
        public int FailedTests { get; set; } = 0;
        public int Warnings { get; set; } = 0;
        public List<string> Issues { get; set; } = new List<string>();

        public double OverallScore => PassedTests + FailedTests > 0 ? 
            (double)PassedTests / (PassedTests + FailedTests) * 10.0 : 0.0;

        public void Assert(bool condition, string testName)
        {
            if (condition)
            {
                PassedTests++;
            }
            else
            {
                FailedTests++;
                Issues.Add($"❌ {testName}");
                Console.WriteLine($"    ❌ {testName}");
            }
        }

        public void Warning(string message)
        {
            Warnings++;
            Issues.Add($"⚠️ {message}");
            Console.WriteLine($"    ⚠️ {message}");
        }

        public void Merge(ValidationResult other)
        {
            PassedTests += other.PassedTests;
            FailedTests += other.FailedTests;
            Warnings += other.Warnings;
            Issues.AddRange(other.Issues);
        }
    }
} 