using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using Newtonsoft.Json;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 符号系统综合管理器
    /// 统一管理图层、线型、字体、块定义和填充图案等符号系统组件
    /// </summary>
    public class SymbolSystemManager : ObservableObject, IDisposable
    {
        #region 私有字段

        private DocumentBase _document;
        private LayerManager _layerManager;
        private LinetypeManager _linetypeManager;
        private FontManager _fontManager;
        private BlockDefinitionManager _blockDefinitionManager;
        private BlockReferenceManager _blockReferenceManager;
        private HatchPatternManager _hatchPatternManager;
        private bool _isInitialized;

        #endregion

        #region 事件

        /// <summary>
        /// 符号系统初始化完成事件
        /// </summary>
        public event EventHandler<EventArgs> SymbolSystemInitialized;

        /// <summary>
        /// 符号库更新事件
        /// </summary>
        public event EventHandler<SymbolLibraryEventArgs> SymbolLibraryUpdated;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化符号系统管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public SymbolSystemManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            InitializeManagers();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 图层管理器
        /// </summary>
        public LayerManager LayerManager
        {
            get => _layerManager;
            private set => SetProperty(ref _layerManager, value);
        }

        /// <summary>
        /// 线型管理器
        /// </summary>
        public LinetypeManager LinetypeManager
        {
            get => _linetypeManager;
            private set => SetProperty(ref _linetypeManager, value);
        }

        /// <summary>
        /// 字体管理器
        /// </summary>
        public FontManager FontManager
        {
            get => _fontManager;
            private set => SetProperty(ref _fontManager, value);
        }

        /// <summary>
        /// 块定义管理器
        /// </summary>
        public BlockDefinitionManager BlockDefinitionManager
        {
            get => _blockDefinitionManager;
            private set => SetProperty(ref _blockDefinitionManager, value);
        }

        /// <summary>
        /// 块引用管理器
        /// </summary>
        public BlockReferenceManager BlockReferenceManager
        {
            get => _blockReferenceManager;
            private set => SetProperty(ref _blockReferenceManager, value);
        }

        /// <summary>
        /// 填充图案管理器
        /// </summary>
        public HatchPatternManager HatchPatternManager
        {
            get => _hatchPatternManager;
            private set => SetProperty(ref _hatchPatternManager, value);
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            private set => SetProperty(ref _isInitialized, value);
        }

        #endregion

        #region 综合管理方法

        /// <summary>
        /// 导出符号库
        /// </summary>
        /// <param name="exportPath">导出路径</param>
        /// <param name="exportOptions">导出选项</param>
        public void ExportSymbolLibrary(string exportPath, SymbolLibraryExportOptions exportOptions)
        {
            if (string.IsNullOrWhiteSpace(exportPath))
                throw new ArgumentException("导出路径不能为空", nameof(exportPath));

            var symbolLibrary = new SymbolLibrary
            {
                Name = exportOptions.LibraryName ?? "符号库",
                Description = exportOptions.Description ?? "",
                Version = "1.0",
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            // 导出图层
            if (exportOptions.IncludeLayers)
            {
                symbolLibrary.Layers = _layerManager.Layers
                    .Where(l => !_layerManager.IsDefaultLayer(l))
                    .Select(CreateLayerData)
                    .ToList();
            }

            // 导出线型
            if (exportOptions.IncludeLinetypes)
            {
                symbolLibrary.Linetypes = _linetypeManager.CustomLinetypes
                    .Select(CreateLinetypeData)
                    .ToList();
            }

            // 导出字体样式
            if (exportOptions.IncludeFonts)
            {
                symbolLibrary.TextStyles = _fontManager.TextStyles
                    .Where(ts => ts.IsCustom)
                    .Select(CreateTextStyleData)
                    .ToList();
            }

            // 导出块定义
            if (exportOptions.IncludeBlocks)
            {
                symbolLibrary.BlockDefinitions = _blockDefinitionManager.GetUserDefinedBlocks()
                    .Select(CreateBlockDefinitionData)
                    .ToList();
            }

            // 导出填充图案
            if (exportOptions.IncludeHatchPatterns)
            {
                symbolLibrary.HatchPatterns = _hatchPatternManager.CustomPatterns
                    .Select(CreateHatchPatternData)
                    .ToList();
            }

            // 序列化并保存
            var json = JsonConvert.SerializeObject(symbolLibrary, Formatting.Indented);
            File.WriteAllText(exportPath, json);

            OnSymbolLibraryUpdated(SymbolLibraryOperation.Export, exportPath);
        }

        /// <summary>
        /// 导入符号库
        /// </summary>
        /// <param name="importPath">导入路径</param>
        /// <param name="importOptions">导入选项</param>
        /// <returns>导入统计信息</returns>
        public SymbolLibraryImportResult ImportSymbolLibrary(string importPath, SymbolLibraryImportOptions importOptions)
        {
            if (!File.Exists(importPath))
                throw new FileNotFoundException($"符号库文件不存在: {importPath}");

            var result = new SymbolLibraryImportResult();

            try
            {
                var json = File.ReadAllText(importPath);
                var symbolLibrary = JsonConvert.DeserializeObject<SymbolLibrary>(json);

                if (symbolLibrary == null)
                    throw new InvalidOperationException("无效的符号库文件格式");

                // 导入图层
                if (importOptions.IncludeLayers && symbolLibrary.Layers != null)
                {
                    foreach (var layerData in symbolLibrary.Layers)
                    {
                        try
                        {
                            if (!_layerManager.LayerExists(layerData.Name) || importOptions.OverwriteExisting)
                            {
                                var layer = CreateLayerFromData(layerData);
                                if (layer != null)
                                {
                                    if (_layerManager.LayerExists(layerData.Name))
                                    {
                                        result.OverwrittenLayers++;
                                    }
                                    else
                                    {
                                        result.ImportedLayers++;
                                    }
                                }
                            }
                            else
                            {
                                result.SkippedLayers++;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"导入图层 '{layerData.Name}' 失败: {ex.Message}");
                        }
                    }
                }

                // 导入线型
                if (importOptions.IncludeLinetypes && symbolLibrary.Linetypes != null)
                {
                    foreach (var linetypeData in symbolLibrary.Linetypes)
                    {
                        try
                        {
                            if (!_linetypeManager.LinetypeExists(linetypeData.Name) || importOptions.OverwriteExisting)
                            {
                                var linetype = CreateLinetypeFromData(linetypeData);
                                if (linetype != null)
                                {
                                    if (_linetypeManager.LinetypeExists(linetypeData.Name))
                                    {
                                        result.OverwrittenLinetypes++;
                                    }
                                    else
                                    {
                                        result.ImportedLinetypes++;
                                    }
                                }
                            }
                            else
                            {
                                result.SkippedLinetypes++;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"导入线型 '{linetypeData.Name}' 失败: {ex.Message}");
                        }
                    }
                }

                // 导入文本样式
                if (importOptions.IncludeFonts && symbolLibrary.TextStyles != null)
                {
                    foreach (var textStyleData in symbolLibrary.TextStyles)
                    {
                        try
                        {
                            if (!_fontManager.TextStyleExists(textStyleData.Name) || importOptions.OverwriteExisting)
                            {
                                var textStyle = CreateTextStyleFromData(textStyleData);
                                if (textStyle != null)
                                {
                                    if (_fontManager.TextStyleExists(textStyleData.Name))
                                    {
                                        result.OverwrittenTextStyles++;
                                    }
                                    else
                                    {
                                        result.ImportedTextStyles++;
                                    }
                                }
                            }
                            else
                            {
                                result.SkippedTextStyles++;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"导入文本样式 '{textStyleData.Name}' 失败: {ex.Message}");
                        }
                    }
                }

                // 导入块定义
                if (importOptions.IncludeBlocks && symbolLibrary.BlockDefinitions != null)
                {
                    foreach (var blockData in symbolLibrary.BlockDefinitions)
                    {
                        try
                        {
                            if (!_blockDefinitionManager.BlockDefinitionExists(blockData.Name) || importOptions.OverwriteExisting)
                            {
                                var block = CreateBlockDefinitionFromData(blockData);
                                if (block != null)
                                {
                                    if (_blockDefinitionManager.BlockDefinitionExists(blockData.Name))
                                    {
                                        result.OverwrittenBlocks++;
                                    }
                                    else
                                    {
                                        result.ImportedBlocks++;
                                    }
                                }
                            }
                            else
                            {
                                result.SkippedBlocks++;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"导入块定义 '{blockData.Name}' 失败: {ex.Message}");
                        }
                    }
                }

                // 导入填充图案
                if (importOptions.IncludeHatchPatterns && symbolLibrary.HatchPatterns != null)
                {
                    foreach (var hatchData in symbolLibrary.HatchPatterns)
                    {
                        try
                        {
                            if (!_hatchPatternManager.PatternExists(hatchData.Name) || importOptions.OverwriteExisting)
                            {
                                var pattern = CreateHatchPatternFromData(hatchData);
                                if (pattern != null)
                                {
                                    if (_hatchPatternManager.PatternExists(hatchData.Name))
                                    {
                                        result.OverwrittenHatchPatterns++;
                                    }
                                    else
                                    {
                                        result.ImportedHatchPatterns++;
                                    }
                                }
                            }
                            else
                            {
                                result.SkippedHatchPatterns++;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"导入填充图案 '{hatchData.Name}' 失败: {ex.Message}");
                        }
                    }
                }

                OnSymbolLibraryUpdated(SymbolLibraryOperation.Import, importPath);
            }
            catch (Exception ex)
            {
                result.Errors.Add($"导入符号库失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 重置符号系统
        /// </summary>
        /// <param name="keepStandard">是否保留标准符号</param>
        public void ResetSymbolSystem(bool keepStandard = true)
        {
            if (!keepStandard)
            {
                // 清空所有自定义符号
                _layerManager?.Layers.Clear();
                _linetypeManager?.CustomLinetypes.Clear();
                _fontManager?.TextStyles.Clear();
                _blockDefinitionManager?.BlockDefinitions.Clear();
                _blockReferenceManager?.BlockReferences.Clear();
                _hatchPatternManager?.CustomPatterns.Clear();
            }

            // 重新初始化管理器
            InitializeManagers();
        }

        /// <summary>
        /// 获取符号系统统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public SymbolSystemStatistics GetStatistics()
        {
            return new SymbolSystemStatistics
            {
                LayerCount = _layerManager?.Layers.Count ?? 0,
                LinetypeCount = _linetypeManager?.AllLinetypes.Count() ?? 0,
                FontCount = _fontManager?.AllFonts.Count() ?? 0,
                TextStyleCount = _fontManager?.TextStyles.Count ?? 0,
                BlockDefinitionCount = _blockDefinitionManager?.BlockDefinitions.Count ?? 0,
                BlockReferenceCount = _blockReferenceManager?.BlockReferences.Count ?? 0,
                HatchPatternCount = _hatchPatternManager?.AllPatterns.Count() ?? 0,
                CustomLinetypeCount = _linetypeManager?.CustomLinetypes.Count ?? 0,
                CustomTextStyleCount = _fontManager?.TextStyles.Count(ts => ts.IsCustom) ?? 0,
                UserDefinedBlockCount = _blockDefinitionManager?.GetUserDefinedBlocks().Count() ?? 0,
                CustomHatchPatternCount = _hatchPatternManager?.CustomPatterns.Count ?? 0
            };
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManagers()
        {
            try
            {
                // 创建各个管理器
                LayerManager = new LayerManager(_document);
                LinetypeManager = new LinetypeManager(_document);
                FontManager = new FontManager(_document);
                BlockDefinitionManager = new BlockDefinitionManager(_document);
                BlockReferenceManager = new BlockReferenceManager(_document, _blockDefinitionManager);
                HatchPatternManager = new HatchPatternManager(_document);

                // 设置管理器之间的关联
                SetupManagerRelationships();

                IsInitialized = true;
                OnSymbolSystemInitialized();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"初始化符号系统失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 设置管理器之间的关联关系
        /// </summary>
        private void SetupManagerRelationships()
        {
            // 这里可以设置管理器之间的事件订阅和数据同步
            // 例如：当删除图层时，需要处理该图层上的实体
        }

        /// <summary>
        /// 创建图层数据
        /// </summary>
        private LayerData CreateLayerData(EntityLayer layer)
        {
            return new LayerData
            {
                Name = layer.Name,
                Description = layer.Description,
                IsVisible = layer.IsVisible,
                IsLocked = layer.IsLocked,
                IsMarkerable = layer.IsMarkerable
            };
        }

        /// <summary>
        /// 从数据创建图层
        /// </summary>
        private EntityLayer CreateLayerFromData(LayerData data)
        {
            return _layerManager.CreateLayer(data.Name, null, 1.0f, data.Description);
        }

        /// <summary>
        /// 创建线型数据
        /// </summary>
        private LinetypeData CreateLinetypeData(LinetypeInfo linetype)
        {
            return new LinetypeData
            {
                Name = linetype.Name,
                Description = linetype.Description,
                Pattern = linetype.Pattern
            };
        }

        /// <summary>
        /// 从数据创建线型
        /// </summary>
        private LinetypeInfo CreateLinetypeFromData(LinetypeData data)
        {
            return _linetypeManager.CreateCustomLinetype(data.Name, data.Description, data.Pattern);
        }

        /// <summary>
        /// 创建文本样式数据
        /// </summary>
        private TextStyleData CreateTextStyleData(TextStyleInfo textStyle)
        {
            return new TextStyleData
            {
                Name = textStyle.Name,
                FontName = textStyle.Font?.Name,
                FontSize = textStyle.FontSize,
                IsBold = textStyle.IsBold,
                IsItalic = textStyle.IsItalic,
                WidthFactor = textStyle.WidthFactor,
                ObliqueAngle = textStyle.ObliqueAngle
            };
        }

        /// <summary>
        /// 从数据创建文本样式
        /// </summary>
        private TextStyleInfo CreateTextStyleFromData(TextStyleData data)
        {
            var font = _fontManager.GetFont(data.FontName) ?? _fontManager.CurrentFont;
            return _fontManager.CreateTextStyle(data.Name, font, data.FontSize, 
                data.IsBold, data.IsItalic, data.WidthFactor, data.ObliqueAngle);
        }

        /// <summary>
        /// 创建块定义数据
        /// </summary>
        private BlockDefinitionData CreateBlockDefinitionData(BlockDefinition blockDef)
        {
            return new BlockDefinitionData
            {
                Name = blockDef.Name,
                Description = blockDef.Description,
                BasePoint = blockDef.BasePoint,
                // 实体数据需要序列化，这里简化处理
                EntityCount = blockDef.Entities.Count
            };
        }

        /// <summary>
        /// 从数据创建块定义
        /// </summary>
        private BlockDefinition CreateBlockDefinitionFromData(BlockDefinitionData data)
        {
            // 这里需要反序列化实体数据，简化为创建空块
            return _blockDefinitionManager.CreateBlockDefinition(data.Name, data.BasePoint, 
                Enumerable.Empty<EntityBase>(), data.Description);
        }

        /// <summary>
        /// 创建填充图案数据
        /// </summary>
        private HatchPatternData CreateHatchPatternData(HatchPatternInfo pattern)
        {
            return new HatchPatternData
            {
                Name = pattern.Name,
                Description = pattern.Description,
                Type = pattern.Type,
                Pattern = pattern.Pattern
            };
        }

        /// <summary>
        /// 从数据创建填充图案
        /// </summary>
        private HatchPatternInfo CreateHatchPatternFromData(HatchPatternData data)
        {
            return _hatchPatternManager.CreateCustomPattern(data.Name, data.Description, 
                data.Type, data.Pattern?.Lines);
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发符号系统初始化完成事件
        /// </summary>
        protected virtual void OnSymbolSystemInitialized()
        {
            SymbolSystemInitialized?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 触发符号库更新事件
        /// </summary>
        protected virtual void OnSymbolLibraryUpdated(SymbolLibraryOperation operation, string filePath)
        {
            SymbolLibraryUpdated?.Invoke(this, new SymbolLibraryEventArgs(operation, filePath));
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _fontManager?.Dispose();
            
            LayerManager = null;
            LinetypeManager = null;
            FontManager = null;
            BlockDefinitionManager = null;
            BlockReferenceManager = null;
            HatchPatternManager = null;
        }

        #endregion
    }

    #region 辅助类和枚举

    /// <summary>
    /// 符号库操作类型
    /// </summary>
    public enum SymbolLibraryOperation
    {
        Import,
        Export
    }

    /// <summary>
    /// 符号库导出选项
    /// </summary>
    public class SymbolLibraryExportOptions
    {
        public string LibraryName { get; set; }
        public string Description { get; set; }
        public bool IncludeLayers { get; set; } = true;
        public bool IncludeLinetypes { get; set; } = true;
        public bool IncludeFonts { get; set; } = true;
        public bool IncludeBlocks { get; set; } = true;
        public bool IncludeHatchPatterns { get; set; } = true;
    }

    /// <summary>
    /// 符号库导入选项
    /// </summary>
    public class SymbolLibraryImportOptions
    {
        public bool IncludeLayers { get; set; } = true;
        public bool IncludeLinetypes { get; set; } = true;
        public bool IncludeFonts { get; set; } = true;
        public bool IncludeBlocks { get; set; } = true;
        public bool IncludeHatchPatterns { get; set; } = true;
        public bool OverwriteExisting { get; set; } = false;
    }

    /// <summary>
    /// 符号库导入结果
    /// </summary>
    public class SymbolLibraryImportResult
    {
        public int ImportedLayers { get; set; }
        public int ImportedLinetypes { get; set; }
        public int ImportedTextStyles { get; set; }
        public int ImportedBlocks { get; set; }
        public int ImportedHatchPatterns { get; set; }
        
        public int OverwrittenLayers { get; set; }
        public int OverwrittenLinetypes { get; set; }
        public int OverwrittenTextStyles { get; set; }
        public int OverwrittenBlocks { get; set; }
        public int OverwrittenHatchPatterns { get; set; }
        
        public int SkippedLayers { get; set; }
        public int SkippedLinetypes { get; set; }
        public int SkippedTextStyles { get; set; }
        public int SkippedBlocks { get; set; }
        public int SkippedHatchPatterns { get; set; }
        
        public List<string> Errors { get; set; } = new List<string>();
        
        public bool HasErrors => Errors.Count > 0;
        public int TotalImported => ImportedLayers + ImportedLinetypes + ImportedTextStyles + ImportedBlocks + ImportedHatchPatterns;
    }

    /// <summary>
    /// 符号系统统计信息
    /// </summary>
    public class SymbolSystemStatistics
    {
        public int LayerCount { get; set; }
        public int LinetypeCount { get; set; }
        public int FontCount { get; set; }
        public int TextStyleCount { get; set; }
        public int BlockDefinitionCount { get; set; }
        public int BlockReferenceCount { get; set; }
        public int HatchPatternCount { get; set; }
        
        public int CustomLinetypeCount { get; set; }
        public int CustomTextStyleCount { get; set; }
        public int UserDefinedBlockCount { get; set; }
        public int CustomHatchPatternCount { get; set; }
    }

    /// <summary>
    /// 符号库事件参数
    /// </summary>
    public class SymbolLibraryEventArgs : EventArgs
    {
        public SymbolLibraryOperation Operation { get; }
        public string FilePath { get; }

        public SymbolLibraryEventArgs(SymbolLibraryOperation operation, string filePath)
        {
            Operation = operation;
            FilePath = filePath;
        }
    }

    // 数据类定义
    public class SymbolLibrary
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Version { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        
        public List<LayerData> Layers { get; set; }
        public List<LinetypeData> Linetypes { get; set; }
        public List<TextStyleData> TextStyles { get; set; }
        public List<BlockDefinitionData> BlockDefinitions { get; set; }
        public List<HatchPatternData> HatchPatterns { get; set; }
    }

    public class LayerData
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsVisible { get; set; }
        public bool IsLocked { get; set; }
        public bool IsMarkerable { get; set; }
    }

    public class LinetypeData
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public LinetypePattern Pattern { get; set; }
    }

    public class TextStyleData
    {
        public string Name { get; set; }
        public string FontName { get; set; }
        public float FontSize { get; set; }
        public bool IsBold { get; set; }
        public bool IsItalic { get; set; }
        public float WidthFactor { get; set; }
        public float ObliqueAngle { get; set; }
    }

    public class BlockDefinitionData
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public System.Numerics.Vector2 BasePoint { get; set; }
        public int EntityCount { get; set; }
        // 实际应用中需要包含完整的实体数据
    }

    public class HatchPatternData
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public HatchPatternType Type { get; set; }
        public HatchPattern Pattern { get; set; }
    }

    #endregion
} 