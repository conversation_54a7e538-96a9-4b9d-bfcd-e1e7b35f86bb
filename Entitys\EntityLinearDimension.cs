using System;
using System.ComponentModel;
using System.Numerics;
using PropertyTools;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 线性标注实体
    /// </summary>
    [Browsable(true)]
    [DisplayName("线性标注")]
    public class EntityLinearDimension : EntityDimension
    {
        private Vector2 firstPoint;
        private Vector2 secondPoint;
        private Vector2 dimensionLinePosition;
        private double rotation = 0.0; // 标注旋转角度（弧度）

        #region 构造函数

        public EntityLinearDimension() : base(DimensionType.Linear)
        {
            firstPoint = Vector2.Zero;
            secondPoint = new Vector2(10, 0);
            dimensionLinePosition = new Vector2(5, 5);
            Update();
        }

        public EntityLinearDimension(Vector2 first, Vector2 second, Vector2 dimLinePos) : base(DimensionType.Linear)
        {
            firstPoint = first;
            secondPoint = second;
            dimensionLinePosition = dimLinePos;
            Update();
        }

        public EntityLinearDimension(Vector2 first, Vector2 second, Vector2 dimLinePos, double rotationAngle) : base(DimensionType.Linear)
        {
            firstPoint = first;
            secondPoint = second;
            dimensionLinePosition = dimLinePos;
            rotation = rotationAngle;
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("第一点")]
        public Vector2 FirstPoint
        {
            get => firstPoint;
            set
            {
                if (firstPoint != value)
                {
                    firstPoint = value;
                    OnPropertyChanged(nameof(FirstPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("第二点")]
        public Vector2 SecondPoint
        {
            get => secondPoint;
            set
            {
                if (secondPoint != value)
                {
                    secondPoint = value;
                    OnPropertyChanged(nameof(SecondPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("标注线位置")]
        public Vector2 DimensionLinePosition
        {
            get => dimensionLinePosition;
            set
            {
                if (dimensionLinePosition != value)
                {
                    dimensionLinePosition = value;
                    OnPropertyChanged(nameof(DimensionLinePosition));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("旋转角度")]
        public double Rotation
        {
            get => rotation;
            set
            {
                if (Math.Abs(rotation - value) > 1e-10)
                {
                    rotation = value;
                    OnPropertyChanged(nameof(Rotation));
                    Update();
                }
            }
        }

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            if (Math.Abs(rotation) < 1e-10)
            {
                // 水平或垂直标注
                var diff = secondPoint - firstPoint;
                ActualMeasurement = Math.Abs(diff.X) > Math.Abs(diff.Y) ? Math.Abs(diff.X) : Math.Abs(diff.Y);
            }
            else
            {
                // 旋转标注 - 沿着旋转方向的投影长度
                var direction = new Vector2((float)Math.Cos(rotation), (float)Math.Sin(rotation));
                var diff = secondPoint - firstPoint;
                ActualMeasurement = Math.Abs(Vector2.Dot(diff, direction));
            }
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            // 计算标注方向
            Vector2 dimDirection;
            if (Math.Abs(rotation) < 1e-10)
            {
                // 自动判断水平还是垂直
                var diff = secondPoint - firstPoint;
                dimDirection = Math.Abs(diff.X) > Math.Abs(diff.Y) ? Vector2.UnitX : Vector2.UnitY;
            }
            else
            {
                dimDirection = new Vector2((float)Math.Cos(rotation), (float)Math.Sin(rotation));
            }

            var perpDirection = new Vector2(-dimDirection.Y, dimDirection.X);

            // 投影第一点和第二点到标注方向
            var projFirst = Vector2.Dot(firstPoint, dimDirection);
            var projSecond = Vector2.Dot(secondPoint, dimDirection);
            var projDimLine = Vector2.Dot(dimensionLinePosition, dimDirection);

            // 计算标注线的起点和终点
            var dimLineStart = dimDirection * Math.Min(projFirst, projSecond) + 
                             perpDirection * Vector2.Dot(dimensionLinePosition, perpDirection);
            var dimLineEnd = dimDirection * Math.Max(projFirst, projSecond) + 
                           perpDirection * Vector2.Dot(dimensionLinePosition, perpDirection);

            geometry.DimensionLine = new LineGeometry(dimLineStart, dimLineEnd);

            // 计算延伸线
            if (Style.ShowExtensionLines)
            {
                // 第一点的延伸线
                var firstProjPoint = dimDirection * projFirst + perpDirection * Vector2.Dot(firstPoint, perpDirection);
                var firstExtStart = firstProjPoint + perpDirection * (Vector2.Dot(dimensionLinePosition - firstPoint, perpDirection) > 0 ? 
                    Style.ExtensionLineOffset : -Style.ExtensionLineOffset);
                var firstExtEnd = dimDirection * projFirst + perpDirection * Vector2.Dot(dimensionLinePosition, perpDirection) +
                                perpDirection * (Vector2.Dot(dimensionLinePosition - firstPoint, perpDirection) > 0 ? 
                                    Style.ExtensionLineExtend : -Style.ExtensionLineExtend);

                geometry.ExtensionLines.Add(new LineGeometry(firstExtStart, firstExtEnd));

                // 第二点的延伸线
                var secondProjPoint = dimDirection * projSecond + perpDirection * Vector2.Dot(secondPoint, perpDirection);
                var secondExtStart = secondProjPoint + perpDirection * (Vector2.Dot(dimensionLinePosition - secondPoint, perpDirection) > 0 ? 
                    Style.ExtensionLineOffset : -Style.ExtensionLineOffset);
                var secondExtEnd = dimDirection * projSecond + perpDirection * Vector2.Dot(dimensionLinePosition, perpDirection) +
                                 perpDirection * (Vector2.Dot(dimensionLinePosition - secondPoint, perpDirection) > 0 ? 
                                     Style.ExtensionLineExtend : -Style.ExtensionLineExtend);

                geometry.ExtensionLines.Add(new LineGeometry(secondExtStart, secondExtEnd));
            }

            // 计算箭头
            if (Style.ShowArrows)
            {
                var arrowDirection = Vector2.Normalize(dimLineEnd - dimLineStart);
                geometry.Arrows.Add(new ArrowGeometry(dimLineStart, arrowDirection, Style.ArrowSize));
                geometry.Arrows.Add(new ArrowGeometry(dimLineEnd, -arrowDirection, Style.ArrowSize));
            }

            // 计算文本位置
            geometry.TextPosition = (dimLineStart + dimLineEnd) * 0.5f + perpDirection * Style.TextHeight * 0.5f;

            return geometry;
        }

        public override EntityBase Clone()
        {
            var clone = new EntityLinearDimension(firstPoint, secondPoint, dimensionLinePosition, rotation);
            CopyPropertiesTo(clone);
            return clone;
        }

        #endregion

        #region 工厂方法

        /// <summary>
        /// 创建水平线性标注
        /// </summary>
        public static EntityLinearDimension CreateHorizontal(Vector2 first, Vector2 second, double yOffset)
        {
            var dimLinePos = new Vector2((first.X + second.X) * 0.5f, Math.Max(first.Y, second.Y) + yOffset);
            return new EntityLinearDimension(first, second, dimLinePos, 0);
        }

        /// <summary>
        /// 创建垂直线性标注
        /// </summary>
        public static EntityLinearDimension CreateVertical(Vector2 first, Vector2 second, double xOffset)
        {
            var dimLinePos = new Vector2(Math.Max(first.X, second.X) + xOffset, (first.Y + second.Y) * 0.5f);
            return new EntityLinearDimension(first, second, dimLinePos, Math.PI / 2);
        }

        #endregion
    }
} 