<Window x:Class="McLaser.EditViewerSk.Views.DimensionStyleManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="标注样式管理器" Height="600" Width="800" 
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Width" Value="100"/>
        </Style>
        
        <Style x:Key="PropertyControlStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Height" Value="25"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 样式列表 -->
        <DockPanel Grid.Column="0" Margin="10">
            <TextBlock Text="标注样式" Style="{StaticResource HeaderTextStyle}" DockPanel.Dock="Top"/>
            
            <StackPanel DockPanel.Dock="Bottom" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                <Button Content="新建" Width="60" Height="30" Margin="5" Click="NewStyle_Click"/>
                <Button Content="删除" Width="60" Height="30" Margin="5" Click="DeleteStyle_Click"/>
                <Button Content="复制" Width="60" Height="30" Margin="5" Click="CopyStyle_Click"/>
            </StackPanel>
            
            <ListBox Name="StyleListBox" SelectionChanged="StyleListBox_SelectionChanged" 
                     DisplayMemberPath="Name" Margin="0,5"/>
        </DockPanel>
        
        <!-- 样式属性编辑 -->
        <ScrollViewer Grid.Column="1" Margin="10" VerticalScrollBarVisibility="Auto">
            <StackPanel Name="PropertiesPanel">
                <TextBlock Text="样式属性" Style="{StaticResource HeaderTextStyle}"/>
                
                <!-- 基本信息 -->
                <GroupBox Header="基本信息" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="样式名称:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="StyleNameTextBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="描述:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="DescriptionTextBox" Grid.Row="1" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                    </Grid>
                </GroupBox>
                
                <!-- 文本属性 -->
                <GroupBox Header="文本属性" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="文本高度:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="TextHeightTextBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="字体:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <ComboBox Name="FontComboBox" Grid.Row="1" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="文本颜色:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <Button Name="TextColorButton" Grid.Row="2" Grid.Column="1" Style="{StaticResource PropertyControlStyle}" 
                                Content="选择颜色" Click="TextColorButton_Click"/>
                        
                        <TextBlock Text="小数位数:" Grid.Row="3" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="DecimalPlacesTextBox" Grid.Row="3" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                    </Grid>
                </GroupBox>
                
                <!-- 箭头属性 -->
                <GroupBox Header="箭头属性" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="箭头大小:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="ArrowSizeTextBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="显示箭头:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <CheckBox Name="ShowArrowsCheckBox" Grid.Row="1" Grid.Column="1" VerticalAlignment="Center" Margin="5,2"/>
                    </Grid>
                </GroupBox>
                
                <!-- 延伸线属性 -->
                <GroupBox Header="延伸线属性" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="延伸线偏移:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="ExtensionLineOffsetTextBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="延伸线延伸:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="ExtensionLineExtendTextBox" Grid.Row="1" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="显示延伸线:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <CheckBox Name="ShowExtensionLinesCheckBox" Grid.Row="2" Grid.Column="1" VerticalAlignment="Center" Margin="5,2"/>
                    </Grid>
                </GroupBox>
                
                <!-- 单位和格式 -->
                <GroupBox Header="单位和格式" Margin="0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="前缀:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="PrefixTextBox" Grid.Row="0" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                        
                        <TextBlock Text="后缀:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="SuffixTextBox" Grid.Row="1" Grid.Column="1" Style="{StaticResource PropertyControlStyle}"/>
                    </Grid>
                </GroupBox>
                
                <!-- 按钮区域 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20">
                    <Button Content="应用" Width="80" Height="35" Margin="5" Click="Apply_Click"/>
                    <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click"/>
                    <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
