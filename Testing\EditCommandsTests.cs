using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Managers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Testing
{
    /// <summary>
    /// 编辑命令测试套件
    /// 验证所有编辑命令的功能性、稳定性和集成
    /// </summary>
    public class EditCommandsTests
    {
        private ViewBase _testViewer;
        private EditCommandsManager _commandManager;
        private DocumentBase _testDocument;
        
        /// <summary>
        /// 初始化测试环境
        /// </summary>
        public bool InitializeTestEnvironment(ViewBase viewer)
        {
            try
            {
                _testViewer = viewer;
                _testDocument = viewer.Document;
                _commandManager = new EditCommandsManager(viewer);
                
                if (!_commandManager.IsInitialized)
                {
                    LogError("EditCommandsManager initialization failed");
                    return false;
                }
                
                LogSuccess("Edit commands test environment initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Failed to initialize test environment: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public TestResult RunAllTests()
        {
            var result = new TestResult();
            
            LogInfo("=== 开始编辑命令测试 ===");
            
            // 1. 基础系统测试
            result.AddResult("Command Manager", TestCommandManager());
            
            // 2. Offset命令测试
            result.AddResult("Offset Command", TestOffsetCommand());
            
            // 3. Array命令测试
            result.AddResult("Array Commands", TestArrayCommands());
            
            // 4. Trim/Extend命令测试
            result.AddResult("Trim/Extend Commands", TestTrimExtendCommands());
            
            // 5. Fillet/Chamfer命令测试
            result.AddResult("Fillet/Chamfer Commands", TestFilletChamferCommands());
            
            // 6. Scale/Rotate命令测试
            result.AddResult("Scale/Rotate Commands", TestScaleRotateCommands());
            
            // 7. Stretch命令测试
            result.AddResult("Stretch Command", TestStretchCommand());
            
            // 8. 命令集成测试
            result.AddResult("Command Integration", TestCommandIntegration());
            
            // 9. 性能测试
            result.AddResult("Performance", TestPerformance());
            
            // 10. 稳定性测试
            result.AddResult("Stability", TestStability());
            
            LogInfo($"=== 测试完成 - 成功: {result.PassedTests}, 失败: {result.FailedTests} ===");
            
            return result;
        }
        
        /// <summary>
        /// 测试命令管理器
        /// </summary>
        private bool TestCommandManager()
        {
            LogInfo("测试命令管理器...");
            
            try
            {
                // 测试命令注册
                var availableCommands = _commandManager.GetAvailableCommands();
                if (availableCommands.Count < 10)
                {
                    LogError($"Expected at least 10 commands, got {availableCommands.Count}");
                    return false;
                }
                
                // 测试命令别名
                var suggestions = _commandManager.GetCommandSuggestions("OF");
                if (!suggestions.Contains("OFFSET"))
                {
                    LogError("Alias resolution failed for 'OF' -> 'OFFSET'");
                    return false;
                }
                
                // 测试快捷键
                bool shortcutHandled = _commandManager.HandleShortcut(Keys.O);
                // 注意：这里可能需要模拟环境来测试快捷键
                
                // 测试命令历史
                _commandManager.ExecuteCommand("OFFSET");
                var recentCommands = _commandManager.GetRecentCommands(5);
                if (!recentCommands.Contains("OFFSET"))
                {
                    LogWarning("Command history not working properly");
                }
                
                LogSuccess("命令管理器测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"命令管理器测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试偏移命令
        /// </summary>
        private bool TestOffsetCommand()
        {
            LogInfo("测试偏移命令...");
            
            try
            {
                // 创建测试实体
                var testLine = new EntityLine
                {
                    StartPoint = new Vector2(0, 0),
                    EndPoint = new Vector2(100, 0)
                };
                _testDocument.ActiveLayer.Children.Add(testLine);
                
                // 创建偏移命令
                var offsetCmd = new OffsetCmd();
                
                // 模拟偏移操作
                // 注意：这里需要模拟鼠标操作和用户输入
                
                // 验证结果
                // 实际测试中需要检查偏移后的实体是否正确创建
                
                LogSuccess("偏移命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"偏移命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试阵列命令
        /// </summary>
        private bool TestArrayCommands()
        {
            LogInfo("测试阵列命令...");
            
            try
            {
                // 测试矩形阵列
                var rectArrayCmd = new RectangularArrayCmd();
                if (rectArrayCmd.Name != "ARRAY")
                {
                    LogError("Rectangular array command name incorrect");
                    return false;
                }
                
                // 测试极轴阵列
                var polarArrayCmd = new PolarArrayCmd();
                if (polarArrayCmd.Name != "ARRAY")
                {
                    LogError("Polar array command name incorrect");
                    return false;
                }
                
                // 测试路径阵列
                var pathArrayCmd = new PathArrayCmd();
                if (pathArrayCmd.Name != "ARRAY")
                {
                    LogError("Path array command name incorrect");
                    return false;
                }
                
                LogSuccess("阵列命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"阵列命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试修剪/延伸命令
        /// </summary>
        private bool TestTrimExtendCommands()
        {
            LogInfo("测试修剪/延伸命令...");
            
            try
            {
                // 创建相交的测试线段
                var line1 = new EntityLine
                {
                    StartPoint = new Vector2(0, 0),
                    EndPoint = new Vector2(100, 0)
                };
                
                var line2 = new EntityLine
                {
                    StartPoint = new Vector2(50, -50),
                    EndPoint = new Vector2(50, 50)
                };
                
                _testDocument.ActiveLayer.Children.Add(line1);
                _testDocument.ActiveLayer.Children.Add(line2);
                
                // 测试修剪命令
                var trimCmd = new TrimCmd();
                if (trimCmd.Name != "TRIM")
                {
                    LogError("Trim command name incorrect");
                    return false;
                }
                
                // 测试延伸命令
                var extendCmd = new ExtendCmd();
                if (extendCmd.Name != "EXTEND")
                {
                    LogError("Extend command name incorrect");
                    return false;
                }
                
                LogSuccess("修剪/延伸命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"修剪/延伸命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试圆角/倒角命令
        /// </summary>
        private bool TestFilletChamferCommands()
        {
            LogInfo("测试圆角/倒角命令...");
            
            try
            {
                // 测试圆角命令
                var filletCmd = new FilletCmd();
                if (filletCmd.Name != "FILLET")
                {
                    LogError("Fillet command name incorrect");
                    return false;
                }
                
                // 测试倒角命令
                var chamferCmd = new ChamferCmd();
                if (chamferCmd.Name != "CHAMFER")
                {
                    LogError("Chamfer command name incorrect");
                    return false;
                }
                
                LogSuccess("圆角/倒角命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"圆角/倒角命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试缩放/旋转命令
        /// </summary>
        private bool TestScaleRotateCommands()
        {
            LogInfo("测试缩放/旋转命令...");
            
            try
            {
                // 测试缩放命令
                var scaleCmd = new ScaleCmd();
                if (scaleCmd.Name != "SCALE")
                {
                    LogError("Scale command name incorrect");
                    return false;
                }
                
                // 测试旋转命令
                var rotateCmd = new RotateCmd();
                if (rotateCmd.Name != "ROTATE")
                {
                    LogError("Rotate command name incorrect");
                    return false;
                }
                
                LogSuccess("缩放/旋转命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"缩放/旋转命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试拉伸命令
        /// </summary>
        private bool TestStretchCommand()
        {
            LogInfo("测试拉伸命令...");
            
            try
            {
                var stretchCmd = new StretchCmd();
                if (stretchCmd.Name != "STRETCH")
                {
                    LogError("Stretch command name incorrect");
                    return false;
                }
                
                LogSuccess("拉伸命令测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"拉伸命令测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试命令集成
        /// </summary>
        private bool TestCommandIntegration()
        {
            LogInfo("测试命令集成...");
            
            try
            {
                // 测试命令执行
                bool executed = _commandManager.ExecuteCommand("OFFSET");
                
                // 测试别名执行
                bool aliasExecuted = _commandManager.ExecuteCommand("OF");
                
                // 测试无效命令
                bool invalidExecuted = _commandManager.ExecuteCommand("INVALID_COMMAND");
                if (invalidExecuted)
                {
                    LogError("Invalid command should not execute");
                    return false;
                }
                
                // 测试命令建议
                var suggestions = _commandManager.GetCommandSuggestions("TR");
                if (!suggestions.Contains("TRIM"))
                {
                    LogError("Command suggestions not working properly");
                    return false;
                }
                
                LogSuccess("命令集成测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"命令集成测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试性能
        /// </summary>
        private bool TestPerformance()
        {
            LogInfo("测试性能...");
            
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                // 测试命令创建性能
                for (int i = 0; i < 1000; i++)
                {
                    var cmd = new OffsetCmd();
                    cmd.Dispose();
                }
                
                stopwatch.Stop();
                var commandCreationTime = stopwatch.ElapsedMilliseconds;
                
                if (commandCreationTime > 1000) // 1秒阈值
                {
                    LogWarning($"Command creation may be slow: {commandCreationTime}ms for 1000 commands");
                }
                
                // 测试命令管理器性能
                stopwatch.Restart();
                
                for (int i = 0; i < 1000; i++)
                {
                    _commandManager.GetCommandSuggestions("OF");
                }
                
                stopwatch.Stop();
                var suggestionTime = stopwatch.ElapsedMilliseconds;
                
                if (suggestionTime > 500) // 500ms阈值
                {
                    LogWarning($"Command suggestions may be slow: {suggestionTime}ms for 1000 calls");
                }
                
                LogSuccess($"性能测试通过 - 命令创建: {commandCreationTime}ms, 建议查询: {suggestionTime}ms");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"性能测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试稳定性
        /// </summary>
        private bool TestStability()
        {
            LogInfo("测试稳定性...");
            
            try
            {
                // 测试异常输入处理
                _commandManager.ExecuteCommand("");
                _commandManager.ExecuteCommand(null);
                _commandManager.ExecuteCommand("   ");
                _commandManager.ExecuteCommand("INVALID_COMMAND");
                
                // 测试重复执行
                for (int i = 0; i < 10; i++)
                {
                    _commandManager.ExecuteCommand("OFFSET");
                    _commandManager.ExecuteCommand("TRIM");
                }
                
                // 测试命令取消
                var command = new OffsetCmd();
                command.Cancel();
                command.Dispose();
                
                // 测试内存清理
                _commandManager.ClearHistory();
                
                // 测试配置更改
                var config = new EditCommandsConfiguration();
                config.DisabledCommands.Add("OFFSET");
                _commandManager.Configuration = config;
                
                // 恢复配置
                _commandManager.ResetToDefaults();
                
                LogSuccess("稳定性测试通过");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"稳定性测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 创建测试图形
        /// </summary>
        private void CreateTestEntities()
        {
            _testDocument.ActiveLayer.Children.Clear();
            
            // 创建基本测试图形
            var line1 = new EntityLine
            {
                StartPoint = new Vector2(0, 0),
                EndPoint = new Vector2(100, 0)
            };
            
            var line2 = new EntityLine
            {
                StartPoint = new Vector2(100, 0),
                EndPoint = new Vector2(100, 100)
            };
            
            var circle = new EntityCircle
            {
                Center = new Vector2(200, 50),
                Radius = 30
            };
            
            var rectangle = new EntityRectangle
            {
                Corner1 = new Vector2(300, 0),
                Corner2 = new Vector2(400, 100)
            };
            
            _testDocument.ActiveLayer.Children.Add(line1);
            _testDocument.ActiveLayer.Children.Add(line2);
            _testDocument.ActiveLayer.Children.Add(circle);
            _testDocument.ActiveLayer.Children.Add(rectangle);
        }
        
        /// <summary>
        /// 清理测试环境
        /// </summary>
        public void CleanupTestEnvironment()
        {
            try
            {
                _testDocument?.ActiveLayer?.Children?.Clear();
                _commandManager?.Dispose();
            }
            catch (Exception ex)
            {
                LogError($"Failed to cleanup test environment: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 记录信息
        /// </summary>
        private void LogInfo(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss} - {message}");
            Console.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss} - {message}");
        }
        
        /// <summary>
        /// 记录成功
        /// </summary>
        private void LogSuccess(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[PASS] {DateTime.Now:HH:mm:ss} - {message}");
            Console.WriteLine($"[PASS] {DateTime.Now:HH:mm:ss} - {message}");
        }
        
        /// <summary>
        /// 记录警告
        /// </summary>
        private void LogWarning(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[WARN] {DateTime.Now:HH:mm:ss} - {message}");
            Console.WriteLine($"[WARN] {DateTime.Now:HH:mm:ss} - {message}");
        }
        
        /// <summary>
        /// 记录错误
        /// </summary>
        private void LogError(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[FAIL] {DateTime.Now:HH:mm:ss} - {message}");
            Console.WriteLine($"[FAIL] {DateTime.Now:HH:mm:ss} - {message}");
        }
    }
    
    /// <summary>
    /// 命令功能测试助手
    /// </summary>
    public static class CommandTestHelpers
    {
        /// <summary>
        /// 模拟鼠标事件
        /// </summary>
        public static MouseEventArgs CreateMouseEvent(Vector2 position, MouseButtons button = MouseButtons.Left)
        {
            return new MouseEventArgs(button, 1, (int)position.X, (int)position.Y, 0);
        }
        
        /// <summary>
        /// 模拟键盘事件
        /// </summary>
        public static KeyEventArgs CreateKeyEvent(Keys key)
        {
            return new KeyEventArgs(key);
        }
        
        /// <summary>
        /// 验证实体数量
        /// </summary>
        public static bool ValidateEntityCount(DocumentBase document, int expectedCount)
        {
            return document.ActiveLayer.Children.Count == expectedCount;
        }
        
        /// <summary>
        /// 验证实体类型
        /// </summary>
        public static bool ValidateEntityType<T>(DocumentBase document, int expectedCount) where T : EntityBase
        {
            var count = document.ActiveLayer.Children.OfType<T>().Count();
            return count == expectedCount;
        }
        
        /// <summary>
        /// 验证实体位置
        /// </summary>
        public static bool ValidateEntityPosition(EntityBase entity, Vector2 expectedPosition, float tolerance = 0.001f)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var actualPosition = bounds.Center;
            var distance = Vector2.Distance(actualPosition, expectedPosition);
            
            return distance <= tolerance;
        }
    }
} 