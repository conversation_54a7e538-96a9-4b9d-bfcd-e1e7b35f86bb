using System;
using System.ComponentModel;
using System.Numerics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Viewport;
using McLaser.EditViewerSk.Managers;

namespace McLaser.EditViewerSk.UI
{
    /// <summary>
    /// 增强的状态栏管理器
    /// 提供实时坐标、捕捉状态、图层信息和缩放比例显示
    /// </summary>
    public class EnhancedStatusBarManager : INotifyPropertyChanged
    {
        private readonly ViewBase _viewBase;
        private readonly ViewportManager _viewportManager;
        private readonly Phase2IntegrationManager _phase2Manager;
        
        // 坐标信息
        private Vector2 _currentPosition = Vector2.Zero;
        private string _coordinateFormat = "F3";
        
        // 捕捉状态
        private string _snapStatus = "关闭";
        private bool _isSnapEnabled = false;
        
        // 极轴追踪状态
        private bool _isPolarTrackingEnabled = false;
        
        // 对象追踪状态
        private bool _isObjectTrackingEnabled = false;
        
        // 动态输入状态
        private bool _isDynamicInputEnabled = false;
        
        // 图层信息
        private string _currentLayer = "0";
        private Brush _layerColor = Brushes.Black;
        
        // 缩放比例
        private float _zoomScale = 1.0f;
        private string _zoomPercentage = "100%";
        
        // 标注样式
        private string _dimensionStyle = "标准";
        
        // 状态消息
        private string _statusMessage = "就绪";
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        public EnhancedStatusBarManager(ViewBase viewBase, ViewportManager viewportManager)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _viewportManager = viewportManager ?? throw new ArgumentNullException(nameof(viewportManager));
            _phase2Manager = _viewBase.GetPhase2Manager();
            
            // 订阅事件
            _viewBase.MouseMove += OnMouseMove;
            
            // 初始化状态
            UpdateAllStatus();
        }
        
        #region 属性
        
        /// <summary>
        /// 当前鼠标位置X坐标
        /// </summary>
        public string CurrentX
        {
            get => _currentPosition.X.ToString(_coordinateFormat);
        }
        
        /// <summary>
        /// 当前鼠标位置Y坐标
        /// </summary>
        public string CurrentY
        {
            get => _currentPosition.Y.ToString(_coordinateFormat);
        }
        
        /// <summary>
        /// 坐标格式化字符串
        /// </summary>
        public string CoordinateFormat
        {
            get => _coordinateFormat;
            set
            {
                if (_coordinateFormat != value)
                {
                    _coordinateFormat = value;
                    OnPropertyChanged(nameof(CoordinateFormat));
                    OnPropertyChanged(nameof(CurrentX));
                    OnPropertyChanged(nameof(CurrentY));
                }
            }
        }
        
        /// <summary>
        /// 捕捉状态文本
        /// </summary>
        public string SnapStatus
        {
            get => _snapStatus;
            private set
            {
                if (_snapStatus != value)
                {
                    _snapStatus = value;
                    OnPropertyChanged(nameof(SnapStatus));
                }
            }
        }
        
        /// <summary>
        /// 捕捉状态颜色
        /// </summary>
        public Brush SnapStatusColor
        {
            get => _isSnapEnabled ? Brushes.Green : Brushes.Gray;
        }
        
        /// <summary>
        /// 极轴追踪状态
        /// </summary>
        public string PolarTrackingStatus
        {
            get => _isPolarTrackingEnabled ? "极轴:开" : "极轴:关";
        }
        
        /// <summary>
        /// 极轴追踪状态颜色
        /// </summary>
        public Brush PolarTrackingColor
        {
            get => _isPolarTrackingEnabled ? Brushes.Green : Brushes.Gray;
        }
        
        /// <summary>
        /// 对象追踪状态
        /// </summary>
        public string ObjectTrackingStatus
        {
            get => _isObjectTrackingEnabled ? "追踪:开" : "追踪:关";
        }
        
        /// <summary>
        /// 对象追踪状态颜色
        /// </summary>
        public Brush ObjectTrackingColor
        {
            get => _isObjectTrackingEnabled ? Brushes.Green : Brushes.Gray;
        }
        
        /// <summary>
        /// 动态输入状态
        /// </summary>
        public string DynamicInputStatus
        {
            get => _isDynamicInputEnabled ? "输入:开" : "输入:关";
        }
        
        /// <summary>
        /// 动态输入状态颜色
        /// </summary>
        public Brush DynamicInputColor
        {
            get => _isDynamicInputEnabled ? Brushes.Green : Brushes.Gray;
        }
        
        /// <summary>
        /// 当前图层名称
        /// </summary>
        public string CurrentLayer
        {
            get => _currentLayer;
            private set
            {
                if (_currentLayer != value)
                {
                    _currentLayer = value;
                    OnPropertyChanged(nameof(CurrentLayer));
                }
            }
        }
        
        /// <summary>
        /// 图层颜色
        /// </summary>
        public Brush LayerColor
        {
            get => _layerColor;
            private set
            {
                if (_layerColor != value)
                {
                    _layerColor = value;
                    OnPropertyChanged(nameof(LayerColor));
                }
            }
        }
        
        /// <summary>
        /// 缩放百分比
        /// </summary>
        public string ZoomPercentage
        {
            get => _zoomPercentage;
            private set
            {
                if (_zoomPercentage != value)
                {
                    _zoomPercentage = value;
                    OnPropertyChanged(nameof(ZoomPercentage));
                }
            }
        }
        
        /// <summary>
        /// 标注样式
        /// </summary>
        public string DimensionStyle
        {
            get => _dimensionStyle;
            set
            {
                if (_dimensionStyle != value)
                {
                    _dimensionStyle = value;
                    OnPropertyChanged(nameof(DimensionStyle));
                }
            }
        }
        
        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged(nameof(StatusMessage));
                }
            }
        }
        
        #endregion
        
        #region 更新方法
        
        /// <summary>
        /// 更新坐标显示
        /// </summary>
        /// <param name="position">新的坐标位置</param>
        public void UpdateCoordinates(Vector2 position)
        {
            _currentPosition = position;
            OnPropertyChanged(nameof(CurrentX));
            OnPropertyChanged(nameof(CurrentY));
        }
        
        /// <summary>
        /// 更新捕捉状态
        /// </summary>
        public void UpdateSnapStatus()
        {
            if (_viewBase._inputManager?.snapMgr != null)
            {
                var snapModes = _viewBase._inputManager.snapMgr.RunningSnapModes;
                _isSnapEnabled = snapModes != ObjectSnapMode.Undefined;
                
                if (_isSnapEnabled)
                {
                    var activeSnaps = GetActiveSnapModes(snapModes);
                    SnapStatus = $"捕捉:{activeSnaps}";
                }
                else
                {
                    SnapStatus = "捕捉:关闭";
                }
                
                OnPropertyChanged(nameof(SnapStatusColor));
            }
        }
        
        /// <summary>
        /// 更新追踪状态
        /// </summary>
        public void UpdateTrackingStatus()
        {
            if (_phase2Manager != null)
            {
                _isPolarTrackingEnabled = _phase2Manager.GetPolarTrackingEnabled();
                _isObjectTrackingEnabled = _phase2Manager.GetObjectTrackingEnabled();
                _isDynamicInputEnabled = _phase2Manager.GetDynamicInputEnabled();
                
                OnPropertyChanged(nameof(PolarTrackingStatus));
                OnPropertyChanged(nameof(PolarTrackingColor));
                OnPropertyChanged(nameof(ObjectTrackingStatus));
                OnPropertyChanged(nameof(ObjectTrackingColor));
                OnPropertyChanged(nameof(DynamicInputStatus));
                OnPropertyChanged(nameof(DynamicInputColor));
            }
        }
        
        /// <summary>
        /// 更新图层信息
        /// </summary>
        public void UpdateLayerInfo()
        {
            if (_viewBase.Document?.ActiveLayer != null)
            {
                CurrentLayer = _viewBase.Document.ActiveLayer.Name;
                // 这里可以根据图层属性设置颜色
                LayerColor = Brushes.Black; // 默认颜色
            }
        }
        
        /// <summary>
        /// 更新缩放比例
        /// </summary>
        public void UpdateZoomScale()
        {
            if (_viewportManager != null)
            {
                var currentState = _viewportManager.GetCurrentState();
                if (currentState != null)
                {
                    _zoomScale = currentState.Scale;
                    var percentage = (int)(_zoomScale * 100);
                    ZoomPercentage = $"{percentage}%";
                }
            }
        }
        
        /// <summary>
        /// 更新所有状态
        /// </summary>
        public void UpdateAllStatus()
        {
            UpdateSnapStatus();
            UpdateTrackingStatus();
            UpdateLayerInfo();
            UpdateZoomScale();
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnMouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var modelPoint = _viewBase.CanvasToModel(new Vector2(e.X, e.Y));
            UpdateCoordinates(modelPoint);
        }
        
        #endregion
        
        #region 辅助方法
        
        /// <summary>
        /// 获取激活的捕捉模式文本
        /// </summary>
        private string GetActiveSnapModes(ObjectSnapMode snapModes)
        {
            var modes = new System.Collections.Generic.List<string>();
            
            if ((snapModes & ObjectSnapMode.End) != 0) modes.Add("端");
            if ((snapModes & ObjectSnapMode.Mid) != 0) modes.Add("中");
            if ((snapModes & ObjectSnapMode.Center) != 0) modes.Add("心");
            if ((snapModes & ObjectSnapMode.Intersection) != 0) modes.Add("交");
            if ((snapModes & ObjectSnapMode.Perpendicular) != 0) modes.Add("垂");
            if ((snapModes & ObjectSnapMode.Tangent) != 0) modes.Add("切");
            if ((snapModes & ObjectSnapMode.Quad) != 0) modes.Add("象");
            if ((snapModes & ObjectSnapMode.Near) != 0) modes.Add("近");
            
            return modes.Count > 0 ? string.Join(",", modes) : "基本";
        }
        
        #endregion
        
        #region INotifyPropertyChanged
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        #endregion
        
        #region 资源清理
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _viewBase.MouseMove -= OnMouseMove;
        }
        
        #endregion
    }
}
