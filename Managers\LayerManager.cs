using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.Tables;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 图层管理器
    /// 负责管理所有图层的创建、删除、属性设置等操作
    /// </summary>
    public class LayerManager : ObservableObject
    {
        #region 私有字段

        private ObservableCollection<EntityLayer> _layers;
        private EntityLayer _currentLayer;
        private DocumentBase _document;
        private bool _showHiddenLayers;

        #endregion

        #region 事件

        /// <summary>
        /// 图层添加事件
        /// </summary>
        public event EventHandler<LayerEventArgs> LayerAdded;

        /// <summary>
        /// 图层删除事件
        /// </summary>
        public event EventHandler<LayerEventArgs> LayerRemoved;

        /// <summary>
        /// 图层属性改变事件
        /// </summary>
        public event EventHandler<LayerPropertyChangedEventArgs> LayerPropertyChanged;

        /// <summary>
        /// 当前图层改变事件
        /// </summary>
        public event EventHandler<LayerEventArgs> CurrentLayerChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化图层管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public LayerManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _layers = new ObservableCollection<EntityLayer>();
            _showHiddenLayers = true;

            // 创建默认图层
            CreateDefaultLayer();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 图层集合
        /// </summary>
        public ObservableCollection<EntityLayer> Layers
        {
            get => _layers;
            private set => SetProperty(ref _layers, value);
        }

        /// <summary>
        /// 当前活动图层
        /// </summary>
        public EntityLayer CurrentLayer
        {
            get => _currentLayer;
            set
            {
                if (SetProperty(ref _currentLayer, value))
                {
                    OnCurrentLayerChanged(value);
                }
            }
        }

        /// <summary>
        /// 是否显示隐藏图层
        /// </summary>
        public bool ShowHiddenLayers
        {
            get => _showHiddenLayers;
            set => SetProperty(ref _showHiddenLayers, value);
        }

        /// <summary>
        /// 可见图层数量
        /// </summary>
        public int VisibleLayerCount => _layers.Count(l => l.IsVisible);

        /// <summary>
        /// 锁定图层数量
        /// </summary>
        public int LockedLayerCount => _layers.Count(l => l.IsLocked);

        #endregion

        #region 图层操作方法

        /// <summary>
        /// 创建新图层
        /// </summary>
        /// <param name="name">图层名称</param>
        /// <param name="color">图层颜色</param>
        /// <param name="lineWeight">线宽</param>
        /// <param name="description">图层描述</param>
        /// <returns>创建的图层</returns>
        public EntityLayer CreateLayer(string name, SKColor? color = null, float lineWeight = 1.0f, string description = "")
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("图层名称不能为空", nameof(name));

            if (LayerExists(name))
                throw new InvalidOperationException($"图层 '{name}' 已存在");

            var layer = new EntityLayer(name)
            {
                Description = description,
                IsVisible = true,
                IsLocked = false,
                IsMarkerable = true
            };

            // 设置图层颜色
            if (color.HasValue)
            {
                // 这里可以扩展颜色设置逻辑
            }

            _layers.Add(layer);
            OnLayerAdded(layer);

            // 如果这是第一个图层或当前没有活动图层，设置为当前图层
            if (_currentLayer == null || _layers.Count == 1)
            {
                CurrentLayer = layer;
            }

            return layer;
        }

        /// <summary>
        /// 删除图层
        /// </summary>
        /// <param name="layer">要删除的图层</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteLayer(EntityLayer layer)
        {
            if (layer == null)
                return false;

            if (IsDefaultLayer(layer))
                throw new InvalidOperationException("不能删除默认图层");

            if (layer.Children.Count > 0)
            {
                var result = MessageBox.Show(
                    $"图层 '{layer.Name}' 包含 {layer.Children.Count} 个对象。\n是否确定删除？删除后对象将移动到默认图层。",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return false;

                // 将图层中的对象移动到默认图层
                MoveLayerContentsToDefault(layer);
            }

            _layers.Remove(layer);
            OnLayerRemoved(layer);

            // 如果删除的是当前图层，选择新的当前图层
            if (_currentLayer == layer)
            {
                CurrentLayer = GetDefaultLayer() ?? _layers.FirstOrDefault();
            }

            return true;
        }

        /// <summary>
        /// 删除图层（按名称）
        /// </summary>
        /// <param name="layerName">图层名称</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteLayer(string layerName)
        {
            var layer = GetLayer(layerName);
            return layer != null && DeleteLayer(layer);
        }

        /// <summary>
        /// 重命名图层
        /// </summary>
        /// <param name="layer">要重命名的图层</param>
        /// <param name="newName">新名称</param>
        /// <returns>是否重命名成功</returns>
        public bool RenameLayer(EntityLayer layer, string newName)
        {
            if (layer == null || string.IsNullOrWhiteSpace(newName))
                return false;

            if (LayerExists(newName))
                throw new InvalidOperationException($"图层名称 '{newName}' 已存在");

            var oldName = layer.Name;
            layer.Name = newName;

            OnLayerPropertyChanged(layer, "Name", oldName, newName);
            return true;
        }

        /// <summary>
        /// 锁定图层
        /// </summary>
        /// <param name="layer">要锁定的图层</param>
        /// <param name="locked">是否锁定</param>
        public void SetLayerLocked(EntityLayer layer, bool locked)
        {
            if (layer == null) return;

            var oldValue = layer.IsLocked;
            layer.IsLocked = locked;

            OnLayerPropertyChanged(layer, "IsLocked", oldValue, locked);
        }

        /// <summary>
        /// 冻结图层
        /// </summary>
        /// <param name="layer">要冻结的图层</param>
        /// <param name="frozen">是否冻结</param>
        public void SetLayerFrozen(EntityLayer layer, bool frozen)
        {
            if (layer == null) return;

            // 冻结状态通过可见性来控制
            var oldValue = !layer.IsVisible;
            layer.IsVisible = !frozen;

            OnLayerPropertyChanged(layer, "IsFrozen", oldValue, frozen);
        }

        /// <summary>
        /// 设置图层可见性
        /// </summary>
        /// <param name="layer">图层</param>
        /// <param name="visible">是否可见</param>
        public void SetLayerVisible(EntityLayer layer, bool visible)
        {
            if (layer == null) return;

            var oldValue = layer.IsVisible;
            layer.IsVisible = visible;

            OnLayerPropertyChanged(layer, "IsVisible", oldValue, visible);
        }

        /// <summary>
        /// 设置图层可标记性
        /// </summary>
        /// <param name="layer">图层</param>
        /// <param name="markerable">是否可标记</param>
        public void SetLayerMarkerable(EntityLayer layer, bool markerable)
        {
            if (layer == null) return;

            var oldValue = layer.IsMarkerable;
            layer.IsMarkerable = markerable;

            OnLayerPropertyChanged(layer, "IsMarkerable", oldValue, markerable);
        }

        /// <summary>
        /// 批量操作图层
        /// </summary>
        /// <param name="layers">图层列表</param>
        /// <param name="action">操作类型</param>
        /// <param name="value">操作值</param>
        public void BatchOperation(IEnumerable<EntityLayer> layers, LayerBatchOperation action, object value = null)
        {
            if (layers == null) return;

            foreach (var layer in layers)
            {
                switch (action)
                {
                    case LayerBatchOperation.Lock:
                        SetLayerLocked(layer, (bool)(value ?? true));
                        break;
                    case LayerBatchOperation.Unlock:
                        SetLayerLocked(layer, false);
                        break;
                    case LayerBatchOperation.Freeze:
                        SetLayerFrozen(layer, true);
                        break;
                    case LayerBatchOperation.Thaw:
                        SetLayerFrozen(layer, false);
                        break;
                    case LayerBatchOperation.Hide:
                        SetLayerVisible(layer, false);
                        break;
                    case LayerBatchOperation.Show:
                        SetLayerVisible(layer, true);
                        break;
                }
            }
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取图层
        /// </summary>
        /// <param name="name">图层名称</param>
        /// <returns>图层对象</returns>
        public EntityLayer GetLayer(string name)
        {
            return _layers.FirstOrDefault(l => string.Equals(l.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查图层是否存在
        /// </summary>
        /// <param name="name">图层名称</param>
        /// <returns>是否存在</returns>
        public bool LayerExists(string name)
        {
            return _layers.Any(l => string.Equals(l.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取默认图层
        /// </summary>
        /// <returns>默认图层</returns>
        public EntityLayer GetDefaultLayer()
        {
            return _layers.FirstOrDefault(l => IsDefaultLayer(l));
        }

        /// <summary>
        /// 判断是否为默认图层
        /// </summary>
        /// <param name="layer">图层</param>
        /// <returns>是否为默认图层</returns>
        public bool IsDefaultLayer(EntityLayer layer)
        {
            return layer != null && (layer.Name == "0" || layer.Name == "Default");
        }

        /// <summary>
        /// 获取可见图层
        /// </summary>
        /// <returns>可见图层列表</returns>
        public IEnumerable<EntityLayer> GetVisibleLayers()
        {
            return _layers.Where(l => l.IsVisible);
        }

        /// <summary>
        /// 获取锁定图层
        /// </summary>
        /// <returns>锁定图层列表</returns>
        public IEnumerable<EntityLayer> GetLockedLayers()
        {
            return _layers.Where(l => l.IsLocked);
        }

        /// <summary>
        /// 获取可编辑图层
        /// </summary>
        /// <returns>可编辑图层列表</returns>
        public IEnumerable<EntityLayer> GetEditableLayers()
        {
            return _layers.Where(l => l.IsVisible && !l.IsLocked);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建默认图层
        /// </summary>
        private void CreateDefaultLayer()
        {
            if (!LayerExists("0"))
            {
                var defaultLayer = new EntityLayer("0")
                {
                    Description = "默认图层",
                    IsVisible = true,
                    IsLocked = false,
                    IsMarkerable = true
                };

                _layers.Add(defaultLayer);
                _currentLayer = defaultLayer;
            }
        }

        /// <summary>
        /// 将图层内容移动到默认图层
        /// </summary>
        /// <param name="layer">源图层</param>
        private void MoveLayerContentsToDefault(EntityLayer layer)
        {
            var defaultLayer = GetDefaultLayer();
            if (defaultLayer == null || layer == defaultLayer) return;

            var entities = layer.Children.ToList();
            foreach (var entity in entities)
            {
                layer.Remove(entity);
                defaultLayer.Add(entity);
                entity.Parent = defaultLayer;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发图层添加事件
        /// </summary>
        /// <param name="layer">添加的图层</param>
        protected virtual void OnLayerAdded(EntityLayer layer)
        {
            LayerAdded?.Invoke(this, new LayerEventArgs(layer));
        }

        /// <summary>
        /// 触发图层删除事件
        /// </summary>
        /// <param name="layer">删除的图层</param>
        protected virtual void OnLayerRemoved(EntityLayer layer)
        {
            LayerRemoved?.Invoke(this, new LayerEventArgs(layer));
        }

        /// <summary>
        /// 触发图层属性改变事件
        /// </summary>
        protected virtual void OnLayerPropertyChanged(EntityLayer layer, string propertyName, object oldValue, object newValue)
        {
            LayerPropertyChanged?.Invoke(this, new LayerPropertyChangedEventArgs(layer, propertyName, oldValue, newValue));
        }

        /// <summary>
        /// 触发当前图层改变事件
        /// </summary>
        protected virtual void OnCurrentLayerChanged(EntityLayer layer)
        {
            CurrentLayerChanged?.Invoke(this, new LayerEventArgs(layer));
        }

        #endregion
    }

    #region 辅助类和枚举

    /// <summary>
    /// 图层批量操作类型
    /// </summary>
    public enum LayerBatchOperation
    {
        Lock,
        Unlock,
        Freeze,
        Thaw,
        Hide,
        Show
    }

    /// <summary>
    /// 图层事件参数
    /// </summary>
    public class LayerEventArgs : EventArgs
    {
        public EntityLayer Layer { get; }

        public LayerEventArgs(EntityLayer layer)
        {
            Layer = layer;
        }
    }

    /// <summary>
    /// 图层属性改变事件参数
    /// </summary>
    public class LayerPropertyChangedEventArgs : EventArgs
    {
        public EntityLayer Layer { get; }
        public string PropertyName { get; }
        public object OldValue { get; }
        public object NewValue { get; }

        public LayerPropertyChangedEventArgs(EntityLayer layer, string propertyName, object oldValue, object newValue)
        {
            Layer = layer;
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }

    #endregion
} 