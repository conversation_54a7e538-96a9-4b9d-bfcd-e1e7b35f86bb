using System.Numerics;

namespace McLaser.EditViewerSk.Interfaces
{
    /// <summary>
    /// 坐标转换服务接口
    /// 统一管理Canvas坐标系和Model坐标系之间的转换
    /// </summary>
    public interface ICoordinateService
    {
        /// <summary>
        /// 将Canvas坐标转换为Model坐标
        /// </summary>
        /// <param name="canvasPoint">Canvas坐标点</param>
        /// <returns>Model坐标点</returns>
        Vector2 CanvasToModel(Vector2 canvasPoint);

        /// <summary>
        /// 将Model坐标转换为Canvas坐标
        /// </summary>
        /// <param name="modelPoint">Model坐标点</param>
        /// <returns>Canvas坐标点</returns>
        Vector2 ModelToCanvas(Vector2 modelPoint);

        /// <summary>
        /// 将Canvas距离转换为Model距离
        /// </summary>
        /// <param name="canvasDistance">Canvas距离</param>
        /// <returns>Model距离</returns>
        float CanvasToModel(double canvasDistance);

        /// <summary>
        /// 将Model距离转换为Canvas距离
        /// </summary>
        /// <param name="modelDistance">Model距离</param>
        /// <returns>Canvas距离</returns>
        float ModelToCanvas(double modelDistance);

        /// <summary>
        /// 屏幕坐标转换为Canvas坐标（用于处理鼠标事件）
        /// </summary>
        /// <param name="screenPoint">屏幕坐标点</param>
        /// <returns>Canvas坐标点</returns>
        Vector2 ScreenToCanvas(Vector2 screenPoint);

        /// <summary>
        /// Canvas坐标转换为屏幕坐标
        /// </summary>
        /// <param name="canvasPoint">Canvas坐标点</param>
        /// <returns>屏幕坐标点</returns>
        Vector2 CanvasToScreen(Vector2 canvasPoint);
    }
} 