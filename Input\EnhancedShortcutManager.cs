using System;
using System.Collections.Generic;
using System.Windows.Forms;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Viewport;
using McLaser.EditViewerSk.Managers;

namespace McLaser.EditViewerSk.Input
{
    /// <summary>
    /// 增强的快捷键管理器
    /// 处理标准CAD快捷键绑定和功能执行
    /// </summary>
    public class EnhancedShortcutManager
    {
        private readonly ViewBase _viewBase;
        private readonly ViewToolbarManager _viewToolbarManager;
        private readonly Phase2IntegrationManager _phase2Manager;
        private Viewport.InteractiveViewManager _interactiveViewManager;
        private UI.EnhancedStatusBarManager _statusBarManager;
        
        // 快捷键映射
        private readonly Dictionary<Keys, Action> _shortcutActions;
        private readonly Dictionary<Keys, string> _shortcutDescriptions;
        
        // 上一个命令记录
        private string _lastCommand = "";
        
        // 事件
        public event EventHandler<ShortcutExecutedEventArgs> ShortcutExecuted;
        
        public EnhancedShortcutManager(ViewBase viewBase, ViewToolbarManager viewToolbarManager)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _viewToolbarManager = viewToolbarManager ?? throw new ArgumentNullException(nameof(viewToolbarManager));
            _phase2Manager = _viewBase.GetPhase2Manager();
            
            _shortcutActions = new Dictionary<Keys, Action>();
            _shortcutDescriptions = new Dictionary<Keys, string>();
            
            InitializeShortcuts();
        }
        
        /// <summary>
        /// 初始化快捷键映射
        /// </summary>
        private void InitializeShortcuts()
        {
            // F8 - 极轴追踪开关
            RegisterShortcut(Keys.F8, TogglePolarTracking, "切换极轴追踪");
            
            // F9 - 对象捕捉开关
            RegisterShortcut(Keys.F9, ToggleObjectSnap, "切换对象捕捉");
            
            // ESC - 取消当前命令
            RegisterShortcut(Keys.Escape, CancelCurrentCommand, "取消当前命令");
            
            // Ctrl+Z - 撤销
            RegisterShortcut(Keys.Control | Keys.Z, ExecuteUndo, "撤销");
            
            // Ctrl+Y - 重做
            RegisterShortcut(Keys.Control | Keys.Y, ExecuteRedo, "重做");
            
            // 空格键 - 重复上一命令
            RegisterShortcut(Keys.Space, RepeatLastCommand, "重复上一命令");
            
            // Delete - 删除选中对象
            RegisterShortcut(Keys.Delete, DeleteSelected, "删除选中对象");
            
            // Ctrl+A - 全选
            RegisterShortcut(Keys.Control | Keys.A, SelectAll, "全选");
            
            // Ctrl+C - 复制
            RegisterShortcut(Keys.Control | Keys.C, CopySelected, "复制");
            
            // Ctrl+V - 粘贴
            RegisterShortcut(Keys.Control | Keys.V, PasteClipboard, "粘贴");
            
            // F3 - 对象捕捉开关（备用）
            RegisterShortcut(Keys.F3, ToggleObjectSnap, "切换对象捕捉");
            
            // F11 - 对象追踪开关
            RegisterShortcut(Keys.F11, ToggleObjectTracking, "切换对象追踪");
            
            // F12 - 动态输入开关
            RegisterShortcut(Keys.F12, ToggleDynamicInput, "切换动态输入");
            
            // 视图相关快捷键
            RegisterShortcut(Keys.Control | Keys.D1, () => _viewToolbarManager.SetTopView(), "俯视图");
            RegisterShortcut(Keys.Control | Keys.D2, () => _viewToolbarManager.SetFrontView(), "前视图");
            RegisterShortcut(Keys.Control | Keys.D3, () => _viewToolbarManager.SetRightView(), "右视图");
            RegisterShortcut(Keys.Control | Keys.D4, () => _viewToolbarManager.SetIsometricView(), "等轴测视图");

            // 缩放相关快捷键
            RegisterShortcut(Keys.Control | Keys.E, () => _viewToolbarManager.ZoomToFit(), "缩放到全部显示");
            RegisterShortcut(Keys.Control | Keys.W, () => _viewToolbarManager.ActivateZoomWindow(), "缩放窗口");

            // 视图历史
            RegisterShortcut(Keys.Control | Keys.Left, () => _viewToolbarManager.GoToPreviousView(), "上一视图");
            RegisterShortcut(Keys.Control | Keys.Right, () => _viewToolbarManager.GoToNextView(), "下一视图");

            // 便捷操作快捷键
            RegisterShortcut(Keys.Control | Keys.R, ResetView, "重置视图");
            RegisterShortcut(Keys.Control | Keys.Shift | Keys.Z, () => QuickZoom(1.5f), "快速放大");
            RegisterShortcut(Keys.Control | Keys.Shift | Keys.X, () => QuickZoom(0.67f), "快速缩小");
        }
        
        /// <summary>
        /// 注册快捷键
        /// </summary>
        private void RegisterShortcut(Keys key, Action action, string description)
        {
            _shortcutActions[key] = action;
            _shortcutDescriptions[key] = description;
        }
        
        /// <summary>
        /// 处理快捷键
        /// </summary>
        /// <param name="keyData">按键数据</param>
        /// <returns>是否处理成功</returns>
        public bool HandleShortcut(Keys keyData)
        {
            if (_shortcutActions.TryGetValue(keyData, out var action))
            {
                try
                {
                    action.Invoke();
                    
                    // 触发事件
                    var description = _shortcutDescriptions.TryGetValue(keyData, out var desc) ? desc : "未知命令";
                    OnShortcutExecuted(keyData, description);
                    
                    return true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"执行快捷键失败: {ex.Message}");
                    return false;
                }
            }
            
            return false;
        }
        
        #region 快捷键功能实现
        
        /// <summary>
        /// 切换极轴追踪
        /// </summary>
        private void TogglePolarTracking()
        {
            if (_phase2Manager != null)
            {
                var currentState = _phase2Manager.GetPolarTrackingEnabled();
                _phase2Manager.SetPolarTrackingEnabled(!currentState);

                var status = !currentState ? "开启" : "关闭";
                _viewBase.Document.Prompt = $"极轴追踪已{status}";

                // 立即更新状态栏
                _statusBarManager?.UpdateTrackingStatus();
            }
        }
        
        /// <summary>
        /// 切换对象捕捉
        /// </summary>
        private void ToggleObjectSnap()
        {
            if (_viewBase._inputManager?.snapMgr != null)
            {
                var currentModes = _viewBase._inputManager.snapMgr.RunningSnapModes;
                var isEnabled = currentModes != ObjectSnapMode.Undefined;

                if (isEnabled)
                {
                    _viewBase._inputManager.snapMgr.RunningSnapModes = ObjectSnapMode.Undefined;
                    _viewBase.Document.Prompt = "对象捕捉已关闭";
                }
                else
                {
                    _viewBase._inputManager.snapMgr.RunningSnapModes = ObjectSnapMode.Basic;
                    _viewBase.Document.Prompt = "对象捕捉已开启";
                }

                // 立即更新状态栏
                _statusBarManager?.UpdateSnapStatus();
            }
        }
        
        /// <summary>
        /// 切换对象追踪
        /// </summary>
        private void ToggleObjectTracking()
        {
            if (_phase2Manager != null)
            {
                var currentState = _phase2Manager.GetObjectTrackingEnabled();
                _phase2Manager.SetObjectTrackingEnabled(!currentState);
                
                var status = !currentState ? "开启" : "关闭";
                _viewBase.Document.Prompt = $"对象追踪已{status}";
            }
        }
        
        /// <summary>
        /// 切换动态输入
        /// </summary>
        private void ToggleDynamicInput()
        {
            if (_phase2Manager != null)
            {
                var currentState = _phase2Manager.GetDynamicInputEnabled();
                _phase2Manager.SetDynamicInputEnabled(!currentState);
                
                var status = !currentState ? "开启" : "关闭";
                _viewBase.Document.Prompt = $"动态输入已{status}";
            }
        }
        
        /// <summary>
        /// 取消当前命令
        /// </summary>
        private void CancelCurrentCommand()
        {
            // 取消当前命令
            _viewBase._cmdsMgr?.CancelCurrentCommand();
            
            // 清除选择
            _viewBase.Document.SelectedEntitys.Clear();
            
            // 停用视图工具
            _viewToolbarManager?.DeactivateCurrentTool();
            
            _viewBase.Document.Prompt = "命令已取消";
        }
        
        /// <summary>
        /// 执行撤销
        /// </summary>
        private void ExecuteUndo()
        {
            _viewBase.Document.Action.ActUndo();
            _lastCommand = "UNDO";
        }
        
        /// <summary>
        /// 执行重做
        /// </summary>
        private void ExecuteRedo()
        {
            _viewBase.Document.Action.ActRedo();
            _lastCommand = "REDO";
        }
        
        /// <summary>
        /// 重复上一命令
        /// </summary>
        private void RepeatLastCommand()
        {
            if (!string.IsNullOrEmpty(_lastCommand))
            {
                // 这里可以根据需要实现命令重复逻辑
                _viewBase.Document.Prompt = $"重复命令: {_lastCommand}";
            }
            else
            {
                _viewBase.Document.Prompt = "没有可重复的命令";
            }
        }
        
        /// <summary>
        /// 删除选中对象
        /// </summary>
        private void DeleteSelected()
        {
            if (_viewBase.Document.SelectedEntitys.Count > 0)
            {
                // 执行删除命令
                var deleteCount = _viewBase.Document.SelectedEntitys.Count;
                _viewBase.Document.SelectedEntitys.Clear();
                _viewBase.Document.Prompt = $"已删除 {deleteCount} 个对象";
                _lastCommand = "DELETE";
            }
            else
            {
                _viewBase.Document.Prompt = "没有选中的对象";
            }
        }
        
        /// <summary>
        /// 全选
        /// </summary>
        private void SelectAll()
        {
            // 实现全选逻辑
            _viewBase.Document.Prompt = "全选命令";
            _lastCommand = "SELECTALL";
        }
        
        /// <summary>
        /// 复制选中对象
        /// </summary>
        private void CopySelected()
        {
            if (_viewBase.Document.SelectedEntitys.Count > 0)
            {
                _viewBase.Document.Prompt = $"已复制 {_viewBase.Document.SelectedEntitys.Count} 个对象";
                _lastCommand = "COPY";
            }
            else
            {
                _viewBase.Document.Prompt = "没有选中的对象";
            }
        }
        
        /// <summary>
        /// 粘贴剪贴板内容
        /// </summary>
        private void PasteClipboard()
        {
            _viewBase.Document.Prompt = "粘贴命令";
            _lastCommand = "PASTE";
        }

        /// <summary>
        /// 重置视图
        /// </summary>
        private void ResetView()
        {
            _interactiveViewManager?.ResetView();
            _viewBase.Document.Prompt = "视图已重置";
            _lastCommand = "RESETVIEW";
        }

        /// <summary>
        /// 快速缩放
        /// </summary>
        private void QuickZoom(float scaleFactor)
        {
            _interactiveViewManager?.QuickZoom(scaleFactor);
            _viewBase.Document.Prompt = $"快速缩放: {scaleFactor:F2}x";
            _lastCommand = "QUICKZOOM";
        }
        
        #endregion
        
        /// <summary>
        /// 获取所有快捷键描述
        /// </summary>
        /// <returns>快捷键描述字典</returns>
        public Dictionary<Keys, string> GetShortcutDescriptions()
        {
            return new Dictionary<Keys, string>(_shortcutDescriptions);
        }
        
        /// <summary>
        /// 设置上一个命令
        /// </summary>
        /// <param name="command">命令名称</param>
        public void SetLastCommand(string command)
        {
            _lastCommand = command;
        }

        /// <summary>
        /// 设置交互式视图管理器
        /// </summary>
        public void SetInteractiveViewManager(Viewport.InteractiveViewManager manager)
        {
            _interactiveViewManager = manager;
        }

        /// <summary>
        /// 设置状态栏管理器
        /// </summary>
        public void SetStatusBarManager(UI.EnhancedStatusBarManager manager)
        {
            _statusBarManager = manager;
        }
        
        /// <summary>
        /// 触发快捷键执行事件
        /// </summary>
        private void OnShortcutExecuted(Keys key, string description)
        {
            ShortcutExecuted?.Invoke(this, new ShortcutExecutedEventArgs(key, description));
        }
    }
    
    /// <summary>
    /// 快捷键执行事件参数
    /// </summary>
    public class ShortcutExecutedEventArgs : EventArgs
    {
        public Keys Key { get; }
        public string Description { get; }
        
        public ShortcutExecutedEventArgs(Keys key, string description)
        {
            Key = key;
            Description = description;
        }
    }
}
