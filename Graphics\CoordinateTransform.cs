using System;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 坐标转换服务实现
    /// 基于CAD软件标准，支持多种坐标空间的精确转换
    /// </summary>
    public class CoordinateTransform : ICoordinateTransform
    {
        private readonly ViewBase _viewBase;
        private SKMatrix _viewMatrix;
        private SKMatrix _projectionMatrix;
        private bool _matrixCacheValid;

        public CoordinateTransform(ViewBase viewBase)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _matrixCacheValid = false;
        }

        /// <summary>
        /// 在不同坐标空间之间转换点
        /// </summary>
        public Vector2 TransformPoint(Vector2 point, CoordinateSpace from, CoordinateSpace to)
        {
            if (from == to)
                return point;

            return from switch
            {
                CoordinateSpace.Screen when to == CoordinateSpace.Viewport => ScreenToViewport(point),
                CoordinateSpace.Screen when to == CoordinateSpace.Model => ScreenToModel(point),
                CoordinateSpace.Viewport when to == CoordinateSpace.Screen => ViewportToScreen(point),
                CoordinateSpace.Viewport when to == CoordinateSpace.Model => ViewportToModel(point),
                CoordinateSpace.Model when to == CoordinateSpace.Screen => ModelToScreen(point),
                CoordinateSpace.Model when to == CoordinateSpace.Viewport => ModelToViewport(point),
                _ => point
            };
        }

        /// <summary>
        /// 在不同坐标空间之间转换距离
        /// </summary>
        public double TransformDistance(double distance, CoordinateSpace from, CoordinateSpace to)
        {
            if (from == to)
                return distance;

            // 使用单位向量进行距离转换
            var unitVector = new Vector2((float)distance, 0);
            var transformedVector = TransformPoint(unitVector, from, to);
            var origin = TransformPoint(Vector2.Zero, from, to);
            
            return Math.Sqrt(Math.Pow(transformedVector.X - origin.X, 2) + Math.Pow(transformedVector.Y - origin.Y, 2));
        }

        /// <summary>
        /// 在不同坐标空间之间转换角度
        /// </summary>
        public double TransformAngle(double angle, CoordinateSpace from, CoordinateSpace to)
        {
            if (from == to)
                return angle;

            // 对于Y轴翻转的坐标系，需要调整角度
            return from switch
            {
                CoordinateSpace.Model when to == CoordinateSpace.Viewport => -angle, // Y轴翻转
                CoordinateSpace.Viewport when to == CoordinateSpace.Model => -angle, // Y轴翻转
                _ => angle
            };
        }

        /// <summary>
        /// 获取当前视图变换矩阵
        /// </summary>
        public SKMatrix GetViewMatrix()
        {
            UpdateMatrixCache();
            return _viewMatrix;
        }

        /// <summary>
        /// 获取当前投影矩阵
        /// </summary>
        public SKMatrix GetProjectionMatrix()
        {
            UpdateMatrixCache();
            return _projectionMatrix;
        }

        #region 私有转换方法

        private Vector2 ScreenToViewport(Vector2 screenPoint)
        {
            // Screen和Viewport在当前实现中是相同的
            // 未来可以在这里添加DPI缩放处理
            return screenPoint;
        }

        private Vector2 ScreenToModel(Vector2 screenPoint)
        {
            var viewportPoint = ScreenToViewport(screenPoint);
            return ViewportToModel(viewportPoint);
        }

        private Vector2 ViewportToScreen(Vector2 viewportPoint)
        {
            // Screen和Viewport在当前实现中是相同的
            return viewportPoint;
        }

        private Vector2 ViewportToModel(Vector2 viewportPoint)
        {
            return _viewBase.CanvasToModel(viewportPoint);
        }

        private Vector2 ModelToScreen(Vector2 modelPoint)
        {
            var viewportPoint = ModelToViewport(modelPoint);
            return ViewportToScreen(viewportPoint);
        }

        private Vector2 ModelToViewport(Vector2 modelPoint)
        {
            return _viewBase.ModelToCanvas(modelPoint);
        }

        private void UpdateMatrixCache()
        {
            if (_matrixCacheValid)
                return;

            // 获取当前的变换矩阵
            var currentMatrix = GetCurrentTransformMatrix();
            
            // 分解为视图矩阵和投影矩阵
            _viewMatrix = ExtractViewMatrix(currentMatrix);
            _projectionMatrix = ExtractProjectionMatrix(currentMatrix);
            
            _matrixCacheValid = true;
        }

        private SKMatrix GetCurrentTransformMatrix()
        {
            // 从ViewBase获取当前的变换矩阵
            // 这里使用反射或友好访问来获取_matrixTrans
            var matrixField = typeof(ViewBase).GetField("_matrixTrans", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (matrixField?.GetValue(_viewBase) is SKMatrix matrix)
            {
                return matrix;
            }

            return SKMatrix.CreateIdentity();
        }

        private SKMatrix ExtractViewMatrix(SKMatrix combinedMatrix)
        {
            // 提取视图变换部分（旋转和平移）
            var viewMatrix = SKMatrix.CreateIdentity();
            viewMatrix.ScaleX = Math.Sign(combinedMatrix.ScaleX);
            viewMatrix.ScaleY = Math.Sign(combinedMatrix.ScaleY);
            viewMatrix.TransX = combinedMatrix.TransX;
            viewMatrix.TransY = combinedMatrix.TransY;
            return viewMatrix;
        }

        private SKMatrix ExtractProjectionMatrix(SKMatrix combinedMatrix)
        {
            // 提取投影变换部分（缩放）
            var projMatrix = SKMatrix.CreateIdentity();
            projMatrix.ScaleX = Math.Abs(combinedMatrix.ScaleX);
            projMatrix.ScaleY = Math.Abs(combinedMatrix.ScaleY);
            return projMatrix;
        }

        #endregion

        /// <summary>
        /// 使矩阵缓存无效
        /// </summary>
        public void InvalidateMatrixCache()
        {
            _matrixCacheValid = false;
        }
    }
} 