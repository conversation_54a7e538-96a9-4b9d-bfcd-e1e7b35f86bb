namespace McLaser.EditViewerSk.Enums
{
    /// <summary>
    /// 实体选择模式
    /// 定义了专业CAD软件中常用的各种选择方式
    /// </summary>
    public enum EntitySelectionMode
    {
        /// <summary>
        /// 点选模式 - 单击选择单个实体
        /// </summary>
        Point,
        
        /// <summary>
        /// 窗口选择模式 - 从左到右拖拽，完全包含在选择框内的实体被选中
        /// </summary>
        Window,
        
        /// <summary>
        /// 交叉选择模式 - 从右到左拖拽，与选择框相交或包含的实体被选中
        /// </summary>
        Cross,
        
        /// <summary>
        /// 多边形窗口选择 - 定义多边形区域，完全包含在内的实体被选中
        /// </summary>
        PolygonWindow,
        
        /// <summary>
        /// 多边形交叉选择 - 定义多边形区域，与多边形相交或包含的实体被选中
        /// </summary>
        PolygonCross,
        
        /// <summary>
        /// 圆形窗口选择 - 定义圆形区域，完全包含在内的实体被选中
        /// </summary>
        CircleWindow,
        
        /// <summary>
        /// 圆形交叉选择 - 定义圆形区域，与圆形相交或包含的实体被选中
        /// </summary>
        CircleCross,
        
        /// <summary>
        /// 围栏选择 - 定义线段路径，与路径相交的实体被选中
        /// </summary>
        Fence,
        
        /// <summary>
        /// 全选 - 选择当前图层中的所有实体
        /// </summary>
        All,
        
        /// <summary>
        /// 反选 - 反转当前选择状态
        /// </summary>
        Invert,
        
        /// <summary>
        /// 按类型选择 - 选择指定类型的所有实体
        /// </summary>
        ByType,
        
        /// <summary>
        /// 按颜色选择 - 选择指定颜色的所有实体
        /// </summary>
        ByColor,
        
        /// <summary>
        /// 按图层选择 - 选择指定图层中的所有实体
        /// </summary>
        ByLayer
    }
} 