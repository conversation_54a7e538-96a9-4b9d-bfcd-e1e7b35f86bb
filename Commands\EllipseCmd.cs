using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Numerics;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 椭圆绘制命令
    /// 支持多种椭圆创建方式：中心半径法、轴线法、三点法
    /// </summary>
    public class EllipseCmd : DrawCmd
    {
        #region 枚举
        /// <summary>
        /// 椭圆绘制模式
        /// </summary>
        public enum EllipseMode
        {
            CenterRadius,  // 中心+半径模式
            AxisEndpoints, // 轴线端点模式  
            ThreePoints    // 三点模式
        }
        #endregion

        #region 私有字段
        private EllipseMode _currentMode = EllipseMode.CenterRadius;
        private int _step = 0;
        private Vector2 _centerPoint;
        private Vector2 _firstAxisPoint;
        private Vector2 _secondAxisPoint;
        private Vector2 _currentPoint;
        private double _radiusX;
        private double _radiusY;
        private double _rotation;
        private EntityEllipse _previewEllipse;
        private List<EntityBase> _newEntities = new List<EntityBase>();
        #endregion

        #region 属性
        /// <summary>
        /// 当前绘制模式
        /// </summary>
        public EllipseMode CurrentMode
        {
            get => _currentMode;
            set
            {
                if (_currentMode != value)
                {
                    _currentMode = value;
                    Reset();
                }
            }
        }

        /// <summary>
        /// 新创建的实体列表
        /// </summary>
        protected override IEnumerable<EntityBase> newEntities => _newEntities;
        #endregion

        #region 构造函数
        public EllipseCmd()
        {
            // 默认使用中心半径模式
            _currentMode = EllipseMode.CenterRadius;
        }

        public EllipseCmd(EllipseMode mode)
        {
            _currentMode = mode;
        }
        #endregion

        #region 重写方法
        /// <summary>
        /// 初始化命令
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();
            Reset();
            _mgr.Viewer.Prompt = GetModePrompt();
        }

        /// <summary>
        /// 终止命令
        /// </summary>
        public override void Terminate()
        {
            RemovePreview();
            base.Terminate();
        }

        /// <summary>
        /// 鼠标移动事件
        /// </summary>
        public override EventResultStatus OnMouseMove(Vector2 position)
        {
            _currentPoint = position;
            UpdatePreview();
            return EventResultStatus.Handled;
        }

        /// <summary>
        /// 鼠标左键点击事件
        /// </summary>
        public override EventResultStatus OnMouseDown(Vector2 position)
        {
            switch (_currentMode)
            {
                case EllipseMode.CenterRadius:
                    return HandleCenterRadiusMode(position);
                case EllipseMode.AxisEndpoints:
                    return HandleAxisEndpointsMode(position);
                case EllipseMode.ThreePoints:
                    return HandleThreePointsMode(position);
                default:
                    return EventResultStatus.Unhandled;
            }
        }

        /// <summary>
        /// 键盘事件
        /// </summary>
        public override EventResultStatus OnKeyDown(System.Windows.Forms.Keys keyData)
        {
            switch (keyData)
            {
                case System.Windows.Forms.Keys.Escape:
                    if (_step > 0)
                    {
                        Reset();
                        return EventResultStatus.Handled;
                    }
                    break;
                    
                case System.Windows.Forms.Keys.M:
                    // 切换模式
                    SwitchMode();
                    return EventResultStatus.Handled;
                    
                case System.Windows.Forms.Keys.Enter:
                case System.Windows.Forms.Keys.Space:
                    if (_previewEllipse != null)
                    {
                        ConfirmEllipse();
                        return EventResultStatus.Handled;
                    }
                    break;
            }
            
            return base.OnKeyDown(keyData);
        }
        #endregion

        #region 模式处理方法
        /// <summary>
        /// 处理中心半径模式
        /// </summary>
        private EventResultStatus HandleCenterRadiusMode(Vector2 position)
        {
            switch (_step)
            {
                case 0: // 选择中心点
                    _centerPoint = position;
                    _step = 1;
                    _mgr.Viewer.Prompt = "指定第一个轴的半径或端点:";
                    CreatePreviewEllipse();
                    break;
                    
                case 1: // 选择第一个轴半径
                    _radiusX = Vector2.Distance(_centerPoint, position);
                    _firstAxisPoint = position;
                    _rotation = Math.Atan2(position.Y - _centerPoint.Y, position.X - _centerPoint.X) * 180.0 / Math.PI;
                    _step = 2;
                    _mgr.Viewer.Prompt = "指定第二个轴的半径:";
                    break;
                    
                case 2: // 选择第二个轴半径
                    CalculateSecondRadius(position);
                    ConfirmEllipse();
                    break;
            }
            
            return EventResultStatus.Handled;
        }

        /// <summary>
        /// 处理轴线端点模式
        /// </summary>
        private EventResultStatus HandleAxisEndpointsMode(Vector2 position)
        {
            switch (_step)
            {
                case 0: // 第一个轴的第一个端点
                    _firstAxisPoint = position;
                    _step = 1;
                    _mgr.Viewer.Prompt = "指定第一个轴的另一个端点:";
                    break;
                    
                case 1: // 第一个轴的第二个端点
                    _secondAxisPoint = position;
                    _centerPoint = (_firstAxisPoint + _secondAxisPoint) * 0.5f;
                    _radiusX = Vector2.Distance(_firstAxisPoint, _secondAxisPoint) * 0.5;
                    _rotation = Math.Atan2(_secondAxisPoint.Y - _firstAxisPoint.Y, _secondAxisPoint.X - _firstAxisPoint.X) * 180.0 / Math.PI;
                    _step = 2;
                    _mgr.Viewer.Prompt = "指定第二个轴的距离:";
                    CreatePreviewEllipse();
                    break;
                    
                case 2: // 第二个轴的半径
                    CalculateSecondRadius(position);
                    ConfirmEllipse();
                    break;
            }
            
            return EventResultStatus.Handled;
        }

        /// <summary>
        /// 处理三点模式
        /// </summary>
        private EventResultStatus HandleThreePointsMode(Vector2 position)
        {
            // 三点椭圆算法比较复杂，这里实现简化版本
            switch (_step)
            {
                case 0: // 第一个点
                    _firstAxisPoint = position;
                    _step = 1;
                    _mgr.Viewer.Prompt = "指定椭圆上的第二个点:";
                    break;
                    
                case 1: // 第二个点
                    _secondAxisPoint = position;
                    _step = 2;
                    _mgr.Viewer.Prompt = "指定椭圆上的第三个点:";
                    break;
                    
                case 2: // 第三个点
                    CalculateEllipseFromThreePoints(_firstAxisPoint, _secondAxisPoint, position);
                    CreatePreviewEllipse();
                    ConfirmEllipse();
                    break;
            }
            
            return EventResultStatus.Handled;
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 重置命令状态
        /// </summary>
        private void Reset()
        {
            _step = 0;
            RemovePreview();
            _mgr.Viewer.Prompt = GetModePrompt();
        }

        /// <summary>
        /// 获取模式提示信息
        /// </summary>
        private string GetModePrompt()
        {
            switch (_currentMode)
            {
                case EllipseMode.CenterRadius:
                    return "椭圆中心半径模式 - 指定椭圆的中心点: [按M切换模式]";
                case EllipseMode.AxisEndpoints:
                    return "椭圆轴线模式 - 指定第一个轴的端点: [按M切换模式]";
                case EllipseMode.ThreePoints:
                    return "椭圆三点模式 - 指定椭圆上的第一个点: [按M切换模式]";
                default:
                    return "椭圆绘制模式";
            }
        }

        /// <summary>
        /// 切换绘制模式
        /// </summary>
        private void SwitchMode()
        {
            var modes = Enum.GetValues(typeof(EllipseMode));
            var currentIndex = Array.IndexOf(modes, _currentMode);
            var nextIndex = (currentIndex + 1) % modes.Length;
            CurrentMode = (EllipseMode)modes.GetValue(nextIndex);
        }

        /// <summary>
        /// 创建预览椭圆
        /// </summary>
        private void CreatePreviewEllipse()
        {
            RemovePreview();
            
            _previewEllipse = new EntityEllipse(_centerPoint, Math.Max(_radiusX, 0.1), Math.Max(_radiusY, 0.1), _rotation)
            {
                Name = "预览椭圆",
                Color = SkiaSharp.SKColors.Gray,
                IsSelected = false
            };
            
            // 添加到临时图层进行预览
            _mgr.Viewer.Document.ActiveLayer.Children.Add(_previewEllipse);
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        private void UpdatePreview()
        {
            if (_previewEllipse == null) return;
            
            switch (_currentMode)
            {
                case EllipseMode.CenterRadius:
                    if (_step == 1)
                    {
                        // 更新第一个轴半径
                        _radiusX = Vector2.Distance(_centerPoint, _currentPoint);
                        _rotation = Math.Atan2(_currentPoint.Y - _centerPoint.Y, _currentPoint.X - _centerPoint.X) * 180.0 / Math.PI;
                        _previewEllipse.RadiusX = _radiusX;
                        _previewEllipse.Rotation = _rotation;
                    }
                    else if (_step == 2)
                    {
                        // 更新第二个轴半径
                        CalculateSecondRadius(_currentPoint);
                        _previewEllipse.RadiusY = _radiusY;
                    }
                    break;
                    
                case EllipseMode.AxisEndpoints:
                    if (_step == 2)
                    {
                        CalculateSecondRadius(_currentPoint);
                        _previewEllipse.RadiusY = _radiusY;
                    }
                    break;
            }
            
            _mgr.Viewer.RepaintCanvas();
        }

        /// <summary>
        /// 计算第二个轴的半径
        /// </summary>
        private void CalculateSecondRadius(Vector2 point)
        {
            // 将点投影到垂直于第一个轴的方向上
            var toPoint = point - _centerPoint;
            var axisDirection = new Vector2((float)Math.Cos(_rotation * Math.PI / 180.0), (float)Math.Sin(_rotation * Math.PI / 180.0));
            var perpDirection = new Vector2(-axisDirection.Y, axisDirection.X);
            
            // 计算投影长度作为第二个轴的半径
            _radiusY = Math.Abs(Vector2.Dot(toPoint, perpDirection));
        }

        /// <summary>
        /// 从三个点计算椭圆参数（简化实现）
        /// </summary>
        private void CalculateEllipseFromThreePoints(Vector2 p1, Vector2 p2, Vector2 p3)
        {
            // 简化实现：使用三点确定椭圆的近似参数
            _centerPoint = (p1 + p2 + p3) / 3.0f;
            
            var d1 = Vector2.Distance(_centerPoint, p1);
            var d2 = Vector2.Distance(_centerPoint, p2);
            var d3 = Vector2.Distance(_centerPoint, p3);
            
            _radiusX = Math.Max(d1, Math.Max(d2, d3));
            _radiusY = Math.Min(d1, Math.Min(d2, d3));
            _rotation = 0; // 简化处理
        }

        /// <summary>
        /// 确认椭圆创建
        /// </summary>
        private void ConfirmEllipse()
        {
            RemovePreview();
            
            if (_radiusX > 0 && _radiusY > 0)
            {
                var ellipse = new EntityEllipse(_centerPoint, _radiusX, _radiusY, _rotation)
                {
                    Name = $"椭圆_{DateTime.Now.Ticks}",
                    Color = _mgr.Viewer.Document.ActiveLayer.Color,
                    Parent = _mgr.Viewer.Document.ActiveLayer
                };
                
                _newEntities.Clear();
                _newEntities.Add(ellipse);
                
                // 提交到数据库
                Commit();
                
                // 重置准备下一个椭圆
                Reset();
            }
        }

        /// <summary>
        /// 移除预览
        /// </summary>
        private void RemovePreview()
        {
            if (_previewEllipse != null)
            {
                _mgr.Viewer.Document.ActiveLayer.Children.Remove(_previewEllipse);
                _previewEllipse.Dispose();
                _previewEllipse = null;
                _mgr.Viewer.RepaintCanvas();
            }
        }

        /// <summary>
        /// 提交椭圆到数据库
        /// </summary>
        protected override void Commit()
        {
            foreach (var entity in _newEntities)
            {
                _mgr.Viewer.Document.ActiveLayer.Children.Add(entity);
            }
            
            _mgr.Viewer.RepaintCanvas();
        }
        #endregion
    }
} 