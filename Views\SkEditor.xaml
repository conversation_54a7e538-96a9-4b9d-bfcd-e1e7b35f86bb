﻿<UserControl x:Class="McLaser.EditViewerSk.Views.SkEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:McLaser.EditViewerSk.ViewModels"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.EditViewerSk" 
             xmlns:views ="clr-namespace:McLaser.EditViewerSk.Views"
             xmlns:base ="clr-namespace:McLaser.EditViewerSk.Entitys"
      
             xmlns:pt="http://propertytools.org/wpf" 
             xmlns:xctk="http://schemas.xceed.com/wpf/xaml/toolkit"
             xmlns:cv="clr-namespace:McLaser.EditViewerSk.ViewModels" 
             xmlns:winfm="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
             xmlns:wf="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
        xmlns:wfi="clr-namespace:System.Windows.Forms.Integration;assembly=WindowsFormsIntegration"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" 
             d:DataContext="{d:DesignInstance Type=vm:SkEditorViewModel}">

    <UserControl.Resources>
        <Style x:Key="NoButtonStyle" TargetType="{x:Type ToggleButton}">
            <Style.Setters>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToggleButton">
                            <ContentPresenter HorizontalAlignment="Center"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style.Setters>
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="FontWeight" Value="Bold"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>


    <Grid >
        <Grid.ColumnDefinitions>

            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="1"/>
        </Grid.ColumnDefinitions>


        <DockPanel  LastChildFill="True">
            <Label Content="{Binding Doc.FileName}" Height="28" DockPanel.Dock="Top" Background="AliceBlue"/>
            <Label Content="{Binding}" Height="30" DockPanel.Dock="Bottom" Visibility="Collapsed"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>

                <!-- 图层管理面板 -->
                <TabControl>
                    <TabItem Header="图层管理">
                        <local:LayerPropertiesPanel Name="LayerPropertiesPanel"/>
                    </TabItem>
                    <TabItem Header="传统视图">
                        <DockPanel LastChildFill="True">
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Top">
                                <Button Content="上移" />
                                <Button Content="下移" />
                                <Button Content="移到最前" />
                                <Button Content="移到最后" />
                            </StackPanel>
                            <!--HierarchySource="{Binding Doc.Layers,Mode=OneWay}"-->
                            <pt:TreeListBox Name="tv"  HierarchySource="{Binding Doc.Layers,Mode=OneWay}"
                        pt:TreeListBoxDragDropHelper.IsDragSource="True"
                        pt:TreeListBoxDragDropHelper.IsDropTarget="True"
                        VirtualizingPanel.IsVirtualizing="True" VirtualizingPanel.VirtualizationMode="Recycling"
                        MouseLeftButtonUp="tv_MouseLeftButtonUp" Background="AliceBlue" KeyUp="tv_KeyUp">
                        <pt:TreeListBox.ContextMenu>
                            <ContextMenu>
                                <pt:EnumMenuItem Header="插入测高使能" Tag="1" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入C轴跟随使能" Tag="2" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入Z轴跟随使能" Tag="3" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入激光初始化" Tag="4" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入清除当前补偿表" Tag="5" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入延时" Tag="6" Click="InsertEntityCmd_Click" />
                            </ContextMenu>
                        </pt:TreeListBox.ContextMenu>

                        <pt:TreeListBox.ItemTemplate>
                            <HierarchicalDataTemplate DataType="{x:Type base:EntityBase}" ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <Image Source="{Binding Icon}" Width="18" Height="18"/>
                                    <TextBlock Text="{Binding Name}" Margin="5, 0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </pt:TreeListBox.ItemTemplate>

                 

                            </pt:TreeListBox>
                        </DockPanel>
                    </TabItem>
                </TabControl>

                <DockPanel LastChildFill="True" Grid.Column="1">
                    <ToolBarTray DockPanel.Dock="Top">
                        <!-- 主工具栏 -->
                        <ToolBar Band="1">
                            <Button Content="{local:Image Icons/Toolbars/101.png}" Click="btnNew_Click" ToolTip="新建"/>
                            <Button Content="{local:Image Icons/Toolbars/102.png}" Click="btnOpen_Click" ToolTip="打开"/>
                            <Button Content="{local:Image Icons/Toolbars/103.png}" Click="btnSave_Click" ToolTip="保存"/>
                            <Separator/>
                            <Button Content="{local:Image Icons/Toolbars/undo.png}" Click="btnUndo_Click" ToolTip="撤回 (Ctrl+Z)"/>
                            <Button Content="{local:Image Icons/Toolbars/redo.png}" Click="btnRedo_Click" ToolTip="重做 (Ctrl+Y)"/>
                            <Separator/>
                            <Button Content="{local:Image Icons/Toolbars/copy.png}" Click="btnCopy_Click" ToolTip="复制 (Ctrl+C)"/>
                            <Button Content="{local:Image Icons/Toolbars/cut.png}" Click="btnCut_Click" ToolTip="剪切"/>
                            <Button Content="{local:Image Icons/Toolbars/paste.png}" Click="btnPaste_Click" ToolTip="粘贴"/>
                            <Separator/>
                            <Button Content="{local:Image Icons/Toolbars/simul_delete.png}" Click="btnDelete_Click" ToolTip="删除"/>
                        </ToolBar>

                        <!-- 标注工具栏 -->
                        <ToolBar Band="2">
                            <TextBlock Text="标注" VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"/>
                            <Separator/>
                            <Button Command="{Binding DimLinearCommand}" ToolTip="线性标注 (DLI)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="📏" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="线性" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding DimAlignedCommand}" ToolTip="对齐标注 (DAL)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="📐" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="对齐" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding DimRadiusCommand}" ToolTip="半径标注 (DRA)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⭕" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="半径" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding DimDiameterCommand}" ToolTip="直径标注 (DDI)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⊕" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="直径" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding DimAngularCommand}" ToolTip="角度标注 (DAN)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="∠" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="角度" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding DimLeaderCommand}" ToolTip="引线标注 (LE)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="➤" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="引线" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Separator/>
                            <Button Command="{Binding OpenDimensionStyleManagerCommand}" ToolTip="标注样式管理器">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⚙" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="样式" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </ToolBar>

                        <!-- 编辑工具栏 -->
                        <ToolBar Band="3">
                            <TextBlock Text="编辑" VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"/>
                            <Separator/>
                            <Button Command="{Binding OffsetCommand}" ToolTip="偏移 (O)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⇄" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="偏移" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding ArrayCommand}" ToolTip="阵列 (AR)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⊞" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="阵列" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding TrimCommand}" ToolTip="修剪 (TR)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="✂" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="修剪" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding ExtendCommand}" ToolTip="延伸 (EX)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="↗" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="延伸" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding FilletCommand}" ToolTip="圆角 (F)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="◜" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="圆角" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding ChamferCommand}" ToolTip="倒角 (CHA)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="◣" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="倒角" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Separator/>
                            <Button Command="{Binding ScaleCommand}" ToolTip="缩放 (SC)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⚏" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="缩放" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button Command="{Binding RotateCommand}" ToolTip="旋转 (RO)">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="↻" FontSize="16" HorizontalAlignment="Center"/>
                                    <TextBlock Text="旋转" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </ToolBar>
                    </ToolBarTray>
                    <ToolBarTray  DockPanel.Dock="Left" Orientation="Vertical">
                        <ToolBar Band="1">
                            <Button Content="{local:Image Icons/Toolbars/201.png}" Command="{Binding BtnDrawPointCommand}" ToolTip="绘制点"/>
                            <Button Content="{local:Image Icons/Toolbars/202.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/203.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/204.png}" Command="{Binding BtnDrawRectangleCommand}"  ToolTip="绘制矩形"/>
                            <Button Content="{local:Image Icons/Toolbars/205.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/206.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/207.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/208.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/209.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/210.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/211.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/212.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/213.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/214.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/215.png}"/>

                        </ToolBar>
                        <ToolBar Band="2">

                            <Button Content="{local:Image Icons/Toolbars/216.png}" />
                            <Button Content="{local:Image Icons/Toolbars/line.png}" Command="{Binding BtnDrawLineCommand}" ToolTip="绘制直线"/>
                            <Button Content="{local:Image Icons/Toolbars/217.png}" Command="{Binding BtnDrawPolylineCommand}" ToolTip="绘制多段直线"/>
                            <Button Content="{local:Image Icons/Toolbars/218.png}" Command="{Binding BtnDrawCircleCommand}" ToolTip="绘制圆"/>
                            <Button Content="{local:Image Icons/Toolbars/219.png}" Command="{Binding BtnDrawArcCommand}" ToolTip="绘制圆弧"/>
                            <Button Content="{local:Image Icons/Toolbars/220.png}" />
                            <Button Content="{local:Image Icons/Toolbars/img_dxf.png}" Click="btnLoadDxf_Click" ToolTip="导入dxf"/>
                            <Button Content="{local:Image Icons/Toolbars/222.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/223.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/224.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/225.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/226.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/227.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/228.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/229.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/230.png}"/>

                        </ToolBar>

                        <!-- 视图工具栏 -->
                        <ToolBar Band="2">
                            <TextBlock Text="视图工具" VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"/>
                            <Separator/>
                            <Button Name="btnZoomWindow" Click="btnZoomWindow_Click" ToolTip="缩放窗口 (Ctrl+W)" Width="35" Height="25">
                                <TextBlock Text="窗口" FontSize="9"/>
                            </Button>
                            <Button Name="btnRealtimeZoom" Click="btnRealtimeZoom_Click" ToolTip="实时缩放" Width="35" Height="25">
                                <TextBlock Text="缩放" FontSize="9"/>
                            </Button>
                            <Button Name="btnPanTool" Click="btnPanTool_Click" ToolTip="平移" Width="35" Height="25">
                                <TextBlock Text="平移" FontSize="9"/>
                            </Button>
                            <Separator/>
                            <Button Name="btnZoomToFit" Click="btnZoomToFit_Click" ToolTip="缩放到全部显示 (Ctrl+E)" Width="35" Height="25">
                                <TextBlock Text="全部" FontSize="9"/>
                            </Button>
                            <Separator/>
                            <Button Name="btnPreviousView" Click="btnPreviousView_Click" ToolTip="上一视图 (Ctrl+←)" Width="35" Height="25">
                                <TextBlock Text="上一" FontSize="9"/>
                            </Button>
                            <Button Name="btnNextView" Click="btnNextView_Click" ToolTip="下一视图 (Ctrl+→)" Width="35" Height="25">
                                <TextBlock Text="下一" FontSize="9"/>
                            </Button>
                            <Separator/>
                            <Button Name="btnTopView" Click="btnTopView_Click" ToolTip="俯视图 (Ctrl+1)" Width="35" Height="25">
                                <TextBlock Text="俯视" FontSize="9"/>
                            </Button>
                            <Button Name="btnFrontView" Click="btnFrontView_Click" ToolTip="前视图 (Ctrl+2)" Width="35" Height="25">
                                <TextBlock Text="前视" FontSize="9"/>
                            </Button>
                            <Button Name="btnRightView" Click="btnRightView_Click" ToolTip="右视图 (Ctrl+3)" Width="35" Height="25">
                                <TextBlock Text="右视" FontSize="9"/>
                            </Button>
                            <Button Name="btnIsometricView" Click="btnIsometricView_Click" ToolTip="等轴测视图 (Ctrl+4)" Width="35" Height="25">
                                <TextBlock Text="等轴" FontSize="9"/>
                            </Button>
                        </ToolBar>
                    </ToolBarTray>
                    <!-- 对象捕捉控制面板 -->
                    <ToolBarTray DockPanel.Dock="Bottom" Orientation="Horizontal">
                        <ToolBar Band="1">
                            <TextBlock Text="对象捕捉" VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"/>
                            <Separator/>
                            <ToggleButton IsChecked="{Binding IsEndSnapEnabled}" ToolTip="端点捕捉" Width="35" Height="25">
                                <TextBlock Text="端" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsMidSnapEnabled}" ToolTip="中点捕捉" Width="35" Height="25">
                                <TextBlock Text="中" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsCenterSnapEnabled}" ToolTip="中心点捕捉" Width="35" Height="25">
                                <TextBlock Text="心" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsIntersectionSnapEnabled}" ToolTip="交点捕捉" Width="35" Height="25">
                                <TextBlock Text="交" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsPerpendicularSnapEnabled}" ToolTip="垂足捕捉" Width="35" Height="25">
                                <TextBlock Text="垂" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsTangentSnapEnabled}" ToolTip="切点捕捉" Width="35" Height="25">
                                <TextBlock Text="切" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsQuadSnapEnabled}" ToolTip="象限点捕捉" Width="35" Height="25">
                                <TextBlock Text="象" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsNearSnapEnabled}" ToolTip="最近点捕捉" Width="35" Height="25">
                                <TextBlock Text="近" FontSize="10"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsGridSnapEnabled}" ToolTip="网格捕捉" Width="35" Height="25">
                                <TextBlock Text="网" FontSize="10"/>
                            </ToggleButton>
                        </ToolBar>

                        <ToolBar Band="2">
                            <TextBlock Text="高级交互" VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"/>
                            <Separator/>
                            <ToggleButton IsChecked="{Binding IsPolarTrackingEnabled}" ToolTip="极轴追踪 (F8)" Width="50" Height="25">
                                <TextBlock Text="极轴" FontSize="9"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsObjectTrackingEnabled}" ToolTip="对象追踪 (F11)" Width="50" Height="25">
                                <TextBlock Text="追踪" FontSize="9"/>
                            </ToggleButton>
                            <ToggleButton IsChecked="{Binding IsDynamicInputEnabled}" ToolTip="动态输入 (F12)" Width="50" Height="25">
                                <TextBlock Text="输入" FontSize="9"/>
                            </ToggleButton>
                            <Separator/>
                            <Button Command="{Binding OpenPolarTrackingSettingsCommand}" ToolTip="极轴追踪设置" Width="35" Height="25">
                                <TextBlock Text="⚙" FontSize="12"/>
                            </Button>
                            <Button Command="{Binding OpenObjectSnapSettingsCommand}" ToolTip="对象捕捉设置" Width="35" Height="25">
                                <TextBlock Text="⚙" FontSize="12"/>
                            </Button>
                            <Separator/>
                            <Button Command="{Binding OpenSystemSettingsCommand}" ToolTip="系统设置" Width="50" Height="25">
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="⚙" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock Text="设置" FontSize="8" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </ToolBar>
                    </ToolBarTray>

                    <StatusBar DockPanel.Dock="Bottom">
                        <StatusBarItem>
                            <ToggleButton Width="60">
                                <ToggleButton.Style>
                                    <Style TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource NoButtonStyle}">
                                        <Setter Property="Content" Value="CPlane"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter Property="Content">
                                                    <Setter.Value>
                                                        <TextBlock Text="World"/>
                                                    </Setter.Value>
                                                </Setter>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ToggleButton.Style>
                            </ToggleButton>
                        </StatusBarItem>
                        <Separator/>
                        <!-- 坐标显示 -->
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="80" Background="LightBlue" Margin="2">
                                <TextBlock Text="X:" Margin="4,0,2,0" FontWeight="Bold"/>
                                <TextBlock Text="{Binding StatusBarManager.CurrentX}" FontFamily="Consolas" FontWeight="Bold"/>
                            </StackPanel>
                        </StatusBarItem>
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="80" Background="LightBlue" Margin="2">
                                <TextBlock Text="Y:" Margin="4,0,2,0" FontWeight="Bold"/>
                                <TextBlock Text="{Binding StatusBarManager.CurrentY}" FontFamily="Consolas" FontWeight="Bold"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>

                        <!-- 捕捉状态 -->
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="80" Margin="2">
                                <TextBlock Text="{Binding StatusBarManager.SnapStatus}"
                                          Foreground="{Binding StatusBarManager.SnapStatusColor}"
                                          FontWeight="Bold" VerticalAlignment="Center"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>

                        <!-- 追踪状态 -->
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="60" Margin="2">
                                <TextBlock Text="{Binding StatusBarManager.PolarTrackingStatus}"
                                          Foreground="{Binding StatusBarManager.PolarTrackingColor}"
                                          FontSize="10" VerticalAlignment="Center"/>
                            </StackPanel>
                        </StatusBarItem>
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="60" Margin="2">
                                <TextBlock Text="{Binding StatusBarManager.ObjectTrackingStatus}"
                                          Foreground="{Binding StatusBarManager.ObjectTrackingColor}"
                                          FontSize="10" VerticalAlignment="Center"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>

                        <!-- 图层信息 -->
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="100" Margin="2">
                                <Rectangle Width="12" Height="12" Margin="4" Fill="{Binding StatusBarManager.LayerColor}"/>
                                <TextBlock Text="{Binding StatusBarManager.CurrentLayer}" VerticalAlignment="Center" FontWeight="Bold"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>

                        <!-- 缩放比例 -->
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="60" Margin="2">
                                <TextBlock Text="{Binding StatusBarManager.ZoomPercentage}"
                                          VerticalAlignment="Center" FontWeight="Bold" Foreground="Blue"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>

                        <!-- 状态消息 -->
                        <StatusBarItem HorizontalContentAlignment="Stretch">
                            <TextBlock Text="{Binding StatusBarManager.StatusMessage}"
                                      VerticalAlignment="Center" Margin="4,0"
                                      FontStyle="Italic" Foreground="DarkGreen"/>
                        </StatusBarItem>
                    </StatusBar>
                    <StatusBar DockPanel.Dock="Right" Width="35">
                        <StatusBarItem VerticalAlignment="Top">
                            <DockPanel  Margin="0">
                                <Label Background="White" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Gray" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orange" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Yellow" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Blue" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Magenta" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Cyan" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orchid" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Red" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Green" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="White" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Gray" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orange" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Yellow" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Blue" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Magenta" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Cyan" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orchid" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Red" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Green" DockPanel.Dock="Top" Width="25" Height="25"/>
                            </DockPanel>
                        </StatusBarItem>
                    </StatusBar>
                    <Grid>
                        <wfi:WindowsFormsHost x:Name="WinFormsHost" />
                        <!--<skia:SKElement x:Name="skContainer" Focusable="True"/>-->

                        <!-- 动态输入覆盖层 -->
                        <Canvas Name="DynamicInputCanvas" IsHitTestVisible="False">
                            <local:DynamicInputOverlay Name="DynamicInputOverlay"
                                                       InputConfirmed="DynamicInputOverlay_InputConfirmed"
                                                       InputChanged="DynamicInputOverlay_InputChanged"/>
                        </Canvas>
                    </Grid>
                </DockPanel>


                <WindowsFormsHost   VerticalAlignment="Top" Grid.Column="2">
                    <winfm:PropertyGrid x:Name="wfpg" Dock="Fill"></winfm:PropertyGrid >
                </WindowsFormsHost>

            </Grid>

        </DockPanel>





        <!--<TabControl Grid.Column="2" >

            <TabItem Header="加工" Visibility="Collapsed">
                <ContentControl x:Name="MarkingControlView"/>
            </TabItem>

            <TabItem Header="参数">
                <WindowsFormsHost   VerticalAlignment="Top">
                    <winfm:PropertyGrid x:Name="wfpg" Dock="Fill"></winfm:PropertyGrid >
                </WindowsFormsHost>
            </TabItem>

            <TabItem Header="脚本Buffer" Visibility="Collapsed">
                <ContentControl x:Name="BufferView"/>
            </TabItem>
        </TabControl>-->

        <GridSplitter Grid.Column="1" ResizeDirection="Columns" VerticalAlignment="Stretch" HorizontalAlignment="Left" Width="2" Margin="-4,0,0,0" Background="Transparent"/>

    </Grid>



</UserControl>
