using System;
using System.ComponentModel;
using System.Numerics;
using PropertyTools;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 半径标注实体
    /// </summary>
    [Browsable(true)]
    [DisplayName("半径标注")]
    public class EntityRadiusDimension : EntityDimension
    {
        private Vector2 center;
        private double radius;
        private Vector2 leaderEndPoint;

        #region 构造函数

        public EntityRadiusDimension() : base(DimensionType.Radius)
        {
            center = Vector2.Zero;
            radius = 10.0;
            leaderEndPoint = new Vector2(10, 0);
            Update();
        }

        public EntityRadiusDimension(Vector2 centerPoint, double radiusValue) : base(DimensionType.Radius)
        {
            center = centerPoint;
            radius = radiusValue;
            leaderEndPoint = center + new Vector2((float)radius, 0);
            Update();
        }

        public EntityRadiusDimension(Vector2 centerPoint, double radiusValue, Vector2 leaderEnd) : base(DimensionType.Radius)
        {
            center = centerPoint;
            radius = radiusValue;
            leaderEndPoint = leaderEnd;
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("圆心")]
        public Vector2 Center
        {
            get => center;
            set
            {
                if (center != value)
                {
                    center = value;
                    OnPropertyChanged(nameof(Center));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("半径")]
        public double Radius
        {
            get => radius;
            set
            {
                if (Math.Abs(radius - value) > 1e-10)
                {
                    radius = Math.Max(0, value);
                    OnPropertyChanged(nameof(Radius));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("引线终点")]
        public Vector2 LeaderEndPoint
        {
            get => leaderEndPoint;
            set
            {
                if (leaderEndPoint != value)
                {
                    leaderEndPoint = value;
                    OnPropertyChanged(nameof(LeaderEndPoint));
                    Update();
                }
            }
        }

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            ActualMeasurement = radius;
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            // 计算引线方向
            var direction = Vector2.Normalize(leaderEndPoint - center);
            var radiusPoint = center + direction * (float)radius;

            // 创建引线（从圆周到引线终点）
            geometry.DimensionLine = new LineGeometry(radiusPoint, leaderEndPoint);

            // 添加箭头（指向圆心）
            if (Style.ShowArrows)
            {
                geometry.Arrows.Add(new ArrowGeometry(radiusPoint, -direction, Style.ArrowSize));
            }

            // 计算文本位置
            geometry.TextPosition = (radiusPoint + leaderEndPoint) * 0.5f;

            return geometry;
        }

        public override EntityBase Clone()
        {
            var clone = new EntityRadiusDimension(center, radius, leaderEndPoint);
            CopyPropertiesTo(clone);
            return clone;
        }

        protected override string GetDisplayText()
        {
            return $"R{ActualMeasurement.ToString($"F{Style.DecimalPlaces}")}";
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据圆形实体创建半径标注
        /// </summary>
        public static EntityRadiusDimension CreateFromCircle(EntityCircle circle, Vector2 leaderEndPoint)
        {
            return new EntityRadiusDimension(circle.Center, circle.Radius, leaderEndPoint);
        }

        /// <summary>
        /// 根据圆弧实体创建半径标注
        /// </summary>
        public static EntityRadiusDimension CreateFromArc(EntityArc arc, Vector2 leaderEndPoint)
        {
            return new EntityRadiusDimension(arc.Center, arc.Radius, leaderEndPoint);
        }

        #endregion
    }

    /// <summary>
    /// 直径标注实体
    /// </summary>
    [Browsable(true)]
    [DisplayName("直径标注")]
    public class EntityDiameterDimension : EntityDimension
    {
        private Vector2 center;
        private double diameter;
        private Vector2 leaderEndPoint;
        private bool showCenterMark = true;

        #region 构造函数

        public EntityDiameterDimension() : base(DimensionType.Diameter)
        {
            center = Vector2.Zero;
            diameter = 20.0;
            leaderEndPoint = new Vector2(10, 0);
            Update();
        }

        public EntityDiameterDimension(Vector2 centerPoint, double diameterValue) : base(DimensionType.Diameter)
        {
            center = centerPoint;
            diameter = diameterValue;
            leaderEndPoint = center + new Vector2((float)diameter / 2, 0);
            Update();
        }

        public EntityDiameterDimension(Vector2 centerPoint, double diameterValue, Vector2 leaderEnd) : base(DimensionType.Diameter)
        {
            center = centerPoint;
            diameter = diameterValue;
            leaderEndPoint = leaderEnd;
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("圆心")]
        public Vector2 Center
        {
            get => center;
            set
            {
                if (center != value)
                {
                    center = value;
                    OnPropertyChanged(nameof(Center));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("直径")]
        public double Diameter
        {
            get => diameter;
            set
            {
                if (Math.Abs(diameter - value) > 1e-10)
                {
                    diameter = Math.Max(0, value);
                    OnPropertyChanged(nameof(Diameter));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("半径")]
        [ReadOnly(true)]
        public double Radius => diameter / 2.0;

        [Category("几何")]
        [DisplayName("引线终点")]
        public Vector2 LeaderEndPoint
        {
            get => leaderEndPoint;
            set
            {
                if (leaderEndPoint != value)
                {
                    leaderEndPoint = value;
                    OnPropertyChanged(nameof(LeaderEndPoint));
                    Update();
                }
            }
        }

        [Category("显示")]
        [DisplayName("显示中心标记")]
        public bool ShowCenterMark
        {
            get => showCenterMark;
            set
            {
                if (showCenterMark != value)
                {
                    showCenterMark = value;
                    OnPropertyChanged(nameof(ShowCenterMark));
                    InvalidateVisual();
                }
            }
        }

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            ActualMeasurement = diameter;
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            // 计算引线方向
            var direction = Vector2.Normalize(leaderEndPoint - center);
            var radiusPoint = center + direction * (float)(diameter / 2);

            // 创建引线（从圆周到引线终点）
            geometry.DimensionLine = new LineGeometry(radiusPoint, leaderEndPoint);

            // 添加直径线（穿过圆心的对角线）
            var oppositePoint = center - direction * (float)(diameter / 2);
            geometry.ExtensionLines.Add(new LineGeometry(oppositePoint, radiusPoint));

            // 添加箭头
            if (Style.ShowArrows)
            {
                geometry.Arrows.Add(new ArrowGeometry(radiusPoint, -direction, Style.ArrowSize));
                geometry.Arrows.Add(new ArrowGeometry(oppositePoint, direction, Style.ArrowSize));
            }

            // 添加中心标记
            if (showCenterMark)
            {
                var markSize = Style.ArrowSize * 0.5;
                geometry.ExtensionLines.Add(new LineGeometry(
                    center + new Vector2((float)markSize, 0),
                    center - new Vector2((float)markSize, 0)));
                geometry.ExtensionLines.Add(new LineGeometry(
                    center + new Vector2(0, (float)markSize),
                    center - new Vector2(0, (float)markSize)));
            }

            // 计算文本位置
            geometry.TextPosition = (radiusPoint + leaderEndPoint) * 0.5f;

            return geometry;
        }

        public override EntityBase Clone()
        {
            var clone = new EntityDiameterDimension(center, diameter, leaderEndPoint)
            {
                ShowCenterMark = showCenterMark
            };
            CopyPropertiesTo(clone);
            return clone;
        }

        protected override string GetDisplayText()
        {
            return $"Ø{ActualMeasurement.ToString($"F{Style.DecimalPlaces}")}";
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据圆形实体创建直径标注
        /// </summary>
        public static EntityDiameterDimension CreateFromCircle(EntityCircle circle, Vector2 leaderEndPoint)
        {
            return new EntityDiameterDimension(circle.Center, circle.Radius * 2, leaderEndPoint);
        }

        /// <summary>
        /// 根据圆弧实体创建直径标注
        /// </summary>
        public static EntityDiameterDimension CreateFromArc(EntityArc arc, Vector2 leaderEndPoint)
        {
            return new EntityDiameterDimension(arc.Center, arc.Radius * 2, leaderEndPoint);
        }

        #endregion
    }
} 