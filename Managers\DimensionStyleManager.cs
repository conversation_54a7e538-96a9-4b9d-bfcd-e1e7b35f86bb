using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows.Media;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Common;
using Newtonsoft.Json;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 标注样式管理器
    /// </summary>
    public class DimensionStyleManager : ObservableObject
    {
        private static DimensionStyleManager instance;
        private readonly ObservableCollection<DimensionStyle> styles = new ObservableCollection<DimensionStyle>();
        private DimensionStyle currentStyle;
        private string stylesFilePath;

        #region 单例模式

        public static DimensionStyleManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new DimensionStyleManager();
                }
                return instance;
            }
        }

        private DimensionStyleManager()
        {
            stylesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "McLaser", "DimensionStyles.json");
            InitializeDefaultStyles();
            LoadStyles();
        }

        #endregion

        #region 属性

        /// <summary>
        /// 所有可用的标注样式
        /// </summary>
        public ObservableCollection<DimensionStyle> Styles => styles;

        /// <summary>
        /// 当前活动的标注样式
        /// </summary>
        public DimensionStyle CurrentStyle
        {
            get => currentStyle;
            set
            {
                if (currentStyle != value)
                {
                    currentStyle = value;
                    OnPropertyChanged(nameof(CurrentStyle));
                    CurrentStyleChanged?.Invoke(this, new StyleChangedEventArgs(currentStyle));
                }
            }
        }

        /// <summary>
        /// 样式文件路径
        /// </summary>
        public string StylesFilePath
        {
            get => stylesFilePath;
            set
            {
                if (stylesFilePath != value)
                {
                    stylesFilePath = value;
                    OnPropertyChanged(nameof(StylesFilePath));
                }
            }
        }

        #endregion

        #region 事件

        public event EventHandler<StyleChangedEventArgs> CurrentStyleChanged;
        public event EventHandler<StyleEventArgs> StyleAdded;
        public event EventHandler<StyleEventArgs> StyleRemoved;
        public event EventHandler<StyleModifiedEventArgs> StyleModified;

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加新样式
        /// </summary>
        public void AddStyle(DimensionStyle style)
        {
            if (style == null) return;

            // 确保样式名称唯一
            var originalName = style.Name;
            var counter = 1;
            while (styles.Any(s => s.Name == style.Name))
            {
                style.Name = $"{originalName}_{counter++}";
            }

            styles.Add(style);
            StyleAdded?.Invoke(this, new StyleEventArgs(style));
            
            if (CurrentStyle == null)
            {
                CurrentStyle = style;
            }
        }

        /// <summary>
        /// 移除样式
        /// </summary>
        public bool RemoveStyle(DimensionStyle style)
        {
            if (style == null || styles.Count <= 1) return false;

            var removed = styles.Remove(style);
            if (removed)
            {
                StyleRemoved?.Invoke(this, new StyleEventArgs(style));
                
                if (CurrentStyle == style)
                {
                    CurrentStyle = styles.FirstOrDefault();
                }
            }
            
            return removed;
        }

        /// <summary>
        /// 根据名称查找样式
        /// </summary>
        public DimensionStyle FindStyle(string name)
        {
            return styles.FirstOrDefault(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 复制样式
        /// </summary>
        public DimensionStyle DuplicateStyle(DimensionStyle source, string newName = null)
        {
            if (source == null) return null;

            var duplicate = (DimensionStyle)source.Clone();
            duplicate.Name = newName ?? $"{source.Name}_Copy";
            
            AddStyle(duplicate);
            return duplicate;
        }

        /// <summary>
        /// 修改样式并通知
        /// </summary>
        public void ModifyStyle(DimensionStyle style, Action<DimensionStyle> modifier)
        {
            if (style == null || modifier == null) return;

            var oldValues = (DimensionStyle)style.Clone();
            modifier(style);
            
            StyleModified?.Invoke(this, new StyleModifiedEventArgs(style, oldValues));
        }

        /// <summary>
        /// 保存样式到文件
        /// </summary>
        public bool SaveStyles()
        {
            try
            {
                var directory = Path.GetDirectoryName(stylesFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonConvert.SerializeObject(styles, Formatting.Indented, new JsonSerializerSettings
                {
                    TypeHandling = TypeHandling.Auto,
                    NullValueHandling = NullValueHandling.Include,
                    DefaultValueHandling = DefaultValueHandling.Include,
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    Converters = { new ColorConverter(), new FontConverter() }
                });

                File.WriteAllText(stylesFilePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存标注样式失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从文件加载样式
        /// </summary>
        public bool LoadStyles()
        {
            try
            {
                if (!File.Exists(stylesFilePath)) return false;

                var json = File.ReadAllText(stylesFilePath);
                var loadedStyles = JsonConvert.DeserializeObject<List<DimensionStyle>>(json, new JsonSerializerSettings
                {
                    TypeHandling = TypeHandling.Auto,
                    NullValueHandling = NullValueHandling.Include,
                    DefaultValueHandling = DefaultValueHandling.Include,
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    Converters = { new ColorConverter(), new FontConverter() }
                });

                if (loadedStyles?.Any() == true)
                {
                    styles.Clear();
                    foreach (var style in loadedStyles)
                    {
                        styles.Add(style);
                    }
                    
                    CurrentStyle = styles.FirstOrDefault();
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载标注样式失败: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// 导出样式
        /// </summary>
        public bool ExportStyles(string filePath, IEnumerable<DimensionStyle> stylesToExport = null)
        {
            try
            {
                var exportStyles = stylesToExport ?? styles;
                var json = JsonConvert.SerializeObject(exportStyles, Formatting.Indented, new JsonSerializerSettings
                {
                    TypeHandling = TypeHandling.Auto,
                    Converters = { new ColorConverter() }
                });

                File.WriteAllText(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出标注样式失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导入样式
        /// </summary>
        public bool ImportStyles(string filePath, bool replaceExisting = false)
        {
            try
            {
                if (!File.Exists(filePath)) return false;

                var json = File.ReadAllText(filePath);
                var importedStyles = JsonConvert.DeserializeObject<List<DimensionStyle>>(json, new JsonSerializerSettings
                {
                    TypeHandling = TypeHandling.Auto,
                    Converters = { new ColorConverter() }
                });

                if (importedStyles?.Any() == true)
                {
                    if (replaceExisting)
                    {
                        styles.Clear();
                    }

                    foreach (var style in importedStyles)
                    {
                        AddStyle(style);
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入标注样式失败: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// 重置为默认样式
        /// </summary>
        public void ResetToDefaults()
        {
            styles.Clear();
            InitializeDefaultStyles();
            CurrentStyle = styles.FirstOrDefault();
        }

        #endregion

        #region 私有方法

        private void InitializeDefaultStyles()
        {
            // 标准样式
            var standard = new DimensionStyle
            {
                Name = "Standard",
                TextHeight = 2.5,
                ArrowSize = 2.5,
                ExtensionLineOffset = 0.625,
                ExtensionLineExtend = 1.25,
                DimensionLineSpacing = 3.75,
                TextColor = Colors.Black,
                LineColor = Colors.Black,
                TextFont = "Arial",
                DecimalPlaces = 2,
                ShowExtensionLines = true,
                ShowArrows = true
            };

            // ISO 样式
            var iso = new DimensionStyle
            {
                Name = "ISO",
                TextHeight = 2.5,
                ArrowSize = 2.5,
                ExtensionLineOffset = 0.625,
                ExtensionLineExtend = 1.25,
                DimensionLineSpacing = 3.75,
                TextColor = Colors.Black,
                LineColor = Colors.Black,
                TextFont = "Arial",
                DecimalPlaces = 2,
                ShowExtensionLines = true,
                ShowArrows = true
            };

            // 机械制图样式
            var mechanical = new DimensionStyle
            {
                Name = "Mechanical",
                TextHeight = 3.0,
                ArrowSize = 3.0,
                ExtensionLineOffset = 1.0,
                ExtensionLineExtend = 1.5,
                DimensionLineSpacing = 5.0,
                TextColor = Colors.Black,
                LineColor = Colors.Black,
                TextFont = "Arial",
                DecimalPlaces = 1,
                ShowExtensionLines = true,
                ShowArrows = true
            };

            // 建筑制图样式
            var architectural = new DimensionStyle
            {
                Name = "Architectural",
                TextHeight = 4.0,
                ArrowSize = 4.0,
                ExtensionLineOffset = 1.25,
                ExtensionLineExtend = 2.0,
                DimensionLineSpacing = 6.0,
                TextColor = Colors.Black,
                LineColor = Colors.Black,
                TextFont = "Arial",
                DecimalPlaces = 0,
                ShowExtensionLines = true,
                ShowArrows = true
            };

            styles.Add(standard);
            styles.Add(iso);
            styles.Add(mechanical);
            styles.Add(architectural);

            CurrentStyle = standard;
        }

        #endregion
    }

    #region 事件参数类

    public class StyleChangedEventArgs : EventArgs
    {
        public DimensionStyle NewStyle { get; }

        public StyleChangedEventArgs(DimensionStyle newStyle)
        {
            NewStyle = newStyle;
        }
    }

    public class StyleEventArgs : EventArgs
    {
        public DimensionStyle Style { get; }

        public StyleEventArgs(DimensionStyle style)
        {
            Style = style;
        }
    }

    public class StyleModifiedEventArgs : EventArgs
    {
        public DimensionStyle Style { get; }
        public DimensionStyle OldValues { get; }

        public StyleModifiedEventArgs(DimensionStyle style, DimensionStyle oldValues)
        {
            Style = style;
            OldValues = oldValues;
        }
    }

    #endregion

    #region JSON转换器

    public class ColorConverter : JsonConverter<Color>
    {
        public override void WriteJson(JsonWriter writer, Color value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString());
        }

        public override Color ReadJson(JsonReader reader, Type objectType, Color existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var colorString = reader.Value?.ToString();
            if (string.IsNullOrEmpty(colorString)) return Colors.Black;

            try
            {
                return (Color)ColorConverter.ConvertFromString(colorString);
            }
            catch
            {
                return Colors.Black;
            }
        }
    }

    /// <summary>
    /// 字体JSON转换器
    /// </summary>
    public class FontConverter : JsonConverter<FontFamily>
    {
        public override void WriteJson(JsonWriter writer, FontFamily value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
                return;
            }

            writer.WriteValue(value.Source);
        }

        public override FontFamily ReadJson(JsonReader reader, Type objectType, FontFamily existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var fontString = reader.Value?.ToString();
            if (string.IsNullOrEmpty(fontString))
                return new FontFamily("Arial");

            try
            {
                return new FontFamily(fontString);
            }
            catch
            {
                return new FontFamily("Arial");
            }
        }
    }

    #endregion
}