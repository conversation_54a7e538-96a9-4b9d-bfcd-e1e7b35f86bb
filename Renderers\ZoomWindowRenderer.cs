using System;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Renderers
{
    /// <summary>
    /// 缩放窗口渲染器
    /// 负责渲染缩放窗口选择框
    /// </summary>
    public class ZoomWindowRenderer
    {
        private readonly ViewBase _viewBase;
        private Vector2? _startPoint;
        private Vector2? _endPoint;
        private bool _isActive = false;
        
        // 渲染样式
        private readonly SKPaint _rectanglePaint;
        private readonly SKPaint _fillPaint;
        
        public ZoomWindowRenderer(ViewBase viewBase)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            
            // 初始化绘制样式
            _rectanglePaint = new SKPaint
            {
                Color = SKColors.Blue,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0)
            };
            
            _fillPaint = new SKPaint
            {
                Color = SKColor.FromArgb(30, 0, 0, 255), // 半透明蓝色
                Style = SKPaintStyle.Fill
            };
        }
        
        /// <summary>
        /// 开始缩放窗口选择
        /// </summary>
        /// <param name="startPoint">起始点（屏幕坐标）</param>
        public void StartZoomWindow(Vector2 startPoint)
        {
            _startPoint = startPoint;
            _endPoint = startPoint;
            _isActive = true;
        }
        
        /// <summary>
        /// 更新缩放窗口选择
        /// </summary>
        /// <param name="endPoint">结束点（屏幕坐标）</param>
        public void UpdateZoomWindow(Vector2 endPoint)
        {
            if (_isActive)
            {
                _endPoint = endPoint;
                _viewBase.RepaintCanvas();
            }
        }
        
        /// <summary>
        /// 结束缩放窗口选择
        /// </summary>
        /// <returns>选择的矩形区域，如果无效则返回null</returns>
        public SKRect? EndZoomWindow()
        {
            if (_isActive && _startPoint.HasValue && _endPoint.HasValue)
            {
                var rect = CreateRectangle(_startPoint.Value, _endPoint.Value);
                
                _isActive = false;
                _startPoint = null;
                _endPoint = null;
                _viewBase.RepaintCanvas();
                
                // 检查矩形是否有效（最小尺寸）
                if (rect.Width > 10 && rect.Height > 10)
                {
                    return rect;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 取消缩放窗口选择
        /// </summary>
        public void CancelZoomWindow()
        {
            _isActive = false;
            _startPoint = null;
            _endPoint = null;
            _viewBase.RepaintCanvas();
        }
        
        /// <summary>
        /// 渲染缩放窗口
        /// </summary>
        /// <param name="canvas">画布</param>
        public void Render(SKCanvas canvas)
        {
            if (_isActive && _startPoint.HasValue && _endPoint.HasValue)
            {
                var rect = CreateRectangle(_startPoint.Value, _endPoint.Value);
                
                // 绘制填充
                canvas.DrawRect(rect, _fillPaint);
                
                // 绘制边框
                canvas.DrawRect(rect, _rectanglePaint);
                
                // 绘制角点标记
                DrawCornerMarkers(canvas, rect);
                
                // 绘制尺寸信息
                DrawDimensionInfo(canvas, rect);
            }
        }
        
        /// <summary>
        /// 是否正在进行缩放窗口选择
        /// </summary>
        public bool IsActive => _isActive;
        
        /// <summary>
        /// 当前选择矩形
        /// </summary>
        public SKRect? CurrentRectangle
        {
            get
            {
                if (_isActive && _startPoint.HasValue && _endPoint.HasValue)
                {
                    return CreateRectangle(_startPoint.Value, _endPoint.Value);
                }
                return null;
            }
        }
        
        #region 私有方法
        
        /// <summary>
        /// 创建矩形
        /// </summary>
        private SKRect CreateRectangle(Vector2 start, Vector2 end)
        {
            return new SKRect(
                Math.Min(start.X, end.X),
                Math.Min(start.Y, end.Y),
                Math.Max(start.X, end.X),
                Math.Max(start.Y, end.Y)
            );
        }
        
        /// <summary>
        /// 绘制角点标记
        /// </summary>
        private void DrawCornerMarkers(SKCanvas canvas, SKRect rect)
        {
            const float markerSize = 8.0f;
            var markerPaint = new SKPaint
            {
                Color = SKColors.Blue,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f
            };
            
            // 左上角
            canvas.DrawLine(rect.Left, rect.Top, rect.Left + markerSize, rect.Top, markerPaint);
            canvas.DrawLine(rect.Left, rect.Top, rect.Left, rect.Top + markerSize, markerPaint);
            
            // 右上角
            canvas.DrawLine(rect.Right, rect.Top, rect.Right - markerSize, rect.Top, markerPaint);
            canvas.DrawLine(rect.Right, rect.Top, rect.Right, rect.Top + markerSize, markerPaint);
            
            // 左下角
            canvas.DrawLine(rect.Left, rect.Bottom, rect.Left + markerSize, rect.Bottom, markerPaint);
            canvas.DrawLine(rect.Left, rect.Bottom, rect.Left, rect.Bottom - markerSize, markerPaint);
            
            // 右下角
            canvas.DrawLine(rect.Right, rect.Bottom, rect.Right - markerSize, rect.Bottom, markerPaint);
            canvas.DrawLine(rect.Right, rect.Bottom, rect.Right, rect.Bottom - markerSize, markerPaint);
            
            markerPaint.Dispose();
        }
        
        /// <summary>
        /// 绘制尺寸信息
        /// </summary>
        private void DrawDimensionInfo(SKCanvas canvas, SKRect rect)
        {
            var textPaint = new SKPaint
            {
                Color = SKColors.Blue,
                TextSize = 12.0f,
                IsAntialias = true
            };
            
            // 计算模型空间尺寸
            var modelTopLeft = _viewBase.CanvasToModel(new Vector2(rect.Left, rect.Top));
            var modelBottomRight = _viewBase.CanvasToModel(new Vector2(rect.Right, rect.Bottom));
            
            var width = Math.Abs(modelBottomRight.X - modelTopLeft.X);
            var height = Math.Abs(modelBottomRight.Y - modelTopLeft.Y);
            
            var dimensionText = $"{width:F2} × {height:F2}";
            
            // 在矩形中心绘制尺寸文本
            var textBounds = new SKRect();
            textPaint.MeasureText(dimensionText, ref textBounds);
            
            var textX = rect.MidX - textBounds.Width / 2;
            var textY = rect.MidY + textBounds.Height / 2;
            
            // 绘制文本背景
            var backgroundRect = new SKRect(
                textX - 4, textY - textBounds.Height - 2,
                textX + textBounds.Width + 4, textY + 2
            );
            
            var backgroundPaint = new SKPaint
            {
                Color = SKColor.FromArgb(200, 255, 255, 255),
                Style = SKPaintStyle.Fill
            };
            
            canvas.DrawRect(backgroundRect, backgroundPaint);
            canvas.DrawText(dimensionText, textX, textY, textPaint);
            
            textPaint.Dispose();
            backgroundPaint.Dispose();
        }
        
        #endregion
        
        #region 资源清理
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _rectanglePaint?.Dispose();
            _fillPaint?.Dispose();
        }
        
        #endregion
    }
}
