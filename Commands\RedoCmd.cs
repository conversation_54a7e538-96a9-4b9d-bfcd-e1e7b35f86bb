﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 重做命令
    /// </summary>
    public class RedoCmd : Command
    {
        public override void Initialize()
        {
            base.Initialize();

            // 检查是否可以重做
            if (_mgr.CanRedo)
            {
                this.pointer.Document.Prompt = "重做上一个撤销的操作";
                _mgr.FinishCurrentCommand();
            }
            else
            {
                this.pointer.Document.Prompt = "没有可重做的操作";
                _mgr.CancelCurrentCommand();
            }
        }

        /// <summary>
        /// 重做命令不需要提交逻辑，由CommandsMgr处理
        /// </summary>
        protected override void Commit()
        {
            // 重做逻辑由CommandsMgr.Redo()处理
        }

        /// <summary>
        /// 重做命令不需要回滚逻辑
        /// </summary>
        protected override void Rollback()
        {
            // 不需要回滚
        }

        public override string Description => "重做上一个撤销的操作";
    }
}
