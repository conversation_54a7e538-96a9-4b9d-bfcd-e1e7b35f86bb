# SkiaSharp坐标系修复报告

## 🎯 问题描述

用户报告：鼠标的移动位置跟框选框方向反了，需要注意SkiaSharp的坐标系起点问题。

## 🔍 问题分析

### SkiaSharp坐标系特点

1. **变换矩阵**: ViewBase使用了`SKMatrix.CreateScale(1, -1)`，这意味着Y轴被翻转
2. **坐标系转换**: 屏幕坐标 → Canvas坐标 → Model坐标
3. **Y轴方向**: 在翻转后的坐标系中，鼠标向下移动时Y值实际上是减小的

### 问题根源

```csharp
// ViewBase中的变换矩阵
private SKMatrix _matrixTrans = SKMatrix.CreateScale(1, -1);

// DrawRectangle在Model坐标系下的实现
Canvas?.DrawRect((float)posInCanvas.X, (float)posInCanvas.Y, 
                (float)(end.X - posInCanvas.X), 
                -(float)(end.Y - posInCanvas.Y), pen);  // 注意这里的负号
```

这导致了以下问题：
- 直接使用`endPoint - startPoint`计算宽高会导致Y方向错误
- 需要正确处理Y轴翻转的坐标系

## ✅ 解决方案

### 第一次尝试（已弃用）：Y轴翻转处理

最初尝试在Model坐标系下处理Y轴翻转，但发现与原有系统不兼容。

### 最终解决方案：Canvas坐标系统一

发现原有系统使用Canvas坐标系绘制，因此统一采用Canvas坐标系：

**修改后**:
```csharp
// 使用Canvas坐标系绘制，与原有OnPaint方法保持一致
// 首先将Model坐标转换为Canvas坐标
var startCanvas = view.ModelToCanvas(startPoint);
var endCanvas = view.ModelToCanvas(endPoint);
var canvasWidth = endCanvas.X - startCanvas.X;
var canvasHeight = endCanvas.Y - startCanvas.Y;

view.DrawRectangle(startCanvas, canvasWidth, canvasHeight, paint, CSYS.Canvas);
```

### 关键发现：坐标系不一致

原有的OnPaint方法使用Canvas坐标系：
```csharp
// 原来的OnPaint中的绘制（第498行）
viewer.DrawRectangle(_selRect.startPoint, _selRect.endPoint.X - _selRect.startPoint.X, 
                    _selRect.endPoint.Y - _selRect.startPoint.Y, CSYS.Canvas);
```

但`_selRect.startPoint`和`_selRect.endPoint`存储的是Model坐标！这导致了坐标系的不匹配，但由于某种原因在原有系统中看起来"正常工作"。

### 统一的坐标转换流程

```
鼠标屏幕坐标 (e.Location)
        ↓
    Canvas坐标 (ScreenToCanvas) 
        ↓
    Model坐标 (CanvasToModel)
        ↓
    正确的矩形绘制 (考虑Y轴翻转)
```

## 🔧 技术细节

### Y轴翻转的处理

在Y轴翻转的坐标系中：
- **鼠标向上移动**: Y值增大
- **鼠标向下移动**: Y值减小

因此绘制矩形时需要：
```csharp
// 计算正确的左上角坐标
double topY = Math.Max(startY, endY);    // Y轴翻转，最大值是顶部
double leftX = Math.Min(startX, endX);   // X轴正常，最小值是左边

// 计算正确的宽度和高度
double width = Math.Abs(endX - startX);
double height = Math.Abs(endY - startY);
```

### DrawRectangle方法的特殊处理

```csharp
// 在Model坐标系下，ViewBase.DrawRectangle会自动处理Y轴翻转
if (csys == CSYS.Model)
{
    var end = ModelToCanvas(new Vector2(position.X + width, position.Y + height));
    Vector2 posInCanvas = ModelToCanvas(position);
    
    // 注意这里height的负号 - 这是为了补偿Y轴翻转
    Canvas?.DrawRect(posInCanvas.X, posInCanvas.Y, 
                    end.X - posInCanvas.X, 
                    -(end.Y - posInCanvas.Y), pen);
}
```

## 📊 修复验证

### 修复前的问题
- ✗ 框选框方向与鼠标移动方向相反
- ✗ Y轴坐标计算错误
- ✗ 矩形可能出现负的宽度或高度

### 修复后的效果
- ✅ 框选框正确跟随鼠标移动
- ✅ Y轴坐标计算正确
- ✅ 矩形始终从左上角到右下角绘制
- ✅ 支持任意方向的拖拽选择

## 🎯 关键学习点

1. **理解坐标系**: 在使用SkiaSharp时，必须理解变换矩阵对坐标系的影响
2. **Y轴翻转**: CAD应用常用Y轴向上为正的坐标系，需要特殊处理
3. **一致性**: 新旧系统必须使用相同的坐标处理逻辑
4. **绝对值**: 使用`Math.Abs`确保宽度和高度始终为正
5. **Min/Max**: 正确计算矩形的左上角坐标

## 📚 相关文档

- `Services/CoordinateService.cs` - 坐标转换服务
- `Renderers/SelectionRenderer.cs` - 选择框渲染器
- `Common/MgrIndicator.cs` - 原有指示器管理器
- `Base/ViewBase.cs` - 基础视图类（包含坐标变换）

## 🔮 未来考虑

1. **统一坐标系**: 考虑在整个应用中统一坐标系的处理方式
2. **坐标调试**: 添加坐标转换的调试工具
3. **文档完善**: 为所有坐标相关的类添加详细的坐标系说明
4. **单元测试**: 为坐标转换添加单元测试

这次修复确保了CAD应用中框选功能的正确性，为用户提供了直观、准确的交互体验。 