using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Numerics;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 动态输入界面覆盖层
    /// </summary>
    public partial class DynamicInputOverlay : UserControl
    {
        public enum InputMode
        {
            Hidden,
            Coordinate,      // 坐标输入 (X, Y)
            Distance,        // 距离输入
            Angle,           // 角度输入
            DistanceAngle,   // 极坐标输入 (距离 + 角度)
            InfoDisplay      // 信息显示
        }

        private InputMode _currentMode = InputMode.Hidden;
        private Vector2 _basePoint = Vector2.Zero;
        private Vector2 _currentPoint = Vector2.Zero;
        private bool _isUpdating = false;

        public event EventHandler<DynamicInputEventArgs> InputConfirmed;
        public event EventHandler<DynamicInputEventArgs> InputChanged;

        public InputMode CurrentMode
        {
            get => _currentMode;
            set
            {
                _currentMode = value;
                UpdateVisibility();
            }
        }

        public Vector2 BasePoint
        {
            get => _basePoint;
            set
            {
                _basePoint = value;
                UpdateDisplay();
            }
        }

        public Vector2 CurrentPoint
        {
            get => _currentPoint;
            set
            {
                _currentPoint = value;
                UpdateDisplay();
            }
        }

        public DynamicInputOverlay()
        {
            InitializeComponent();
            UpdateVisibility();
        }

        private void UpdateVisibility()
        {
            // 隐藏所有面板
            CoordinateInputPanel.Visibility = Visibility.Collapsed;
            DistanceInputPanel.Visibility = Visibility.Collapsed;
            AngleInputPanel.Visibility = Visibility.Collapsed;
            DistanceAngleInputPanel.Visibility = Visibility.Collapsed;
            InfoDisplayPanel.Visibility = Visibility.Collapsed;

            // 显示当前模式的面板
            switch (_currentMode)
            {
                case InputMode.Coordinate:
                    MainGrid.Visibility = Visibility.Visible;
                    CoordinateInputPanel.Visibility = Visibility.Visible;
                    XCoordinateTextBox.Focus();
                    break;
                case InputMode.Distance:
                    MainGrid.Visibility = Visibility.Visible;
                    DistanceInputPanel.Visibility = Visibility.Visible;
                    DistanceTextBox.Focus();
                    break;
                case InputMode.Angle:
                    MainGrid.Visibility = Visibility.Visible;
                    AngleInputPanel.Visibility = Visibility.Visible;
                    AngleTextBox.Focus();
                    break;
                case InputMode.DistanceAngle:
                    MainGrid.Visibility = Visibility.Visible;
                    DistanceAngleInputPanel.Visibility = Visibility.Visible;
                    PolarDistanceTextBox.Focus();
                    break;
                case InputMode.InfoDisplay:
                    MainGrid.Visibility = Visibility.Visible;
                    InfoDisplayPanel.Visibility = Visibility.Visible;
                    break;
                case InputMode.Hidden:
                default:
                    MainGrid.Visibility = Visibility.Collapsed;
                    break;
            }
        }

        private void UpdateDisplay()
        {
            if (_isUpdating) return;

            _isUpdating = true;
            try
            {
                switch (_currentMode)
                {
                    case InputMode.Coordinate:
                        if (string.IsNullOrEmpty(XCoordinateTextBox.Text))
                            XCoordinateTextBox.Text = _currentPoint.X.ToString("F3");
                        if (string.IsNullOrEmpty(YCoordinateTextBox.Text))
                            YCoordinateTextBox.Text = _currentPoint.Y.ToString("F3");
                        break;

                    case InputMode.Distance:
                        var distance = Vector2.Distance(_basePoint, _currentPoint);
                        if (string.IsNullOrEmpty(DistanceTextBox.Text))
                            DistanceTextBox.Text = distance.ToString("F3");
                        break;

                    case InputMode.Angle:
                        var angle = CalculateAngle(_basePoint, _currentPoint);
                        if (string.IsNullOrEmpty(AngleTextBox.Text))
                            AngleTextBox.Text = angle.ToString("F1");
                        break;

                    case InputMode.DistanceAngle:
                        var polarDistance = Vector2.Distance(_basePoint, _currentPoint);
                        var polarAngle = CalculateAngle(_basePoint, _currentPoint);
                        if (string.IsNullOrEmpty(PolarDistanceTextBox.Text))
                            PolarDistanceTextBox.Text = polarDistance.ToString("F3");
                        if (string.IsNullOrEmpty(PolarAngleTextBox.Text))
                            PolarAngleTextBox.Text = polarAngle.ToString("F1");
                        break;

                    case InputMode.InfoDisplay:
                        CurrentCoordinateText.Text = $"坐标: ({_currentPoint.X:F3}, {_currentPoint.Y:F3})";
                        var infoDistance = Vector2.Distance(_basePoint, _currentPoint);
                        var infoAngle = CalculateAngle(_basePoint, _currentPoint);
                        CurrentDistanceText.Text = $"距离: {infoDistance:F3}";
                        CurrentAngleText.Text = $"角度: {infoAngle:F1}°";
                        CurrentDistanceText.Visibility = Visibility.Visible;
                        CurrentAngleText.Visibility = Visibility.Visible;
                        break;
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private double CalculateAngle(Vector2 from, Vector2 to)
        {
            var delta = to - from;
            var angle = Math.Atan2(delta.Y, delta.X) * 180.0 / Math.PI;
            return angle < 0 ? angle + 360 : angle;
        }

        private void InputTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            var textBox = sender as TextBox;
            
            switch (e.Key)
            {
                case Key.Enter:
                    ConfirmInput();
                    e.Handled = true;
                    break;
                    
                case Key.Tab:
                    HandleTabNavigation(textBox, e.KeyboardDevice.Modifiers.HasFlag(ModifierKeys.Shift));
                    e.Handled = true;
                    break;
                    
                case Key.Escape:
                    CancelInput();
                    e.Handled = true;
                    break;
            }
        }

        private void HandleTabNavigation(TextBox currentTextBox, bool reverse)
        {
            switch (_currentMode)
            {
                case InputMode.Coordinate:
                    if (currentTextBox == XCoordinateTextBox && !reverse)
                        YCoordinateTextBox.Focus();
                    else if (currentTextBox == YCoordinateTextBox && reverse)
                        XCoordinateTextBox.Focus();
                    break;
                    
                case InputMode.DistanceAngle:
                    if (currentTextBox == PolarDistanceTextBox && !reverse)
                        PolarAngleTextBox.Focus();
                    else if (currentTextBox == PolarAngleTextBox && reverse)
                        PolarDistanceTextBox.Focus();
                    break;
            }
        }

        private void CoordinateTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            if (double.TryParse(XCoordinateTextBox.Text, out double x) &&
                double.TryParse(YCoordinateTextBox.Text, out double y))
            {
                _currentPoint = new Vector2((float)x, (float)y);
                OnInputChanged();
            }
        }

        private void DistanceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            if (double.TryParse(DistanceTextBox.Text, out double distance))
            {
                var currentAngle = CalculateAngle(_basePoint, _currentPoint);
                var radians = currentAngle * Math.PI / 180.0;
                _currentPoint = _basePoint + new Vector2(
                    (float)(distance * Math.Cos(radians)),
                    (float)(distance * Math.Sin(radians))
                );
                OnInputChanged();
            }
        }

        private void AngleTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            if (double.TryParse(AngleTextBox.Text, out double angle))
            {
                var currentDistance = Vector2.Distance(_basePoint, _currentPoint);
                var radians = angle * Math.PI / 180.0;
                _currentPoint = _basePoint + new Vector2(
                    (float)(currentDistance * Math.Cos(radians)),
                    (float)(currentDistance * Math.Sin(radians))
                );
                OnInputChanged();
            }
        }

        private void PolarTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            if (double.TryParse(PolarDistanceTextBox.Text, out double distance) &&
                double.TryParse(PolarAngleTextBox.Text, out double angle))
            {
                var radians = angle * Math.PI / 180.0;
                _currentPoint = _basePoint + new Vector2(
                    (float)(distance * Math.Cos(radians)),
                    (float)(distance * Math.Sin(radians))
                );
                OnInputChanged();
            }
        }

        private void ConfirmInput()
        {
            OnInputConfirmed();
        }

        private void CancelInput()
        {
            CurrentMode = InputMode.Hidden;
        }

        private void OnInputChanged()
        {
            InputChanged?.Invoke(this, new DynamicInputEventArgs
            {
                Mode = _currentMode,
                Point = _currentPoint,
                BasePoint = _basePoint
            });
        }

        private void OnInputConfirmed()
        {
            InputConfirmed?.Invoke(this, new DynamicInputEventArgs
            {
                Mode = _currentMode,
                Point = _currentPoint,
                BasePoint = _basePoint
            });
        }

        public void SetPosition(Point screenPosition)
        {
            Canvas.SetLeft(this, screenPosition.X + 20);
            Canvas.SetTop(this, screenPosition.Y - 60);
        }

        public void ClearInputs()
        {
            _isUpdating = true;
            try
            {
                XCoordinateTextBox.Clear();
                YCoordinateTextBox.Clear();
                DistanceTextBox.Clear();
                AngleTextBox.Clear();
                PolarDistanceTextBox.Clear();
                PolarAngleTextBox.Clear();
            }
            finally
            {
                _isUpdating = false;
            }
        }
    }

    public class DynamicInputEventArgs : EventArgs
    {
        public DynamicInputOverlay.InputMode Mode { get; set; }
        public Vector2 Point { get; set; }
        public Vector2 BasePoint { get; set; }
    }
}
