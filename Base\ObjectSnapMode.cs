﻿namespace McLaser.EditViewerSk.Base
{
    /// <summary>
    /// 对象捕捉模式
    /// 基于AutoCAD、SolidWorks等主流CAD软件的标准
    /// </summary>
    public enum ObjectSnapMode
    {
        // 未定义
        Undefined = 0,
        
        // 基础捕捉点
        End = 1,          // 端点
        Mid = 2,          // 中点
        Grid = 3,         // 网格点
        Center = 4,       // 中心点
        Node = 8,         // 节点
        Quad = 16,        // 象限点
        Ins = 32,         // 插入点
        Perpendicular = 64,   // 垂足
        Tangent = 128,    // 切点
        Near = 256,       // 最近点
        
        // 高级捕捉点
        Intersection = 512,     // 交点
        Extension = 1024,       // 延伸
        Parallel = 2048,        // 平行
        GeometricCenter = 4096, // 几何中心
        ApparentIntersection = 8192, // 虚交点
        
        // 特殊捕捉模式
        From = 16384,          // 从点
        MidBetween2Points = 32768, // 两点中间
        
        // 追踪相关
        PolarTracking = 65536,     // 极轴追踪
        ObjectTracking = 131072,   // 对象追踪
        
        // 组合模式
        All = End | Mid | Center | Node | Quad | Ins | Perpendicular | Tangent | 
              Intersection | Extension | Parallel | GeometricCenter,
              
        // 常用组合
        Basic = End | Mid | Center | Intersection,
        Advanced = All | ApparentIntersection | Extension | Parallel
    }
}
