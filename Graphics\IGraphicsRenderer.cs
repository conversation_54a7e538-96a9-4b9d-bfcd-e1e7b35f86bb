using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Enums;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 图形渲染器接口
    /// 基于CAD软件标准设计，支持多种坐标系和渲染模式
    /// </summary>
    public interface IGraphicsRenderer
    {
        #region 基础绘制方法

        /// <summary>
        /// 绘制直线
        /// </summary>
        void DrawLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制圆形
        /// </summary>
        void DrawCircle(Vector2 center, double radius, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制圆弧
        /// </summary>
        void DrawArc(Vector2 center, double radius, double startAngle, double sweepAngle, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制矩形
        /// </summary>
        void DrawRectangle(Vector2 position, double width, double height, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制椭圆
        /// </summary>
        void DrawEllipse(Vector2 center, double radiusX, double radiusY, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制多边形
        /// </summary>
        void DrawPolygon(Vector2[] points, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制路径
        /// </summary>
        void DrawPath(SKPath path, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制文本
        /// </summary>
        void DrawText(string text, Vector2 position, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        #endregion

        #region CAD专业功能

        /// <summary>
        /// 绘制带箭头的线条（用于标注）
        /// </summary>
        void DrawArrowLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, double arrowSize = 10, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制标注
        /// </summary>
        void DrawDimension(Vector2 startPoint, Vector2 endPoint, Vector2 textPosition, string text, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制箭头
        /// </summary>
        void DrawArrow(Vector2 point, Vector2 direction, double size, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制标注延伸线
        /// </summary>
        void DrawExtensionLine(Vector2 startPoint, Vector2 endPoint, double offset, double extend, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制网格
        /// </summary>
        void DrawGrid(double spacing, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制坐标轴
        /// </summary>
        void DrawCoordinateAxis(Vector2 origin, double length, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        /// <summary>
        /// 绘制选择高亮
        /// </summary>
        void DrawSelectionHighlight(Vector2[] points, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model);

        #endregion

        #region 渲染控制

        /// <summary>
        /// 开始渲染批次（性能优化）
        /// </summary>
        void BeginRender();

        /// <summary>
        /// 结束渲染批次
        /// </summary>
        void EndRender();

        /// <summary>
        /// 保存渲染状态
        /// </summary>
        void SaveState();

        /// <summary>
        /// 恢复渲染状态
        /// </summary>
        void RestoreState();

        /// <summary>
        /// 设置剪裁区域
        /// </summary>
        void SetClipRegion(Vector2 topLeft, Vector2 bottomRight, CoordinateSpace space = CoordinateSpace.Model);

        #endregion

        #region 变换矩阵操作

        /// <summary>
        /// 保存当前变换状态并应用新矩阵
        /// </summary>
        void PushMatrix(SKMatrix matrix);

        /// <summary>
        /// 恢复之前的变换状态
        /// </summary>
        void PopMatrix();

        /// <summary>
        /// 保存当前渲染状态
        /// </summary>
        void SaveState();

        /// <summary>
        /// 恢复之前的渲染状态
        /// </summary>
        void RestoreState();

        #endregion

        #region 坐标转换

        /// <summary>
        /// 获取坐标转换服务
        /// </summary>
        ICoordinateTransform CoordinateTransform { get; }

        #endregion
    }

    /// <summary>
    /// 坐标空间枚举
    /// </summary>
    public enum CoordinateSpace
    {
        /// <summary>
        /// 屏幕坐标空间（像素）
        /// </summary>
        Screen,

        /// <summary>
        /// 视口坐标空间（设备无关单位）
        /// </summary>
        Viewport,

        /// <summary>
        /// 模型坐标空间（世界坐标）
        /// </summary>
        Model
    }

    /// <summary>
    /// 坐标转换服务接口
    /// </summary>
    public interface ICoordinateTransform
    {
        /// <summary>
        /// 在不同坐标空间之间转换点
        /// </summary>
        Vector2 TransformPoint(Vector2 point, CoordinateSpace from, CoordinateSpace to);

        /// <summary>
        /// 在不同坐标空间之间转换距离
        /// </summary>
        double TransformDistance(double distance, CoordinateSpace from, CoordinateSpace to);

        /// <summary>
        /// 在不同坐标空间之间转换角度
        /// </summary>
        double TransformAngle(double angle, CoordinateSpace from, CoordinateSpace to);

        /// <summary>
        /// 获取当前视图变换矩阵
        /// </summary>
        SKMatrix GetViewMatrix();

        /// <summary>
        /// 获取当前投影矩阵
        /// </summary>
        SKMatrix GetProjectionMatrix();
    }
} 