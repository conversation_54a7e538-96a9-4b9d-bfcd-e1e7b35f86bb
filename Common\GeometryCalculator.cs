using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Numerics;

namespace McLaser.EditViewerSk.Common
{
    /// <summary>
    /// 几何计算工具类
    /// 提供统一、精确的几何计算方法，避免代码重复
    /// </summary>
    public static class GeometryCalculator
    {
        public const float DefaultTolerance = 1e-6f;
        
        #region 交点计算
        
        /// <summary>
        /// 计算两直线的交点
        /// </summary>
        public static Vector2? CalculateLineLineIntersection(Vector2 line1Start, Vector2 line1End, 
                                                           Vector2 line2Start, Vector2 line2End, 
                                                           bool extendLines = false, 
                                                           float tolerance = DefaultTolerance)
        {
            var d1 = line1End - line1Start;
            var d2 = line2End - line2Start;
            var d3 = line1Start - line2Start;
            
            var cross = Vector2Cross(d1, d2);
            if (Math.Abs(cross) < tolerance) return null; // 平行线
            
            var t1 = Vector2Cross(d3, d2) / cross;
            var t2 = Vector2Cross(d3, d1) / cross;
            
            if (extendLines || (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1))
            {
                return line1Start + t1 * d1;
            }
            
            return null;
        }
        
        /// <summary>
        /// 计算直线与圆的交点
        /// </summary>
        public static List<Vector2> CalculateLineCircleIntersections(Vector2 lineStart, Vector2 lineEnd,
                                                                   Vector2 circleCenter, float radius,
                                                                   bool extendLine = false,
                                                                   float tolerance = DefaultTolerance)
        {
            var intersections = new List<Vector2>();
            
            var d = lineEnd - lineStart;
            var f = lineStart - circleCenter;
            
            var a = Vector2.Dot(d, d);
            var b = 2 * Vector2.Dot(f, d);
            var c = Vector2.Dot(f, f) - radius * radius;
            
            var discriminant = b * b - 4 * a * c;
            
            if (discriminant >= 0)
            {
                discriminant = (float)Math.Sqrt(discriminant);
                
                var t1 = (-b - discriminant) / (2 * a);
                var t2 = (-b + discriminant) / (2 * a);
                
                if (extendLine || (t1 >= 0 && t1 <= 1))
                {
                    intersections.Add(lineStart + t1 * d);
                }
                
                if (extendLine || (t2 >= 0 && t2 <= 1))
                {
                    if (Math.Abs(t2 - t1) > tolerance)
                    {
                        intersections.Add(lineStart + t2 * d);
                    }
                }
            }
            
            return intersections;
        }
        
        /// <summary>
        /// 计算两圆的交点
        /// </summary>
        public static List<Vector2> CalculateCircleCircleIntersections(Vector2 center1, float radius1,
                                                                      Vector2 center2, float radius2,
                                                                      float tolerance = DefaultTolerance)
        {
            var intersections = new List<Vector2>();
            
            var d = Vector2.Distance(center1, center2);
            
            // 检查圆是否相交
            if (d > radius1 + radius2 || d < Math.Abs(radius1 - radius2) || d < tolerance)
            {
                return intersections; // 无交点或重合
            }
            
            var a = (radius1 * radius1 - radius2 * radius2 + d * d) / (2 * d);
            var h = (float)Math.Sqrt(radius1 * radius1 - a * a);
            
            var p = center1 + a * Vector2.Normalize(center2 - center1);
            
            var offset = h * new Vector2(
                -(center2.Y - center1.Y) / d,
                (center2.X - center1.X) / d
            );
            
            intersections.Add(p + offset);
            if (h > tolerance) // 两个不同的交点
            {
                intersections.Add(p - offset);
            }
            
            return intersections;
        }
        
        #endregion
        
        #region 距离计算
        
        /// <summary>
        /// 点到直线的距离
        /// </summary>
        public static float PointToLineDistance(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            var lineVec = lineEnd - lineStart;
            var pointVec = point - lineStart;
            
            if (lineVec.LengthSquared() < DefaultTolerance)
            {
                return Vector2.Distance(point, lineStart);
            }
            
            var cross = Math.Abs(Vector2Cross(lineVec, pointVec));
            return cross / lineVec.Length();
        }
        
        /// <summary>
        /// 点到线段的距离
        /// </summary>
        public static float PointToSegmentDistance(Vector2 point, Vector2 segmentStart, Vector2 segmentEnd)
        {
            var segmentVec = segmentEnd - segmentStart;
            var pointVec = point - segmentStart;
            
            if (segmentVec.LengthSquared() < DefaultTolerance)
            {
                return Vector2.Distance(point, segmentStart);
            }
            
            var t = Vector2.Dot(pointVec, segmentVec) / segmentVec.LengthSquared();
            t = Math.Max(0, Math.Min(1, t));
            
            var projection = segmentStart + t * segmentVec;
            return Vector2.Distance(point, projection);
        }
        
        #endregion
        
        #region 角度计算
        
        /// <summary>
        /// 计算两个向量之间的角度（弧度）
        /// </summary>
        public static float CalculateAngle(Vector2 vector1, Vector2 vector2)
        {
            var dot = Vector2.Dot(Vector2.Normalize(vector1), Vector2.Normalize(vector2));
            return (float)Math.Acos(Math.Max(-1, Math.Min(1, dot)));
        }
        
        /// <summary>
        /// 计算从中心点到目标点的角度（弧度）
        /// </summary>
        public static float CalculateAngle(Vector2 center, Vector2 target)
        {
            var delta = target - center;
            return (float)Math.Atan2(delta.Y, delta.X);
        }
        
        /// <summary>
        /// 角度标准化到[0, 2π)
        /// </summary>
        public static float NormalizeAngle(float angle)
        {
            while (angle < 0) angle += (float)(2 * Math.PI);
            while (angle >= 2 * Math.PI) angle -= (float)(2 * Math.PI);
            return angle;
        }
        
        #endregion
        
        #region 变换计算
        
        /// <summary>
        /// 创建绕点旋转的变换矩阵
        /// </summary>
        public static Matrix3x2 CreateRotationMatrix(Vector2 center, float angleRadians)
        {
            return Matrix3x2.CreateTranslation(-center) *
                   Matrix3x2.CreateRotation(angleRadians) *
                   Matrix3x2.CreateTranslation(center);
        }
        
        /// <summary>
        /// 创建绕点缩放的变换矩阵
        /// </summary>
        public static Matrix3x2 CreateScaleMatrix(Vector2 center, float scale)
        {
            return Matrix3x2.CreateTranslation(-center) *
                   Matrix3x2.CreateScale(scale) *
                   Matrix3x2.CreateTranslation(center);
        }
        
        /// <summary>
        /// 创建绕点非统一缩放的变换矩阵
        /// </summary>
        public static Matrix3x2 CreateScaleMatrix(Vector2 center, Vector2 scale)
        {
            return Matrix3x2.CreateTranslation(-center) *
                   Matrix3x2.CreateScale(scale) *
                   Matrix3x2.CreateTranslation(center);
        }
        
        #endregion
        
        #region 偏移计算
        
        /// <summary>
        /// 计算直线的偏移
        /// </summary>
        public static (Vector2 start, Vector2 end) CalculateLineOffset(Vector2 lineStart, Vector2 lineEnd, 
                                                                       float distance, bool leftSide = true)
        {
            var direction = Vector2.Normalize(lineEnd - lineStart);
            var normal = new Vector2(-direction.Y, direction.X);
            
            if (!leftSide) normal = -normal;
            
            var offset = normal * distance;
            
            return (lineStart + offset, lineEnd + offset);
        }
        
        /// <summary>
        /// 计算圆的偏移
        /// </summary>
        public static (Vector2 center, float radius) CalculateCircleOffset(Vector2 center, float radius, 
                                                                           float distance, bool outward = true)
        {
            var newRadius = outward ? radius + distance : radius - distance;
            return (center, Math.Max(0, newRadius));
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 2D向量叉积
        /// </summary>
        public static float Vector2Cross(Vector2 a, Vector2 b)
        {
            return a.X * b.Y - a.Y * b.X;
        }
        
        /// <summary>
        /// 检查点是否在矩形内
        /// </summary>
        public static bool IsPointInRectangle(Vector2 point, Vector2 rectMin, Vector2 rectMax)
        {
            return point.X >= rectMin.X && point.X <= rectMax.X &&
                   point.Y >= rectMin.Y && point.Y <= rectMax.Y;
        }
        
        /// <summary>
        /// 检查两个浮点数是否相等（考虑误差）
        /// </summary>
        public static bool FloatEquals(float a, float b, float tolerance = DefaultTolerance)
        {
            return Math.Abs(a - b) < tolerance;
        }
        
        /// <summary>
        /// 检查两个向量是否相等（考虑误差）
        /// </summary>
        public static bool Vector2Equals(Vector2 a, Vector2 b, float tolerance = DefaultTolerance)
        {
            return Vector2.Distance(a, b) < tolerance;
        }
        
        /// <summary>
        /// 将点投影到直线上
        /// </summary>
        public static Vector2 ProjectPointOntoLine(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            var lineVec = lineEnd - lineStart;
            var pointVec = point - lineStart;
            
            if (lineVec.LengthSquared() < DefaultTolerance)
            {
                return lineStart;
            }
            
            var t = Vector2.Dot(pointVec, lineVec) / lineVec.LengthSquared();
            return lineStart + t * lineVec;
        }
        
        /// <summary>
        /// 将点约束到线段上
        /// </summary>
        public static Vector2 ClampPointToSegment(Vector2 point, Vector2 segmentStart, Vector2 segmentEnd)
        {
            var segmentVec = segmentEnd - segmentStart;
            var pointVec = point - segmentStart;
            
            if (segmentVec.LengthSquared() < DefaultTolerance)
            {
                return segmentStart;
            }
            
            var t = Vector2.Dot(pointVec, segmentVec) / segmentVec.LengthSquared();
            t = Math.Max(0, Math.Min(1, t));
            
            return segmentStart + t * segmentVec;
        }
        
        #endregion
        
        #region 边界框计算
        
        /// <summary>
        /// 计算点集的边界框
        /// </summary>
        public static (Vector2 min, Vector2 max) CalculateBoundingBox(IEnumerable<Vector2> points)
        {
            var minX = float.MaxValue;
            var minY = float.MaxValue;
            var maxX = float.MinValue;
            var maxY = float.MinValue;
            
            bool hasPoints = false;
            
            foreach (var point in points)
            {
                hasPoints = true;
                minX = Math.Min(minX, point.X);
                minY = Math.Min(minY, point.Y);
                maxX = Math.Max(maxX, point.X);
                maxY = Math.Max(maxY, point.Y);
            }
            
            if (!hasPoints)
            {
                return (Vector2.Zero, Vector2.Zero);
            }
            
            return (new Vector2(minX, minY), new Vector2(maxX, maxY));
        }
        
        #endregion
    }
    
    /// <summary>
    /// 几何计算结果
    /// </summary>
    public class GeometryResult<T>
    {
        public bool Success { get; set; }
        public T Value { get; set; }
        public string ErrorMessage { get; set; }
        
        public static GeometryResult<T> CreateSuccess(T value)
        {
            return new GeometryResult<T> { Success = true, Value = value };
        }
        
        public static GeometryResult<T> CreateFailure(string errorMessage)
        {
            return new GeometryResult<T> { Success = false, ErrorMessage = errorMessage };
        }
    }
} 