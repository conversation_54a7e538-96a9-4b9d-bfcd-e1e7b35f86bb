using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Numerics;

namespace McLaser.EditViewerSk.Services
{
    /// <summary>
    /// 坐标转换服务实现
    /// 封装了ViewBase中的坐标转换逻辑，提供统一的坐标系转换服务
    /// </summary>
    public class CoordinateService : ICoordinateService
    {
        private readonly ViewBase _viewBase;

        public CoordinateService(ViewBase viewBase)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
        }

        /// <summary>
        /// 将Canvas坐标转换为Model坐标
        /// </summary>
        public Vector2 CanvasToModel(Vector2 canvasPoint)
        {
            return _viewBase.CanvasToModel(canvasPoint);
        }

        /// <summary>
        /// 将Model坐标转换为Canvas坐标
        /// </summary>
        public Vector2 ModelToCanvas(Vector2 modelPoint)
        {
            return _viewBase.ModelToCanvas(modelPoint);
        }

        /// <summary>
        /// 将Canvas距离转换为Model距离
        /// </summary>
        public float CanvasToModel(double canvasDistance)
        {
            return _viewBase.CanvasToModel(canvasDistance);
        }

        /// <summary>
        /// 将Model距离转换为Canvas距离
        /// </summary>
        public float ModelToCanvas(double modelDistance)
        {
            return _viewBase.ModelToCanvas(modelDistance);
        }

        /// <summary>
        /// 屏幕坐标转换为Canvas坐标
        /// 处理SkiaSharp坐标系的特殊情况
        /// </summary>
        public Vector2 ScreenToCanvas(Vector2 screenPoint)
        {
            // 对于鼠标事件，屏幕坐标直接对应Canvas坐标
            // 注意：ViewBase使用了Y轴翻转的变换矩阵 CreateScale(1, -1)
            // 但鼠标坐标不需要在这里翻转，因为变换会在CanvasToModel中处理
            return screenPoint;
        }

        /// <summary>
        /// Canvas坐标转换为屏幕坐标
        /// </summary>
        public Vector2 CanvasToScreen(Vector2 canvasPoint)
        {
            // 当前实现中，Canvas坐标就是屏幕坐标
            return canvasPoint;
        }
    }
} 