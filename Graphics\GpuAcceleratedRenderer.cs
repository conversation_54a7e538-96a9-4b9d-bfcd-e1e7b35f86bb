using McLaser.EditViewerSk.Base;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Numerics;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// GPU加速渲染器
    /// 利用SkiaSharp的GPU加速功能提升渲染性能
    /// </summary>
    public class GpuAcceleratedRenderer : IGraphicsRenderer, IDisposable
    {
        #region 私有字段

        private readonly ViewBase _viewBase;
        private readonly SKCanvas _canvas;
        private readonly ICoordinateTransform _coordinateTransform;
        private GRContext _grContext;
        private SKSurface _surface;
        
        // 渲染缓存
        private readonly Dictionary<string, SKPicture> _pictureCache;
        private readonly Dictionary<string, SKShader> _shaderCache;
        private readonly Dictionary<string, SKPaint> _paintCache;
        
        // 批处理
        private readonly List<RenderCommand> _renderCommands;
        private bool _isBatchMode;
        private int _renderDepth;
        
        // 性能统计
        private long _frameCount;
        private TimeSpan _totalRenderTime;
        private readonly Stopwatch _frameTimer;
        
        // GPU资源管理
        private bool _isGpuEnabled;
        private bool _isDisposed;
        private readonly int _maxCacheSize = 1000;

        #endregion

        #region 构造函数

        public GpuAcceleratedRenderer(ViewBase viewBase, SKCanvas canvas, GRContext grContext = null)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _canvas = canvas ?? throw new ArgumentNullException(nameof(canvas));
            _coordinateTransform = new CoordinateTransform(viewBase);
            _grContext = grContext;
            
            _pictureCache = new Dictionary<string, SKPicture>();
            _shaderCache = new Dictionary<string, SKShader>();
            _paintCache = new Dictionary<string, SKPaint>();
            _renderCommands = new List<RenderCommand>();
            _frameTimer = new Stopwatch();
            
            _isGpuEnabled = _grContext != null;
            _isBatchMode = false;
            _renderDepth = 0;
            
            InitializeGpuResources();
        }

        #endregion

        #region 公共属性

        public ICoordinateTransform CoordinateTransform => _coordinateTransform;

        /// <summary>
        /// 是否启用GPU加速
        /// </summary>
        public bool IsGpuEnabled => _isGpuEnabled;

        /// <summary>
        /// 平均帧率
        /// </summary>
        public double AverageFrameRate => _frameCount > 0 ? 1000.0 / (_totalRenderTime.TotalMilliseconds / _frameCount) : 0;

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double CacheHitRate { get; private set; }

        #endregion

        #region GPU资源管理

        /// <summary>
        /// 初始化GPU资源
        /// </summary>
        private void InitializeGpuResources()
        {
            try
            {
                if (_grContext != null)
                {
                    // 创建GPU表面
                    var info = new SKImageInfo(
                        (int)_viewBase.Width, 
                        (int)_viewBase.Height, 
                        SKColorType.Rgba8888, 
                        SKAlphaType.Premul);
                    
                    _surface = SKSurface.Create(_grContext, false, info);
                    _isGpuEnabled = _surface != null;
                    
                    Debug.WriteLine($"GPU acceleration {(_isGpuEnabled ? "enabled" : "disabled")}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"GPU initialization failed: {ex.Message}");
                _isGpuEnabled = false;
            }
        }

        /// <summary>
        /// 设置GPU上下文
        /// </summary>
        /// <param name="grContext">GPU上下文</param>
        public void SetGpuContext(GRContext grContext)
        {
            _grContext = grContext;
            InitializeGpuResources();
        }

        #endregion

        #region 批处理渲染

        public void BeginRender()
        {
            _frameTimer.Restart();
            _renderDepth++;
            
            if (_renderDepth == 1)
            {
                _isBatchMode = true;
                _renderCommands.Clear();
                
                // 开始GPU批处理
                if (_isGpuEnabled && _surface != null)
                {
                    var canvas = _surface.Canvas;
                    canvas.Clear(SKColors.Transparent);
                }
            }
        }

        public void EndRender()
        {
            _renderDepth--;
            
            if (_renderDepth == 0 && _isBatchMode)
            {
                try
                {
                    // 执行批处理渲染
                    ExecuteBatchRender();
                    
                    // GPU表面刷新
                    if (_isGpuEnabled && _surface != null)
                    {
                        _surface.Canvas.Flush();
                        _grContext?.Flush();
                    }
                }
                finally
                {
                    _isBatchMode = false;
                    _frameTimer.Stop();
                    
                    // 更新性能统计
                    _frameCount++;
                    _totalRenderTime += _frameTimer.Elapsed;
                }
            }
        }

        /// <summary>
        /// 执行批处理渲染
        /// </summary>
        private void ExecuteBatchRender()
        {
            if (_renderCommands.Count == 0) return;

            try
            {
                // 按类型分组渲染命令以优化GPU调用
                var groupedCommands = GroupRenderCommands(_renderCommands);
                
                foreach (var group in groupedCommands)
                {
                    ExecuteCommandGroup(group);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 分组渲染命令
        /// </summary>
        /// <param name="commands">命令列表</param>
        /// <returns>分组后的命令</returns>
        private Dictionary<RenderCommandType, List<RenderCommand>> GroupRenderCommands(List<RenderCommand> commands)
        {
            var groups = new Dictionary<RenderCommandType, List<RenderCommand>>();
            
            foreach (var command in commands)
            {
                if (!groups.ContainsKey(command.Type))
                {
                    groups[command.Type] = new List<RenderCommand>();
                }
                groups[command.Type].Add(command);
            }
            
            return groups;
        }

        /// <summary>
        /// 执行命令组
        /// </summary>
        /// <param name="commands">命令组</param>
        private void ExecuteCommandGroup(List<RenderCommand> commands)
        {
            if (commands.Count == 0) return;

            var commandType = commands[0].Type;
            
            switch (commandType)
            {
                case RenderCommandType.Line:
                    ExecuteLineCommands(commands);
                    break;
                case RenderCommandType.Circle:
                    ExecuteCircleCommands(commands);
                    break;
                case RenderCommandType.Rectangle:
                    ExecuteRectangleCommands(commands);
                    break;
                case RenderCommandType.Arc:
                    ExecuteArcCommands(commands);
                    break;
                case RenderCommandType.Text:
                    ExecuteTextCommands(commands);
                    break;
                default:
                    ExecuteGenericCommands(commands);
                    break;
            }
        }

        /// <summary>
        /// 执行线条命令组
        /// </summary>
        /// <param name="commands">线条命令</param>
        private void ExecuteLineCommands(List<RenderCommand> commands)
        {
            try
            {
                // 批量绘制线条以减少GPU调用
                var points = new List<SKPoint>();
                SKPaint currentPaint = null;
                
                foreach (var cmd in commands)
                {
                    var lineCmd = cmd as LineRenderCommand;
                    if (lineCmd != null)
                    {
                        // 如果画笔不同，先绘制之前的线条
                        if (currentPaint != null && !PaintEquals(currentPaint, lineCmd.Paint))
                        {
                            if (points.Count >= 4)
                            {
                                _canvas.DrawPoints(SKPointMode.Lines, points.ToArray(), currentPaint);
                            }
                            points.Clear();
                        }
                        
                        currentPaint = lineCmd.Paint;
                        points.Add(new SKPoint(lineCmd.StartPoint.X, lineCmd.StartPoint.Y));
                        points.Add(new SKPoint(lineCmd.EndPoint.X, lineCmd.EndPoint.Y));
                    }
                }
                
                // 绘制剩余的线条
                if (points.Count >= 4 && currentPaint != null)
                {
                    _canvas.DrawPoints(SKPointMode.Lines, points.ToArray(), currentPaint);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Line batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行圆形命令组
        /// </summary>
        /// <param name="commands">圆形命令</param>
        private void ExecuteCircleCommands(List<RenderCommand> commands)
        {
            try
            {
                foreach (var cmd in commands)
                {
                    var circleCmd = cmd as CircleRenderCommand;
                    if (circleCmd != null)
                    {
                        _canvas.DrawCircle(circleCmd.Center.X, circleCmd.Center.Y, circleCmd.Radius, circleCmd.Paint);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Circle batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行矩形命令组
        /// </summary>
        /// <param name="commands">矩形命令</param>
        private void ExecuteRectangleCommands(List<RenderCommand> commands)
        {
            try
            {
                foreach (var cmd in commands)
                {
                    var rectCmd = cmd as RectangleRenderCommand;
                    if (rectCmd != null)
                    {
                        var rect = new SKRect(rectCmd.Position.X, rectCmd.Position.Y, 
                            rectCmd.Position.X + rectCmd.Width, rectCmd.Position.Y + rectCmd.Height);
                        _canvas.DrawRect(rect, rectCmd.Paint);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Rectangle batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行弧形命令组
        /// </summary>
        /// <param name="commands">弧形命令</param>
        private void ExecuteArcCommands(List<RenderCommand> commands)
        {
            try
            {
                foreach (var cmd in commands)
                {
                    var arcCmd = cmd as ArcRenderCommand;
                    if (arcCmd != null)
                    {
                        var rect = new SKRect(
                            arcCmd.Center.X - arcCmd.Radius, arcCmd.Center.Y - arcCmd.Radius,
                            arcCmd.Center.X + arcCmd.Radius, arcCmd.Center.Y + arcCmd.Radius);
                        _canvas.DrawArc(rect, arcCmd.StartAngle, arcCmd.SweepAngle, false, arcCmd.Paint);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Arc batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行文本命令组
        /// </summary>
        /// <param name="commands">文本命令</param>
        private void ExecuteTextCommands(List<RenderCommand> commands)
        {
            try
            {
                foreach (var cmd in commands)
                {
                    var textCmd = cmd as TextRenderCommand;
                    if (textCmd != null)
                    {
                        _canvas.DrawText(textCmd.Text, textCmd.Position.X, textCmd.Position.Y, textCmd.Paint);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Text batch render error: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行通用命令组
        /// </summary>
        /// <param name="commands">通用命令</param>
        private void ExecuteGenericCommands(List<RenderCommand> commands)
        {
            try
            {
                foreach (var cmd in commands)
                {
                    cmd.Execute(_canvas);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Generic batch render error: {ex.Message}");
            }
        }

        #endregion

        #region IGraphicsRenderer实现

        public void DrawLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var start = TransformPoint(startPoint, space);
            var end = TransformPoint(endPoint, space);

            if (_isBatchMode)
            {
                _renderCommands.Add(new LineRenderCommand(start, end, paint, CoordinateSpace.Viewport));
            }
            else
            {
                _canvas.DrawLine(start.X, start.Y, end.X, end.Y, paint);
            }
        }

        public void DrawCircle(Vector2 center, double radius, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadius = (float)_coordinateTransform.TransformDistance(radius, space, CoordinateSpace.Viewport);

            if (_isBatchMode)
            {
                _renderCommands.Add(new CircleRenderCommand(transformedCenter, transformedRadius, paint, CoordinateSpace.Viewport));
            }
            else
            {
                _canvas.DrawCircle(transformedCenter.X, transformedCenter.Y, transformedRadius, paint);
            }
        }

        public void DrawRectangle(Vector2 position, double width, double height, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedPosition = TransformPoint(position, space);
            var transformedWidth = (float)_coordinateTransform.TransformDistance(width, space, CoordinateSpace.Viewport);
            var transformedHeight = (float)_coordinateTransform.TransformDistance(height, space, CoordinateSpace.Viewport);

            if (space == CoordinateSpace.Model)
            {
                transformedHeight = -transformedHeight;
            }

            if (_isBatchMode)
            {
                _renderCommands.Add(new RectangleRenderCommand(transformedPosition, transformedWidth, transformedHeight, paint, CoordinateSpace.Viewport));
            }
            else
            {
                _canvas.DrawRect(transformedPosition.X, transformedPosition.Y, transformedWidth, transformedHeight, paint);
            }
        }

        public void DrawArc(Vector2 center, double radius, double startAngle, double sweepAngle, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadius = (float)_coordinateTransform.TransformDistance(radius, space, CoordinateSpace.Viewport);

            if (_isBatchMode)
            {
                _renderCommands.Add(new ArcRenderCommand(transformedCenter, transformedRadius, (float)startAngle, (float)sweepAngle, paint, CoordinateSpace.Viewport));
            }
            else
            {
                var rect = new SKRect(
                    transformedCenter.X - transformedRadius, transformedCenter.Y - transformedRadius,
                    transformedCenter.X + transformedRadius, transformedCenter.Y + transformedRadius);
                _canvas.DrawArc(rect, (float)startAngle, (float)sweepAngle, false, paint);
            }
        }

        public void DrawEllipse(Vector2 center, double radiusX, double radiusY, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadiusX = (float)_coordinateTransform.TransformDistance(radiusX, space, CoordinateSpace.Viewport);
            var transformedRadiusY = (float)_coordinateTransform.TransformDistance(radiusY, space, CoordinateSpace.Viewport);

            if (_isBatchMode)
            {
                _renderCommands.Add(new EllipseRenderCommand(transformedCenter, transformedRadiusX, transformedRadiusY, paint, CoordinateSpace.Viewport));
            }
            else
            {
                var rect = new SKRect(
                    transformedCenter.X - transformedRadiusX, transformedCenter.Y - transformedRadiusY,
                    transformedCenter.X + transformedRadiusX, transformedCenter.Y + transformedRadiusY);
                _canvas.DrawOval(rect, paint);
            }
        }

        public void DrawPath(SKPath path, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || path == null) return;

            if (_isBatchMode)
            {
                SKPath transformedPath = path;
                if (space != CoordinateSpace.Viewport)
                {
                    transformedPath = new SKPath();
                    var matrix = GetTransformMatrix(space);
                    path.Transform(in matrix, transformedPath);
                }
                _renderCommands.Add(new PathRenderCommand(transformedPath, paint, CoordinateSpace.Viewport));
            }
            else
            {
                if (space == CoordinateSpace.Viewport)
                {
                    _canvas.DrawPath(path, paint);
                }
                else
                {
                    using var transformedPath = new SKPath();
                    var matrix = GetTransformMatrix(space);
                    path.Transform(in matrix, transformedPath);
                    _canvas.DrawPath(transformedPath, paint);
                }
            }
        }

        public void DrawText(string text, Vector2 position, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || string.IsNullOrEmpty(text)) return;

            var transformedPosition = TransformPoint(position, space);

            if (_isBatchMode)
            {
                _renderCommands.Add(new TextRenderCommand(text, transformedPosition, paint, CoordinateSpace.Viewport));
            }
            else
            {
                _canvas.DrawText(text, transformedPosition.X, transformedPosition.Y, paint);
            }
        }

        public void DrawArrowLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, double arrowSize = 5, CoordinateSpace space = CoordinateSpace.Model)
        {
            DrawLine(startPoint, endPoint, paint, space);

            var direction = Vector2.Normalize(endPoint - startPoint);
            var perpendicular = new Vector2(-direction.Y, direction.X);

            var arrowPoint1 = endPoint - direction * (float)arrowSize + perpendicular * (float)(arrowSize * 0.5);
            var arrowPoint2 = endPoint - direction * (float)arrowSize - perpendicular * (float)(arrowSize * 0.5);

            DrawLine(endPoint, arrowPoint1, paint, space);
            DrawLine(endPoint, arrowPoint2, paint, space);
        }

        public void DrawDimension(Vector2 startPoint, Vector2 endPoint, Vector2 textPosition, string text, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            DrawLine(startPoint, endPoint, paint, space);
            DrawArrowLine(startPoint, endPoint, paint, 5, space);
            DrawText(text, textPosition, paint, space);
        }

        public void DrawGrid(Vector2 origin, double spacing, int countX, int countY, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            BeginRender();
            try
            {
                for (int i = 0; i <= countX; i++)
                {
                    var x = origin.X + i * spacing;
                    DrawLine(new Vector2((float)x, origin.Y), new Vector2((float)x, origin.Y + countY * spacing), paint, space);
                }

                for (int j = 0; j <= countY; j++)
                {
                    var y = origin.Y + j * spacing;
                    DrawLine(new Vector2(origin.X, (float)y), new Vector2(origin.X + countX * spacing, (float)y), paint, space);
                }
            }
            finally
            {
                EndRender();
            }
        }

        public void DrawCoordinateAxes(Vector2 origin, double length, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            DrawArrowLine(origin, new Vector2(origin.X + (float)length, origin.Y), paint, 8, space);
            DrawArrowLine(origin, new Vector2(origin.X, origin.Y + (float)length), paint, 8, space);
        }

        public void DrawSelectionHighlight(Vector2 topLeft, Vector2 bottomRight, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var width = bottomRight.X - topLeft.X;
            var height = bottomRight.Y - topLeft.Y;
            DrawRectangle(topLeft, width, height, paint, space);
        }

        public void SetClipRegion(Vector2 topLeft, Vector2 bottomRight, CoordinateSpace space = CoordinateSpace.Model)
        {
            var transformedTopLeft = TransformPoint(topLeft, space);
            var transformedBottomRight = TransformPoint(bottomRight, space);

            var clipRect = new SKRect(
                Math.Min(transformedTopLeft.X, transformedBottomRight.X),
                Math.Min(transformedTopLeft.Y, transformedBottomRight.Y),
                Math.Max(transformedTopLeft.X, transformedBottomRight.X),
                Math.Max(transformedTopLeft.Y, transformedBottomRight.Y));

            _canvas.ClipRect(clipRect);
        }

        #endregion

        #region 辅助方法

        private Vector2 TransformPoint(Vector2 point, CoordinateSpace space)
        {
            return _coordinateTransform.TransformPoint(point, space, CoordinateSpace.Viewport);
        }

        private SKMatrix GetTransformMatrix(CoordinateSpace fromSpace)
        {
            return _coordinateTransform.GetTransformMatrix(fromSpace, CoordinateSpace.Viewport);
        }

        /// <summary>
        /// 比较两个画笔是否相等
        /// </summary>
        /// <param name="paint1">画笔1</param>
        /// <param name="paint2">画笔2</param>
        /// <returns>是否相等</returns>
        private bool PaintEquals(SKPaint paint1, SKPaint paint2)
        {
            if (paint1 == paint2) return true;
            if (paint1 == null || paint2 == null) return false;

            return paint1.Color == paint2.Color &&
                   paint1.StrokeWidth == paint2.StrokeWidth &&
                   paint1.Style == paint2.Style;
        }

        #endregion

        #region 缓存管理

        /// <summary>
        /// 清理缓存
        /// </summary>
        private void ClearCaches()
        {
            try
            {
                foreach (var picture in _pictureCache.Values)
                {
                    picture?.Dispose();
                }
                _pictureCache.Clear();

                foreach (var shader in _shaderCache.Values)
                {
                    shader?.Dispose();
                }
                _shaderCache.Clear();

                foreach (var paint in _paintCache.Values)
                {
                    paint?.Dispose();
                }
                _paintCache.Clear();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Cache clear error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <returns>性能统计</returns>
        public GpuRenderStats GetStats()
        {
            return new GpuRenderStats
            {
                IsGpuEnabled = _isGpuEnabled,
                FrameCount = _frameCount,
                AverageFrameRate = AverageFrameRate,
                CacheHitRate = CacheHitRate,
                PictureCacheSize = _pictureCache.Count,
                ShaderCacheSize = _shaderCache.Count,
                PaintCacheSize = _paintCache.Count,
                TotalRenderTime = _totalRenderTime.TotalMilliseconds
            };
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                ClearCaches();
                _surface?.Dispose();
                _frameTimer?.Stop();
                _isDisposed = true;

                Debug.WriteLine("GpuAcceleratedRenderer disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"GPU renderer dispose error: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// GPU渲染统计信息
    /// </summary>
    public class GpuRenderStats
    {
        public bool IsGpuEnabled { get; set; }
        public long FrameCount { get; set; }
        public double AverageFrameRate { get; set; }
        public double CacheHitRate { get; set; }
        public int PictureCacheSize { get; set; }
        public int ShaderCacheSize { get; set; }
        public int PaintCacheSize { get; set; }
        public double TotalRenderTime { get; set; }
    }
}
