using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;
using McLaser.EditViewerSk.Marker;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Numerics;
using System.Windows.Media;
using SkiaSharp;
using Newtonsoft.Json;
using PropertyTools;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 标注类型枚举
    /// </summary>
    public enum DimensionType
    {
        Linear,       // 线性标注
        Aligned,      // 对齐标注
        Angular,      // 角度标注
        Radius,       // 半径标注
        Diameter,     // 直径标注
        Leader        // 引线标注
    }

    /// <summary>
    /// 标注样式配置
    /// </summary>
    public class DimensionStyle : ICloneable
    {
        public string Name { get; set; } = "Standard";
        public double TextHeight { get; set; } = 2.5;
        public double ArrowSize { get; set; } = 2.5;
        public double ExtensionLineOffset { get; set; } = 0.625;
        public double ExtensionLineExtend { get; set; } = 1.25;
        public double DimensionLineSpacing { get; set; } = 3.75;
        public Color TextColor { get; set; } = Colors.Black;
        public Color LineColor { get; set; } = Colors.Black;
        public string TextFont { get; set; } = "Arial";
        public int DecimalPlaces { get; set; } = 2;
        public string Prefix { get; set; } = "";
        public string Suffix { get; set; } = "";
        public bool ShowExtensionLines { get; set; } = true;
        public bool ShowArrows { get; set; } = true;

        public object Clone()
        {
            return MemberwiseClone();
        }
    }

    /// <summary>
    /// 标注实体基类
    /// </summary>
    [Browsable(true)]
    [DisplayName("标注")]
    public abstract class EntityDimension : EntityBase
    {
        protected DimensionType dimensionType;
        protected DimensionStyle dimensionStyle;
        protected string measurementText = "";
        protected double actualMeasurement = 0.0;
        protected bool isAssociative = true;
        protected List<EntityBase> associatedEntities = new List<EntityBase>();
        
        #region 构造函数
        
        protected EntityDimension(DimensionType type) : base()
        {
            this.dimensionType = type;
            this.dimensionStyle = new DimensionStyle();
            this.Layer = new EntityLayer() { Name = "Dimensions", Color = Colors.Red };
        }

        #endregion

        #region 属性

        [Category("标注")]
        [DisplayName("标注类型")]
        [ReadOnly(true)]
        public DimensionType DimensionType
        {
            get => dimensionType;
        }

        [Category("标注")]
        [DisplayName("标注样式")]
        public DimensionStyle Style
        {
            get => dimensionStyle;
            set
            {
                if (dimensionStyle != value)
                {
                    dimensionStyle = value ?? new DimensionStyle();
                    OnPropertyChanged(nameof(Style));
                    InvalidateVisual();
                }
            }
        }

        [Category("标注")]
        [DisplayName("测量文本")]
        public string MeasurementText
        {
            get => measurementText;
            set
            {
                if (measurementText != value)
                {
                    measurementText = value ?? "";
                    OnPropertyChanged(nameof(MeasurementText));
                    InvalidateVisual();
                }
            }
        }

        [Category("标注")]
        [DisplayName("实际测量值")]
        [ReadOnly(true)]
        public double ActualMeasurement
        {
            get => actualMeasurement;
            protected set
            {
                if (Math.Abs(actualMeasurement - value) > 1e-10)
                {
                    actualMeasurement = value;
                    OnPropertyChanged(nameof(ActualMeasurement));
                    UpdateMeasurementText();
                }
            }
        }

        [Category("标注")]
        [DisplayName("关联更新")]
        public bool IsAssociative
        {
            get => isAssociative;
            set
            {
                if (isAssociative != value)
                {
                    isAssociative = value;
                    OnPropertyChanged(nameof(IsAssociative));
                }
            }
        }

        [JsonIgnore]
        [Browsable(false)]
        public List<EntityBase> AssociatedEntities => associatedEntities;

        #endregion

        #region 抽象方法

        /// <summary>
        /// 计算实际测量值
        /// </summary>
        protected abstract void CalculateMeasurement();

        /// <summary>
        /// 获取标注的关键点用于渲染
        /// </summary>
        /// <returns></returns>
        public abstract DimensionGeometry GetDimensionGeometry();

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加关联实体
        /// </summary>
        public void AddAssociatedEntity(EntityBase entity)
        {
            if (entity != null && !associatedEntities.Contains(entity))
            {
                associatedEntities.Add(entity);
                if (isAssociative)
                {
                    entity.PropertyChanged += OnAssociatedEntityChanged;
                }
            }
        }

        /// <summary>
        /// 移除关联实体
        /// </summary>
        public void RemoveAssociatedEntity(EntityBase entity)
        {
            if (entity != null && associatedEntities.Contains(entity))
            {
                associatedEntities.Remove(entity);
                entity.PropertyChanged -= OnAssociatedEntityChanged;
            }
        }

        /// <summary>
        /// 更新标注
        /// </summary>
        public virtual void Update()
        {
            CalculateMeasurement();
            UpdateBoundingBox();
            InvalidateVisual();
        }

        #endregion

        #region 私有方法

        private void OnAssociatedEntityChanged(object sender, PropertyChangedEventArgs e)
        {
            if (isAssociative)
            {
                Update();
            }
        }

        private void UpdateMeasurementText()
        {
            if (string.IsNullOrEmpty(measurementText))
            {
                measurementText = $"{dimensionStyle.Prefix}{actualMeasurement.ToString($"F{dimensionStyle.DecimalPlaces}")}{dimensionStyle.Suffix}";
                OnPropertyChanged(nameof(MeasurementText));
            }
        }

        #endregion

        #region EntityBase 实现

        public override void UpdateBoundingBox()
        {
            var geometry = GetDimensionGeometry();
            if (geometry != null)
            {
                // 计算所有标注元素的边界框
                var bounds = CalculateDimensionBounds(geometry);
                BoundingBox = bounds;
            }
        }

        protected virtual BoundingBox CalculateDimensionBounds(DimensionGeometry geometry)
        {
            var minX = double.MaxValue;
            var minY = double.MaxValue;
            var maxX = double.MinValue;
            var maxY = double.MinValue;

            // 检查所有几何元素的边界
            foreach (var point in geometry.GetAllPoints())
            {
                minX = Math.Min(minX, point.X);
                minY = Math.Min(minY, point.Y);
                maxX = Math.Max(maxX, point.X);
                maxY = Math.Max(maxY, point.Y);
            }

            // 考虑文本大小
            var textBounds = EstimateTextBounds(geometry.TextPosition, measurementText);
            minX = Math.Min(minX, textBounds.MinX);
            minY = Math.Min(minY, textBounds.MinY);
            maxX = Math.Max(maxX, textBounds.MaxX);
            maxY = Math.Max(maxY, textBounds.MaxY);

            return new BoundingBox(minX, minY, maxX, maxY);
        }

        private BoundingBox EstimateTextBounds(Vector2 position, string text)
        {
            // 简单的文本边界估算
            var textWidth = text.Length * dimensionStyle.TextHeight * 0.6;
            var textHeight = dimensionStyle.TextHeight;
            
            return new BoundingBox(
                position.X - textWidth / 2,
                position.Y - textHeight / 2,
                position.X + textWidth / 2,
                position.Y + textHeight / 2
            );
        }

        public override bool IsPointInside(Vector2 point, double tolerance = 1.0)
        {
            return BoundingBox?.Contains(point.X, point.Y, tolerance) == true;
        }

        public override void Render(IGraphicsRenderer renderer, CoordinateSpace coordinateSpace)
        {
            if (renderer == null || !IsVisible) return;

            var geometry = GetDimensionGeometry();
            if (geometry == null) return;

            var paint = CreatePaint();
            
            // 渲染标注几何图形
            RenderDimensionGeometry(renderer, geometry, paint, coordinateSpace);
        }

        protected virtual void RenderDimensionGeometry(IGraphicsRenderer renderer, DimensionGeometry geometry, SKPaint paint, CoordinateSpace coordinateSpace)
        {
            // 绘制尺寸线
            if (geometry.DimensionLine != null)
            {
                renderer.DrawLine(geometry.DimensionLine.Start, geometry.DimensionLine.End, paint, coordinateSpace);
            }

            // 绘制延伸线
            if (dimensionStyle.ShowExtensionLines && geometry.ExtensionLines != null)
            {
                foreach (var extLine in geometry.ExtensionLines)
                {
                    renderer.DrawLine(extLine.Start, extLine.End, paint, coordinateSpace);
                }
            }

            // 绘制箭头
            if (dimensionStyle.ShowArrows && geometry.Arrows != null)
            {
                foreach (var arrow in geometry.Arrows)
                {
                    DrawArrow(renderer, arrow, paint, coordinateSpace);
                }
            }

            // 绘制文本
            var displayText = GetDisplayText();
            if (!string.IsNullOrEmpty(displayText))
            {
                var textPaint = CreateTextPaint();
                renderer.DrawText(displayText, geometry.TextPosition, textPaint, coordinateSpace);
            }
        }

        private void DrawArrow(IGraphicsRenderer renderer, ArrowGeometry arrow, SKPaint paint, CoordinateSpace coordinateSpace)
        {
            // 使用渲染器的箭头绘制方法
            var direction = Vector2.Normalize(arrow.LeftWing - arrow.Point);
            renderer.DrawArrow(arrow.Point, direction, dimensionStyle.ArrowSize, paint, coordinateSpace);
        }

        private SKPaint CreatePaint()
        {
            return Graphics.GraphicsExtensions.CreateLinePaint(dimensionStyle.LineColor, (float)LineWeight.Weight);
        }

        private SKPaint CreateTextPaint()
        {
            return Graphics.GraphicsExtensions.CreateTextPaint(dimensionStyle.TextColor, (float)dimensionStyle.TextHeight, dimensionStyle.TextFont);
        }

        public override EntityBase Clone()
        {
            var clone = (EntityDimension)MemberwiseClone();
            clone.dimensionStyle = (DimensionStyle)dimensionStyle.Clone();
            clone.associatedEntities = new List<EntityBase>();
            return clone;
        }

        protected virtual string GetDisplayText()
        {
            return $"{dimensionStyle.Prefix}{actualMeasurement.ToString($"F{dimensionStyle.DecimalPlaces}")}{dimensionStyle.Suffix}";
        }

        protected void CopyPropertiesTo(EntityDimension target)
        {
            if (target == null) return;
            
            target.dimensionStyle = (DimensionStyle)dimensionStyle.Clone();
            target.measurementText = measurementText;
            target.actualMeasurement = actualMeasurement;
            target.isAssociative = isAssociative;
            target.Layer = Layer;
            target.IsVisible = IsVisible;
            target.LineWeight = LineWeight;
        }

        #endregion
    }

    /// <summary>
    /// 标注几何信息
    /// </summary>
    public class DimensionGeometry
    {
        public LineGeometry DimensionLine { get; set; }
        public List<LineGeometry> ExtensionLines { get; set; } = new List<LineGeometry>();
        public List<ArrowGeometry> Arrows { get; set; } = new List<ArrowGeometry>();
        public Vector2 TextPosition { get; set; }

        public IEnumerable<Vector2> GetAllPoints()
        {
            if (DimensionLine != null)
            {
                yield return DimensionLine.Start;
                yield return DimensionLine.End;
            }

            foreach (var extLine in ExtensionLines)
            {
                yield return extLine.Start;
                yield return extLine.End;
            }

            foreach (var arrow in Arrows)
            {
                yield return arrow.Point;
                yield return arrow.LeftWing;
                yield return arrow.RightWing;
            }

            yield return TextPosition;
        }
    }

    public class LineGeometry
    {
        public Vector2 Start { get; set; }
        public Vector2 End { get; set; }

        public LineGeometry(Vector2 start, Vector2 end)
        {
            Start = start;
            End = end;
        }
    }

    public class ArrowGeometry
    {
        public Vector2 Point { get; set; }
        public Vector2 LeftWing { get; set; }
        public Vector2 RightWing { get; set; }

        public ArrowGeometry(Vector2 point, Vector2 direction, double size)
        {
            Point = point;
            var perpendicular = new Vector2(-direction.Y, direction.X);
            LeftWing = point + direction * size * 0.3f + perpendicular * size * 0.15f;
            RightWing = point + direction * size * 0.3f - perpendicular * size * 0.15f;
        }
    }
} 