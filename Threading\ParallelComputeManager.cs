using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Threading
{
    /// <summary>
    /// 并行计算管理器
    /// 提供线程安全的并行计算功能，优化CPU密集型操作
    /// </summary>
    public class ParallelComputeManager : IDisposable
    {
        #region 私有字段

        private static ParallelComputeManager _instance;
        private static readonly object _lockObject = new object();

        private readonly ParallelOptions _parallelOptions;
        private readonly SemaphoreSlim _computeSemaphore;
        private readonly CancellationTokenSource _cancellationTokenSource;
        
        // 性能统计
        private long _parallelTaskCount;
        private long _sequentialTaskCount;
        private TimeSpan _totalParallelTime;
        private TimeSpan _totalSequentialTime;
        
        // 配置参数
        private readonly int _minItemsForParallel = 100;
        private readonly int _maxDegreeOfParallelism;
        private bool _isDisposed;

        #endregion

        #region 单例模式

        public static ParallelComputeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new ParallelComputeManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private ParallelComputeManager()
        {
            _maxDegreeOfParallelism = Math.Max(1, Environment.ProcessorCount - 1); // 保留一个核心给UI线程
            _parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = _maxDegreeOfParallelism
            };
            
            _computeSemaphore = new SemaphoreSlim(_maxDegreeOfParallelism, _maxDegreeOfParallelism);
            _cancellationTokenSource = new CancellationTokenSource();
            _parallelOptions.CancellationToken = _cancellationTokenSource.Token;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 最大并行度
        /// </summary>
        public int MaxDegreeOfParallelism => _maxDegreeOfParallelism;

        /// <summary>
        /// 并行任务数量
        /// </summary>
        public long ParallelTaskCount => _parallelTaskCount;

        /// <summary>
        /// 串行任务数量
        /// </summary>
        public long SequentialTaskCount => _sequentialTaskCount;

        /// <summary>
        /// 平均并行加速比
        /// </summary>
        public double AverageSpeedup
        {
            get
            {
                if (_totalParallelTime.TotalMilliseconds == 0) return 1.0;
                var avgParallel = _totalParallelTime.TotalMilliseconds / Math.Max(1, _parallelTaskCount);
                var avgSequential = _totalSequentialTime.TotalMilliseconds / Math.Max(1, _sequentialTaskCount);
                return avgSequential / avgParallel;
            }
        }

        #endregion

        #region 几何计算并行化

        /// <summary>
        /// 并行计算多个点的变换
        /// </summary>
        /// <param name="points">点集合</param>
        /// <param name="transform">变换矩阵</param>
        /// <returns>变换后的点集合</returns>
        public List<Vector2> ParallelTransformPoints(IEnumerable<Vector2> points, Matrix3x2 transform)
        {
            var pointList = points.ToList();
            if (pointList.Count < _minItemsForParallel)
            {
                return TransformPointsSequential(pointList, transform);
            }

            var startTime = DateTime.Now;
            var result = new Vector2[pointList.Count];

            try
            {
                Parallel.For(0, pointList.Count, _parallelOptions, i =>
                {
                    result[i] = Vector2.Transform(pointList[i], transform);
                });

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return result.ToList();
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel transform points cancelled");
                return TransformPointsSequential(pointList, transform);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel transform points error: {ex.Message}");
                return TransformPointsSequential(pointList, transform);
            }
        }

        /// <summary>
        /// 并行计算多个实体的包围盒
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>包围盒集合</returns>
        public List<McLaser.EditViewerSk.Entitys.BoundingBox> ParallelCalculateBoundingBoxes(IEnumerable<EntityBase> entities)
        {
            var entityList = entities.ToList();
            if (entityList.Count < _minItemsForParallel)
            {
                return CalculateBoundingBoxesSequential(entityList);
            }

            var startTime = DateTime.Now;
            var result = new ConcurrentBag<McLaser.EditViewerSk.Entitys.BoundingBox>();

            try
            {
                Parallel.ForEach(entityList, _parallelOptions, entity =>
                {
                    try
                    {
                        entity.UpdateBoundingBox();
                        if (entity.BBox != null)
                        {
                            result.Add(entity.BBox);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Entity bounding box calculation error: {ex.Message}");
                    }
                });

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return result.ToList();
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel bounding box calculation cancelled");
                return CalculateBoundingBoxesSequential(entityList);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel bounding box calculation error: {ex.Message}");
                return CalculateBoundingBoxesSequential(entityList);
            }
        }

        /// <summary>
        /// 并行计算多个点到直线的距离
        /// </summary>
        /// <param name="points">点集合</param>
        /// <param name="lineStart">直线起点</param>
        /// <param name="lineEnd">直线终点</param>
        /// <returns>距离集合</returns>
        public List<double> ParallelCalculatePointToLineDistances(IEnumerable<Vector2> points, Vector2 lineStart, Vector2 lineEnd)
        {
            var pointList = points.ToList();
            if (pointList.Count < _minItemsForParallel)
            {
                return CalculatePointToLineDistancesSequential(pointList, lineStart, lineEnd);
            }

            var startTime = DateTime.Now;
            var result = new double[pointList.Count];

            try
            {
                Parallel.For(0, pointList.Count, _parallelOptions, i =>
                {
                    result[i] = GeometryCalculator.CalculatePointToLineDistance(pointList[i], lineStart, lineEnd);
                });

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return result.ToList();
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel distance calculation cancelled");
                return CalculatePointToLineDistancesSequential(pointList, lineStart, lineEnd);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel distance calculation error: {ex.Message}");
                return CalculatePointToLineDistancesSequential(pointList, lineStart, lineEnd);
            }
        }

        /// <summary>
        /// 并行查找最近点
        /// </summary>
        /// <param name="targetPoint">目标点</param>
        /// <param name="candidatePoints">候选点集合</param>
        /// <returns>最近点及其距离</returns>
        public (Vector2 point, double distance) ParallelFindNearestPoint(Vector2 targetPoint, IEnumerable<Vector2> candidatePoints)
        {
            var pointList = candidatePoints.ToList();
            if (pointList.Count < _minItemsForParallel)
            {
                return FindNearestPointSequential(targetPoint, pointList);
            }

            var startTime = DateTime.Now;

            try
            {
                var result = pointList.AsParallel()
                    .WithCancellation(_parallelOptions.CancellationToken)
                    .WithDegreeOfParallelism(_maxDegreeOfParallelism)
                    .Select(p => new { Point = p, Distance = Vector2.Distance(targetPoint, p) })
                    .OrderBy(x => x.Distance)
                    .First();

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return (result.Point, result.Distance);
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel nearest point search cancelled");
                return FindNearestPointSequential(targetPoint, pointList);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel nearest point search error: {ex.Message}");
                return FindNearestPointSequential(targetPoint, pointList);
            }
        }

        #endregion

        #region 实体处理并行化

        /// <summary>
        /// 并行克隆实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="transforms">变换矩阵集合</param>
        /// <returns>克隆的实体集合</returns>
        public List<EntityBase> ParallelCloneEntities(IEnumerable<EntityBase> entities, IEnumerable<Matrix3x2> transforms)
        {
            var entityList = entities.ToList();
            var transformList = transforms.ToList();
            
            var totalOperations = entityList.Count * transformList.Count;
            if (totalOperations < _minItemsForParallel)
            {
                return CloneEntitiesSequential(entityList, transformList);
            }

            var startTime = DateTime.Now;
            var result = new ConcurrentBag<EntityBase>();

            try
            {
                Parallel.ForEach(transformList, _parallelOptions, transform =>
                {
                    foreach (var entity in entityList)
                    {
                        try
                        {
                            var clonedEntity = entity.Clone() as EntityBase;
                            if (clonedEntity != null)
                            {
                                clonedEntity.Transform(transform);
                                result.Add(clonedEntity);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Entity clone error: {ex.Message}");
                        }
                    }
                });

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return result.ToList();
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel entity cloning cancelled");
                return CloneEntitiesSequential(entityList, transformList);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel entity cloning error: {ex.Message}");
                return CloneEntitiesSequential(entityList, transformList);
            }
        }

        /// <summary>
        /// 并行过滤实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="predicate">过滤条件</param>
        /// <returns>过滤后的实体集合</returns>
        public List<EntityBase> ParallelFilterEntities(IEnumerable<EntityBase> entities, Func<EntityBase, bool> predicate)
        {
            var entityList = entities.ToList();
            if (entityList.Count < _minItemsForParallel)
            {
                return entityList.Where(predicate).ToList();
            }

            var startTime = DateTime.Now;

            try
            {
                var result = entityList.AsParallel()
                    .WithCancellation(_parallelOptions.CancellationToken)
                    .WithDegreeOfParallelism(_maxDegreeOfParallelism)
                    .Where(predicate)
                    .ToList();

                Interlocked.Increment(ref _parallelTaskCount);
                _totalParallelTime += DateTime.Now - startTime;

                return result;
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("Parallel entity filtering cancelled");
                return entityList.Where(predicate).ToList();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Parallel entity filtering error: {ex.Message}");
                return entityList.Where(predicate).ToList();
            }
        }

        #endregion

        #region 异步任务管理

        /// <summary>
        /// 异步执行计算密集型任务
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="computation">计算函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task<T> ExecuteComputeTaskAsync<T>(Func<T> computation, CancellationToken cancellationToken = default)
        {
            await _computeSemaphore.WaitAsync(cancellationToken);

            try
            {
                return await Task.Run(computation, cancellationToken);
            }
            finally
            {
                _computeSemaphore.Release();
            }
        }

        /// <summary>
        /// 异步批量处理实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="processor">处理函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task ProcessEntitiesAsync(IEnumerable<EntityBase> entities, Action<EntityBase> processor, CancellationToken cancellationToken = default)
        {
            var entityList = entities.ToList();

            if (entityList.Count < _minItemsForParallel)
            {
                await Task.Run(() =>
                {
                    foreach (var entity in entityList)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        processor(entity);
                    }
                }, cancellationToken);
            }
            else
            {
                await Task.Run(() =>
                {
                    Parallel.ForEach(entityList, new ParallelOptions
                    {
                        CancellationToken = cancellationToken,
                        MaxDegreeOfParallelism = _maxDegreeOfParallelism
                    }, processor);
                }, cancellationToken);
            }
        }

        #endregion

        #region 串行方法（回退实现）

        /// <summary>
        /// 串行变换点集合
        /// </summary>
        private List<Vector2> TransformPointsSequential(List<Vector2> points, Matrix3x2 transform)
        {
            var startTime = DateTime.Now;
            var result = new List<Vector2>(points.Count);

            foreach (var point in points)
            {
                result.Add(Vector2.Transform(point, transform));
            }

            Interlocked.Increment(ref _sequentialTaskCount);
            _totalSequentialTime += DateTime.Now - startTime;

            return result;
        }

        /// <summary>
        /// 串行计算包围盒
        /// </summary>
        private List<McLaser.EditViewerSk.Entitys.BoundingBox> CalculateBoundingBoxesSequential(List<EntityBase> entities)
        {
            var startTime = DateTime.Now;
            var result = new List<McLaser.EditViewerSk.Entitys.BoundingBox>();

            foreach (var entity in entities)
            {
                try
                {
                    entity.UpdateBoundingBox();
                    if (entity.BBox != null)
                    {
                        result.Add(entity.BBox);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Entity bounding box calculation error: {ex.Message}");
                }
            }

            Interlocked.Increment(ref _sequentialTaskCount);
            _totalSequentialTime += DateTime.Now - startTime;

            return result;
        }

        /// <summary>
        /// 串行计算点到直线距离
        /// </summary>
        private List<double> CalculatePointToLineDistancesSequential(List<Vector2> points, Vector2 lineStart, Vector2 lineEnd)
        {
            var startTime = DateTime.Now;
            var result = new List<double>(points.Count);

            foreach (var point in points)
            {
                result.Add(GeometryCalculator.CalculatePointToLineDistance(point, lineStart, lineEnd));
            }

            Interlocked.Increment(ref _sequentialTaskCount);
            _totalSequentialTime += DateTime.Now - startTime;

            return result;
        }

        /// <summary>
        /// 串行查找最近点
        /// </summary>
        private (Vector2 point, double distance) FindNearestPointSequential(Vector2 targetPoint, List<Vector2> candidatePoints)
        {
            var startTime = DateTime.Now;

            var nearestPoint = candidatePoints[0];
            var nearestDistance = Vector2.Distance(targetPoint, nearestPoint);

            for (int i = 1; i < candidatePoints.Count; i++)
            {
                var distance = Vector2.Distance(targetPoint, candidatePoints[i]);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPoint = candidatePoints[i];
                }
            }

            Interlocked.Increment(ref _sequentialTaskCount);
            _totalSequentialTime += DateTime.Now - startTime;

            return (nearestPoint, nearestDistance);
        }

        /// <summary>
        /// 串行克隆实体
        /// </summary>
        private List<EntityBase> CloneEntitiesSequential(List<EntityBase> entities, List<Matrix3x2> transforms)
        {
            var startTime = DateTime.Now;
            var result = new List<EntityBase>();

            foreach (var transform in transforms)
            {
                foreach (var entity in entities)
                {
                    try
                    {
                        var clonedEntity = entity.Clone() as EntityBase;
                        if (clonedEntity != null)
                        {
                            clonedEntity.Transform(transform);
                            result.Add(clonedEntity);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Entity clone error: {ex.Message}");
                    }
                }
            }

            Interlocked.Increment(ref _sequentialTaskCount);
            _totalSequentialTime += DateTime.Now - startTime;

            return result;
        }

        #endregion

        #region 性能监控和配置

        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <returns>性能统计</returns>
        public ParallelComputeStats GetStats()
        {
            return new ParallelComputeStats
            {
                MaxDegreeOfParallelism = _maxDegreeOfParallelism,
                ParallelTaskCount = _parallelTaskCount,
                SequentialTaskCount = _sequentialTaskCount,
                AverageSpeedup = AverageSpeedup,
                TotalParallelTime = _totalParallelTime.TotalMilliseconds,
                TotalSequentialTime = _totalSequentialTime.TotalMilliseconds,
                MinItemsForParallel = _minItemsForParallel
            };
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetStats()
        {
            Interlocked.Exchange(ref _parallelTaskCount, 0);
            Interlocked.Exchange(ref _sequentialTaskCount, 0);
            _totalParallelTime = TimeSpan.Zero;
            _totalSequentialTime = TimeSpan.Zero;
        }

        /// <summary>
        /// 取消所有正在进行的并行任务
        /// </summary>
        public void CancelAllTasks()
        {
            try
            {
                _cancellationTokenSource.Cancel();
                Debug.WriteLine("All parallel tasks cancelled");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error cancelling parallel tasks: {ex.Message}");
            }
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                CancelAllTasks();
                _computeSemaphore?.Dispose();
                _cancellationTokenSource?.Dispose();
                _isDisposed = true;

                Debug.WriteLine("ParallelComputeManager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParallelComputeManager dispose error: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 并行计算统计信息
    /// </summary>
    public class ParallelComputeStats
    {
        public int MaxDegreeOfParallelism { get; set; }
        public long ParallelTaskCount { get; set; }
        public long SequentialTaskCount { get; set; }
        public double AverageSpeedup { get; set; }
        public double TotalParallelTime { get; set; }
        public double TotalSequentialTime { get; set; }
        public int MinItemsForParallel { get; set; }
        public double ParallelEfficiency => ParallelTaskCount > 0 ? AverageSpeedup / MaxDegreeOfParallelism : 0;
    }
}
