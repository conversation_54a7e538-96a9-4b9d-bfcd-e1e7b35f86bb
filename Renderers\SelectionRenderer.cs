using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Enums;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace McLaser.EditViewerSk.Renderers
{
    /// <summary>
    /// 选择框渲染器
    /// 负责渲染各种选择操作的视觉反馈
    /// </summary>
    public class SelectionRenderer
    {
        private readonly ICoordinateService _coordinateService;

        public SelectionRenderer(ICoordinateService coordinateService)
        {
            _coordinateService = coordinateService ?? throw new ArgumentNullException(nameof(coordinateService));
        }

        /// <summary>
        /// 渲染选择矩形
        /// </summary>
        /// <param name="view">视图对象</param>
        /// <param name="startPoint">起始点（Model坐标）</param>
        /// <param name="endPoint">结束点（Model坐标）</param>
        /// <param name="selectionMode">选择模式</param>
        public void RenderSelectionRectangle(ViewBase view, Vector2 startPoint, Vector2 endPoint, EntitySelectionMode selectionMode)
        {
            if (view?.Canvas == null) return;

            var paint = CreateSelectionPaint(selectionMode);
            
            try
            {
                double width = endPoint.X - startPoint.X;
                double height = endPoint.Y - startPoint.Y;

                // 尝试使用Canvas坐标系绘制，与原有OnPaint方法保持一致
                // 首先将Model坐标转换为Canvas坐标
                var startCanvas = view.ModelToCanvas(startPoint);
                var endCanvas = view.ModelToCanvas(endPoint);
                var canvasWidth = endCanvas.X - startCanvas.X;
                var canvasHeight = endCanvas.Y - startCanvas.Y;
                
                view.DrawRectangle(startCanvas, canvasWidth, canvasHeight, paint, CSYS.Canvas);
            }
            finally
            {
                paint?.Dispose();
            }
        }

        /// <summary>
        /// 创建选择画笔
        /// </summary>
        /// <param name="selectionMode">选择模式</param>
        /// <returns>画笔对象</returns>
        private SKPaint CreateSelectionPaint(EntitySelectionMode selectionMode)
        {
            var paint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                IsAntialias = true,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0)
            };

            // 根据选择模式设置不同颜色
            switch (selectionMode)
            {
                case EntitySelectionMode.Window:
                    paint.Color = SKColors.Blue;
                    break;
                case EntitySelectionMode.Cross:
                    paint.Color = SKColors.Green;
                    break;
                case EntitySelectionMode.Point:
                    paint.Color = SKColors.Orange;
                    break;
                default:
                    paint.Color = SKColors.Gray;
                    break;
            }

            return paint;
        }

        /// <summary>
        /// 渲染选择反馈（高亮显示选中的对象）
        /// </summary>
        /// <param name="view">视图对象</param>
        /// <param name="selectedEntities">选中的实体列表</param>
        public void RenderSelectionFeedback(ViewBase view, IEnumerable<EntityBase> selectedEntities)
        {
            if (view?.Canvas == null || selectedEntities == null) return;

            var highlightPaint = new SKPaint
            {
                Color = SKColors.Red,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                IsAntialias = true
            };

            try
            {
                foreach (var entity in selectedEntities)
                {
                    if (entity.IsSelected)
                    {
                        // 这里可以添加选中对象的高亮渲染逻辑
                        // 例如绘制边框、改变颜色等
                        RenderEntityHighlight(view, entity, highlightPaint);
                    }
                }
            }
            finally
            {
                highlightPaint?.Dispose();
            }
        }

        /// <summary>
        /// 渲染实体高亮效果
        /// </summary>
        /// <param name="view">视图对象</param>
        /// <param name="entity">实体对象</param>
        /// <param name="paint">画笔</param>
        private void RenderEntityHighlight(ViewBase view, EntityBase entity, SKPaint paint)
        {
            // 获取实体的边界框
            var boundingBox = entity.BoundingBox;
            if (!boundingBox.IsEmpty)
            {
                // 绘制边界框高亮
                view.DrawRectangle(
                    new Vector2((float)boundingBox.Left, (float)boundingBox.Top),
                    boundingBox.Width,
                    boundingBox.Height,
                    paint,
                    CSYS.Model);
            }
        }
    }
} 