using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Managers;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Testing
{
    /// <summary>
    /// 阶段2集成测试
    /// 验证高级交互系统的完整功能
    /// </summary>
    public class Phase2IntegrationTests
    {
        private ViewBase _testView;
        private Phase2IntegrationManager _phase2Manager;
        private DocumentBase _testDocument;
        
        public Phase2IntegrationTests()
        {
            InitializeTestEnvironment();
        }
        
        private void InitializeTestEnvironment()
        {
            // 创建测试文档
            _testDocument = new DocumentBase();
            _testDocument.AddLayer(new EntityLayer { Name = "TestLayer" });
            
            // 创建测试实体
            CreateTestEntities();
        }
        
        private void CreateTestEntities()
        {
            var layer = _testDocument.ActiveLayer;
            
            // 添加测试直线
            var line1 = new EntityLine
            {
                StartPoint = new Vector2(0, 0),
                EndPoint = new Vector2(100, 0)
            };
            layer.Add(line1);
            
            var line2 = new EntityLine
            {
                StartPoint = new Vector2(50, -50),
                EndPoint = new Vector2(50, 50)
            };
            layer.Add(line2);
            
            // 添加测试圆形
            var circle = new EntityCircle
            {
                Center = new Vector2(200, 0),
                Radius = 50
            };
            layer.Add(circle);
            
            // 添加测试矩形
            var rectangle = new EntityRectangle
            {
                LeftTop = new Vector2(300, 50),
                RightBottom = new Vector2(400, -50)
            };
            layer.Add(rectangle);
        }
        
        /// <summary>
        /// 测试对象捕捉功能
        /// </summary>
        public TestResult TestObjectSnapping()
        {
            var result = new TestResult("Object Snapping Test");
            
            try
            {
                if (_phase2Manager?.SnapManager == null)
                {
                    result.AddError("Snap manager not initialized");
                    return result;
                }
                
                var snapManager = _phase2Manager.SnapManager;
                
                // 测试端点捕捉
                snapManager.SetTemporarySnapMode(ObjectSnapMode.End);
                var snapResult = snapManager.Snap(new Vector2(5, 5)); // 接近直线端点
                
                if (Vector2.Distance(snapResult, new Vector2(0, 0)) < 1.0f)
                {
                    result.AddSuccess("Endpoint snap working correctly");
                }
                else
                {
                    result.AddError($"Endpoint snap failed. Expected (0,0), got ({snapResult.X}, {snapResult.Y})");
                }
                
                // 测试中点捕捉
                snapManager.SetTemporarySnapMode(ObjectSnapMode.Mid);
                snapResult = snapManager.Snap(new Vector2(50, 5));
                
                if (Vector2.Distance(snapResult, new Vector2(50, 0)) < 1.0f)
                {
                    result.AddSuccess("Midpoint snap working correctly");
                }
                else
                {
                    result.AddError($"Midpoint snap failed. Expected (50,0), got ({snapResult.X}, {snapResult.Y})");
                }
                
                // 测试中心点捕捉
                snapManager.SetTemporarySnapMode(ObjectSnapMode.Center);
                snapResult = snapManager.Snap(new Vector2(205, 5));
                
                if (Vector2.Distance(snapResult, new Vector2(200, 0)) < 1.0f)
                {
                    result.AddSuccess("Center snap working correctly");
                }
                else
                {
                    result.AddError($"Center snap failed. Expected (200,0), got ({snapResult.X}, {snapResult.Y})");
                }
                
                // 测试交点捕捉
                snapManager.SetTemporarySnapMode(ObjectSnapMode.Intersection);
                snapResult = snapManager.Snap(new Vector2(55, 5));
                
                if (Vector2.Distance(snapResult, new Vector2(50, 0)) < 1.0f)
                {
                    result.AddSuccess("Intersection snap working correctly");
                }
                else
                {
                    result.AddError($"Intersection snap failed. Expected (50,0), got ({snapResult.X}, {snapResult.Y})");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during object snapping test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试极轴追踪功能
        /// </summary>
        public TestResult TestPolarTracking()
        {
            var result = new TestResult("Polar Tracking Test");
            
            try
            {
                if (_phase2Manager?.PolarTracking == null)
                {
                    result.AddError("Polar tracking not initialized");
                    return result;
                }
                
                var polarTracking = _phase2Manager.PolarTracking;
                polarTracking.IsEnabled = true;
                polarTracking.SetBasePoint(new Vector2(0, 0));
                
                // 测试0度追踪
                var trackingResult = polarTracking.UpdateTracking(new Vector2(98, 2));
                if (trackingResult.HasValue && Math.Abs(trackingResult.Value.Y) < 1.0f)
                {
                    result.AddSuccess("0-degree polar tracking working correctly");
                }
                else
                {
                    result.AddError("0-degree polar tracking failed");
                }
                
                // 测试90度追踪
                trackingResult = polarTracking.UpdateTracking(new Vector2(2, 98));
                if (trackingResult.HasValue && Math.Abs(trackingResult.Value.X) < 1.0f)
                {
                    result.AddSuccess("90-degree polar tracking working correctly");
                }
                else
                {
                    result.AddError("90-degree polar tracking failed");
                }
                
                // 测试45度追踪
                trackingResult = polarTracking.UpdateTracking(new Vector2(70, 72));
                if (trackingResult.HasValue && Math.Abs(trackingResult.Value.X - trackingResult.Value.Y) < 5.0f)
                {
                    result.AddSuccess("45-degree polar tracking working correctly");
                }
                else
                {
                    result.AddError("45-degree polar tracking failed");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during polar tracking test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试对象追踪功能
        /// </summary>
        public TestResult TestObjectTracking()
        {
            var result = new TestResult("Object Tracking Test");
            
            try
            {
                if (_phase2Manager?.ObjectTracking == null)
                {
                    result.AddError("Object tracking not initialized");
                    return result;
                }
                
                var objectTracking = _phase2Manager.ObjectTracking;
                objectTracking.IsEnabled = true;
                
                // 添加追踪点
                objectTracking.AddTrackingPoint(new Vector2(0, 0), ObjectSnapMode.End);
                objectTracking.AddTrackingPoint(new Vector2(100, 0), ObjectSnapMode.End);
                
                // 测试水平对齐
                var trackingResult = objectTracking.UpdateTracking(new Vector2(150, 2));
                if (trackingResult.BestAlignmentPoint.HasValue && 
                    Math.Abs(trackingResult.BestAlignmentPoint.Value.Y) < 1.0f)
                {
                    result.AddSuccess("Horizontal object tracking working correctly");
                }
                else
                {
                    result.AddError("Horizontal object tracking failed");
                }
                
                // 测试垂直对齐
                trackingResult = objectTracking.UpdateTracking(new Vector2(2, 50));
                if (trackingResult.BestAlignmentPoint.HasValue && 
                    Math.Abs(trackingResult.BestAlignmentPoint.Value.X) < 1.0f)
                {
                    result.AddSuccess("Vertical object tracking working correctly");
                }
                else
                {
                    result.AddError("Vertical object tracking failed");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during object tracking test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试动态输入功能
        /// </summary>
        public TestResult TestDynamicInput()
        {
            var result = new TestResult("Dynamic Input Test");
            
            try
            {
                if (_phase2Manager?.DynamicInput == null)
                {
                    result.AddError("Dynamic input not initialized");
                    return result;
                }
                
                var dynamicInput = _phase2Manager.DynamicInput;
                
                // 测试启动动态输入
                dynamicInput.StartInput(new Vector2(0, 0), Input.DynamicInputMode.Coordinate);
                
                if (dynamicInput.IsVisible)
                {
                    result.AddSuccess("Dynamic input starts correctly");
                }
                else
                {
                    result.AddError("Dynamic input failed to start");
                }
                
                // 测试位置更新
                dynamicInput.UpdatePosition(new Vector2(50, 50));
                result.AddSuccess("Dynamic input position update working");
                
                // 测试键盘处理
                var keyArgs = new KeyEventArgs(Keys.Tab);
                if (dynamicInput.HandleKeyInput(keyArgs))
                {
                    result.AddSuccess("Dynamic input keyboard handling working");
                }
                
                // 测试结束输入
                dynamicInput.EndInput();
                if (!dynamicInput.IsVisible)
                {
                    result.AddSuccess("Dynamic input ends correctly");
                }
                else
                {
                    result.AddError("Dynamic input failed to end");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during dynamic input test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试选择系统功能
        /// </summary>
        public TestResult TestSelectionSystem()
        {
            var result = new TestResult("Selection System Test");
            
            try
            {
                if (_phase2Manager?.SelectionSystem == null)
                {
                    result.AddError("Selection system not initialized");
                    return result;
                }
                
                var selectionSystem = _phase2Manager.SelectionSystem;
                
                // 测试点选
                selectionSystem.StartSelection(new Vector2(50, 0), Selection.EntitySelectionMode.Point);
                
                if (selectionSystem.SelectedEntities.Count > 0)
                {
                    result.AddSuccess("Point selection working correctly");
                }
                
                // 测试框选
                selectionSystem.StartSelection(new Vector2(-10, -10), Selection.EntitySelectionMode.Window);
                selectionSystem.UpdateSelection(new Vector2(110, 10));
                selectionSystem.CompleteSelection();
                
                if (selectionSystem.SelectedEntities.Count >= 2)
                {
                    result.AddSuccess("Window selection working correctly");
                }
                else
                {
                    result.AddError("Window selection failed");
                }
                
                // 测试全选
                selectionSystem.SelectAllEntities();
                if (selectionSystem.SelectedEntities.Count >= 4)
                {
                    result.AddSuccess("Select all working correctly");
                }
                else
                {
                    result.AddError("Select all failed");
                }
                
                // 测试清除选择
                selectionSystem.ClearSelection();
                if (selectionSystem.SelectedEntities.Count == 0)
                {
                    result.AddSuccess("Clear selection working correctly");
                }
                else
                {
                    result.AddError("Clear selection failed");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during selection system test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试系统集成
        /// </summary>
        public TestResult TestSystemIntegration()
        {
            var result = new TestResult("System Integration Test");
            
            try
            {
                if (_phase2Manager == null)
                {
                    result.AddError("Phase2 manager not initialized");
                    return result;
                }
                
                // 测试鼠标事件处理
                var mouseHandled = _phase2Manager.HandleMouseDown(new Vector2(0, 0), MouseButtons.Left);
                result.AddSuccess("Mouse event handling integrated");
                
                // 测试键盘事件处理
                var keyArgs = new KeyEventArgs(Keys.F8);
                var keyHandled = _phase2Manager.HandleKeyDown(keyArgs);
                if (keyHandled)
                {
                    result.AddSuccess("Keyboard event handling integrated");
                }
                
                // 测试增强位置获取
                var enhancedPosition = _phase2Manager.GetEnhancedPosition(new Vector2(5, 5));
                result.AddSuccess("Enhanced position calculation working");
                
                // 测试设置集成
                var settings = _phase2Manager.Settings;
                settings.ObjectSnapEnabled = false;
                _phase2Manager.ToggleObjectSnap();
                if (settings.ObjectSnapEnabled)
                {
                    result.AddSuccess("Settings integration working");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Exception during integration test: {ex.Message}");
            }
            
            return result;
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public List<TestResult> RunAllTests()
        {
            var results = new List<TestResult>();
            
            try
            {
                results.Add(TestObjectSnapping());
                results.Add(TestPolarTracking());
                results.Add(TestObjectTracking());
                results.Add(TestDynamicInput());
                results.Add(TestSelectionSystem());
                results.Add(TestSystemIntegration());
            }
            catch (Exception ex)
            {
                var errorResult = new TestResult("Test Execution Error");
                errorResult.AddError($"Failed to execute tests: {ex.Message}");
                results.Add(errorResult);
            }
            
            return results;
        }
        
        /// <summary>
        /// 性能测试
        /// </summary>
        public TestResult RunPerformanceTest()
        {
            var result = new TestResult("Performance Test");
            
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                // 模拟大量捕捉操作
                if (_phase2Manager?.SnapManager != null)
                {
                    for (int i = 0; i < 1000; i++)
                    {
                        _phase2Manager.SnapManager.Snap(new Vector2(i % 100, i % 50));
                    }
                }
                
                stopwatch.Stop();
                
                if (stopwatch.ElapsedMilliseconds < 1000)
                {
                    result.AddSuccess($"Performance test passed: {stopwatch.ElapsedMilliseconds}ms for 1000 operations");
                }
                else
                {
                    result.AddWarning($"Performance test slow: {stopwatch.ElapsedMilliseconds}ms for 1000 operations");
                }
                
            }
            catch (Exception ex)
            {
                result.AddError($"Performance test failed: {ex.Message}");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// 测试结果
    /// </summary>
    public class TestResult
    {
        public string TestName { get; }
        public List<string> Successes { get; }
        public List<string> Errors { get; }
        public List<string> Warnings { get; }
        public bool Passed => Errors.Count == 0;
        
        public TestResult(string testName)
        {
            TestName = testName;
            Successes = new List<string>();
            Errors = new List<string>();
            Warnings = new List<string>();
        }
        
        public void AddSuccess(string message)
        {
            Successes.Add(message);
        }
        
        public void AddError(string message)
        {
            Errors.Add(message);
        }
        
        public void AddWarning(string message)
        {
            Warnings.Add(message);
        }
        
        public override string ToString()
        {
            var status = Passed ? "PASSED" : "FAILED";
            return $"{TestName}: {status} ({Successes.Count} successes, {Errors.Count} errors, {Warnings.Count} warnings)";
        }
    }
} 