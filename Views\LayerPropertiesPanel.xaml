<UserControl x:Class="McLaser.EditViewerSk.Views.LayerPropertiesPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="300">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5,10,5,5"/>
        </Style>
        
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Width" Value="80"/>
        </Style>
        
        <Style x:Key="LayerButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="25"/>
            <Setter Property="Height" Value="25"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
    </UserControl.Resources>
    
    <DockPanel>
        <!-- 工具栏 -->
        <ToolBar DockPanel.Dock="Top">
            <Button Content="新建" ToolTip="新建图层" Click="NewLayer_Click"/>
            <Button Content="删除" ToolTip="删除图层" Click="DeleteLayer_Click"/>
            <Button Content="复制" ToolTip="复制图层" Click="CopyLayer_Click"/>
            <Separator/>
            <Button Content="上移" ToolTip="上移图层" Click="MoveUp_Click"/>
            <Button Content="下移" ToolTip="下移图层" Click="MoveDown_Click"/>
        </ToolBar>
        
        <!-- 图层列表 -->
        <DataGrid Name="LayersDataGrid" DockPanel.Dock="Fill" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  SelectionMode="Single"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionChanged="LayersDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <!-- 状态列 -->
                <DataGridTemplateColumn Header="状态" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource LayerButtonStyle}" 
                                        ToolTip="可见性" 
                                        Click="ToggleVisibility_Click"
                                        Tag="{Binding}">
                                    <Button.Content>
                                        <TextBlock Text="{Binding IsVisible, Converter={StaticResource BoolToVisibilityIconConverter}}" 
                                                   FontSize="12"/>
                                    </Button.Content>
                                </Button>
                                <Button Style="{StaticResource LayerButtonStyle}" 
                                        ToolTip="锁定" 
                                        Click="ToggleLock_Click"
                                        Tag="{Binding}">
                                    <Button.Content>
                                        <TextBlock Text="{Binding IsLocked, Converter={StaticResource BoolToLockIconConverter}}" 
                                                   FontSize="12"/>
                                    </Button.Content>
                                </Button>
                                <Button Style="{StaticResource LayerButtonStyle}" 
                                        ToolTip="可标记" 
                                        Click="ToggleMarkerable_Click"
                                        Tag="{Binding}">
                                    <Button.Content>
                                        <TextBlock Text="{Binding IsMarkerable, Converter={StaticResource BoolToMarkerIconConverter}}" 
                                                   FontSize="12"/>
                                    </Button.Content>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 颜色列 -->
                <DataGridTemplateColumn Header="颜色" Width="40">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Rectangle Width="20" Height="15" 
                                       Fill="{Binding LayerColor, Converter={StaticResource ColorToBrushConverter}}"
                                       Stroke="Black" StrokeThickness="1"
                                       MouseLeftButtonUp="ColorRectangle_Click"
                                       Tag="{Binding}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 名称列 -->
                <DataGridTemplateColumn Header="名称" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Name}" VerticalAlignment="Center" Margin="5,0"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" 
                                     VerticalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                
                <!-- 线型列 -->
                <DataGridTemplateColumn Header="线型" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <ComboBox SelectedItem="{Binding Linetype}" 
                                      ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=AvailableLinetypes}"
                                      DisplayMemberPath="Name"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 线宽列 -->
                <DataGridTemplateColumn Header="线宽" Width="60">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBox Text="{Binding LineWeight, UpdateSourceTrigger=PropertyChanged}" 
                                     VerticalAlignment="Center" HorizontalAlignment="Center" Width="40"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- 属性面板 -->
        <Expander DockPanel.Dock="Bottom" Header="图层属性" IsExpanded="True" MaxHeight="200">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Name="PropertiesPanel" Margin="10">
                    <TextBlock Text="选中图层属性" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="名称:" Grid.Row="0" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="LayerNameTextBox" Grid.Row="0" Grid.Column="1" Height="25" Margin="5,2"/>
                        
                        <TextBlock Text="描述:" Grid.Row="1" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="LayerDescriptionTextBox" Grid.Row="1" Grid.Column="1" Height="25" Margin="5,2"/>
                        
                        <TextBlock Text="颜色:" Grid.Row="2" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <Button Name="LayerColorButton" Grid.Row="2" Grid.Column="1" 
                                Content="选择颜色" Height="25" Margin="5,2" 
                                HorizontalAlignment="Left" Width="100" Click="LayerColorButton_Click"/>
                        
                        <TextBlock Text="线型:" Grid.Row="3" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <ComboBox Name="LayerLinetypeComboBox" Grid.Row="3" Grid.Column="1" 
                                  Height="25" Margin="5,2" DisplayMemberPath="Name"/>
                        
                        <TextBlock Text="线宽:" Grid.Row="4" Grid.Column="0" Style="{StaticResource PropertyLabelStyle}"/>
                        <TextBox Name="LayerLineWeightTextBox" Grid.Row="4" Grid.Column="1" 
                                 Height="25" Margin="5,2"/>
                    </Grid>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10">
                        <Button Content="应用" Width="60" Height="25" Margin="5" Click="ApplyProperties_Click"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Expander>
    </DockPanel>
</UserControl>
