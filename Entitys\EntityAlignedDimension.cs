using System;
using System.ComponentModel;
using System.Numerics;
using PropertyTools;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 对齐标注实体 - 标注线平行于两点连线
    /// </summary>
    [Browsable(true)]
    [DisplayName("对齐标注")]
    public class EntityAlignedDimension : EntityDimension
    {
        private Vector2 firstPoint;
        private Vector2 secondPoint;
        private double offset = 5.0; // 标注线与参考线的偏移距离

        #region 构造函数

        public EntityAlignedDimension() : base(DimensionType.Aligned)
        {
            firstPoint = Vector2.Zero;
            secondPoint = new Vector2(10, 5);
            offset = 5.0;
            Update();
        }

        public EntityAlignedDimension(Vector2 first, Vector2 second, double offsetDistance) : base(DimensionType.Aligned)
        {
            firstPoint = first;
            secondPoint = second;
            offset = offsetDistance;
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("第一点")]
        public Vector2 FirstPoint
        {
            get => firstPoint;
            set
            {
                if (firstPoint != value)
                {
                    firstPoint = value;
                    OnPropertyChanged(nameof(FirstPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("第二点")]
        public Vector2 SecondPoint
        {
            get => secondPoint;
            set
            {
                if (secondPoint != value)
                {
                    secondPoint = value;
                    OnPropertyChanged(nameof(SecondPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("偏移距离")]
        public double Offset
        {
            get => offset;
            set
            {
                if (Math.Abs(offset - value) > 1e-10)
                {
                    offset = value;
                    OnPropertyChanged(nameof(Offset));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("标注长度")]
        [ReadOnly(true)]
        public double Length => Vector2.Distance(firstPoint, secondPoint);

        [Category("几何")]
        [DisplayName("标注角度")]
        [ReadOnly(true)]
        public double Angle => Math.Atan2(secondPoint.Y - firstPoint.Y, secondPoint.X - firstPoint.X) * 180.0 / Math.PI;

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            ActualMeasurement = Vector2.Distance(firstPoint, secondPoint);
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            // 计算对齐方向（平行于两点连线）
            var direction = Vector2.Normalize(secondPoint - firstPoint);
            var perpendicular = new Vector2(-direction.Y, direction.X);

            // 计算标注线位置
            var midPoint = (firstPoint + secondPoint) * 0.5f;
            var dimLineStart = firstPoint + perpendicular * offset;
            var dimLineEnd = secondPoint + perpendicular * offset;

            geometry.DimensionLine = new LineGeometry(dimLineStart, dimLineEnd);

            // 计算延伸线
            if (Style.ShowExtensionLines)
            {
                // 第一点的延伸线
                var firstExtStart = firstPoint + perpendicular * (offset > 0 ? Style.ExtensionLineOffset : -Style.ExtensionLineOffset);
                var firstExtEnd = dimLineStart + perpendicular * (offset > 0 ? Style.ExtensionLineExtend : -Style.ExtensionLineExtend);
                geometry.ExtensionLines.Add(new LineGeometry(firstExtStart, firstExtEnd));

                // 第二点的延伸线
                var secondExtStart = secondPoint + perpendicular * (offset > 0 ? Style.ExtensionLineOffset : -Style.ExtensionLineOffset);
                var secondExtEnd = dimLineEnd + perpendicular * (offset > 0 ? Style.ExtensionLineExtend : -Style.ExtensionLineExtend);
                geometry.ExtensionLines.Add(new LineGeometry(secondExtStart, secondExtEnd));
            }

            // 计算箭头
            if (Style.ShowArrows)
            {
                geometry.Arrows.Add(new ArrowGeometry(dimLineStart, direction, Style.ArrowSize));
                geometry.Arrows.Add(new ArrowGeometry(dimLineEnd, -direction, Style.ArrowSize));
            }

            // 计算文本位置
            geometry.TextPosition = (dimLineStart + dimLineEnd) * 0.5f + perpendicular * Style.TextHeight * 0.5f;

            return geometry;
        }

        public override EntityBase Clone()
        {
            var clone = new EntityAlignedDimension(firstPoint, secondPoint, offset);
            CopyPropertiesTo(clone);
            return clone;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据参考线创建对齐标注
        /// </summary>
        public static EntityAlignedDimension CreateFromLine(EntityLine line, double offsetDistance)
        {
            return new EntityAlignedDimension(line.StartPoint, line.EndPoint, offsetDistance);
        }

        /// <summary>
        /// 设置标注线通过指定点
        /// </summary>
        public void SetDimensionLinePosition(Vector2 point)
        {
            // 计算点到参考线的距离作为新的offset
            var direction = Vector2.Normalize(secondPoint - firstPoint);
            var perpendicular = new Vector2(-direction.Y, direction.X);
            var toPoint = point - firstPoint;
            offset = Vector2.Dot(toPoint, perpendicular);
            OnPropertyChanged(nameof(Offset));
            Update();
        }

        #endregion
    }
} 