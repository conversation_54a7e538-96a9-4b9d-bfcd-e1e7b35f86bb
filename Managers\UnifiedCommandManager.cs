using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 统一命令管理器
    /// 整合所有命令管理功能，提供统一的命令注册、执行、缓存和生命周期管理
    /// </summary>
    public class UnifiedCommandManager : IDisposable
    {
        #region 私有字段

        private static UnifiedCommandManager _instance;
        private static readonly object _lockObject = new object();
        
        private readonly Dictionary<string, CommandRegistration> _commandRegistry;
        private readonly Dictionary<string, string> _commandAliases;
        private readonly Dictionary<Keys, string> _shortcutKeys;
        private readonly ConcurrentQueue<Command> _commandPool;
        private readonly Dictionary<string, Queue<Command>> _typedCommandPools;
        private readonly CommandHistory _commandHistory;
        private readonly CommandPerformanceMonitor _performanceMonitor;
        
        private ViewBase _currentViewer;
        private Command _currentCommand;
        private bool _isInitialized;
        private readonly int _maxPoolSize = 50;

        #endregion

        #region 单例模式

        public static UnifiedCommandManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new UnifiedCommandManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private UnifiedCommandManager()
        {
            _commandRegistry = new Dictionary<string, CommandRegistration>();
            _commandAliases = new Dictionary<string, string>();
            _shortcutKeys = new Dictionary<Keys, string>();
            _commandPool = new ConcurrentQueue<Command>();
            _typedCommandPools = new Dictionary<string, Queue<Command>>();
            _commandHistory = new CommandHistory();
            _performanceMonitor = new CommandPerformanceMonitor();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 当前命令
        /// </summary>
        public Command CurrentCommand => _currentCommand;

        /// <summary>
        /// 命令历史
        /// </summary>
        public CommandHistory History => _commandHistory;

        /// <summary>
        /// 性能监控器
        /// </summary>
        public CommandPerformanceMonitor PerformanceMonitor => _performanceMonitor;

        /// <summary>
        /// 已注册的命令数量
        /// </summary>
        public int RegisteredCommandCount => _commandRegistry.Count;

        #endregion

        #region 事件

        /// <summary>
        /// 命令执行开始事件
        /// </summary>
        public event EventHandler<CommandExecutionEventArgs> CommandExecutionStarted;

        /// <summary>
        /// 命令执行完成事件
        /// </summary>
        public event EventHandler<CommandExecutionEventArgs> CommandExecutionCompleted;

        /// <summary>
        /// 命令执行失败事件
        /// </summary>
        public event EventHandler<CommandExecutionEventArgs> CommandExecutionFailed;

        /// <summary>
        /// 命令取消事件
        /// </summary>
        public event EventHandler<CommandExecutionEventArgs> CommandCancelled;

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化命令管理器
        /// </summary>
        /// <param name="viewer">视图对象</param>
        public void Initialize(ViewBase viewer)
        {
            if (_isInitialized) return;

            _currentViewer = viewer ?? throw new ArgumentNullException(nameof(viewer));

            try
            {
                // 注册核心命令
                RegisterCoreCommands();
                
                // 注册编辑命令
                RegisterEditCommands();
                
                // 注册绘图命令
                RegisterDrawCommands();
                
                // 注册标注命令
                RegisterDimensionCommands();
                
                // 设置默认快捷键
                SetupDefaultShortcuts();
                
                // 设置默认别名
                SetupDefaultAliases();

                _isInitialized = true;
                Debug.WriteLine($"UnifiedCommandManager initialized with {_commandRegistry.Count} commands");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize UnifiedCommandManager: {ex.Message}", ex);
            }
        }

        #endregion

        #region 命令注册方法

        /// <summary>
        /// 注册命令
        /// </summary>
        /// <typeparam name="T">命令类型</typeparam>
        /// <param name="name">命令名称</param>
        /// <param name="description">命令描述</param>
        /// <param name="category">命令分类</param>
        /// <param name="shortcut">快捷键</param>
        /// <param name="aliases">别名</param>
        public void RegisterCommand<T>(string name, string description = "", string category = "General", 
            Keys? shortcut = null, params string[] aliases) where T : Command, new()
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Command name cannot be null or empty", nameof(name));

            if (_commandRegistry.ContainsKey(name))
                throw new InvalidOperationException($"Command '{name}' is already registered");

            var registration = new CommandRegistration
            {
                Name = name,
                CommandType = typeof(T),
                Description = description,
                Category = category,
                Factory = () => new T(),
                CreatedDate = DateTime.Now
            };

            _commandRegistry[name] = registration;

            // 注册快捷键
            if (shortcut.HasValue)
            {
                _shortcutKeys[shortcut.Value] = name;
            }

            // 注册别名
            if (aliases != null)
            {
                foreach (var alias in aliases)
                {
                    if (!string.IsNullOrWhiteSpace(alias))
                    {
                        _commandAliases[alias.ToUpper()] = name;
                    }
                }
            }

            // 初始化命令池
            _typedCommandPools[name] = new Queue<Command>();

            Debug.WriteLine($"Registered command: {name} ({typeof(T).Name})");
        }

        /// <summary>
        /// 注册命令工厂
        /// </summary>
        /// <param name="name">命令名称</param>
        /// <param name="factory">命令工厂方法</param>
        /// <param name="description">命令描述</param>
        /// <param name="category">命令分类</param>
        public void RegisterCommandFactory(string name, Func<Command> factory, string description = "", string category = "General")
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Command name cannot be null or empty", nameof(name));

            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            if (_commandRegistry.ContainsKey(name))
                throw new InvalidOperationException($"Command '{name}' is already registered");

            var registration = new CommandRegistration
            {
                Name = name,
                CommandType = null, // 使用工厂方法时类型可能未知
                Description = description,
                Category = category,
                Factory = factory,
                CreatedDate = DateTime.Now
            };

            _commandRegistry[name] = registration;
            _typedCommandPools[name] = new Queue<Command>();

            Debug.WriteLine($"Registered command factory: {name}");
        }

        /// <summary>
        /// 批量注册命令
        /// </summary>
        /// <param name="assembly">程序集</param>
        /// <param name="namespaceName">命名空间</param>
        public void RegisterCommandsFromAssembly(Assembly assembly, string namespaceName = null)
        {
            try
            {
                var commandTypes = assembly.GetTypes()
                    .Where(t => typeof(Command).IsAssignableFrom(t) && 
                               !t.IsAbstract && 
                               t.IsClass &&
                               (string.IsNullOrEmpty(namespaceName) || t.Namespace?.StartsWith(namespaceName) == true))
                    .ToList();

                foreach (var commandType in commandTypes)
                {
                    try
                    {
                        var name = commandType.Name.Replace("Cmd", "").Replace("Command", "");
                        var description = $"Auto-registered command: {name}";
                        
                        var registration = new CommandRegistration
                        {
                            Name = name,
                            CommandType = commandType,
                            Description = description,
                            Category = "Auto",
                            Factory = () => (Command)Activator.CreateInstance(commandType),
                            CreatedDate = DateTime.Now
                        };

                        if (!_commandRegistry.ContainsKey(name))
                        {
                            _commandRegistry[name] = registration;
                            _typedCommandPools[name] = new Queue<Command>();
                            Debug.WriteLine($"Auto-registered command: {name} ({commandType.Name})");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed to auto-register command {commandType.Name}: {ex.Message}");
                    }
                }

                Debug.WriteLine($"Auto-registered {commandTypes.Count} commands from assembly {assembly.GetName().Name}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to register commands from assembly: {ex.Message}");
            }
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <param name="parameters">命令参数</param>
        /// <returns>是否执行成功</returns>
        public bool ExecuteCommand(string commandName, Dictionary<string, object> parameters = null)
        {
            if (string.IsNullOrWhiteSpace(commandName))
                return false;

            var startTime = DateTime.Now;
            var resolvedName = ResolveCommandName(commandName);

            if (string.IsNullOrEmpty(resolvedName))
            {
                Debug.WriteLine($"Unknown command: {commandName}");
                return false;
            }

            try
            {
                // 取消当前命令
                if (_currentCommand != null)
                {
                    CancelCurrentCommand();
                }

                // 获取或创建命令实例
                var command = GetCommandInstance(resolvedName);
                if (command == null)
                {
                    Debug.WriteLine($"Failed to create command: {resolvedName}");
                    return false;
                }

                // 设置参数
                if (parameters != null)
                {
                    SetCommandParameters(command, parameters);
                }

                // 开始执行
                _currentCommand = command;
                OnCommandExecutionStarted(resolvedName, command, startTime);

                // 执行命令
                _currentViewer._cmdsMgr.DoCommand(command);

                // 记录历史和性能
                _commandHistory.AddCommand(resolvedName, startTime);
                _performanceMonitor.RecordExecution(resolvedName, DateTime.Now - startTime);

                // 更新注册信息
                if (_commandRegistry.TryGetValue(resolvedName, out var registration))
                {
                    registration.ExecutionCount++;
                    registration.TotalExecutionTime += DateTime.Now - startTime;
                }

                OnCommandExecutionCompleted(resolvedName, command, startTime);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Command execution failed: {resolvedName}, Error: {ex.Message}");
                OnCommandExecutionFailed(resolvedName, _currentCommand, startTime, ex);
                return false;
            }
        }

        /// <summary>
        /// 处理快捷键
        /// </summary>
        /// <param name="key">按键</param>
        /// <returns>是否处理成功</returns>
        public bool HandleShortcut(Keys key)
        {
            if (_shortcutKeys.TryGetValue(key, out var commandName))
            {
                return ExecuteCommand(commandName);
            }
            return false;
        }

        /// <summary>
        /// 取消当前命令
        /// </summary>
        public void CancelCurrentCommand()
        {
            if (_currentCommand != null)
            {
                try
                {
                    _currentViewer._cmdsMgr.CancelCurrentCommand();
                    OnCommandCancelled(_currentCommand.GetType().Name, _currentCommand, DateTime.Now);

                    // 回收命令到对象池
                    RecycleCommand(_currentCommand);
                    _currentCommand = null;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error cancelling command: {ex.Message}");
                }
            }
        }

        #endregion

        #region 命令池管理

        /// <summary>
        /// 获取命令实例（优先从对象池获取）
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令实例</returns>
        private Command GetCommandInstance(string commandName)
        {
            try
            {
                // 先尝试从类型化对象池获取
                if (_typedCommandPools.TryGetValue(commandName, out var pool) && pool.Count > 0)
                {
                    var pooledCommand = pool.Dequeue();
                    ResetCommand(pooledCommand);
                    return pooledCommand;
                }

                // 从注册表创建新实例
                if (_commandRegistry.TryGetValue(commandName, out var registration))
                {
                    return registration.Factory();
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating command instance: {commandName}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 回收命令到对象池
        /// </summary>
        /// <param name="command">命令对象</param>
        private void RecycleCommand(Command command)
        {
            if (command == null) return;

            try
            {
                var commandName = GetCommandName(command);
                if (!string.IsNullOrEmpty(commandName) &&
                    _typedCommandPools.TryGetValue(commandName, out var pool) &&
                    pool.Count < _maxPoolSize)
                {
                    ResetCommand(command);
                    pool.Enqueue(command);
                }
                else
                {
                    // 如果池已满或无法识别命令类型，直接释放
                    if (command is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error recycling command: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置命令状态
        /// </summary>
        /// <param name="command">命令对象</param>
        private void ResetCommand(Command command)
        {
            try
            {
                // 重置命令到初始状态
                if (command is CommandBase cmdBase)
                {
                    cmdBase.Reset();
                }
                // 对于其他类型的命令，可能需要特殊处理
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting command: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取命令名称
        /// </summary>
        /// <param name="command">命令对象</param>
        /// <returns>命令名称</returns>
        private string GetCommandName(Command command)
        {
            if (command == null) return null;

            // 从注册表中查找匹配的命令名称
            var commandType = command.GetType();
            return _commandRegistry.FirstOrDefault(kvp => kvp.Value.CommandType == commandType).Key;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 解析命令名称（处理别名）
        /// </summary>
        /// <param name="input">输入的命令名称</param>
        /// <returns>实际的命令名称</returns>
        private string ResolveCommandName(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return null;

            var upperInput = input.ToUpper();

            // 检查别名
            if (_commandAliases.TryGetValue(upperInput, out var aliasTarget))
            {
                return aliasTarget;
            }

            // 检查直接匹配
            var directMatch = _commandRegistry.Keys.FirstOrDefault(k =>
                string.Equals(k, input, StringComparison.OrdinalIgnoreCase));

            return directMatch;
        }

        /// <summary>
        /// 设置命令参数
        /// </summary>
        /// <param name="command">命令对象</param>
        /// <param name="parameters">参数字典</param>
        private void SetCommandParameters(Command command, Dictionary<string, object> parameters)
        {
            if (command is CommandBase cmdBase)
            {
                foreach (var param in parameters)
                {
                    cmdBase.SetParameter(param.Key, param.Value);
                }
            }
        }

        #endregion

        #region 事件处理方法

        private void OnCommandExecutionStarted(string commandName, Command command, DateTime startTime)
        {
            CommandExecutionStarted?.Invoke(this, new CommandExecutionEventArgs
            {
                CommandName = commandName,
                Command = command,
                StartTime = startTime,
                Success = false
            });
        }

        private void OnCommandExecutionCompleted(string commandName, Command command, DateTime startTime)
        {
            CommandExecutionCompleted?.Invoke(this, new CommandExecutionEventArgs
            {
                CommandName = commandName,
                Command = command,
                StartTime = startTime,
                EndTime = DateTime.Now,
                Success = true
            });
        }

        private void OnCommandExecutionFailed(string commandName, Command command, DateTime startTime, Exception exception)
        {
            CommandExecutionFailed?.Invoke(this, new CommandExecutionEventArgs
            {
                CommandName = commandName,
                Command = command,
                StartTime = startTime,
                EndTime = DateTime.Now,
                Exception = exception,
                Success = false
            });
        }

        private void OnCommandCancelled(string commandName, Command command, DateTime startTime)
        {
            CommandCancelled?.Invoke(this, new CommandExecutionEventArgs
            {
                CommandName = commandName,
                Command = command,
                StartTime = startTime,
                EndTime = DateTime.Now,
                Success = false
            });
        }

        #endregion

        #region 核心命令注册

        private void RegisterCoreCommands()
        {
            // 注册撤销/重做命令
            RegisterCommand<UndoCmd>("UNDO", "撤销上一个操作", "Edit", Keys.Control | Keys.Z, "U");
            RegisterCommand<RedoCmd>("REDO", "重做上一个操作", "Edit", Keys.Control | Keys.Y, "R");
        }

        private void RegisterEditCommands()
        {
            // 注册编辑命令
            RegisterCommand<OffsetCmd>("OFFSET", "偏移对象", "Edit", null, "O");
            RegisterCommand<ArrayCmd>("ARRAY", "阵列对象", "Edit", null, "AR");
            RegisterCommand<TrimCmd>("TRIM", "修剪对象", "Edit", null, "TR");
            RegisterCommand<ExtendCmd>("EXTEND", "延伸对象", "Edit", null, "EX");
            RegisterCommand<FilletCmd>("FILLET", "圆角", "Edit", null, "F");
            RegisterCommand<ChamferCmd>("CHAMFER", "倒角", "Edit", null, "CHA");
            RegisterCommand<ScaleCmd>("SCALE", "缩放", "Edit", null, "SC");
            RegisterCommand<RotateCmd>("ROTATE", "旋转", "Edit", null, "RO");
            RegisterCommand<StretchCmd>("STRETCH", "拉伸", "Edit", null, "S");
        }

        private void RegisterDrawCommands()
        {
            // 注册绘图命令
            RegisterCommand<LineCmd>("LINE", "绘制直线", "Draw", null, "L");
            RegisterCommand<CircleCmd>("CIRCLE", "绘制圆", "Draw", null, "C");
            RegisterCommand<RectangleCmd>("RECTANGLE", "绘制矩形", "Draw", null, "REC");
            RegisterCommand<PolylineCmd>("POLYLINE", "绘制多段线", "Draw", null, "PL");
            RegisterCommand<ArcCmd>("ARC", "绘制圆弧", "Draw", null, "A");
            RegisterCommand<EllipseCmd>("ELLIPSE", "绘制椭圆", "Draw", null, "EL");
        }

        private void RegisterDimensionCommands()
        {
            // 注册标注命令
            RegisterCommand<LinearDimensionCmd>("DIMLINEAR", "线性标注", "Dimension", null, "DLI");
            RegisterCommand<AlignedDimensionCmd>("DIMALIGNED", "对齐标注", "Dimension", null, "DAL");
            RegisterCommand<RadiusDimensionCmd>("DIMRADIUS", "半径标注", "Dimension", null, "DRA");
            RegisterCommand<DiameterDimensionCmd>("DIMDIAMETER", "直径标注", "Dimension", null, "DDI");
            RegisterCommand<AngularDimensionCmd>("DIMANGULAR", "角度标注", "Dimension", null, "DAN");
            RegisterCommand<LeaderDimensionCmd>("LEADER", "引线标注", "Dimension", null, "LE");
        }

        private void SetupDefaultShortcuts()
        {
            // 设置默认快捷键（已在注册时设置）
            Debug.WriteLine($"Setup {_shortcutKeys.Count} default shortcuts");
        }

        private void SetupDefaultAliases()
        {
            // 设置默认别名（已在注册时设置）
            Debug.WriteLine($"Setup {_commandAliases.Count} default aliases");
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取所有已注册的命令
        /// </summary>
        /// <returns>命令注册信息列表</returns>
        public IEnumerable<CommandRegistration> GetAllCommands()
        {
            return _commandRegistry.Values.ToList();
        }

        /// <summary>
        /// 按分类获取命令
        /// </summary>
        /// <param name="category">分类名称</param>
        /// <returns>命令注册信息列表</returns>
        public IEnumerable<CommandRegistration> GetCommandsByCategory(string category)
        {
            return _commandRegistry.Values.Where(c =>
                string.Equals(c.Category, category, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 搜索命令
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <returns>匹配的命令列表</returns>
        public IEnumerable<CommandRegistration> SearchCommands(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return GetAllCommands();

            var lowerSearch = searchText.ToLower();
            return _commandRegistry.Values.Where(c =>
                c.Name.ToLower().Contains(lowerSearch) ||
                c.Description.ToLower().Contains(lowerSearch));
        }

        /// <summary>
        /// 检查命令是否存在
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <returns>是否存在</returns>
        public bool CommandExists(string commandName)
        {
            return !string.IsNullOrEmpty(ResolveCommandName(commandName));
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            try
            {
                // 取消当前命令
                CancelCurrentCommand();

                // 清理命令池
                foreach (var pool in _typedCommandPools.Values)
                {
                    while (pool.Count > 0)
                    {
                        var command = pool.Dequeue();
                        if (command is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    }
                }

                // 清理通用命令池
                while (_commandPool.TryDequeue(out var command))
                {
                    if (command is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }

                _commandRegistry.Clear();
                _commandAliases.Clear();
                _shortcutKeys.Clear();
                _typedCommandPools.Clear();

                Debug.WriteLine("UnifiedCommandManager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error disposing UnifiedCommandManager: {ex.Message}");
            }
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// 命令注册信息
    /// </summary>
    public class CommandRegistration
    {
        public string Name { get; set; }
        public Type CommandType { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public Func<Command> Factory { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ExecutionCount { get; set; }
        public TimeSpan TotalExecutionTime { get; set; }
    }

    /// <summary>
    /// 命令执行事件参数
    /// </summary>
    public class CommandExecutionEventArgs : EventArgs
    {
        public string CommandName { get; set; }
        public Command Command { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public Exception Exception { get; set; }
        public bool Success { get; set; }
    }

    /// <summary>
    /// 命令历史记录
    /// </summary>
    public class CommandHistory
    {
        private readonly Queue<CommandHistoryEntry> _history;
        private readonly int _maxHistorySize;

        public CommandHistory(int maxSize = 100)
        {
            _maxHistorySize = maxSize;
            _history = new Queue<CommandHistoryEntry>();
        }

        public void AddCommand(string commandName, DateTime executionTime)
        {
            _history.Enqueue(new CommandHistoryEntry
            {
                CommandName = commandName,
                ExecutionTime = executionTime
            });

            // 保持历史记录大小限制
            while (_history.Count > _maxHistorySize)
            {
                _history.Dequeue();
            }
        }

        public List<string> GetRecentCommands(int count = 10)
        {
            return _history.TakeLast(count).Select(h => h.CommandName).ToList();
        }

        public IEnumerable<CommandHistoryEntry> GetHistory()
        {
            return _history.ToList();
        }
    }

    /// <summary>
    /// 命令历史条目
    /// </summary>
    public class CommandHistoryEntry
    {
        public string CommandName { get; set; }
        public DateTime ExecutionTime { get; set; }
    }

    /// <summary>
    /// 命令性能监控器
    /// </summary>
    public class CommandPerformanceMonitor
    {
        private readonly Dictionary<string, CommandPerformanceData> _performanceData;

        public CommandPerformanceMonitor()
        {
            _performanceData = new Dictionary<string, CommandPerformanceData>();
        }

        public void RecordExecution(string commandName, TimeSpan executionTime)
        {
            if (!_performanceData.TryGetValue(commandName, out var data))
            {
                data = new CommandPerformanceData { CommandName = commandName };
                _performanceData[commandName] = data;
            }

            data.ExecutionCount++;
            data.TotalExecutionTime += executionTime;
            data.LastExecutionTime = executionTime;
            data.AverageExecutionTime = TimeSpan.FromTicks(data.TotalExecutionTime.Ticks / data.ExecutionCount);

            if (executionTime > data.MaxExecutionTime)
                data.MaxExecutionTime = executionTime;

            if (data.MinExecutionTime == TimeSpan.Zero || executionTime < data.MinExecutionTime)
                data.MinExecutionTime = executionTime;
        }

        public CommandPerformanceData GetPerformanceData(string commandName)
        {
            return _performanceData.TryGetValue(commandName, out var data) ? data : null;
        }

        public IEnumerable<CommandPerformanceData> GetAllPerformanceData()
        {
            return _performanceData.Values.ToList();
        }
    }

    /// <summary>
    /// 命令性能数据
    /// </summary>
    public class CommandPerformanceData
    {
        public string CommandName { get; set; }
        public int ExecutionCount { get; set; }
        public TimeSpan TotalExecutionTime { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public TimeSpan MinExecutionTime { get; set; }
        public TimeSpan MaxExecutionTime { get; set; }
        public TimeSpan LastExecutionTime { get; set; }
    }

    #endregion
}
