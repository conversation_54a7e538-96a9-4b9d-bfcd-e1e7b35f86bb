﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 撤销命令
    /// </summary>
    public class UndoCmd : Command
    {
        public override void Initialize()
        {
            base.Initialize();

            // 检查是否可以撤销
            if (_mgr.CanUndo)
            {
                this.pointer.Document.Prompt = "撤销上一个操作";
                _mgr.FinishCurrentCommand();
            }
            else
            {
                this.pointer.Document.Prompt = "没有可撤销的操作";
                _mgr.CancelCurrentCommand();
            }
        }

        /// <summary>
        /// 撤销命令不需要提交逻辑，由CommandsMgr处理
        /// </summary>
        protected override void Commit()
        {
            // 撤销逻辑由CommandsMgr.Undo()处理
        }

        /// <summary>
        /// 撤销命令不需要回滚逻辑
        /// </summary>
        protected override void Rollback()
        {
            // 不需要回滚
        }

        public override string Description => "撤销上一个操作";
    }
}
