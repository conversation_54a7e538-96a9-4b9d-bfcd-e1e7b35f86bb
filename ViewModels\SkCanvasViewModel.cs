﻿using McLaser.Core;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Forms;
using System.Windows.Input;


namespace McLaser.EditViewerSk.ViewModels
{



   


    public class SkEditorViewModel : INotifyPropertyChanged
    {

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        private DocumentBase doc;
        public DocumentBase Doc
        {
            get { return doc; }
            set { doc = value; OnPropertyChanged("Doc"); }
        }


        public SkEditorViewModel(DocumentBase doc)
        {
            this.Doc = doc;
            (this.Doc.View as ViewBase).MouseMove -= SkCanvas_MouseMove;
            (this.Doc.View as ViewBase).MouseMove += SkCanvas_MouseMove;
        }

        public Point _currentPosition = new Point();
        public Point CurrentPosition
        {
            get { return _currentPosition; }
            set { _currentPosition = value; OnPropertyChanged("CurrentPosition"); }
        }

        // ==================== 对象捕捉控制属性 ====================
        private bool _isEndSnapEnabled = true;
        public bool IsEndSnapEnabled
        {
            get { return _isEndSnapEnabled; }
            set
            {
                _isEndSnapEnabled = value;
                OnPropertyChanged("IsEndSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isMidSnapEnabled = true;
        public bool IsMidSnapEnabled
        {
            get { return _isMidSnapEnabled; }
            set
            {
                _isMidSnapEnabled = value;
                OnPropertyChanged("IsMidSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isCenterSnapEnabled = true;
        public bool IsCenterSnapEnabled
        {
            get { return _isCenterSnapEnabled; }
            set
            {
                _isCenterSnapEnabled = value;
                OnPropertyChanged("IsCenterSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isIntersectionSnapEnabled = true;
        public bool IsIntersectionSnapEnabled
        {
            get { return _isIntersectionSnapEnabled; }
            set
            {
                _isIntersectionSnapEnabled = value;
                OnPropertyChanged("IsIntersectionSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isPerpendicularSnapEnabled = false;
        public bool IsPerpendicularSnapEnabled
        {
            get { return _isPerpendicularSnapEnabled; }
            set
            {
                _isPerpendicularSnapEnabled = value;
                OnPropertyChanged("IsPerpendicularSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isTangentSnapEnabled = false;
        public bool IsTangentSnapEnabled
        {
            get { return _isTangentSnapEnabled; }
            set
            {
                _isTangentSnapEnabled = value;
                OnPropertyChanged("IsTangentSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isQuadSnapEnabled = false;
        public bool IsQuadSnapEnabled
        {
            get { return _isQuadSnapEnabled; }
            set
            {
                _isQuadSnapEnabled = value;
                OnPropertyChanged("IsQuadSnapEnabled");
                UpdateSnapModes();
            }
        }

        private bool _isNearSnapEnabled = false;
        public bool IsNearSnapEnabled
        {
            get { return _isNearSnapEnabled; }
            set
            {
                _isNearSnapEnabled = value;
                OnPropertyChanged("IsNearSnapEnabled");
                UpdateSnapModes();
            }
        }

        // ==================== 高级交互系统状态属性 ====================
        private bool _isPolarTrackingEnabled = true;
        public bool IsPolarTrackingEnabled
        {
            get { return _isPolarTrackingEnabled; }
            set
            {
                _isPolarTrackingEnabled = value;
                OnPropertyChanged("IsPolarTrackingEnabled");
                UpdatePolarTracking();
            }
        }

        private bool _isObjectTrackingEnabled = true;
        public bool IsObjectTrackingEnabled
        {
            get { return _isObjectTrackingEnabled; }
            set
            {
                _isObjectTrackingEnabled = value;
                OnPropertyChanged("IsObjectTrackingEnabled");
                UpdateObjectTracking();
            }
        }

        private bool _isDynamicInputEnabled = true;
        public bool IsDynamicInputEnabled
        {
            get { return _isDynamicInputEnabled; }
            set
            {
                _isDynamicInputEnabled = value;
                OnPropertyChanged("IsDynamicInputEnabled");
                UpdateDynamicInput();
            }
        }

        private bool _isGridSnapEnabled = true;
        public bool IsGridSnapEnabled
        {
            get { return _isGridSnapEnabled; }
            set
            {
                _isGridSnapEnabled = value;
                OnPropertyChanged("IsGridSnapEnabled");
                UpdateSnapModes();
            }
        }

        private string _currentDimensionStyle = "标准";
        public string CurrentDimensionStyle
        {
            get { return _currentDimensionStyle; }
            set
            {
                _currentDimensionStyle = value;
                OnPropertyChanged("CurrentDimensionStyle");
            }
        }

        private string _currentLayer = "0";
        public string CurrentLayer
        {
            get { return _currentLayer; }
            set
            {
                _currentLayer = value;
                OnPropertyChanged("CurrentLayer");
            }
        }

        public ICommand BtnRedoCommand => new RelayCommand(() =>
        {
            Doc.Action.ActRedo();
        });

        public ICommand BtnUndoCommand => new RelayCommand(() =>
        {
            Doc.Action.ActUndo();
        });

        public ICommand BtnDrawPointCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawPoint();
        });

        public ICommand BtnDrawLineCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawLine();
        });

        public ICommand BtnDrawRectangleCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawRectangle();
        });

        public ICommand BtnDrawCircleCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawCircle();
        });

        public ICommand BtnDrawPolylineCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawPolyline();
        });

        public ICommand BtnDrawArcCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawArc();
        });

        // ==================== 标注命令 ====================
        public ICommand DimLinearCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("DIMLINEAR");
        });

        public ICommand DimAlignedCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("DIMALIGNED");
        });

        public ICommand DimRadiusCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("DIMRADIUS");
        });

        public ICommand DimDiameterCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("DIMDIAMETER");
        });

        public ICommand DimAngularCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("DIMANGULAR");
        });

        public ICommand DimLeaderCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("LEADER");
        });

        // ==================== 编辑命令 ====================
        public ICommand OffsetCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("OFFSET");
        });

        public ICommand ArrayCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("ARRAY");
        });

        public ICommand TrimCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("TRIM");
        });

        public ICommand ExtendCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("EXTEND");
        });

        public ICommand FilletCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("FILLET");
        });

        public ICommand ChamferCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("CHAMFER");
        });

        public ICommand ScaleCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("SCALE");
        });

        public ICommand RotateCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("ROTATE");
        });

        public ICommand StretchCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("STRETCH");
        });

        // ==================== 高级绘图命令 ====================
        public ICommand EllipseCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("ELLIPSE");
        });

        public ICommand SplineCommand => new RelayCommand(() =>
        {
            ExecuteUnifiedCommand("SPLINE");
        });

        // ==================== 管理器命令 ====================
        public ICommand OpenDimensionStyleManagerCommand => new RelayCommand(() =>
        {
            try
            {
                var window = new Views.DimensionStyleManagerWindow();
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening dimension style manager: {ex.Message}");
            }
        });

        public ICommand OpenPolarTrackingSettingsCommand => new RelayCommand(() =>
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var window = new Views.PolarTrackingSettingsWindow(viewer);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening polar tracking settings: {ex.Message}");
            }
        });

        public ICommand OpenObjectSnapSettingsCommand => new RelayCommand(() =>
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var window = new Views.ObjectSnapSettingsWindow(viewer);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening object snap settings: {ex.Message}");
            }
        });

        public ICommand OpenLinetypeManagerCommand => new RelayCommand(() =>
        {
            try
            {
                var window = new Views.LinetypeManagerWindow();
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening linetype manager: {ex.Message}");
            }
        });

        public ICommand OpenBlockManagerCommand => new RelayCommand(() =>
        {
            try
            {
                var window = new Views.BlockManagerWindow();
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening block manager: {ex.Message}");
            }
        });

        public ICommand OpenSystemSettingsCommand => new RelayCommand(() =>
        {
            try
            {
                var window = new Views.SystemSettingsWindow();
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening system settings: {ex.Message}");
            }
        });

        // ==================== 统一命令执行方法 ====================
        private void ExecuteUnifiedCommand(string commandName)
        {
            try
            {
                var commandManager = Managers.UnifiedCommandManager.Instance;
                if (commandManager != null)
                {
                    commandManager.ExecuteCommand(commandName);
                }
                else
                {
                    // 回退到传统方法
                    System.Diagnostics.Debug.WriteLine($"UnifiedCommandManager not available, command: {commandName}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error executing command {commandName}: {ex.Message}");
            }
        }

        private void SkCanvas_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var cur = e.Location;
            var pt = Doc.View.CanvasToModel(new Vector2(cur.X, cur.Y));
            CurrentPosition = new Point(pt.X, pt.Y);
        }

        // ==================== 状态更新方法 ====================
        private void UpdateSnapModes()
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var phase2Manager = viewer?.GetPhase2Manager();

                var snapModes = Base.ObjectSnapMode.Undefined;

                if (IsEndSnapEnabled) snapModes |= Base.ObjectSnapMode.End;
                if (IsMidSnapEnabled) snapModes |= Base.ObjectSnapMode.Mid;
                if (IsCenterSnapEnabled) snapModes |= Base.ObjectSnapMode.Center;
                if (IsIntersectionSnapEnabled) snapModes |= Base.ObjectSnapMode.Intersection;
                if (IsPerpendicularSnapEnabled) snapModes |= Base.ObjectSnapMode.Perpendicular;
                if (IsTangentSnapEnabled) snapModes |= Base.ObjectSnapMode.Tangent;
                if (IsQuadSnapEnabled) snapModes |= Base.ObjectSnapMode.Quad;
                if (IsNearSnapEnabled) snapModes |= Base.ObjectSnapMode.Near;
                if (IsGridSnapEnabled) snapModes |= Base.ObjectSnapMode.Grid;

                if (phase2Manager != null)
                {
                    phase2Manager.ApplyObjectSnapSettings(snapModes);
                }
                else if (viewer?._inputManager?.snapMgr != null)
                {
                    viewer._inputManager.snapMgr.RunningSnapModes = snapModes;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating snap modes: {ex.Message}");
            }
        }

        private void UpdatePolarTracking()
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var phase2Manager = viewer?.GetPhase2Manager();
                if (phase2Manager != null)
                {
                    phase2Manager.SetPolarTrackingEnabled(IsPolarTrackingEnabled);

                    // 同步状态到UI
                    var currentSettings = phase2Manager.GetPolarTrackingSettings();
                    if (currentSettings.IsEnabled != IsPolarTrackingEnabled)
                    {
                        _isPolarTrackingEnabled = currentSettings.IsEnabled;
                        OnPropertyChanged("IsPolarTrackingEnabled");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating polar tracking: {ex.Message}");
            }
        }

        private void UpdateObjectTracking()
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var phase2Manager = viewer?.GetPhase2Manager();
                if (phase2Manager != null)
                {
                    phase2Manager.SetObjectTrackingEnabled(IsObjectTrackingEnabled);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating object tracking: {ex.Message}");
            }
        }

        private void UpdateDynamicInput()
        {
            try
            {
                var viewer = Doc?.View as ViewBase;
                var phase2Manager = viewer?.GetPhase2Manager();
                if (phase2Manager != null)
                {
                    phase2Manager.SetDynamicInputEnabled(IsDynamicInputEnabled);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating dynamic input: {ex.Message}");
            }
        }

    }
}
