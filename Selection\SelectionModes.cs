using System;

namespace McLaser.EditViewerSk.Selection
{
    /// <summary>
    /// CAD实体选择模式
    /// 基于AutoCAD、SolidWorks等主流CAD软件的选择标准
    /// </summary>
    public enum EntitySelectionMode
    {
        /// <summary>
        /// 点选模式 - 点击选择单个实体
        /// </summary>
        Point = 0,

        /// <summary>
        /// 窗口选择模式 - 完全包含在选择框内的实体被选中
        /// 选择方向：从左到右拖拽
        /// </summary>
        Window = 1,

        /// <summary>
        /// 交叉选择模式 - 与选择框有交集的实体被选中
        /// 选择方向：从右到左拖拽
        /// </summary>
        Cross = 2,

        /// <summary>
        /// 多边形窗口选择 - 多边形内的实体被选中
        /// </summary>
        PolygonWindow = 3,

        /// <summary>
        /// 多边形交叉选择 - 与多边形有交集的实体被选中
        /// </summary>
        PolygonCross = 4,

        /// <summary>
        /// 圆形窗口选择 - 圆形区域内的实体被选中
        /// </summary>
        CircleWindow = 5,

        /// <summary>
        /// 圆形交叉选择 - 与圆形区域有交集的实体被选中
        /// </summary>
        CircleCross = 6,

        /// <summary>
        /// 围栏选择 - 与选择路径相交的实体被选中
        /// </summary>
        Fence = 7,

        /// <summary>
        /// 全选模式
        /// </summary>
        All = 8,

        /// <summary>
        /// 反选模式
        /// </summary>
        Invert = 9,

        /// <summary>
        /// 按图层选择
        /// </summary>
        ByLayer = 10,

        /// <summary>
        /// 按类型选择
        /// </summary>
        ByType = 11,

        /// <summary>
        /// 相似实体选择
        /// </summary>
        Similar = 12
    }

    /// <summary>
    /// 选择操作类型
    /// </summary>
    public enum SelectionOperation
    {
        /// <summary>
        /// 新选择 - 清除之前的选择，开始新的选择
        /// </summary>
        New = 0,

        /// <summary>
        /// 添加选择 - 将新选择的实体加入到现有选择集
        /// </summary>
        Add = 1,

        /// <summary>
        /// 移除选择 - 从现有选择集中移除指定实体
        /// </summary>
        Remove = 2,

        /// <summary>
        /// 切换选择 - 如果实体已选中则取消选择，否则选中
        /// </summary>
        Toggle = 3,

        /// <summary>
        /// 交集选择 - 只保留既在原选择集又在新选择中的实体
        /// </summary>
        Intersect = 4
    }

    /// <summary>
    /// 选择过滤器类型
    /// </summary>
    public enum SelectionFilterType
    {
        /// <summary>
        /// 无过滤
        /// </summary>
        None = 0,

        /// <summary>
        /// 按实体类型过滤
        /// </summary>
        EntityType = 1,

        /// <summary>
        /// 按图层过滤
        /// </summary>
        Layer = 2,

        /// <summary>
        /// 按颜色过滤
        /// </summary>
        Color = 3,

        /// <summary>
        /// 按线型过滤
        /// </summary>
        LineType = 4,

        /// <summary>
        /// 按线宽过滤
        /// </summary>
        LineWeight = 5,

        /// <summary>
        /// 按材质过滤
        /// </summary>
        Material = 6,

        /// <summary>
        /// 按块参照过滤
        /// </summary>
        BlockReference = 7,

        /// <summary>
        /// 按文本内容过滤
        /// </summary>
        TextContent = 8,

        /// <summary>
        /// 按尺寸值过滤
        /// </summary>
        DimensionValue = 9,

        /// <summary>
        /// 自定义过滤器
        /// </summary>
        Custom = 100
    }

    /// <summary>
    /// 选择状态
    /// </summary>
    public enum EntitySelectionState
    {
        /// <summary>
        /// 未选中
        /// </summary>
        Unselected = 0,

        /// <summary>
        /// 已选中
        /// </summary>
        Selected = 1,

        /// <summary>
        /// 预选中（鼠标悬停）
        /// </summary>
        Highlighted = 2,

        /// <summary>
        /// 锁定选中（不能被取消选择）
        /// </summary>
        LockedSelection = 3,

        /// <summary>
        /// 隐藏状态（不参与选择）
        /// </summary>
        Hidden = 4,

        /// <summary>
        /// 冻结状态（不参与选择和显示）
        /// </summary>
        Frozen = 5
    }

    /// <summary>
    /// 选择框样式
    /// </summary>
    public class SelectionBoxStyle
    {
        /// <summary>
        /// 窗口选择框颜色
        /// </summary>
        public SkiaSharp.SKColor WindowColor { get; set; } = SkiaSharp.SKColors.Blue;

        /// <summary>
        /// 交叉选择框颜色
        /// </summary>
        public SkiaSharp.SKColor CrossColor { get; set; } = SkiaSharp.SKColors.Green;

        /// <summary>
        /// 选择框透明度
        /// </summary>
        public byte Alpha { get; set; } = 64;

        /// <summary>
        /// 选择框线宽
        /// </summary>
        public float StrokeWidth { get; set; } = 1.0f;

        /// <summary>
        /// 选择框是否虚线
        /// </summary>
        public bool IsDashed { get; set; } = true;

        /// <summary>
        /// 虚线样式
        /// </summary>
        public float[] DashPattern { get; set; } = new float[] { 5.0f, 5.0f };

        /// <summary>
        /// 是否填充选择框
        /// </summary>
        public bool IsFilled { get; set; } = false;

        /// <summary>
        /// 填充透明度
        /// </summary>
        public byte FillAlpha { get; set; } = 32;
    }

    /// <summary>
    /// 选择高亮样式
    /// </summary>
    public class SelectionHighlightStyle
    {
        /// <summary>
        /// 高亮颜色
        /// </summary>
        public SkiaSharp.SKColor HighlightColor { get; set; } = SkiaSharp.SKColors.Orange;

        /// <summary>
        /// 选中颜色
        /// </summary>
        public SkiaSharp.SKColor SelectedColor { get; set; } = SkiaSharp.SKColors.Red;

        /// <summary>
        /// 高亮线宽
        /// </summary>
        public float HighlightWidth { get; set; } = 2.0f;

        /// <summary>
        /// 选中线宽
        /// </summary>
        public float SelectedWidth { get; set; } = 3.0f;

        /// <summary>
        /// 是否闪烁
        /// </summary>
        public bool IsBlinking { get; set; } = false;

        /// <summary>
        /// 闪烁频率（毫秒）
        /// </summary>
        public int BlinkInterval { get; set; } = 500;

        /// <summary>
        /// 是否显示握点
        /// </summary>
        public bool ShowGrips { get; set; } = true;

        /// <summary>
        /// 握点大小
        /// </summary>
        public float GripSize { get; set; } = 4.0f;

        /// <summary>
        /// 握点颜色
        /// </summary>
        public SkiaSharp.SKColor GripColor { get; set; } = SkiaSharp.SKColors.Cyan;
    }
} 