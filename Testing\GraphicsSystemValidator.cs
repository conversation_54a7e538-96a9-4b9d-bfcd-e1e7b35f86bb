using System;
using System.Collections.Generic;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;

namespace McLaser.EditViewerSk.Testing
{
    /// <summary>
    /// 图形系统验证器
    /// 用于测试坐标转换精度、绘制功能和性能
    /// </summary>
    public class GraphicsSystemValidator
    {
        private readonly ViewBase _viewBase;
        private readonly List<ValidationResult> _testResults;

        public GraphicsSystemValidator(ViewBase viewBase)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _testResults = new List<ValidationResult>();
        }

        /// <summary>
        /// 运行所有验证测试
        /// </summary>
        public ValidationReport RunAllTests()
        {
            _testResults.Clear();

            // 1. 坐标转换精度测试
            TestCoordinateTransformationAccuracy();

            // 2. 基础绘制功能测试
            TestBasicDrawingFunctions();

            // 3. 复杂场景测试
            TestComplexScenarios();

            // 4. 性能测试
            TestPerformance();

            // 5. 内存泄漏测试
            TestMemoryLeaks();

            return new ValidationReport(_testResults);
        }

        #region 坐标转换测试

        private void TestCoordinateTransformationAccuracy()
        {
            var startTime = DateTime.Now;

            try
            {
                // 测试1: 基本坐标转换精度
                var testPoints = new Vector2[]
                {
                    new Vector2(0, 0),
                    new Vector2(100, 100),
                    new Vector2(-50, 75),
                    new Vector2(1000, -500),
                    new Vector2(0.001f, 0.001f)
                };

                var transform = new CoordinateTransform(_viewBase);
                
                foreach (var point in testPoints)
                {
                    // Model -> Viewport -> Model 往返转换测试
                    var viewportPoint = transform.TransformPoint(point, CoordinateSpace.Model, CoordinateSpace.Viewport);
                    var backToModel = transform.TransformPoint(viewportPoint, CoordinateSpace.Viewport, CoordinateSpace.Model);
                    
                    var error = Vector2.Distance(point, backToModel);
                    var tolerance = 0.001f; // 允许的误差

                    _testResults.Add(new ValidationResult
                    {
                        TestName = $"坐标转换精度测试 - 点({point.X}, {point.Y})",
                        Passed = error < tolerance,
                        Details = $"原始点: {point}, 转换后: {backToModel}, 误差: {error:F6}",
                        ExecutionTime = DateTime.Now - startTime
                    });
                }

                // 测试2: 距离转换精度
                var testDistances = new double[] { 1.0, 10.0, 100.0, 0.1, 1000.0 };
                
                foreach (var distance in testDistances)
                {
                    var viewportDistance = transform.TransformDistance(distance, CoordinateSpace.Model, CoordinateSpace.Viewport);
                    var backToModel = transform.TransformDistance(viewportDistance, CoordinateSpace.Viewport, CoordinateSpace.Model);
                    
                    var error = Math.Abs(distance - backToModel);
                    var tolerance = distance * 0.001; // 0.1% 的相对误差

                    _testResults.Add(new ValidationResult
                    {
                        TestName = $"距离转换精度测试 - {distance}",
                        Passed = error < tolerance,
                        Details = $"原始距离: {distance}, 转换后: {backToModel:F6}, 误差: {error:F6}",
                        ExecutionTime = DateTime.Now - startTime
                    });
                }

                // 测试3: 角度转换精度
                var testAngles = new double[] { 0, 30, 45, 90, 180, 270, 360, -45 };
                
                foreach (var angle in testAngles)
                {
                    var viewportAngle = transform.TransformAngle(angle, CoordinateSpace.Model, CoordinateSpace.Viewport);
                    var backToModel = transform.TransformAngle(viewportAngle, CoordinateSpace.Viewport, CoordinateSpace.Model);
                    
                    var error = Math.Abs(angle - backToModel);
                    var tolerance = 0.01; // 0.01度的误差

                    _testResults.Add(new ValidationResult
                    {
                        TestName = $"角度转换精度测试 - {angle}度",
                        Passed = error < tolerance,
                        Details = $"原始角度: {angle}°, 转换后: {backToModel:F6}°, 误差: {error:F6}°",
                        ExecutionTime = DateTime.Now - startTime
                    });
                }

            }
            catch (Exception ex)
            {
                _testResults.Add(new ValidationResult
                {
                    TestName = "坐标转换测试",
                    Passed = false,
                    Details = $"测试异常: {ex.Message}",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
        }

        #endregion

        #region 绘制功能测试

        private void TestBasicDrawingFunctions()
        {
            var startTime = DateTime.Now;

            try
            {
                var renderer = _viewBase.GetGraphicsRenderer();
                if (renderer == null)
                {
                    _testResults.Add(new ValidationResult
                    {
                        TestName = "图形渲染器获取",
                        Passed = false,
                        Details = "无法获取图形渲染器实例",
                        ExecutionTime = DateTime.Now - startTime
                    });
                    return;
                }

                var testPaint = new SKPaint
                {
                    Color = SKColors.Blue,
                    StrokeWidth = 2.0f,
                    Style = SKPaintStyle.Stroke,
                    IsAntialias = true
                };

                // 测试基础绘制方法
                var tests = new Dictionary<string, System.Action>
                {
                    ["DrawLine"] = () => renderer.DrawLine(Vector2.Zero, new Vector2(100, 100), testPaint),
                    ["DrawCircle"] = () => renderer.DrawCircle(Vector2.Zero, 50, testPaint),
                    ["DrawRectangle"] = () => renderer.DrawRectangle(new Vector2(10, 10), 100, 50, testPaint),
                    ["DrawArc"] = () => renderer.DrawArc(Vector2.Zero, 30, 0, 90, testPaint),
                    ["DrawText"] = () => renderer.DrawText("测试文本", new Vector2(20, 20), testPaint),
                    ["DrawArrowLine"] = () => renderer.DrawArrowLine(Vector2.Zero, new Vector2(80, 0), testPaint, 10),
                    ["DrawGrid"] = () => renderer.DrawGrid(20, testPaint),
                    ["DrawCoordinateAxis"] = () => renderer.DrawCoordinateAxis(Vector2.Zero, 50, testPaint)
                };

                foreach (var test in tests)
                {
                    try
                    {
                        var methodStartTime = DateTime.Now;
                        test.Value();
                        
                        _testResults.Add(new ValidationResult
                        {
                            TestName = $"绘制方法测试 - {test.Key}",
                            Passed = true,
                            Details = "方法调用成功",
                            ExecutionTime = DateTime.Now - methodStartTime
                        });
                    }
                    catch (Exception ex)
                    {
                        _testResults.Add(new ValidationResult
                        {
                            TestName = $"绘制方法测试 - {test.Key}",
                            Passed = false,
                            Details = $"方法调用失败: {ex.Message}",
                            ExecutionTime = DateTime.Now - startTime
                        });
                    }
                }

                testPaint.Dispose();
            }
            catch (Exception ex)
            {
                _testResults.Add(new ValidationResult
                {
                    TestName = "基础绘制功能测试",
                    Passed = false,
                    Details = $"测试异常: {ex.Message}",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
        }

        #endregion

        #region 复杂场景测试

        private void TestComplexScenarios()
        {
            var startTime = DateTime.Now;

            try
            {
                var renderer = _viewBase.GetGraphicsRenderer();
                if (renderer == null) return;

                // 测试1: 大量图元绘制
                var complexStartTime = DateTime.Now;
                var paint = new SKPaint { Color = SKColors.Red, StrokeWidth = 1.0f, Style = SKPaintStyle.Stroke };
                
                for (int i = 0; i < 1000; i++)
                {
                    var start = new Vector2(i % 100, i / 100);
                    var end = new Vector2((i + 1) % 100, (i + 1) / 100);
                    renderer.DrawLine(start, end, paint);
                }

                _testResults.Add(new ValidationResult
                {
                    TestName = "大量图元绘制测试 - 1000条线段",
                    Passed = true,
                    Details = "成功绘制1000条线段",
                    ExecutionTime = DateTime.Now - complexStartTime
                });

                // 测试2: 不同坐标空间混合绘制
                complexStartTime = DateTime.Now;
                renderer.DrawLine(Vector2.Zero, new Vector2(100, 100), paint, CoordinateSpace.Model);
                renderer.DrawLine(Vector2.Zero, new Vector2(100, 100), paint, CoordinateSpace.Viewport);
                renderer.DrawLine(Vector2.Zero, new Vector2(100, 100), paint, CoordinateSpace.Screen);

                _testResults.Add(new ValidationResult
                {
                    TestName = "不同坐标空间混合绘制测试",
                    Passed = true,
                    Details = "成功在不同坐标空间绘制",
                    ExecutionTime = DateTime.Now - complexStartTime
                });

                // 测试3: 渲染状态管理
                complexStartTime = DateTime.Now;
                renderer.BeginRender();
                renderer.SaveState();
                renderer.DrawCircle(Vector2.Zero, 25, paint);
                renderer.RestoreState();
                renderer.EndRender();

                _testResults.Add(new ValidationResult
                {
                    TestName = "渲染状态管理测试",
                    Passed = true,
                    Details = "成功管理渲染状态",
                    ExecutionTime = DateTime.Now - complexStartTime
                });

                paint.Dispose();
            }
            catch (Exception ex)
            {
                _testResults.Add(new ValidationResult
                {
                    TestName = "复杂场景测试",
                    Passed = false,
                    Details = $"测试异常: {ex.Message}",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
        }

        #endregion

        #region 性能测试

        private void TestPerformance()
        {
            var startTime = DateTime.Now;

            try
            {
                var renderer = _viewBase.GetGraphicsRenderer();
                if (renderer == null) return;

                var paint = new SKPaint { Color = SKColors.Green, StrokeWidth = 1.0f };

                // 性能测试1: 单一图元绘制速度
                var perfStartTime = DateTime.Now;
                for (int i = 0; i < 10000; i++)
                {
                    renderer.DrawLine(Vector2.Zero, new Vector2(i % 1000, i / 1000), paint);
                }
                var singleElementTime = DateTime.Now - perfStartTime;

                _testResults.Add(new ValidationResult
                {
                    TestName = "单一图元绘制性能测试 - 10000条线段",
                    Passed = singleElementTime.TotalMilliseconds < 1000, // 期望在1秒内完成
                    Details = $"绘制10000条线段耗时: {singleElementTime.TotalMilliseconds:F2}ms",
                    ExecutionTime = singleElementTime
                });

                // 性能测试2: 批次渲染性能
                perfStartTime = DateTime.Now;
                renderer.BeginRender();
                for (int i = 0; i < 10000; i++)
                {
                    renderer.DrawCircle(new Vector2(i % 100, i / 100), 5, paint);
                }
                renderer.EndRender();
                var batchRenderTime = DateTime.Now - perfStartTime;

                _testResults.Add(new ValidationResult
                {
                    TestName = "批次渲染性能测试 - 10000个圆形",
                    Passed = batchRenderTime.TotalMilliseconds < 1000,
                    Details = $"批次绘制10000个圆形耗时: {batchRenderTime.TotalMilliseconds:F2}ms",
                    ExecutionTime = batchRenderTime
                });

                paint.Dispose();
            }
            catch (Exception ex)
            {
                _testResults.Add(new ValidationResult
                {
                    TestName = "性能测试",
                    Passed = false,
                    Details = $"测试异常: {ex.Message}",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
        }

        #endregion

        #region 内存测试

        private void TestMemoryLeaks()
        {
            var startTime = DateTime.Now;

            try
            {
                var initialMemory = GC.GetTotalMemory(true);

                var renderer = _viewBase.GetGraphicsRenderer();
                if (renderer == null) return;

                // 创建并销毁大量画笔对象
                for (int i = 0; i < 1000; i++)
                {
                    using (var paint = new SKPaint { Color = SKColors.Blue })
                    {
                        renderer.DrawLine(Vector2.Zero, new Vector2(10, 10), paint);
                    }
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(true);
                var memoryIncrease = finalMemory - initialMemory;

                _testResults.Add(new ValidationResult
                {
                    TestName = "内存泄漏测试",
                    Passed = memoryIncrease < 1024 * 1024, // 期望内存增长小于1MB
                    Details = $"内存增长: {memoryIncrease / 1024.0:F2} KB",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
            catch (Exception ex)
            {
                _testResults.Add(new ValidationResult
                {
                    TestName = "内存泄漏测试",
                    Passed = false,
                    Details = $"测试异常: {ex.Message}",
                    ExecutionTime = DateTime.Now - startTime
                });
            }
        }

        #endregion
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public string TestName { get; set; }
        public bool Passed { get; set; }
        public string Details { get; set; }
        public TimeSpan ExecutionTime { get; set; }
    }

    /// <summary>
    /// 验证报告
    /// </summary>
    public class ValidationReport
    {
        private readonly List<ValidationResult> _results;

        public ValidationReport(List<ValidationResult> results)
        {
            _results = new List<ValidationResult>(results);
        }

        public int TotalTests => _results.Count;
        public int PassedTests => _results.FindAll(r => r.Passed).Count;
        public int FailedTests => TotalTests - PassedTests;
        public double PassRate => TotalTests > 0 ? (double)PassedTests / TotalTests * 100.0 : 0.0;

        public TimeSpan TotalExecutionTime
        {
            get
            {
                var total = TimeSpan.Zero;
                foreach (var result in _results)
                {
                    total = total.Add(result.ExecutionTime);
                }
                return total;
            }
        }

        public IEnumerable<ValidationResult> Results => _results;
        public IEnumerable<ValidationResult> FailedResults => _results.FindAll(r => !r.Passed);

        public override string ToString()
        {
            var sb = new System.Text.StringBuilder();
            sb.AppendLine("=== 图形系统验证报告 ===");
            sb.AppendLine($"总测试数: {TotalTests}");
            sb.AppendLine($"通过: {PassedTests}");
            sb.AppendLine($"失败: {FailedTests}");
            sb.AppendLine($"通过率: {PassRate:F1}%");
            sb.AppendLine($"总执行时间: {TotalExecutionTime.TotalMilliseconds:F2}ms");
            sb.AppendLine();

            if (FailedTests > 0)
            {
                sb.AppendLine("=== 失败的测试 ===");
                foreach (var failed in FailedResults)
                {
                    sb.AppendLine($"❌ {failed.TestName}");
                    sb.AppendLine($"   详情: {failed.Details}");
                    sb.AppendLine($"   执行时间: {failed.ExecutionTime.TotalMilliseconds:F2}ms");
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }
    }
} 