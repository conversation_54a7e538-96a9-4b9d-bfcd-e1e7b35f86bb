using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 缩放命令
    /// 实现专业CAD级别的缩放功能，支持统一缩放和非统一缩放
    /// </summary>
    public class ScaleCmd : Command
    {
        private ScaleState _currentState = ScaleState.SelectEntities;
        private List<EntityBase> _selectedEntities = new List<EntityBase>();
        private Vector2 _basePoint;
        private float _scaleFactor = 1.0f;
        private Vector2 _scaleFactorXY = Vector2.One;
        private List<EntityBase> _previewEntities = new List<EntityBase>();
        private ScaleOptions _options = new ScaleOptions();
        
        // 视觉样式
        private SKPaint _selectedPaint;
        private SKPaint _previewPaint;
        private SKPaint _basePointPaint;
        private SKPaint _referencePaint;
        
        public override string Name => "SCALE";
        public override string Description => "缩放选定的对象";
        
        public ScaleCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 0, 255, 0), // 半透明绿色
                IsAntialias = true
            };
            
            _basePointPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Red,
                IsAntialias = true
            };
            
            _referencePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 0.5f,
                Color = SKColors.Gray,
                PathEffect = SKPathEffect.CreateDash(new float[] { 3, 3 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = ScaleState.SelectEntities;
            _selectedEntities.Clear();
            _previewEntities.Clear();
            
            _viewer.Document.Prompt = "选择要缩放的对象：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case ScaleState.SelectEntities:
                    HandleEntitySelection(currentPoint);
                    break;
                    
                case ScaleState.SpecifyBasePoint:
                    HandleBasePointSelection(currentPoint);
                    break;
                    
                case ScaleState.SpecifyScaleFactor:
                    HandleScaleFactorInput(currentPoint);
                    break;
                    
                case ScaleState.SpecifySecondPoint:
                    HandleSecondPointInput(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case ScaleState.SpecifyScaleFactor:
                case ScaleState.SpecifySecondPoint:
                    UpdateScalePreview(currentPoint);
                    _viewer.RepaintCanvas();
                    break;
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == ScaleState.SelectEntities && _selectedEntities.Count > 0)
                    {
                        StartBasePointSelection();
                    }
                    else if (_currentState == ScaleState.SpecifyScaleFactor || _currentState == ScaleState.SpecifySecondPoint)
                    {
                        if (_viewer._dynamicInputer?.GetResultNumber() != null)
                        {
                            _scaleFactor = Math.Max(0.001f, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                            _viewer._dynamicInputer.EndInput();
                        }
                        CompleteScale();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.A:
                    if (_currentState == ScaleState.SelectEntities)
                    {
                        SelectAllEntities();
                    }
                    break;
                    
                case Keys.C:
                    // 切换复制模式
                    _options.Copy = !_options.Copy;
                    _viewer.Document.Prompt += $" [复制: {(_options.Copy ? "开" : "关")}]";
                    break;
                    
                case Keys.R:
                    // 参考缩放
                    if (_currentState == ScaleState.SpecifyScaleFactor)
                    {
                        _currentState = ScaleState.SpecifyReferenceLength;
                        _viewer.Document.Prompt = "指定参考长度：";
                    }
                    break;
                    
                case Keys.U:
                    // 切换非统一缩放
                    _options.NonUniform = !_options.NonUniform;
                    _viewer.Document.Prompt += $" [非统一缩放: {(_options.NonUniform ? "开" : "关")}]";
                    break;
            }
        }
        
        private void HandleEntitySelection(Vector2 point)
        {
            var hitEntity = FindSelectableEntity(point);
            
            if (hitEntity != null && !_selectedEntities.Contains(hitEntity))
            {
                _selectedEntities.Add(hitEntity);
                _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，继续选择或按Enter确认：";
            }
        }
        
        private void HandleBasePointSelection(Vector2 point)
        {
            _basePoint = point;
            _currentState = ScaleState.SpecifyScaleFactor;
            _viewer.Document.Prompt = $"指定缩放因子或 [R]参考：";
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.StartInput(point, DynInputStatus.WaitForNumber);
            }
        }
        
        private void HandleScaleFactorInput(Vector2 point)
        {
            if (_viewer._dynamicInputer?.GetResultNumber() != null)
            {
                _scaleFactor = Math.Max(0.001f, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                _viewer._dynamicInputer.EndInput();
                CompleteScale();
            }
            else
            {
                // 通过鼠标位置计算缩放因子
                var distance = Vector2.Distance(_basePoint, point);
                var referenceDistance = CalculateReferenceDistance();
                
                if (referenceDistance > 0.001f)
                {
                    _scaleFactor = distance / referenceDistance;
                    CompleteScale();
                }
            }
        }
        
        private void HandleSecondPointInput(Vector2 point)
        {
            var distance = Vector2.Distance(_basePoint, point);
            var referenceDistance = CalculateReferenceDistance();
            
            if (referenceDistance > 0.001f)
            {
                _scaleFactor = distance / referenceDistance;
                CompleteScale();
            }
        }
        
        private void StartBasePointSelection()
        {
            _currentState = ScaleState.SpecifyBasePoint;
            _viewer.Document.Prompt = "指定基点：";
        }
        
        private void SelectAllEntities()
        {
            _selectedEntities.Clear();
            _selectedEntities.AddRange(_viewer.Document.ActiveLayer.Children);
            _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，按Enter确认：";
        }
        
        private void UpdateScalePreview(Vector2 currentPoint)
        {
            _previewEntities.Clear();
            
            // 计算当前缩放因子
            float currentScaleFactor = _scaleFactor;
            
            if (_currentState == ScaleState.SpecifyScaleFactor)
            {
                var distance = Vector2.Distance(_basePoint, currentPoint);
                var referenceDistance = CalculateReferenceDistance();
                
                if (referenceDistance > 0.001f)
                {
                    currentScaleFactor = distance / referenceDistance;
                }
            }
            
            // 生成预览实体
            var transform = CreateScaleTransform(_basePoint, currentScaleFactor);
            
            foreach (var entity in _selectedEntities)
            {
                var scaledEntity = TransformEntity(entity, transform);
                if (scaledEntity != null)
                {
                    _previewEntities.Add(scaledEntity);
                }
            }
        }
        
        private float CalculateReferenceDistance()
        {
            if (_selectedEntities.Count == 0) return 100.0f;
            
            // 计算选择集的边界框对角线长度作为参考
            var bounds = CalculateSelectionBounds();
            if (bounds != null)
            {
                return Math.Max(bounds.Width, bounds.Height);
            }
            
            return 100.0f;
        }
        
        private BoundingBox CalculateSelectionBounds()
        {
            if (_selectedEntities.Count == 0) return null;
            
            var minX = float.MaxValue;
            var minY = float.MaxValue;
            var maxX = float.MinValue;
            var maxY = float.MinValue;
            
            foreach (var entity in _selectedEntities)
            {
                var bounds = entity.BoundingBox;
                if (bounds != null && !bounds.IsEmpty)
                {
                    minX = Math.Min(minX, bounds.MinX);
                    minY = Math.Min(minY, bounds.MinY);
                    maxX = Math.Max(maxX, bounds.MaxX);
                    maxY = Math.Max(maxY, bounds.MaxY);
                }
            }
            
            if (minX != float.MaxValue)
            {
                return new BoundingBox(minX, minY, maxX, maxY);
            }
            
            return null;
        }
        
        private Matrix3x2 CreateScaleTransform(Vector2 basePoint, float scaleFactor)
        {
            if (_options.NonUniform)
            {
                return Matrix3x2.CreateTranslation(-basePoint) *
                       Matrix3x2.CreateScale(_scaleFactorXY) *
                       Matrix3x2.CreateTranslation(basePoint);
            }
            else
            {
                return Matrix3x2.CreateTranslation(-basePoint) *
                       Matrix3x2.CreateScale(scaleFactor) *
                       Matrix3x2.CreateTranslation(basePoint);
            }
        }
        
        private EntityBase TransformEntity(EntityBase entity, Matrix3x2 transform)
        {
            switch (entity)
            {
                case EntityLine line:
                    return new EntityLine
                    {
                        StartPoint = Vector2.Transform(line.StartPoint, transform),
                        EndPoint = Vector2.Transform(line.EndPoint, transform),
                        LineType = line.LineType,
                        LineWeight = line.LineWeight,
                        Color = line.Color
                    };
                    
                case EntityCircle circle:
                    var newCenter = Vector2.Transform(circle.Center, transform);
                    var newRadius = circle.Radius * _scaleFactor; // 简化处理
                    return new EntityCircle
                    {
                        Center = newCenter,
                        Radius = newRadius,
                        LineType = circle.LineType,
                        LineWeight = circle.LineWeight,
                        Color = circle.Color
                    };
                    
                case EntityArc arc:
                    var arcCenter = Vector2.Transform(arc.Center, transform);
                    var arcRadius = arc.Radius * _scaleFactor;
                    return new EntityArc
                    {
                        Center = arcCenter,
                        Radius = arcRadius,
                        StartAngle = arc.StartAngle,
                        EndAngle = arc.EndAngle,
                        LineType = arc.LineType,
                        LineWeight = arc.LineWeight,
                        Color = arc.Color
                    };
                    
                case EntityRectangle rect:
                    return new EntityRectangle
                    {
                        Corner1 = Vector2.Transform(rect.Corner1, transform),
                        Corner2 = Vector2.Transform(rect.Corner2, transform),
                        LineType = rect.LineType,
                        LineWeight = rect.LineWeight,
                        Color = rect.Color
                    };
                    
                default:
                    return null;
            }
        }
        
        private void CompleteScale()
        {
            if (_selectedEntities.Count == 0 || _scaleFactor <= 0) return;
            
            try
            {
                var transform = CreateScaleTransform(_basePoint, _scaleFactor);
                
                if (_options.Copy)
                {
                    // 复制模式：创建新实体
                    foreach (var entity in _selectedEntities)
                    {
                        var scaledEntity = TransformEntity(entity, transform);
                        if (scaledEntity != null)
                        {
                            _viewer.Document.ActiveLayer.Children.Add(scaledEntity);
                        }
                    }
                }
                else
                {
                    // 移动模式：修改原实体
                    for (int i = 0; i < _selectedEntities.Count; i++)
                    {
                        var entity = _selectedEntities[i];
                        var scaledEntity = TransformEntity(entity, transform);
                        
                        if (scaledEntity != null)
                        {
                            var index = _viewer.Document.ActiveLayer.Children.IndexOf(entity);
                            if (index >= 0)
                            {
                                _viewer.Document.ActiveLayer.Children[index] = scaledEntity;
                            }
                        }
                    }
                }
                
                _viewer.RepaintCanvas();
                _viewer.Document.Prompt = $"缩放完成，缩放因子: {_scaleFactor:F3}";
                Finish();
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"缩放失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Scale error: {ex.Message}");
            }
        }
        
        private EntityBase FindSelectableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制选中实体
            foreach (var entity in _selectedEntities)
            {
                RenderSelectedEntity(canvas, entity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制基点
            if (_currentState != ScaleState.SelectEntities && _currentState != ScaleState.SpecifyBasePoint)
            {
                canvas.DrawCircle(_basePoint.X, _basePoint.Y, 4, _basePointPaint);
                
                // 绘制参考线
                var bounds = CalculateSelectionBounds();
                if (bounds != null)
                {
                    var referenceDistance = CalculateReferenceDistance();
                    canvas.DrawCircle(_basePoint.X, _basePoint.Y, referenceDistance, _referencePaint);
                }
            }
        }
        
        private void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _selectedPaint);
            }
        }
        
        private void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityCircle circle:
                    canvas.DrawCircle(circle.Center.X, circle.Center.Y, circle.Radius, _previewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _previewPaint);
                    break;
                    
                case EntityRectangle rectangle:
                    canvas.DrawRect(rectangle.Corner1.X, rectangle.Corner1.Y,
                                  rectangle.Width, rectangle.Height, _previewPaint);
                    break;
            }
        }
        
        public override void Cancel()
        {
            _selectedEntities.Clear();
            _previewEntities.Clear();
            _currentState = ScaleState.SelectEntities;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _selectedPaint?.Dispose();
                _previewPaint?.Dispose();
                _basePointPaint?.Dispose();
                _referencePaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    /// <summary>
    /// 旋转命令
    /// </summary>
    public class RotateCmd : Command
    {
        private RotateState _currentState = RotateState.SelectEntities;
        private List<EntityBase> _selectedEntities = new List<EntityBase>();
        private Vector2 _basePoint;
        private float _rotationAngle = 0.0f;
        private Vector2 _referencePoint;
        private List<EntityBase> _previewEntities = new List<EntityBase>();
        private RotateOptions _options = new RotateOptions();
        
        // 视觉样式
        private SKPaint _selectedPaint;
        private SKPaint _previewPaint;
        private SKPaint _basePointPaint;
        private SKPaint _anglePaint;
        
        public override string Name => "ROTATE";
        public override string Description => "旋转选定的对象";
        
        public RotateCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 255, 0, 0), // 半透明红色
                IsAntialias = true
            };
            
            _basePointPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Blue,
                IsAntialias = true
            };
            
            _anglePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColors.Cyan,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = RotateState.SelectEntities;
            _selectedEntities.Clear();
            _previewEntities.Clear();
            
            _viewer.Document.Prompt = "选择要旋转的对象：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case RotateState.SelectEntities:
                    HandleEntitySelection(currentPoint);
                    break;
                    
                case RotateState.SpecifyBasePoint:
                    HandleBasePointSelection(currentPoint);
                    break;
                    
                case RotateState.SpecifyRotationAngle:
                    HandleRotationAngleInput(currentPoint);
                    break;
                    
                case RotateState.SpecifySecondPoint:
                    HandleSecondPointInput(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case RotateState.SpecifyRotationAngle:
                case RotateState.SpecifySecondPoint:
                    UpdateRotatePreview(currentPoint);
                    _viewer.RepaintCanvas();
                    break;
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == RotateState.SelectEntities && _selectedEntities.Count > 0)
                    {
                        StartBasePointSelection();
                    }
                    else if (_currentState == RotateState.SpecifyRotationAngle || _currentState == RotateState.SpecifySecondPoint)
                    {
                        if (_viewer._dynamicInputer?.GetResultNumber() != null)
                        {
                            _rotationAngle = (float)_viewer._dynamicInputer.GetResultNumber().Value;
                            _viewer._dynamicInputer.EndInput();
                        }
                        CompleteRotation();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.A:
                    if (_currentState == RotateState.SelectEntities)
                    {
                        SelectAllEntities();
                    }
                    break;
                    
                case Keys.C:
                    // 切换复制模式
                    _options.Copy = !_options.Copy;
                    _viewer.Document.Prompt += $" [复制: {(_options.Copy ? "开" : "关")}]";
                    break;
                    
                case Keys.R:
                    // 参考角度
                    if (_currentState == RotateState.SpecifyRotationAngle)
                    {
                        _currentState = RotateState.SpecifyReferencePoint;
                        _viewer.Document.Prompt = "指定参考点：";
                    }
                    break;
            }
        }
        
        private void HandleEntitySelection(Vector2 point)
        {
            var hitEntity = FindSelectableEntity(point);
            
            if (hitEntity != null && !_selectedEntities.Contains(hitEntity))
            {
                _selectedEntities.Add(hitEntity);
                _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，继续选择或按Enter确认：";
            }
        }
        
        private void HandleBasePointSelection(Vector2 point)
        {
            _basePoint = point;
            _currentState = RotateState.SpecifyRotationAngle;
            _viewer.Document.Prompt = $"指定旋转角度或 [R]参考：";
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.StartInput(point, DynInputStatus.WaitForNumber);
            }
        }
        
        private void HandleRotationAngleInput(Vector2 point)
        {
            if (_viewer._dynamicInputer?.GetResultNumber() != null)
            {
                _rotationAngle = (float)_viewer._dynamicInputer.GetResultNumber().Value;
                _viewer._dynamicInputer.EndInput();
                CompleteRotation();
            }
            else
            {
                // 通过鼠标位置计算角度
                var angle = CalculateAngle(_basePoint, point);
                _rotationAngle = angle;
                CompleteRotation();
            }
        }
        
        private void HandleSecondPointInput(Vector2 point)
        {
            var angle1 = CalculateAngle(_basePoint, _referencePoint);
            var angle2 = CalculateAngle(_basePoint, point);
            _rotationAngle = angle2 - angle1;
            CompleteRotation();
        }
        
        private void StartBasePointSelection()
        {
            _currentState = RotateState.SpecifyBasePoint;
            _viewer.Document.Prompt = "指定基点：";
        }
        
        private void SelectAllEntities()
        {
            _selectedEntities.Clear();
            _selectedEntities.AddRange(_viewer.Document.ActiveLayer.Children);
            _viewer.Document.Prompt = $"已选择 {_selectedEntities.Count} 个对象，按Enter确认：";
        }
        
        private void UpdateRotatePreview(Vector2 currentPoint)
        {
            _previewEntities.Clear();
            
            // 计算当前旋转角度
            float currentAngle = _rotationAngle;
            
            if (_currentState == RotateState.SpecifyRotationAngle)
            {
                currentAngle = CalculateAngle(_basePoint, currentPoint);
            }
            else if (_currentState == RotateState.SpecifySecondPoint)
            {
                var angle1 = CalculateAngle(_basePoint, _referencePoint);
                var angle2 = CalculateAngle(_basePoint, currentPoint);
                currentAngle = angle2 - angle1;
            }
            
            // 生成预览实体
            var transform = CreateRotationTransform(_basePoint, currentAngle);
            
            foreach (var entity in _selectedEntities)
            {
                var rotatedEntity = TransformEntity(entity, transform);
                if (rotatedEntity != null)
                {
                    _previewEntities.Add(rotatedEntity);
                }
            }
        }
        
        private float CalculateAngle(Vector2 center, Vector2 point)
        {
            var delta = point - center;
            var angleRad = Math.Atan2(delta.Y, delta.X);
            return (float)(angleRad * 180.0 / Math.PI);
        }
        
        private Matrix3x2 CreateRotationTransform(Vector2 basePoint, float angleDegrees)
        {
            var angleRadians = angleDegrees * Math.PI / 180.0;
            
            return Matrix3x2.CreateTranslation(-basePoint) *
                   Matrix3x2.CreateRotation((float)angleRadians) *
                   Matrix3x2.CreateTranslation(basePoint);
        }
        
        private EntityBase TransformEntity(EntityBase entity, Matrix3x2 transform)
        {
            switch (entity)
            {
                case EntityLine line:
                    return new EntityLine
                    {
                        StartPoint = Vector2.Transform(line.StartPoint, transform),
                        EndPoint = Vector2.Transform(line.EndPoint, transform),
                        LineType = line.LineType,
                        LineWeight = line.LineWeight,
                        Color = line.Color
                    };
                    
                case EntityCircle circle:
                    return new EntityCircle
                    {
                        Center = Vector2.Transform(circle.Center, transform),
                        Radius = circle.Radius,
                        LineType = circle.LineType,
                        LineWeight = circle.LineWeight,
                        Color = circle.Color
                    };
                    
                case EntityArc arc:
                    var newCenter = Vector2.Transform(arc.Center, transform);
                    // 弧的角度也需要相应旋转
                    var rotationAngle = _rotationAngle;
                    return new EntityArc
                    {
                        Center = newCenter,
                        Radius = arc.Radius,
                        StartAngle = arc.StartAngle + rotationAngle,
                        EndAngle = arc.EndAngle + rotationAngle,
                        LineType = arc.LineType,
                        LineWeight = arc.LineWeight,
                        Color = arc.Color
                    };
                    
                case EntityRectangle rect:
                    return new EntityRectangle
                    {
                        Corner1 = Vector2.Transform(rect.Corner1, transform),
                        Corner2 = Vector2.Transform(rect.Corner2, transform),
                        LineType = rect.LineType,
                        LineWeight = rect.LineWeight,
                        Color = rect.Color
                    };
                    
                default:
                    return null;
            }
        }
        
        private void CompleteRotation()
        {
            if (_selectedEntities.Count == 0) return;
            
            try
            {
                var transform = CreateRotationTransform(_basePoint, _rotationAngle);
                
                if (_options.Copy)
                {
                    // 复制模式：创建新实体
                    foreach (var entity in _selectedEntities)
                    {
                        var rotatedEntity = TransformEntity(entity, transform);
                        if (rotatedEntity != null)
                        {
                            _viewer.Document.ActiveLayer.Children.Add(rotatedEntity);
                        }
                    }
                }
                else
                {
                    // 移动模式：修改原实体
                    for (int i = 0; i < _selectedEntities.Count; i++)
                    {
                        var entity = _selectedEntities[i];
                        var rotatedEntity = TransformEntity(entity, transform);
                        
                        if (rotatedEntity != null)
                        {
                            var index = _viewer.Document.ActiveLayer.Children.IndexOf(entity);
                            if (index >= 0)
                            {
                                _viewer.Document.ActiveLayer.Children[index] = rotatedEntity;
                            }
                        }
                    }
                }
                
                _viewer.RepaintCanvas();
                _viewer.Document.Prompt = $"旋转完成，角度: {_rotationAngle:F1}°";
                Finish();
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"旋转失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Rotate error: {ex.Message}");
            }
        }
        
        private EntityBase FindSelectableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制选中实体
            foreach (var entity in _selectedEntities)
            {
                RenderSelectedEntity(canvas, entity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制基点和角度指示
            if (_currentState != RotateState.SelectEntities && _currentState != RotateState.SpecifyBasePoint)
            {
                canvas.DrawCircle(_basePoint.X, _basePoint.Y, 4, _basePointPaint);
                
                // 绘制角度线
                if (_currentState == RotateState.SpecifyRotationAngle || _currentState == RotateState.SpecifySecondPoint)
                {
                    RenderAngleIndicator(canvas);
                }
            }
        }
        
        private void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _selectedPaint);
            }
        }
        
        private void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityCircle circle:
                    canvas.DrawCircle(circle.Center.X, circle.Center.Y, circle.Radius, _previewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _previewPaint);
                    break;
                    
                case EntityRectangle rectangle:
                    canvas.DrawRect(rectangle.Corner1.X, rectangle.Corner1.Y,
                                  rectangle.Width, rectangle.Height, _previewPaint);
                    break;
            }
        }
        
        private void RenderAngleIndicator(SKCanvas canvas)
        {
            // 绘制角度指示弧
            var radius = 50.0f;
            var rect = new SKRect(_basePoint.X - radius, _basePoint.Y - radius,
                                 _basePoint.X + radius, _basePoint.Y + radius);
            
            canvas.DrawArc(rect, 0, _rotationAngle, false, _anglePaint);
            
            // 绘制角度文本
            var angleText = $"{_rotationAngle:F1}°";
            var textPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = 12,
                Color = SKColors.Cyan,
                IsAntialias = true
            };
            
            canvas.DrawText(angleText, _basePoint.X + radius + 5, _basePoint.Y, textPaint);
            textPaint.Dispose();
        }
        
        public override void Cancel()
        {
            _selectedEntities.Clear();
            _previewEntities.Clear();
            _currentState = RotateState.SelectEntities;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _selectedPaint?.Dispose();
                _previewPaint?.Dispose();
                _basePointPaint?.Dispose();
                _anglePaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    #region 枚举和数据结构
    
    public enum ScaleState
    {
        SelectEntities,
        SpecifyBasePoint,
        SpecifyScaleFactor,
        SpecifySecondPoint,
        SpecifyReferenceLength
    }
    
    public enum RotateState
    {
        SelectEntities,
        SpecifyBasePoint,
        SpecifyRotationAngle,
        SpecifySecondPoint,
        SpecifyReferencePoint
    }
    
    public class ScaleOptions
    {
        public bool Copy { get; set; } = false;
        public bool NonUniform { get; set; } = false;
        public bool Reference { get; set; } = false;
    }
    
    public class RotateOptions
    {
        public bool Copy { get; set; } = false;
        public bool Reference { get; set; } = false;
    }
    
    #endregion
} 