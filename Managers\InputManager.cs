using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Enums;
using McLaser.EditViewerSk.Handlers;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Renderers;
using McLaser.EditViewerSk.Services;
using System;
using System.Collections.Generic;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 输入管理器
    /// 统一管理所有鼠标和键盘输入，替代原来的MgrIndicator
    /// </summary>
    public class InputManager
    {
        private readonly ViewBase _view;
        private readonly ICoordinateService _coordinateService;
        private readonly List<MouseInputHandler> _handlers;
        private InteractionState _currentState;

        // 各种处理器
        private readonly SelectionHandler _selectionHandler;
        private readonly SelectionRenderer _selectionRenderer;

        public InteractionState CurrentState => _currentState;

        public InputManager(ViewBase view)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _coordinateService = new CoordinateService(view);
            _handlers = new List<MouseInputHandler>();
            _currentState = InteractionState.Idle;

            // 初始化渲染器和处理器
            _selectionRenderer = new SelectionRenderer(_coordinateService);
            _selectionHandler = new SelectionHandler(_selectionRenderer);

            // 注册处理器
            RegisterHandler(_selectionHandler);

            // 建立处理链
            BuildHandlerChain();
        }

        /// <summary>
        /// 注册事件处理器
        /// </summary>
        public void RegisterHandler(MouseInputHandler handler)
        {
            _handlers.Add(handler);
            _handlers.Sort((a, b) => b.Priority.CompareTo(a.Priority)); // 按优先级排序
            BuildHandlerChain();
        }

        /// <summary>
        /// 构建处理器链
        /// </summary>
        private void BuildHandlerChain()
        {
            for (int i = 0; i < _handlers.Count - 1; i++)
            {
                _handlers[i].SetNext(_handlers[i + 1]);
            }
        }

        /// <summary>
        /// 处理鼠标按下事件
        /// </summary>
        public Command OnMouseDown(MouseEventArgs e)
        {
            var context = CreateMouseEventContext(e, MouseEventType.Down);
            var result = ProcessMouseEvent(context);
            return result?.Command;
        }

        /// <summary>
        /// 处理鼠标移动事件
        /// </summary>
        public void OnMouseMove(MouseEventArgs e)
        {
            var context = CreateMouseEventContext(e, MouseEventType.Move);
            ProcessMouseEvent(context);
        }

        /// <summary>
        /// 处理鼠标释放事件
        /// </summary>
        public void OnMouseUp(MouseEventArgs e)
        {
            var context = CreateMouseEventContext(e, MouseEventType.Up);
            ProcessMouseEvent(context);
        }

        /// <summary>
        /// 处理鼠标双击事件
        /// </summary>
        public void OnMouseDoubleClick(MouseEventArgs e)
        {
            var context = CreateMouseEventContext(e, MouseEventType.DoubleClick);
            ProcessMouseEvent(context);
        }

        /// <summary>
        /// 创建鼠标事件上下文
        /// </summary>
        private MouseEventContext CreateMouseEventContext(MouseEventArgs e, MouseEventType eventType)
        {
            var screenPosition = new Vector2(e.Location.X, e.Location.Y);
            var canvasPosition = _coordinateService.ScreenToCanvas(screenPosition);
            var modelPosition = _coordinateService.CanvasToModel(canvasPosition);

            return new MouseEventContext
            {
                EventArgs = e,
                EventType = eventType,
                CurrentState = _currentState,
                Position = canvasPosition,
                ModelPosition = modelPosition,
                View = _view,
                CoordinateService = _coordinateService,
                IsHandled = false
            };
        }

        /// <summary>
        /// 处理鼠标事件
        /// </summary>
        private MouseEventResult ProcessMouseEvent(MouseEventContext context)
        {
            if (_handlers.Count == 0)
                return MouseEventResult.Unhandled;

            // 通过处理器链处理事件
            var result = _handlers[0].Handle(context);

            // 如果状态发生变化，更新当前状态
            if (result.NewState.HasValue)
            {
                _currentState = result.NewState.Value;
            }

            return result;
        }

        /// <summary>
        /// 渲染交互元素
        /// </summary>
        public void OnPaint(ViewBase view)
        {
            // 渲染选择框
            _selectionHandler?.Render(view);

            // 可以在这里添加其他交互元素的渲染
        }

        /// <summary>
        /// 处理键盘按下事件
        /// </summary>
        public bool OnKeyDown(KeyEventArgs e)
        {
            // 处理键盘事件
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    // ESC键取消当前操作
                    _currentState = InteractionState.Idle;
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 处理键盘释放事件
        /// </summary>
        public bool OnKeyUp(KeyEventArgs e)
        {
            // 处理键盘释放事件
            return false;
        }

        /// <summary>
        /// 获取当前捕捉点（兼容原有接口）
        /// </summary>
        public Vector2 CurrentSnapPoint { get; private set; }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            _handlers.Clear();
        }
    }
} 