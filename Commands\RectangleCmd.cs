﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class RectangleCmd : DrawCmd
    {
        private EntityRectangle _rectangle = null;
        private DocumentBase doc;

        public RectangleCmd(DocumentBase doc)
        {
            this.doc = doc;
        }



        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityRectangle[1] { _rectangle }; }
        }

        /// <summary>
        /// 步骤
        /// </summary>
        private Step _step = Step.Step1_SpecifyStartPoint;
        private enum Step
        {
            Step1_SpecifyStartPoint = 1,
            Step2_SpecifyEndPoint = 2,
        }

        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyStartPoint;
            this.pointer.Mode = IndicatorMode.Locate;
            this.pointer.Document.Prompt = "指定矩形的第一个角点:";
        }

        protected override void Commit()
        {
            if (this.newEntities != null && this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            try
            {
                switch (_step)
                {
                    case Step.Step1_SpecifyStartPoint:
                        if (e.Button == MouseButtons.Left)
                        {
                            _rectangle = new EntityRectangle();
                            _rectangle.StartPoint = this.pointer.CurrentSnapPoint;
                            _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                            _rectangle.LayerId = doc.ActiveLayer?.Id ?? 0;
                            _rectangle.Color = doc.ActiveLayer?.Color ?? SkiaSharp.SKColors.White;

                            this.newEntities.Add(_rectangle);
                            _step = Step.Step2_SpecifyEndPoint;
                            this.pointer.Document.Prompt = "指定矩形的对角点:";
                        }
                        else if (e.Button == MouseButtons.Right)
                        {
                            _mgr.CancelCurrentCommand();
                        }
                        break;
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"创建矩形时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"RectangleCmd.OnMouseDown error: {ex}");
                _mgr.CancelCurrentCommand();
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecifyEndPoint)
            {
                _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                _mgr.FinishCurrentCommand();
            }
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecifyEndPoint)
            {
                _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                //_viewer.RepaintCanvas();
            }

            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {

            if (_rectangle != null)
            {
                ViewBase viewer = _mgr.Viewer as ViewBase;
                _rectangle.Render(viewer);
            }
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }
    }
}
