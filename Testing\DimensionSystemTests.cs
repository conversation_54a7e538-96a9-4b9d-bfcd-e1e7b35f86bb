using System;
using System.Numerics;
using System.Collections.Generic;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Managers;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;

namespace McLaser.EditViewerSk.Testing
{
    /// <summary>
    /// 标注系统测试类
    /// </summary>
    public class DimensionSystemTests
    {
        private DocumentBase testDocument;

        public DimensionSystemTests()
        {
            testDocument = new DocumentBase();
        }

        #region 标注实体测试

        /// <summary>
        /// 测试线性标注创建和基本功能
        /// </summary>
        public bool TestLinearDimension()
        {
            try
            {
                var linear = new EntityLinearDimension(
                    new Vector2(0, 0),
                    new Vector2(10, 0),
                    new Vector2(5, 5)
                );

                // 验证基本属性
                if (Math.Abs(linear.ActualMeasurement - 10.0) > 1e-10)
                    return false;

                // 验证几何计算
                var geometry = linear.GetDimensionGeometry();
                if (geometry?.DimensionLine == null)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"线性标注测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试对齐标注
        /// </summary>
        public bool TestAlignedDimension()
        {
            try
            {
                var aligned = new EntityAlignedDimension(
                    new Vector2(0, 0),
                    new Vector2(3, 4),
                    3.0
                );

                // 验证测量值（应该是5，3-4-5直角三角形）
                if (Math.Abs(aligned.ActualMeasurement - 5.0) > 1e-10)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"对齐标注测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试半径标注
        /// </summary>
        public bool TestRadiusDimension()
        {
            try
            {
                var radius = new EntityRadiusDimension(
                    new Vector2(0, 0),
                    10.0,
                    new Vector2(10, 0)
                );

                if (Math.Abs(radius.ActualMeasurement - 10.0) > 1e-10)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"半径标注测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试直径标注
        /// </summary>
        public bool TestDiameterDimension()
        {
            try
            {
                var diameter = new EntityDiameterDimension(
                    new Vector2(0, 0),
                    20.0,
                    new Vector2(10, 0)
                );

                if (Math.Abs(diameter.ActualMeasurement - 20.0) > 1e-10)
                    return false;

                if (Math.Abs(diameter.Radius - 10.0) > 1e-10)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"直径标注测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试角度标注
        /// </summary>
        public bool TestAngularDimension()
        {
            try
            {
                var angular = new EntityAngularDimension(
                    Vector2.Zero,
                    new Vector2(1, 0),
                    new Vector2(0, 1)
                );

                // 应该是90度（π/2弧度）
                if (Math.Abs(angular.ActualMeasurement - Math.PI / 2) > 1e-10)
                    return false;

                if (Math.Abs(angular.AngleDegrees - 90.0) > 1e-10)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"角度标注测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试引线标注
        /// </summary>
        public bool TestLeaderDimension()
        {
            try
            {
                var points = new List<Vector2>
                {
                    Vector2.Zero,
                    new Vector2(5, 5),
                    new Vector2(10, 5)
                };

                var leader = new EntityLeaderDimension(points, "测试注释");

                if (leader.LeaderPoints.Count != 3)
                    return false;

                if (leader.AnnotationText != "测试注释")
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"引线标注测试失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 标注样式管理测试

        /// <summary>
        /// 测试标注样式管理器
        /// </summary>
        public bool TestDimensionStyleManager()
        {
            try
            {
                var manager = DimensionStyleManager.Instance;

                // 验证默认样式
                if (manager.Styles.Count < 1)
                    return false;

                if (manager.CurrentStyle == null)
                    return false;

                // 添加新样式
                var customStyle = new DimensionStyle
                {
                    Name = "测试样式",
                    TextHeight = 5.0,
                    ArrowSize = 5.0
                };

                var originalCount = manager.Styles.Count;
                manager.AddStyle(customStyle);

                if (manager.Styles.Count != originalCount + 1)
                    return false;

                // 查找样式
                var found = manager.FindStyle("测试样式");
                if (found == null || found.TextHeight != 5.0)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"标注样式管理器测试失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 关联更新测试

        /// <summary>
        /// 测试标注关联更新机制
        /// </summary>
        public bool TestDimensionAssociation()
        {
            try
            {
                var manager = DimensionAssociationManager.Instance;

                // 创建测试实体
                var line = new EntityLine(Vector2.Zero, new Vector2(10, 0));
                var dimension = new EntityLinearDimension(Vector2.Zero, new Vector2(10, 0), new Vector2(5, 5));

                // 创建关联
                manager.CreateAssociation(dimension, line);

                // 验证关联
                var associatedEntities = manager.GetAssociatedEntities(dimension);
                var associatedDimensions = manager.GetAssociatedDimensions(line);

                if (!associatedEntities.Contains(line))
                    return false;

                if (!associatedDimensions.Contains(dimension))
                    return false;

                // 测试自动更新
                var originalMeasurement = dimension.ActualMeasurement;
                line.EndPoint = new Vector2(20, 0); // 改变线长度

                manager.UpdateAssociatedDimensions(line);

                if (Math.Abs(dimension.ActualMeasurement - originalMeasurement) < 1e-10)
                    return false; // 应该已经更新了

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"标注关联测试失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 综合测试

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public TestResults RunAllTests()
        {
            var results = new TestResults();

            results.LinearDimensionTest = TestLinearDimension();
            results.AlignedDimensionTest = TestAlignedDimension();
            results.RadiusDimensionTest = TestRadiusDimension();
            results.DiameterDimensionTest = TestDiameterDimension();
            results.AngularDimensionTest = TestAngularDimension();
            results.LeaderDimensionTest = TestLeaderDimension();
            results.StyleManagerTest = TestDimensionStyleManager();
            results.AssociationTest = TestDimensionAssociation();

            return results;
        }

        /// <summary>
        /// 测试标注系统与图形渲染的集成
        /// </summary>
        public bool TestDimensionRendering(IGraphicsRenderer renderer)
        {
            if (renderer == null) return false;

            try
            {
                var linear = new EntityLinearDimension(
                    new Vector2(0, 0),
                    new Vector2(10, 0),
                    new Vector2(5, 5)
                );

                // 尝试渲染标注
                linear.Render(renderer, CoordinateSpace.Model);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"标注渲染测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 性能测试 - 创建大量标注
        /// </summary>
        public bool TestDimensionPerformance(int dimensionCount = 1000)
        {
            try
            {
                var startTime = DateTime.Now;

                for (int i = 0; i < dimensionCount; i++)
                {
                    var linear = new EntityLinearDimension(
                        new Vector2(i, 0),
                        new Vector2(i + 10, 0),
                        new Vector2(i + 5, 5)
                    );

                    testDocument.AddEntity(linear);
                }

                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                System.Diagnostics.Debug.WriteLine($"创建 {dimensionCount} 个标注耗时: {duration.TotalMilliseconds}ms");

                return duration.TotalSeconds < 5.0; // 应该在5秒内完成
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"性能测试失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// 测试结果类
    /// </summary>
    public class TestResults
    {
        public bool LinearDimensionTest { get; set; }
        public bool AlignedDimensionTest { get; set; }
        public bool RadiusDimensionTest { get; set; }
        public bool DiameterDimensionTest { get; set; }
        public bool AngularDimensionTest { get; set; }
        public bool LeaderDimensionTest { get; set; }
        public bool StyleManagerTest { get; set; }
        public bool AssociationTest { get; set; }

        public bool AllTestsPassed =>
            LinearDimensionTest &&
            AlignedDimensionTest &&
            RadiusDimensionTest &&
            DiameterDimensionTest &&
            AngularDimensionTest &&
            LeaderDimensionTest &&
            StyleManagerTest &&
            AssociationTest;

        public override string ToString()
        {
            return $"标注系统测试结果:\n" +
                   $"线性标注: {(LinearDimensionTest ? "通过" : "失败")}\n" +
                   $"对齐标注: {(AlignedDimensionTest ? "通过" : "失败")}\n" +
                   $"半径标注: {(RadiusDimensionTest ? "通过" : "失败")}\n" +
                   $"直径标注: {(DiameterDimensionTest ? "通过" : "失败")}\n" +
                   $"角度标注: {(AngularDimensionTest ? "通过" : "失败")}\n" +
                   $"引线标注: {(LeaderDimensionTest ? "通过" : "失败")}\n" +
                   $"样式管理: {(StyleManagerTest ? "通过" : "失败")}\n" +
                   $"关联更新: {(AssociationTest ? "通过" : "失败")}\n" +
                   $"总体结果: {(AllTestsPassed ? "全部通过" : "存在失败")}";
        }
    }
} 