﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class PointCmd : DrawCmd
    {
        private EntityPoint _point = null;
        private DocumentBase doc;


        public PointCmd(DocumentBase doc)
        {
                this.doc = doc; 
        }


        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityPoint[1] { _point }; }
        }

        public override void Initialize()
        {
            base.Initialize();

            this.pointer.Mode = IndicatorMode.Locate;
            this.pointer.Document.Prompt = "指定点的位置:";
        }

        protected override void Commit()
        {
            if (this.newEntities != null && this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            try
            {
                if (e.Button == MouseButtons.Left)
                {
                    _point = new EntityPoint(this.pointer.CurrentSnapPoint);
                    _point.LayerId = doc.ActiveLayer?.Id ?? 0;
                    _point.Color = doc.ActiveLayer?.Color ?? SkiaSharp.SKColors.White;
                    _mgr.FinishCurrentCommand();
                }
                else if (e.Button == MouseButtons.Right)
                {
                    _mgr.CancelCurrentCommand();
                }
            }
            catch (System.Exception ex)
            {
                this.pointer.Document.Prompt = $"创建点时发生错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"PointCmd.OnMouseDown error: {ex}");
                _mgr.CancelCurrentCommand();
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
                return EventResult.Handled;
            }

            return EventResult.Unhandled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            if (_point != null)
            {
                ViewBase viewer = _mgr.Viewer as ViewBase;
                _point.Render(viewer);
            }
        }
    }
}
