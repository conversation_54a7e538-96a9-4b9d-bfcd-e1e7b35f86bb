﻿using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Numerics;
using System.Windows;
 
using System.Windows.Forms;
using Point = System.Drawing.Point;


namespace McLaser.EditViewerSk.Base
{

    public enum IndicatorMode
    {
        Default = 0,  // 默认模式
        Select = 1, // 选择模式
        Locate = 2,  // 定位模式
        Drag = 3,  // 拖动模式
    }

    //指示器状态机
    public enum IndicatorSatus
    {
        None,
        OverLayout,
        NoneClick,
        SelectedOneEntity,
        ControlPointClick,
    }


    public class MgrIndicator
    {
        private ViewBase _view = null;
        private PickupBox _pickupBox = null;
        public SelectRectangle _selRect = null;
        private LocateCross _locateCross = null;
        private MgrSnap _snapNodesMgr = null;
        private AnchorsMgr _anchorMgr = null;

        private EntityBase _hittedEntity = null;
        private EntityBase _hittedEntityOld = null;
        private Point _hittedControlPoint = new Point();
        private IndicatorSatus _curSatus = IndicatorSatus.None;



        private IndicatorMode _mode = IndicatorMode.Default;
        public IndicatorMode Mode
        {
            get { return _mode; }
            set
            {
                if (_mode != value)
                {
                    _mode = value;

                }
            }
        }



        //当前捕捉的节点位置
        private Vector2 _currSnapPoint = new Vector2(0, 0);
        public Vector2 CurrentSnapPoint => _currSnapPoint;


        //拾取框大小
        public int PickupBoxSide { get; set; } = 2;

        //定位十字架大小
        public int LocateCrossLength { get; set; }

        //是否显示锚点
        private bool _isShowAnchor = true;
        public bool bIsShowAnchor
        {
            get { return _isShowAnchor; }
            set
            {
                if (_isShowAnchor != value)
                {
                    _anchorMgr.Clear();
                    _isShowAnchor = value;
                    if (_isShowAnchor)
                    {
                        _anchorMgr.Update();
                    }
                }
            }
        }


        Point _mouseDownPosition = new Point(0, 0);
        Vector2 _pos = new Vector2(0, 0);

        public MgrIndicator(ViewBase view)
        {
            _view = view;

            _pickupBox = new PickupBox(_view);
            _pickupBox.side = 10;

            _locateCross = new LocateCross(_view);
            _locateCross.length = 70;

            _snapNodesMgr = new MgrSnap(_view);
            _anchorMgr = new AnchorsMgr(_view);


        }



        private IndicatorSatus GetIndicatorSatus(object sender, MouseEventArgs e, IndicatorSatus preStatus)
        {
            var newSatus = preStatus;
            var point = e.Location;

            if (e.Button == MouseButtons.Left)
            {
                //_view.HitTest(point, out _hittedEntity);
                //switch (preStatus)
                //{


                //    //初始情况下
                //    case IndicatorSatus.OverLayout:
                //    case IndicatorSatus.None:

                //        if (_hittedEntity != null)
                //        {
                //            newSatus = IndicatorSatus.SelectedOneEntity;
                //        }
                //        else  //啥也没有
                //        {
                //            newSatus = IndicatorSatus.NoneClick;
                //        }
                //        _hittedEntityOld = _hittedEntity;
                //        break;




                //    //已经击中一个Entity
                //    case IndicatorSatus.SelectedOneEntity:


                //        //没击中
                //        if (_hittedEntity == null)
                //        {
                //            newSatus = IndicatorSatus.NoneClick;
                //            _hittedEntityOld = _hittedEntity;
                //            break;
                //        }

                //        //击中其他Entity
                //        if (_hittedEntity != _hittedEntityOld)
                //        {
                //            newSatus = IndicatorSatus.SelectedOneEntity;
                //            _hittedEntityOld = _hittedEntity;
                //            break;
                //        }

                //        //击中控制点
                //        bool bIsHitPtInControl = false;
                //        foreach (var entity in _hittedEntity.ControlPoints)
                //        {
                //            if (_hittedEntity.IsPointInControlPoint(_view, point, out _hittedControlPoint))
                //            {
                //                bIsHitPtInControl = true;
                //                break;
                //            }
                //        }

                //        //击中本体
                //        if (!bIsHitPtInControl) //没有击中控制点，那么就是击中了本体
                //        {
                //            newSatus = IndicatorSatus.SelectedOneEntity;
                //        }
                //        else
                //        {
                //            newSatus = IndicatorSatus.ControlPointClick;
                //        }

                //        _hittedEntityOld = _hittedEntity;
                //        break;


                //    //已经击中控制点了
                //    case IndicatorSatus.ControlPointClick:

                //        if (_hittedEntity != null)
                //        {
                //            newSatus = IndicatorSatus.SelectedOneEntity;
                //        }
                //        else  //啥也没有
                //        {
                //            newSatus = IndicatorSatus.NoneClick;
                //        }
                //        _hittedEntityOld = _hittedEntity;
                //        break;

                //}


            }

            return newSatus;
        }

        public Command OnMouseDown(object sender, MouseEventArgs e)
        {
            var cur = e.Location;
            _pos.X = (float)cur.X;
            _pos.Y = (float)cur.Y;
            _mouseDownPosition = cur;
            Command cmd = null;

            //var newStatus = GetIndicatorSatus(e, _curSatus);
            //switch (newStatus)
            //{
            //    case IndicatorSatus.None:
            //        //释放选中显示

            //        break;


            //    case IndicatorSatus.SelectedOneEntity:
            //        //选中显示

            //        break;


            //    case IndicatorSatus.ControlPointClick:

            //        break;
            //}


            switch (_mode)
            {
                case IndicatorMode.Default:

                    {
                        if (e.Button == MouseButtons.Left)
                        {
                            if (_anchorMgr.currentGripPoint == null)
                            {
                                _pickupBox.center = _pos;
                                _selRect = new SelectRectangle(_view);
                                // 将Canvas坐标转换为Model坐标
                                Vector2 modelPos = _view.CanvasToModel(_pos);
                                _selRect.startPoint = _selRect.endPoint = modelPos;

                            }
                            else
                            {
                                //_view.HitTest(cur, out EntityBase hittedEntity);
                                //if (hittedEntity != null)
                                //{
                                //    GripPointMoveCmd gripMoveCmd = new Commands.GripPointMoveCmd(
                                //       hittedEntity, _anchorMgr.currentGripPointIndex, _anchorMgr.currentGripPoint);
                                //    cmd = gripMoveCmd;
                                //}


                                //Commands.Command anchorCmd = _anchorMgr.currentAnchorCmd;
                                //if (anchorCmd != null)
                                //{
                                //    cmd = anchorCmd;
                                //}
                            }
                        }
                    }

                    break;

                case IndicatorMode.Select:

                    if (e.Button == MouseButtons.Left)
                    {
                        _pickupBox.center = _pos;

                        _selRect = new SelectRectangle(_view);
                        // 将Canvas坐标转换为Model坐标
                        Vector2 modelPos = _view.CanvasToModel(_pos);
                        _selRect.startPoint = _selRect.endPoint = modelPos;

                    }
                    break;



                case IndicatorMode.Locate:
                    _currSnapPoint = _snapNodesMgr.Snap(_pos);
                    break;



                case IndicatorMode.Drag:
                    break;



                default:
                    break;


            }

            return cmd;
        }



        public void OnMouseMove(object sender, MouseEventArgs e)
        {

            var cur = e.Location;
            _pos.X = (float)cur.X;
            _pos.Y = (float)cur.Y;
            var newStatus = GetIndicatorSatus(sender, e, _curSatus);



            switch (_mode)
            {
                case IndicatorMode.Default:
                    if (_selRect != null)
                    {
                        // 将Canvas坐标转换为Model坐标
                        Vector2 modelPos = _view.CanvasToModel(_pos);
                        _selRect.endPoint = modelPos;
                        _view.RepaintCanvas();
                    }
                    else
                    {
                        _currSnapPoint = _anchorMgr.Snap(_pos);
                    }
                    break;

                case IndicatorMode.Select:
                    if (_selRect != null)
                    {
                        // 将Canvas坐标转换为Model坐标
                        Vector2 modelPos = _view.CanvasToModel(_pos);
                        _selRect.endPoint = modelPos;

                        _view.RepaintCanvas();
                    }
                    break;

                case IndicatorMode.Locate:
                    _currSnapPoint = _snapNodesMgr.Snap(_pos);
                    break;

                case IndicatorMode.Drag:
                    break;

                default:
                    break;
            }

            // _view.RepaintCanvas();
        }



        public void OnMouseUp(IInputElement sender, MouseEventArgs e)
        {
            
            if (e.Button == MouseButtons.Left)
            {
                if (_selRect != null)
                {
                    var cur = e.Location;
                    int count = _view.HitTest(_mouseDownPosition, cur, out List<EntityBase> hittedEntitys);
                    if(bIsShiftKepDown && _view.Document.SelectedEntitys!=null&& _view.Document.SelectedEntitys.Count>0)
                    {
                        hittedEntitys = hittedEntitys.Where(x => !_view.Document.SelectedEntitys.Contains(x)).ToList() ;
                        _view.Document.SelectedEntitys.ForEach(x => x.IsSelected = false);
                    }


                    if(count == 0)
                    {
                   

                    }
                    _view.Document.SelectedEntitys = hittedEntitys;

                    
                }
                _selRect = null;
                _view.RepaintCanvas();
            }
        }




        public void Paint(ViewBase view)
        {
            if (_selRect != null)
            {
                SKPaint selectionPen = new SKPaint()
                {
                    Color = _selRect.selectMode == SelectRectangle.SelectMode.Window ? SKColors.Blue : SKColors.Green,
                    StrokeWidth = 1.0f,
                    Style = SKPaintStyle.Stroke,
                    IsAntialias = true,
                    PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0)
                };
                
                // 使用Canvas坐标系绘制，与OnPaint方法保持一致
                // 将Model坐标转换为Canvas坐标
                var startCanvas = view.ModelToCanvas(_selRect.startPoint);
                var endCanvas = view.ModelToCanvas(_selRect.endPoint);
                double canvasWidth = endCanvas.X - startCanvas.X;
                double canvasHeight = endCanvas.Y - startCanvas.Y;
                
                view.DrawRectangle(startCanvas, canvasWidth, canvasHeight, selectionPen, CSYS.Canvas);
            }
        }


        public void OnMouseDoubleClick(MouseEventArgs e)
        {
            switch (Mode)
            {
                case IndicatorMode.Default:
                    if (e.Button == MouseButtons.Left)
                    {
                        if (_anchorMgr.currentGripPoint == null)
                        {
                            _pickupBox.center = _pos;
                            //List<Selection> sels = _pickupBox.Select(_presenter.CurrentBlock);
                            //if (sels.Count > 0)
                            //{
                            //    foreach (Selection sel in sels)
                            //    {
                            //        DBObject dbobj = (_presenter.document as Document).Database.GetObject(sel.objectId);
                            //        if (dbobj != null && dbobj is Text)
                            //        {
                            //            (_presenter.document as Document).Selections.Clear();
                            //        }
                            //    }
                            //}
                        }
                    }
                    break;

                default:
                    break;
            }
        }


        bool bIsShiftKepDown = false;
        public bool OnKeyDown(KeyEventArgs e)
        {
            if(e.KeyCode == Keys.ControlKey)
            {
                bIsShiftKepDown = true;
            }
            return false;
        }

        public bool OnKeyUp(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.LControlKey)
            {
                bIsShiftKepDown = false;
            }
            return false;
        }

        public void OnPaint(ViewBase viewer)
        {
            if (_isShowAnchor)
            {
                _anchorMgr.OnPaint();
            }

            switch (_mode)
            {
                case IndicatorMode.Default:
                    {
                        if (_selRect != null)
                        {
                            //_selRect.OnPaint(canvas);
                            viewer.DrawRectangle(_selRect.startPoint, _selRect.endPoint.X - _selRect.startPoint.X, _selRect.endPoint.Y - _selRect.startPoint.Y, CSYS.Canvas);
                        }
                        else
                        {
                            Vector2 currSnapPointInCanvas = _view.ModelToCanvas(_currSnapPoint);

                        }
                    }
                    break;

                case IndicatorMode.Select:
                    if (_selRect != null)
                    {
                        //_selRect.OnPaint(canvas);
                        viewer.DrawRectangle(_selRect.startPoint, _selRect.endPoint.X - _selRect.startPoint.X, _selRect.endPoint.Y - _selRect.startPoint.Y, CSYS.Canvas);
                    }
                    else
                    {
                        //graphics.DrawImage(_bitmap,
                        //    (float)(_pos.X - _bitmap.Width / 2),
                        //    (float)(_pos.Y - _bitmap.Height / 2));
                    }
                    break;

                case IndicatorMode.Locate:
                    {
                        Vector2 currSnapPointInCanvas = _view.ModelToCanvas(_currSnapPoint);
                        //graphics.DrawImage(_bitmap,
                        //    (float)(currSnapPointInCanvas.X - _bitmap.Width / 2),
                        //    (float)(currSnapPointInCanvas.Y - _bitmap.Height / 2));

                        // _presenter.canvasDraw.Canvas = graphics;
                        _snapNodesMgr.OnPaint(_view);
                    }
                    break;

                case IndicatorMode.Drag:
                    break;

                default:
                    break;
            }
        }

        public void OnSelectionChanged()
        {
            if (_isShowAnchor)
            {
                _anchorMgr.Update();
            }
        }

        public void UpdateGripPoints()
        {
            _anchorMgr.Clear();
            if (_isShowAnchor)
            {
                _anchorMgr.Update();
            }
        }

        private bool IsShiftKeyDown
        {
            get
            {
                return false;
                //return (Control.ModifierKeys & Keys.Shift) == Keys.Shift;
            }
        }
    }


}
