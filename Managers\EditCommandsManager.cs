using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 编辑命令管理器
    /// 统一管理所有编辑命令，提供命令注册、快捷键映射和执行控制
    /// </summary>
    public class EditCommandsManager
    {
        private ViewBase _viewer;
        private Dictionary<string, Func<Command>> _commandFactories;
        private Dictionary<string, string> _commandAliases;
        private Dictionary<Keys, string> _shortcutKeys;
        private CommandHistory _commandHistory;
        private EditCommandsConfiguration _configuration;
        
        public bool IsInitialized { get; private set; }
        
        public EditCommandsConfiguration Configuration
        {
            get { return _configuration; }
            set { _configuration = value; ApplyConfiguration(); }
        }
        
        public CommandHistory History
        {
            get { return _commandHistory; }
        }
        
        public event EventHandler<CommandExecutedEventArgs> CommandExecuted;
        public event EventHandler<CommandFailedEventArgs> CommandFailed;
        
        public EditCommandsManager(ViewBase viewer)
        {
            _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
            _commandFactories = new Dictionary<string, Func<Command>>(StringComparer.OrdinalIgnoreCase);
            _commandAliases = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _shortcutKeys = new Dictionary<Keys, string>();
            _commandHistory = new CommandHistory();
            _configuration = new EditCommandsConfiguration();
            
            InitializeCommands();
            InitializeShortcuts();
            InitializeAliases();
            
            IsInitialized = true;
        }
        
        private void InitializeCommands()
        {
            try
            {
                // 注册所有编辑命令
                RegisterCommand("OFFSET", () => new OffsetCmd());
                RegisterCommand("ARRAY", () => new RectangularArrayCmd());
                RegisterCommand("RECTARRAY", () => new RectangularArrayCmd());
                RegisterCommand("POLARARRAY", () => new PolarArrayCmd());
                RegisterCommand("PATHARRAY", () => new PathArrayCmd());
                RegisterCommand("TRIM", () => new TrimCmd());
                RegisterCommand("EXTEND", () => new ExtendCmd());
                RegisterCommand("FILLET", () => new FilletCmd());
                RegisterCommand("CHAMFER", () => new ChamferCmd());
                RegisterCommand("SCALE", () => new ScaleCmd());
                RegisterCommand("ROTATE", () => new RotateCmd());
                RegisterCommand("STRETCH", () => new StretchCmd());
                
                System.Diagnostics.Debug.WriteLine($"Registered {_commandFactories.Count} edit commands");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing commands: {ex.Message}");
                throw;
            }
        }
        
        private void InitializeShortcuts()
        {
            // 设置默认快捷键
            SetShortcut(Keys.O, "OFFSET");
            SetShortcut(Keys.Control | Keys.A, "ARRAY");
            SetShortcut(Keys.T, "TRIM");
            SetShortcut(Keys.E, "EXTEND");
            SetShortcut(Keys.F, "FILLET");
            SetShortcut(Keys.Control | Keys.H, "CHAMFER");
            SetShortcut(Keys.S, "SCALE");
            SetShortcut(Keys.R, "ROTATE");
            SetShortcut(Keys.Control | Keys.S, "STRETCH");
        }
        
        private void InitializeAliases()
        {
            // 设置命令别名
            SetAlias("OF", "OFFSET");
            SetAlias("AR", "ARRAY");
            SetAlias("TR", "TRIM");
            SetAlias("EX", "EXTEND");
            SetAlias("FI", "FILLET");
            SetAlias("CH", "CHAMFER");
            SetAlias("SC", "SCALE");
            SetAlias("RO", "ROTATE");
            SetAlias("ST", "STRETCH");
            
            // 中文别名
            SetAlias("偏移", "OFFSET");
            SetAlias("阵列", "ARRAY");
            SetAlias("修剪", "TRIM");
            SetAlias("延伸", "EXTEND");
            SetAlias("圆角", "FILLET");
            SetAlias("倒角", "CHAMFER");
            SetAlias("缩放", "SCALE");
            SetAlias("旋转", "ROTATE");
            SetAlias("拉伸", "STRETCH");
        }
        
        private void ApplyConfiguration()
        {
            if (_configuration == null) return;
            
            // 应用快捷键配置
            if (_configuration.CustomShortcuts.Count > 0)
            {
                foreach (var shortcut in _configuration.CustomShortcuts)
                {
                    SetShortcut(shortcut.Key, shortcut.Value);
                }
            }
            
            // 应用别名配置
            if (_configuration.CustomAliases.Count > 0)
            {
                foreach (var alias in _configuration.CustomAliases)
                {
                    SetAlias(alias.Key, alias.Value);
                }
            }
        }
        
        /// <summary>
        /// 注册命令
        /// </summary>
        public void RegisterCommand(string commandName, Func<Command> commandFactory)
        {
            if (string.IsNullOrEmpty(commandName))
                throw new ArgumentException("Command name cannot be null or empty", nameof(commandName));
            
            if (commandFactory == null)
                throw new ArgumentNullException(nameof(commandFactory));
            
            _commandFactories[commandName.ToUpper()] = commandFactory;
        }
        
        /// <summary>
        /// 设置快捷键
        /// </summary>
        public void SetShortcut(Keys key, string commandName)
        {
            if (string.IsNullOrEmpty(commandName)) return;
            
            _shortcutKeys[key] = commandName.ToUpper();
        }
        
        /// <summary>
        /// 设置别名
        /// </summary>
        public void SetAlias(string alias, string commandName)
        {
            if (string.IsNullOrEmpty(alias) || string.IsNullOrEmpty(commandName)) return;
            
            _commandAliases[alias.ToUpper()] = commandName.ToUpper();
        }
        
        /// <summary>
        /// 执行命令
        /// </summary>
        public bool ExecuteCommand(string commandInput)
        {
            if (string.IsNullOrWhiteSpace(commandInput)) return false;
            
            try
            {
                var commandName = ResolveCommandName(commandInput.Trim());
                
                if (string.IsNullOrEmpty(commandName))
                {
                    _viewer.Document.Prompt = $"未知命令: {commandInput}";
                    return false;
                }
                
                if (!IsCommandAvailable(commandName))
                {
                    _viewer.Document.Prompt = $"命令 {commandName} 当前不可用";
                    return false;
                }
                
                var command = CreateCommand(commandName);
                if (command == null)
                {
                    _viewer.Document.Prompt = $"无法创建命令: {commandName}";
                    return false;
                }
                
                // 执行命令
                var success = ExecuteCommandInternal(command, commandName);
                
                if (success)
                {
                    // 记录到历史
                    _commandHistory.AddCommand(commandName, DateTime.Now);
                    
                    // 触发事件
                    CommandExecuted?.Invoke(this, new CommandExecutedEventArgs
                    {
                        CommandName = commandName,
                        Command = command,
                        ExecutionTime = DateTime.Now
                    });
                }
                
                return success;
            }
            catch (Exception ex)
            {
                var errorMessage = $"执行命令失败: {ex.Message}";
                _viewer.Document.Prompt = errorMessage;
                
                CommandFailed?.Invoke(this, new CommandFailedEventArgs
                {
                    CommandName = commandInput,
                    Error = ex,
                    ErrorMessage = errorMessage
                });
                
                System.Diagnostics.Debug.WriteLine($"Command execution error: {ex}");
                return false;
            }
        }
        
        /// <summary>
        /// 处理快捷键
        /// </summary>
        public bool HandleShortcut(Keys key)
        {
            if (_shortcutKeys.TryGetValue(key, out string commandName))
            {
                return ExecuteCommand(commandName);
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取命令建议
        /// </summary>
        public List<string> GetCommandSuggestions(string partialInput)
        {
            if (string.IsNullOrWhiteSpace(partialInput))
                return new List<string>();
            
            var input = partialInput.ToUpper();
            var suggestions = new List<string>();
            
            // 搜索命令名
            suggestions.AddRange(_commandFactories.Keys.Where(cmd => cmd.StartsWith(input)));
            
            // 搜索别名
            suggestions.AddRange(_commandAliases.Keys.Where(alias => alias.StartsWith(input))
                                                   .Select(alias => _commandAliases[alias]));
            
            return suggestions.Distinct().Take(10).ToList();
        }
        
        /// <summary>
        /// 获取所有可用命令
        /// </summary>
        public List<CommandInfo> GetAvailableCommands()
        {
            var commands = new List<CommandInfo>();
            
            foreach (var factory in _commandFactories)
            {
                try
                {
                    var command = factory.Value();
                    var shortcuts = _shortcutKeys.Where(s => s.Value == factory.Key)
                                                 .Select(s => s.Key.ToString())
                                                 .ToList();
                    var aliases = _commandAliases.Where(a => a.Value == factory.Key)
                                                 .Select(a => a.Key)
                                                 .ToList();
                    
                    commands.Add(new CommandInfo
                    {
                        Name = factory.Key,
                        Description = command.Description,
                        Shortcuts = shortcuts,
                        Aliases = aliases,
                        IsAvailable = IsCommandAvailable(factory.Key)
                    });
                    
                    command.Dispose();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error getting command info for {factory.Key}: {ex.Message}");
                }
            }
            
            return commands.OrderBy(c => c.Name).ToList();
        }
        
        private string ResolveCommandName(string input)
        {
            var upperInput = input.ToUpper();
            
            // 直接匹配命令名
            if (_commandFactories.ContainsKey(upperInput))
                return upperInput;
            
            // 匹配别名
            if (_commandAliases.TryGetValue(upperInput, out string commandName))
                return commandName;
            
            // 部分匹配
            var partialMatches = _commandFactories.Keys.Where(cmd => cmd.StartsWith(upperInput)).ToList();
            if (partialMatches.Count == 1)
                return partialMatches[0];
            
            return null;
        }
        
        private bool IsCommandAvailable(string commandName)
        {
            // 检查命令是否可用（可以根据当前状态、选择等条件判断）
            if (!_commandFactories.ContainsKey(commandName))
                return false;
            
            // 检查配置中是否禁用了该命令
            if (_configuration.DisabledCommands.Contains(commandName))
                return false;
            
            // 可以添加更多可用性检查逻辑
            return true;
        }
        
        private Command CreateCommand(string commandName)
        {
            if (_commandFactories.TryGetValue(commandName, out var factory))
            {
                return factory();
            }
            
            return null;
        }
        
        private bool ExecuteCommandInternal(Command command, string commandName)
        {
            try
            {
                // 检查是否有其他命令正在执行
                if (_viewer._cmdsMgr.CurrentCmd != null)
                {
                    _viewer._cmdsMgr.CurrentCmd.Cancel();
                }
                
                // 执行新命令
                _viewer._cmdsMgr.DoCommand(command);
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error executing command {commandName}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取命令历史
        /// </summary>
        public List<string> GetRecentCommands(int count = 10)
        {
            return _commandHistory.GetRecentCommands(count);
        }
        
        /// <summary>
        /// 清除命令历史
        /// </summary>
        public void ClearHistory()
        {
            _commandHistory.Clear();
        }
        
        /// <summary>
        /// 导出配置
        /// </summary>
        public EditCommandsConfiguration ExportConfiguration()
        {
            var config = new EditCommandsConfiguration();
            
            // 导出自定义快捷键
            foreach (var shortcut in _shortcutKeys)
            {
                config.CustomShortcuts[shortcut.Key] = shortcut.Value;
            }
            
            // 导出自定义别名
            foreach (var alias in _commandAliases)
            {
                config.CustomAliases[alias.Key] = alias.Value;
            }
            
            config.DisabledCommands = new List<string>(_configuration.DisabledCommands);
            
            return config;
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefaults()
        {
            _shortcutKeys.Clear();
            _commandAliases.Clear();
            _configuration = new EditCommandsConfiguration();
            
            InitializeShortcuts();
            InitializeAliases();
        }
        
        public void Dispose()
        {
            _commandFactories?.Clear();
            _commandAliases?.Clear();
            _shortcutKeys?.Clear();
            _commandHistory?.Clear();
        }
    }
    
    /// <summary>
    /// 编辑命令配置
    /// </summary>
    public class EditCommandsConfiguration
    {
        public Dictionary<Keys, string> CustomShortcuts { get; set; } = new Dictionary<Keys, string>();
        public Dictionary<string, string> CustomAliases { get; set; } = new Dictionary<string, string>();
        public List<string> DisabledCommands { get; set; } = new List<string>();
        public bool EnableCommandHistory { get; set; } = true;
        public int MaxHistorySize { get; set; } = 50;
        public bool ShowCommandSuggestions { get; set; } = true;
        public bool CaseSensitiveCommands { get; set; } = false;
    }
    
    /// <summary>
    /// 命令历史记录
    /// </summary>
    public class CommandHistory
    {
        private List<CommandHistoryEntry> _history = new List<CommandHistoryEntry>();
        private int _maxSize = 50;
        
        public int MaxSize
        {
            get { return _maxSize; }
            set { _maxSize = Math.Max(1, value); TrimHistory(); }
        }
        
        public void AddCommand(string commandName, DateTime executionTime)
        {
            _history.Add(new CommandHistoryEntry
            {
                CommandName = commandName,
                ExecutionTime = executionTime
            });
            
            TrimHistory();
        }
        
        public List<string> GetRecentCommands(int count)
        {
            return _history.TakeLast(Math.Min(count, _history.Count))
                          .Reverse()
                          .Select(h => h.CommandName)
                          .ToList();
        }
        
        public void Clear()
        {
            _history.Clear();
        }
        
        private void TrimHistory()
        {
            if (_history.Count > _maxSize)
            {
                _history.RemoveRange(0, _history.Count - _maxSize);
            }
        }
    }
    
    /// <summary>
    /// 命令历史条目
    /// </summary>
    public class CommandHistoryEntry
    {
        public string CommandName { get; set; }
        public DateTime ExecutionTime { get; set; }
    }
    
    /// <summary>
    /// 命令信息
    /// </summary>
    public class CommandInfo
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public List<string> Shortcuts { get; set; } = new List<string>();
        public List<string> Aliases { get; set; } = new List<string>();
        public bool IsAvailable { get; set; }
    }
    
    /// <summary>
    /// 命令执行事件参数
    /// </summary>
    public class CommandExecutedEventArgs : EventArgs
    {
        public string CommandName { get; set; }
        public Command Command { get; set; }
        public DateTime ExecutionTime { get; set; }
    }
    
    /// <summary>
    /// 命令失败事件参数
    /// </summary>
    public class CommandFailedEventArgs : EventArgs
    {
        public string CommandName { get; set; }
        public Exception Error { get; set; }
        public string ErrorMessage { get; set; }
    }
} 