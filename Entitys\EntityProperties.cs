using System;
using System.ComponentModel;
using Newtonsoft.Json;
using SkiaSharp;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 线型枚举
    /// 定义CAD标准线型
    /// </summary>
    public enum LineTypeStyle
    {
        /// <summary>实线</summary>
        [Description("实线")]
        Continuous,
        
        /// <summary>虚线</summary>
        [Description("虚线")]
        Dashed,
        
        /// <summary>点线</summary>
        [Description("点线")]
        Dotted,
        
        /// <summary>点划线</summary>
        [Description("点划线")]
        DashDot,
        
        /// <summary>双点划线</summary>
        [Description("双点划线")]
        DashDotDot,
        
        /// <summary>中心线</summary>
        [Description("中心线")]
        Center,
        
        /// <summary>边界线</summary>
        [Description("边界线")]
        Border,
        
        /// <summary>隐藏线</summary>
        [Description("隐藏线")]
        Hidden,
        
        /// <summary>自定义</summary>
        [Description("自定义")]
        Custom
    }

    /// <summary>
    /// 线宽枚举
    /// 定义CAD标准线宽（毫米）
    /// </summary>
    public enum LineWeightValue
    {
        /// <summary>默认线宽</summary>
        [Description("默认")]
        Default = 0,
        
        /// <summary>0.05mm</summary>
        [Description("0.05mm")]
        LineWeight005 = 5,
        
        /// <summary>0.09mm</summary>
        [Description("0.09mm")]
        LineWeight009 = 9,
        
        /// <summary>0.13mm</summary>
        [Description("0.13mm")]
        LineWeight013 = 13,
        
        /// <summary>0.15mm</summary>
        [Description("0.15mm")]
        LineWeight015 = 15,
        
        /// <summary>0.18mm</summary>
        [Description("0.18mm")]
        LineWeight018 = 18,
        
        /// <summary>0.20mm</summary>
        [Description("0.20mm")]
        LineWeight020 = 20,
        
        /// <summary>0.25mm</summary>
        [Description("0.25mm")]
        LineWeight025 = 25,
        
        /// <summary>0.30mm</summary>
        [Description("0.30mm")]
        LineWeight030 = 30,
        
        /// <summary>0.35mm</summary>
        [Description("0.35mm")]
        LineWeight035 = 35,
        
        /// <summary>0.40mm</summary>
        [Description("0.40mm")]
        LineWeight040 = 40,
        
        /// <summary>0.50mm</summary>
        [Description("0.50mm")]
        LineWeight050 = 50,
        
        /// <summary>0.53mm</summary>
        [Description("0.53mm")]
        LineWeight053 = 53,
        
        /// <summary>0.60mm</summary>
        [Description("0.60mm")]
        LineWeight060 = 60,
        
        /// <summary>0.70mm</summary>
        [Description("0.70mm")]
        LineWeight070 = 70,
        
        /// <summary>0.80mm</summary>
        [Description("0.80mm")]
        LineWeight080 = 80,
        
        /// <summary>0.90mm</summary>
        [Description("0.90mm")]
        LineWeight090 = 90,
        
        /// <summary>1.00mm</summary>
        [Description("1.00mm")]
        LineWeight100 = 100,
        
        /// <summary>1.20mm</summary>
        [Description("1.20mm")]
        LineWeight120 = 120,
        
        /// <summary>1.40mm</summary>
        [Description("1.40mm")]
        LineWeight140 = 140,
        
        /// <summary>1.58mm</summary>
        [Description("1.58mm")]
        LineWeight158 = 158,
        
        /// <summary>2.00mm</summary>
        [Description("2.00mm")]
        LineWeight200 = 200,
        
        /// <summary>2.11mm</summary>
        [Description("2.11mm")]
        LineWeight211 = 211
    }

    /// <summary>
    /// 填充类型枚举
    /// </summary>
    public enum FillType
    {
        /// <summary>无填充</summary>
        [Description("无填充")]
        None,
        
        /// <summary>实心填充</summary>
        [Description("实心填充")]
        Solid,
        
        /// <summary>图案填充</summary>
        [Description("图案填充")]
        Pattern,
        
        /// <summary>渐变填充</summary>
        [Description("渐变填充")]
        Gradient
    }

    /// <summary>
    /// 填充图案枚举
    /// </summary>
    public enum FillPattern
    {
        /// <summary>实心</summary>
        [Description("实心")]
        Solid,
        
        /// <summary>水平线</summary>
        [Description("水平线")]
        Horizontal,
        
        /// <summary>垂直线</summary>
        [Description("垂直线")]
        Vertical,
        
        /// <summary>斜线</summary>
        [Description("斜线")]
        Diagonal,
        
        /// <summary>反斜线</summary>
        [Description("反斜线")]
        BackDiagonal,
        
        /// <summary>交叉线</summary>
        [Description("交叉线")]
        Cross,
        
        /// <summary>斜交叉</summary>
        [Description("斜交叉")]
        DiagonalCross,
        
        /// <summary>点阵</summary>
        [Description("点阵")]
        Dots,
        
        /// <summary>网格</summary>
        [Description("网格")]
        Grid,
        
        /// <summary>砖块</summary>
        [Description("砖块")]
        Brick,
        
        /// <summary>鱼鳞</summary>
        [Description("鱼鳞")]
        Scale,
        
        /// <summary>自定义</summary>
        [Description("自定义")]
        Custom
    }

    /// <summary>
    /// 图元属性类
    /// 封装CAD图元的所有显示属性
    /// </summary>
    [TypeConverter(typeof(ExpandableObjectConverter))]
    public class EntityProperties : ICloneable, INotifyPropertyChanged
    {
        #region 事件
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        #region 私有字段
        private SKColor _color = SKColors.Black;
        private LineTypeStyle _lineType = LineTypeStyle.Continuous;
        private LineWeightValue _lineWeight = LineWeightValue.Default;
        private FillType _fillType = FillType.None;
        private SKColor _fillColor = SKColors.Transparent;
        private FillPattern _fillPattern = FillPattern.Solid;
        private double _patternScale = 1.0;
        private double _transparency = 0.0; // 0-100%
        private bool _visible = true;
        private int _displayOrder = 0;
        
        [JsonIgnore]
        private SKPaint _cachedPaint;
        private bool _paintNeedsUpdate = true;
        
        [JsonIgnore]
        private float[] _lineDashPattern;
        private bool _dashPatternNeedsUpdate = true;
        #endregion

        #region 公共属性
        /// <summary>
        /// 图元颜色
        /// </summary>
        [Category("外观"), DisplayName("颜色"), Browsable(true)]
        public SKColor Color
        {
            get => _color;
            set
            {
                if (_color != value)
                {
                    _color = value;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(Color));
                }
            }
        }

        /// <summary>
        /// 线型
        /// </summary>
        [Category("线条"), DisplayName("线型"), Browsable(true)]
        public LineTypeStyle LineType
        {
            get => _lineType;
            set
            {
                if (_lineType != value)
                {
                    _lineType = value;
                    _paintNeedsUpdate = true;
                    _dashPatternNeedsUpdate = true;
                    OnPropertyChanged(nameof(LineType));
                }
            }
        }

        /// <summary>
        /// 线宽
        /// </summary>
        [Category("线条"), DisplayName("线宽"), Browsable(true)]
        public LineWeightValue LineWeight
        {
            get => _lineWeight;
            set
            {
                if (_lineWeight != value)
                {
                    _lineWeight = value;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(LineWeight));
                    OnPropertyChanged(nameof(LineWidthMM));
                }
            }
        }

        /// <summary>
        /// 线宽（毫米）
        /// </summary>
        [Category("线条"), DisplayName("线宽(mm)"), Browsable(true), ReadOnly(true)]
        public double LineWidthMM => (int)_lineWeight / 100.0;

        /// <summary>
        /// 填充类型
        /// </summary>
        [Category("填充"), DisplayName("填充类型"), Browsable(true)]
        public FillType FillType
        {
            get => _fillType;
            set
            {
                if (_fillType != value)
                {
                    _fillType = value;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(FillType));
                }
            }
        }

        /// <summary>
        /// 填充颜色
        /// </summary>
        [Category("填充"), DisplayName("填充颜色"), Browsable(true)]
        public SKColor FillColor
        {
            get => _fillColor;
            set
            {
                if (_fillColor != value)
                {
                    _fillColor = value;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(FillColor));
                }
            }
        }

        /// <summary>
        /// 填充图案
        /// </summary>
        [Category("填充"), DisplayName("填充图案"), Browsable(true)]
        public FillPattern FillPattern
        {
            get => _fillPattern;
            set
            {
                if (_fillPattern != value)
                {
                    _fillPattern = value;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(FillPattern));
                }
            }
        }

        /// <summary>
        /// 图案缩放比例
        /// </summary>
        [Category("填充"), DisplayName("图案缩放"), Browsable(true)]
        public double PatternScale
        {
            get => _patternScale;
            set
            {
                var clampedValue = Math.Max(0.1, Math.Min(10.0, value));
                if (Math.Abs(_patternScale - clampedValue) > double.Epsilon)
                {
                    _patternScale = clampedValue;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(PatternScale));
                }
            }
        }

        /// <summary>
        /// 透明度（0-100%）
        /// </summary>
        [Category("外观"), DisplayName("透明度"), Browsable(true)]
        public double Transparency
        {
            get => _transparency;
            set
            {
                var clampedValue = Math.Max(0.0, Math.Min(100.0, value));
                if (Math.Abs(_transparency - clampedValue) > double.Epsilon)
                {
                    _transparency = clampedValue;
                    _paintNeedsUpdate = true;
                    OnPropertyChanged(nameof(Transparency));
                    OnPropertyChanged(nameof(Alpha));
                }
            }
        }

        /// <summary>
        /// Alpha值（0-255）
        /// </summary>
        [JsonIgnore, Browsable(false)]
        public byte Alpha => (byte)(255 * (100.0 - _transparency) / 100.0);

        /// <summary>
        /// 是否可见
        /// </summary>
        [Category("外观"), DisplayName("可见"), Browsable(true)]
        public bool Visible
        {
            get => _visible;
            set
            {
                if (_visible != value)
                {
                    _visible = value;
                    OnPropertyChanged(nameof(Visible));
                }
            }
        }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Category("外观"), DisplayName("显示顺序"), Browsable(true)]
        public int DisplayOrder
        {
            get => _displayOrder;
            set
            {
                if (_displayOrder != value)
                {
                    _displayOrder = value;
                    OnPropertyChanged(nameof(DisplayOrder));
                }
            }
        }
        #endregion

        #region 构造函数
        public EntityProperties()
        {
            // 初始化默认属性
        }

        public EntityProperties(SKColor color, LineTypeStyle lineType = LineTypeStyle.Continuous, 
            LineWeightValue lineWeight = LineWeightValue.Default)
        {
            _color = color;
            _lineType = lineType;
            _lineWeight = lineWeight;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 获取用于绘制的SKPaint对象
        /// </summary>
        public SKPaint GetDrawingPaint(bool isStroke = true)
        {
            if (_paintNeedsUpdate || _cachedPaint == null)
            {
                UpdateCachedPaint(isStroke);
            }
            
            return _cachedPaint;
        }

        /// <summary>
        /// 获取线条绘制画笔
        /// </summary>
        public SKPaint GetStrokePaint()
        {
            var paint = new SKPaint
            {
                Color = GetColorWithAlpha(_color),
                Style = SKPaintStyle.Stroke,
                StrokeWidth = GetPixelLineWidth(),
                IsAntialias = true,
                StrokeCap = SKStrokeCap.Round,
                StrokeJoin = SKStrokeJoin.Round
            };

            // 设置虚线样式
            if (_lineType != LineTypeStyle.Continuous)
            {
                paint.PathEffect = SKPathEffect.CreateDash(GetDashPattern(), 0);
            }

            return paint;
        }

        /// <summary>
        /// 获取填充绘制画笔
        /// </summary>
        public SKPaint GetFillPaint()
        {
            if (_fillType == FillType.None)
                return null;

            var paint = new SKPaint
            {
                Color = GetColorWithAlpha(_fillColor),
                Style = SKPaintStyle.Fill,
                IsAntialias = true
            };

            // 根据填充类型设置
            switch (_fillType)
            {
                case FillType.Pattern:
                    paint.Shader = CreatePatternShader();
                    break;
                case FillType.Gradient:
                    // 渐变填充需要额外的参数，这里简化处理
                    break;
            }

            return paint;
        }

        /// <summary>
        /// 克隆属性
        /// </summary>
        public object Clone()
        {
            return new EntityProperties
            {
                Color = _color,
                LineType = _lineType,
                LineWeight = _lineWeight,
                FillType = _fillType,
                FillColor = _fillColor,
                FillPattern = _fillPattern,
                PatternScale = _patternScale,
                Transparency = _transparency,
                Visible = _visible,
                DisplayOrder = _displayOrder
            };
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void Reset()
        {
            Color = SKColors.Black;
            LineType = LineTypeStyle.Continuous;
            LineWeight = LineWeightValue.Default;
            FillType = FillType.None;
            FillColor = SKColors.Transparent;
            FillPattern = FillPattern.Solid;
            PatternScale = 1.0;
            Transparency = 0.0;
            Visible = true;
            DisplayOrder = 0;
        }

        /// <summary>
        /// 从另一个属性对象复制
        /// </summary>
        public void CopyFrom(EntityProperties other)
        {
            if (other == null) return;

            Color = other.Color;
            LineType = other.LineType;
            LineWeight = other.LineWeight;
            FillType = other.FillType;
            FillColor = other.FillColor;
            FillPattern = other.FillPattern;
            PatternScale = other.PatternScale;
            Transparency = other.Transparency;
            Visible = other.Visible;
            DisplayOrder = other.DisplayOrder;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 更新缓存的画笔
        /// </summary>
        private void UpdateCachedPaint(bool isStroke)
        {
            _cachedPaint?.Dispose();
            
            _cachedPaint = new SKPaint
            {
                Color = GetColorWithAlpha(isStroke ? _color : _fillColor),
                Style = isStroke ? SKPaintStyle.Stroke : SKPaintStyle.Fill,
                IsAntialias = true
            };

            if (isStroke)
            {
                _cachedPaint.StrokeWidth = GetPixelLineWidth();
                _cachedPaint.StrokeCap = SKStrokeCap.Round;
                _cachedPaint.StrokeJoin = SKStrokeJoin.Round;

                if (_lineType != LineTypeStyle.Continuous)
                {
                    _cachedPaint.PathEffect = SKPathEffect.CreateDash(GetDashPattern(), 0);
                }
            }
            else if (_fillType == FillType.Pattern)
            {
                _cachedPaint.Shader = CreatePatternShader();
            }

            _paintNeedsUpdate = false;
        }

        /// <summary>
        /// 获取带透明度的颜色
        /// </summary>
        private SKColor GetColorWithAlpha(SKColor color)
        {
            return new SKColor(color.Red, color.Green, color.Blue, Alpha);
        }

        /// <summary>
        /// 获取像素线宽
        /// </summary>
        private float GetPixelLineWidth()
        {
            if (_lineWeight == LineWeightValue.Default)
                return 1.0f;
                
            // 将毫米转换为像素（假设96 DPI）
            var mmToPixel = 96.0 / 25.4; // 1英寸 = 25.4毫米
            return (float)(LineWidthMM * mmToPixel);
        }

        /// <summary>
        /// 获取虚线样式数组
        /// </summary>
        private float[] GetDashPattern()
        {
            if (!_dashPatternNeedsUpdate && _lineDashPattern != null)
                return _lineDashPattern;

            var baseWidth = Math.Max(1.0f, GetPixelLineWidth());
            
            switch (_lineType)
            {
                case LineTypeStyle.Dashed:
                    _lineDashPattern = new float[] { 5 * baseWidth, 3 * baseWidth };
                    break;
                case LineTypeStyle.Dotted:
                    _lineDashPattern = new float[] { 1 * baseWidth, 2 * baseWidth };
                    break;
                case LineTypeStyle.DashDot:
                    _lineDashPattern = new float[] { 5 * baseWidth, 2 * baseWidth, 1 * baseWidth, 2 * baseWidth };
                    break;
                case LineTypeStyle.DashDotDot:
                    _lineDashPattern = new float[] { 5 * baseWidth, 2 * baseWidth, 1 * baseWidth, 2 * baseWidth, 1 * baseWidth, 2 * baseWidth };
                    break;
                case LineTypeStyle.Center:
                    _lineDashPattern = new float[] { 10 * baseWidth, 3 * baseWidth, 2 * baseWidth, 3 * baseWidth };
                    break;
                case LineTypeStyle.Border:
                    _lineDashPattern = new float[] { 10 * baseWidth, 3 * baseWidth, 2 * baseWidth, 3 * baseWidth, 2 * baseWidth, 3 * baseWidth };
                    break;
                case LineTypeStyle.Hidden:
                    _lineDashPattern = new float[] { 3 * baseWidth, 3 * baseWidth };
                    break;
                default:
                    _lineDashPattern = null;
                    break;
            }

            _dashPatternNeedsUpdate = false;
            return _lineDashPattern;
        }

        /// <summary>
        /// 创建图案着色器
        /// </summary>
        private SKShader CreatePatternShader()
        {
            var size = (int)(16 * _patternScale);
            var bitmap = new SKBitmap(size, size);
            var canvas = new SKCanvas(bitmap);
            
            canvas.Clear(SKColors.Transparent);
            
            var paint = new SKPaint
            {
                Color = GetColorWithAlpha(_fillColor),
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1,
                IsAntialias = true
            };

            switch (_fillPattern)
            {
                case FillPattern.Horizontal:
                    for (int y = 0; y < size; y += 4)
                        canvas.DrawLine(0, y, size, y, paint);
                    break;
                case FillPattern.Vertical:
                    for (int x = 0; x < size; x += 4)
                        canvas.DrawLine(x, 0, x, size, paint);
                    break;
                case FillPattern.Diagonal:
                    for (int i = -size; i < size * 2; i += 4)
                        canvas.DrawLine(i, 0, i + size, size, paint);
                    break;
                case FillPattern.BackDiagonal:
                    for (int i = 0; i < size * 2; i += 4)
                        canvas.DrawLine(i, 0, i - size, size, paint);
                    break;
                case FillPattern.Cross:
                    for (int y = 0; y < size; y += 4)
                        canvas.DrawLine(0, y, size, y, paint);
                    for (int x = 0; x < size; x += 4)
                        canvas.DrawLine(x, 0, x, size, paint);
                    break;
                case FillPattern.Dots:
                    paint.Style = SKPaintStyle.Fill;
                    for (int y = 2; y < size; y += 4)
                        for (int x = 2; x < size; x += 4)
                            canvas.DrawCircle(x, y, 1, paint);
                    break;
            }

            canvas.Dispose();
            return SKShader.CreateBitmap(bitmap, SKShaderTileMode.Repeat, SKShaderTileMode.Repeat);
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _cachedPaint?.Dispose();
            _cachedPaint = null;
        }
        #endregion

        #region 静态方法
        /// <summary>
        /// 创建预定义属性
        /// </summary>
        public static class Presets
        {
            public static EntityProperties Default => new EntityProperties();
            public static EntityProperties RedSolid => new EntityProperties(SKColors.Red);
            public static EntityProperties BlueDashed => new EntityProperties(SKColors.Blue, LineTypeStyle.Dashed);
            public static EntityProperties GreenDotted => new EntityProperties(SKColors.Green, LineTypeStyle.Dotted);
            public static EntityProperties ConstructionLine => new EntityProperties(SKColors.Gray, LineTypeStyle.DashDot, LineWeightValue.LineWeight009);
            public static EntityProperties CenterLine => new EntityProperties(SKColors.Blue, LineTypeStyle.Center, LineWeightValue.LineWeight015);
            public static EntityProperties BorderLine => new EntityProperties(SKColors.Black, LineTypeStyle.Continuous, LineWeightValue.LineWeight035);
        }
        #endregion
    }
} 