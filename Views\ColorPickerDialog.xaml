<Window x:Class="McLaser.EditViewerSk.Views.ColorPickerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="颜色选择器" Height="450" Width="600" 
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="ColorButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="25"/>
            <Setter Property="Height" Value="25"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="Gray"/>
        </Style>
        
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="200"/>
        </Grid.ColumnDefinitions>
        
        <!-- 颜色选择区域 -->
        <Border Grid.Row="0" Grid.Column="0" Grid.RowSpan="2" BorderBrush="Gray" BorderThickness="1" Margin="0,0,10,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- HSV颜色选择器 -->
                <StackPanel Grid.Row="0" Margin="10">
                    <TextBlock Text="HSV颜色选择" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <!-- 色相滑块 -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="色相:" Width="40" VerticalAlignment="Center"/>
                        <Slider Name="HueSlider" Width="200" Minimum="0" Maximum="360" 
                                Value="0" ValueChanged="HueSlider_ValueChanged"/>
                        <TextBlock Name="HueValueText" Text="0°" Width="30" VerticalAlignment="Center" Margin="5,0"/>
                    </StackPanel>
                    
                    <!-- 饱和度滑块 -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="饱和度:" Width="40" VerticalAlignment="Center"/>
                        <Slider Name="SaturationSlider" Width="200" Minimum="0" Maximum="100" 
                                Value="100" ValueChanged="SaturationSlider_ValueChanged"/>
                        <TextBlock Name="SaturationValueText" Text="100%" Width="30" VerticalAlignment="Center" Margin="5,0"/>
                    </StackPanel>
                    
                    <!-- 明度滑块 -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="明度:" Width="40" VerticalAlignment="Center"/>
                        <Slider Name="ValueSlider" Width="200" Minimum="0" Maximum="100" 
                                Value="100" ValueChanged="ValueSlider_ValueChanged"/>
                        <TextBlock Name="ValueValueText" Text="100%" Width="30" VerticalAlignment="Center" Margin="5,0"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- RGB输入 -->
                <StackPanel Grid.Row="1" Margin="10">
                    <TextBlock Text="RGB值" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="60"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Text="R:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                        <TextBox Name="RedTextBox" Grid.Row="0" Grid.Column="1" Height="25" 
                                 Text="255" TextChanged="RgbTextBox_TextChanged"/>
                        
                        <TextBlock Text="G:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center"/>
                        <TextBox Name="GreenTextBox" Grid.Row="0" Grid.Column="3" Height="25" 
                                 Text="0" TextChanged="RgbTextBox_TextChanged"/>
                        
                        <TextBlock Text="B:" Grid.Row="0" Grid.Column="4" VerticalAlignment="Center"/>
                        <TextBox Name="BlueTextBox" Grid.Row="0" Grid.Column="5" Height="25" 
                                 Text="0" TextChanged="RgbTextBox_TextChanged"/>
                        
                        <TextBlock Text="十六进制:" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" VerticalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBox Name="HexTextBox" Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="4" Height="25" 
                                 Text="#FF0000" Margin="0,10,0,0" TextChanged="HexTextBox_TextChanged"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 预设颜色和预览 -->
        <StackPanel Grid.Row="0" Grid.Column="1" Grid.RowSpan="2">
            <!-- 颜色预览 -->
            <TextBlock Text="颜色预览" Style="{StaticResource HeaderTextStyle}"/>
            <Border Name="ColorPreviewBorder" Height="60" Background="Red" 
                    BorderBrush="Gray" BorderThickness="1" Margin="0,5"/>
            
            <!-- 预设颜色 -->
            <TextBlock Text="预设颜色" Style="{StaticResource HeaderTextStyle}" Margin="0,15,0,5"/>
            <WrapPanel Name="PresetColorsPanel">
                <!-- 基础颜色 -->
                <Button Style="{StaticResource ColorButtonStyle}" Background="Red" Click="PresetColor_Click" Tag="#FF0000"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Green" Click="PresetColor_Click" Tag="#00FF00"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Blue" Click="PresetColor_Click" Tag="#0000FF"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Yellow" Click="PresetColor_Click" Tag="#FFFF00"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Cyan" Click="PresetColor_Click" Tag="#00FFFF"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Magenta" Click="PresetColor_Click" Tag="#FF00FF"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="Black" Click="PresetColor_Click" Tag="#000000"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="White" Click="PresetColor_Click" Tag="#FFFFFF"/>
                
                <!-- 灰度颜色 -->
                <Button Style="{StaticResource ColorButtonStyle}" Background="#808080" Click="PresetColor_Click" Tag="#808080"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#C0C0C0" Click="PresetColor_Click" Tag="#C0C0C0"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#404040" Click="PresetColor_Click" Tag="#404040"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#606060" Click="PresetColor_Click" Tag="#606060"/>
                
                <!-- 常用CAD颜色 -->
                <Button Style="{StaticResource ColorButtonStyle}" Background="#800000" Click="PresetColor_Click" Tag="#800000"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#008000" Click="PresetColor_Click" Tag="#008000"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#000080" Click="PresetColor_Click" Tag="#000080"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#808000" Click="PresetColor_Click" Tag="#808000"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#800080" Click="PresetColor_Click" Tag="#800080"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#008080" Click="PresetColor_Click" Tag="#008080"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#FFA500" Click="PresetColor_Click" Tag="#FFA500"/>
                <Button Style="{StaticResource ColorButtonStyle}" Background="#FFC0CB" Click="PresetColor_Click" Tag="#FFC0CB"/>
            </WrapPanel>
            
            <!-- 最近使用的颜色 -->
            <TextBlock Text="最近使用" Style="{StaticResource HeaderTextStyle}" Margin="0,15,0,5"/>
            <WrapPanel Name="RecentColorsPanel" Height="60">
                <!-- 最近使用的颜色将动态添加 -->
            </WrapPanel>
        </StackPanel>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                    Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="确定" Width="80" Height="35" Margin="5" Click="OK_Click" IsDefault="True"/>
            <Button Content="取消" Width="80" Height="35" Margin="5" Click="Cancel_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
