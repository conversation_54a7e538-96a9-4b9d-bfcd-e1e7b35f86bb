using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace McLaser.EditViewerSk.Tracking
{
    /// <summary>
    /// 极轴追踪设置类
    /// </summary>
    public class PolarTrackingSettings : INotifyPropertyChanged
    {
        private bool _isEnabled = true;
        private float _angleTolerance = 2.0f;
        private bool _showAngleInfo = true;
        private bool _showDistanceInfo = true;
        private bool _extendToScreenEdge = true;
        private float _trackingLineWidth = 1.0f;
        private List<float> _trackingAngles = new List<float>();

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 是否启用极轴追踪
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged(nameof(IsEnabled));
                }
            }
        }

        /// <summary>
        /// 角度容差（度）
        /// </summary>
        public float AngleTolerance
        {
            get => _angleTolerance;
            set
            {
                if (Math.Abs(_angleTolerance - value) > 0.001f)
                {
                    _angleTolerance = Math.Max(0.1f, Math.Min(10.0f, value));
                    OnPropertyChanged(nameof(AngleTolerance));
                }
            }
        }

        /// <summary>
        /// 显示角度信息
        /// </summary>
        public bool ShowAngleInfo
        {
            get => _showAngleInfo;
            set
            {
                if (_showAngleInfo != value)
                {
                    _showAngleInfo = value;
                    OnPropertyChanged(nameof(ShowAngleInfo));
                }
            }
        }

        /// <summary>
        /// 显示距离信息
        /// </summary>
        public bool ShowDistanceInfo
        {
            get => _showDistanceInfo;
            set
            {
                if (_showDistanceInfo != value)
                {
                    _showDistanceInfo = value;
                    OnPropertyChanged(nameof(ShowDistanceInfo));
                }
            }
        }

        /// <summary>
        /// 追踪线延伸到屏幕边缘
        /// </summary>
        public bool ExtendToScreenEdge
        {
            get => _extendToScreenEdge;
            set
            {
                if (_extendToScreenEdge != value)
                {
                    _extendToScreenEdge = value;
                    OnPropertyChanged(nameof(ExtendToScreenEdge));
                }
            }
        }

        /// <summary>
        /// 追踪线宽度
        /// </summary>
        public float TrackingLineWidth
        {
            get => _trackingLineWidth;
            set
            {
                if (Math.Abs(_trackingLineWidth - value) > 0.001f)
                {
                    _trackingLineWidth = Math.Max(0.5f, Math.Min(5.0f, value));
                    OnPropertyChanged(nameof(TrackingLineWidth));
                }
            }
        }

        /// <summary>
        /// 追踪角度列表
        /// </summary>
        public List<float> TrackingAngles
        {
            get => _trackingAngles;
            set
            {
                _trackingAngles = value ?? new List<float>();
                OnPropertyChanged(nameof(TrackingAngles));
            }
        }

        /// <summary>
        /// 构造函数，初始化默认设置
        /// </summary>
        public PolarTrackingSettings()
        {
            // 初始化默认追踪角度
            _trackingAngles = new List<float> { 0, 30, 45, 60, 90, 120, 135, 180 };
        }

        /// <summary>
        /// 复制设置
        /// </summary>
        public PolarTrackingSettings Clone()
        {
            return new PolarTrackingSettings
            {
                IsEnabled = this.IsEnabled,
                AngleTolerance = this.AngleTolerance,
                ShowAngleInfo = this.ShowAngleInfo,
                ShowDistanceInfo = this.ShowDistanceInfo,
                ExtendToScreenEdge = this.ExtendToScreenEdge,
                TrackingLineWidth = this.TrackingLineWidth,
                TrackingAngles = new List<float>(this.TrackingAngles)
            };
        }

        /// <summary>
        /// 从另一个设置对象复制值
        /// </summary>
        public void CopyFrom(PolarTrackingSettings other)
        {
            if (other == null) return;

            IsEnabled = other.IsEnabled;
            AngleTolerance = other.AngleTolerance;
            ShowAngleInfo = other.ShowAngleInfo;
            ShowDistanceInfo = other.ShowDistanceInfo;
            ExtendToScreenEdge = other.ExtendToScreenEdge;
            TrackingLineWidth = other.TrackingLineWidth;
            TrackingAngles = new List<float>(other.TrackingAngles);
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            IsEnabled = true;
            AngleTolerance = 2.0f;
            ShowAngleInfo = true;
            ShowDistanceInfo = true;
            ExtendToScreenEdge = true;
            TrackingLineWidth = 1.0f;
            TrackingAngles = new List<float> { 0, 30, 45, 60, 90, 120, 135, 180 };
        }

        /// <summary>
        /// 检查指定角度是否在追踪角度范围内
        /// </summary>
        public bool IsAngleTracked(float angle)
        {
            // 标准化角度到0-360范围
            angle = angle % 360;
            if (angle < 0) angle += 360;

            foreach (var trackingAngle in TrackingAngles)
            {
                var normalizedTrackingAngle = trackingAngle % 360;
                if (normalizedTrackingAngle < 0) normalizedTrackingAngle += 360;

                var diff = Math.Abs(angle - normalizedTrackingAngle);
                if (diff > 180) diff = 360 - diff;

                if (diff <= AngleTolerance)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 获取最接近的追踪角度
        /// </summary>
        public float GetNearestTrackingAngle(float angle)
        {
            if (TrackingAngles.Count == 0) return angle;

            // 标准化角度到0-360范围
            angle = angle % 360;
            if (angle < 0) angle += 360;

            float nearestAngle = TrackingAngles[0];
            float minDiff = float.MaxValue;

            foreach (var trackingAngle in TrackingAngles)
            {
                var normalizedTrackingAngle = trackingAngle % 360;
                if (normalizedTrackingAngle < 0) normalizedTrackingAngle += 360;

                var diff = Math.Abs(angle - normalizedTrackingAngle);
                if (diff > 180) diff = 360 - diff;

                if (diff < minDiff)
                {
                    minDiff = diff;
                    nearestAngle = normalizedTrackingAngle;
                }
            }

            return nearestAngle;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
