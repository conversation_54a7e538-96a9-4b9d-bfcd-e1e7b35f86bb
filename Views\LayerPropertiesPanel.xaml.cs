using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Managers;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using SkiaSharp;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 图层属性管理面板
    /// </summary>
    public partial class LayerPropertiesPanel : UserControl
    {
        private DocumentBase _document;
        private LayerManager _layerManager;
        private EntityLayer _selectedLayer;
        private bool _isUpdating = false;

        public ObservableCollection<object> AvailableLinetypes { get; set; }

        public LayerPropertiesPanel()
        {
            InitializeComponent();
            AvailableLinetypes = new ObservableCollection<object>();
            InitializeConverters();
        }

        public void Initialize(DocumentBase document)
        {
            _document = document;
            if (_document != null)
            {
                _layerManager = new LayerManager(_document);
                LoadLayers();
                LoadLinetypes();
            }
        }

        private void InitializeConverters()
        {
            // 添加值转换器到资源
            Resources.Add("BoolToVisibilityIconConverter", new BoolToIconConverter("👁", "🚫"));
            Resources.Add("BoolToLockIconConverter", new BoolToIconConverter("🔒", "🔓"));
            Resources.Add("BoolToMarkerIconConverter", new BoolToIconConverter("✓", "✗"));
            Resources.Add("ColorToBrushConverter", new ColorToBrushConverter());
        }

        private void LoadLayers()
        {
            if (_layerManager != null)
            {
                LayersDataGrid.ItemsSource = _layerManager.Layers;
            }
        }

        private void LoadLinetypes()
        {
            // TODO: 从LinetypeManager加载线型
            AvailableLinetypes.Clear();
            AvailableLinetypes.Add(new { Name = "实线" });
            AvailableLinetypes.Add(new { Name = "虚线" });
            AvailableLinetypes.Add(new { Name = "点线" });
            AvailableLinetypes.Add(new { Name = "点划线" });
        }

        private void LayersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isUpdating) return;

            _selectedLayer = LayersDataGrid.SelectedItem as EntityLayer;
            LoadSelectedLayerProperties();
        }

        private void LoadSelectedLayerProperties()
        {
            if (_selectedLayer == null)
            {
                ClearPropertiesPanel();
                return;
            }

            _isUpdating = true;
            try
            {
                LayerNameTextBox.Text = _selectedLayer.Name;
                LayerDescriptionTextBox.Text = _selectedLayer.Description;
                // LayerLineWeightTextBox.Text = _selectedLayer.LineWeight.ToString();
                
                // TODO: 设置颜色和线型
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void ClearPropertiesPanel()
        {
            LayerNameTextBox.Text = "";
            LayerDescriptionTextBox.Text = "";
            LayerLineWeightTextBox.Text = "";
        }

        private void NewLayer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newLayer = _layerManager.CreateLayer($"图层{_layerManager.Layers.Count + 1}");
                LayersDataGrid.SelectedItem = newLayer;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建图层失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteLayer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedLayer == null) return;

            var result = MessageBox.Show($"确定要删除图层 '{_selectedLayer.Name}' 吗？", "确认删除", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _layerManager.DeleteLayer(_selectedLayer);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除图层失败: {ex.Message}", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CopyLayer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedLayer == null) return;

            try
            {
                var copiedLayer = _layerManager.CreateLayer(_selectedLayer.Name + "_副本");
                copiedLayer.Description = _selectedLayer.Description;
                // TODO: 复制其他属性
                LayersDataGrid.SelectedItem = copiedLayer;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制图层失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MoveUp_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现图层上移
            MessageBox.Show("图层上移功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void MoveDown_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现图层下移
            MessageBox.Show("图层下移功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ToggleVisibility_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var layer = button?.Tag as EntityLayer;
            if (layer != null)
            {
                _layerManager.SetLayerVisible(layer, !layer.IsVisible);
            }
        }

        private void ToggleLock_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var layer = button?.Tag as EntityLayer;
            if (layer != null)
            {
                _layerManager.SetLayerLocked(layer, !layer.IsLocked);
            }
        }

        private void ToggleMarkerable_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var layer = button?.Tag as EntityLayer;
            if (layer != null)
            {
                _layerManager.SetLayerMarkerable(layer, !layer.IsMarkerable);
            }
        }

        private void ColorRectangle_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var rectangle = sender as System.Windows.Shapes.Rectangle;
                var layer = rectangle?.Tag as EntityLayer;
                if (layer != null)
                {
                    var colorPicker = new ColorPickerDialog();
                    if (colorPicker.ShowDialog() == true)
                    {
                        var selectedColor = colorPicker.SelectedSKColor;
                        // TODO: 应用颜色到图层
                        // layer.LayerColor = selectedColor;

                        // 更新UI显示
                        rectangle.Fill = new SolidColorBrush(colorPicker.SelectedColor);
                        LayersDataGrid.Items.Refresh();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LayerColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedLayer != null)
                {
                    var colorPicker = new ColorPickerDialog();
                    if (colorPicker.ShowDialog() == true)
                    {
                        var selectedColor = colorPicker.SelectedSKColor;
                        // TODO: 应用颜色到选中图层
                        // _selectedLayer.LayerColor = selectedColor;

                        // 更新按钮显示
                        LayerColorButton.Content = $"颜色: #{selectedColor.Red:X2}{selectedColor.Green:X2}{selectedColor.Blue:X2}";
                        LayersDataGrid.Items.Refresh();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择颜色失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyProperties_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedLayer == null || _isUpdating) return;

            try
            {
                // 应用属性更改
                if (!string.IsNullOrEmpty(LayerNameTextBox.Text))
                {
                    _layerManager.RenameLayer(_selectedLayer, LayerNameTextBox.Text);
                }

                _selectedLayer.Description = LayerDescriptionTextBox.Text;

                // TODO: 应用其他属性

                MessageBox.Show("图层属性已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用属性失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // 值转换器
    public class BoolToIconConverter : IValueConverter
    {
        private string _trueIcon;
        private string _falseIcon;

        public BoolToIconConverter(string trueIcon, string falseIcon)
        {
            _trueIcon = trueIcon;
            _falseIcon = falseIcon;
        }

        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? _trueIcon : _falseIcon;
            }
            return _falseIcon;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class ColorToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            // TODO: 实现SKColor到Brush的转换
            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
