using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Threading
{
    /// <summary>
    /// 异步文件管理器
    /// 提供线程安全的文件I/O操作，优化大文件处理性能
    /// </summary>
    public class AsyncFileManager : IDisposable
    {
        #region 私有字段

        private static AsyncFileManager _instance;
        private static readonly object _lockObject = new object();

        private readonly SemaphoreSlim _fileSemaphore;
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _fileLocks;
        private readonly CancellationTokenSource _cancellationTokenSource;
        
        // 性能统计
        private long _readOperations;
        private long _writeOperations;
        private TimeSpan _totalReadTime;
        private TimeSpan _totalWriteTime;
        
        // 配置参数
        private readonly int _maxConcurrentOperations = 4;
        private readonly int _bufferSize = 8192;
        private bool _isDisposed;

        #endregion

        #region 单例模式

        public static AsyncFileManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new AsyncFileManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private AsyncFileManager()
        {
            _fileSemaphore = new SemaphoreSlim(_maxConcurrentOperations, _maxConcurrentOperations);
            _fileLocks = new ConcurrentDictionary<string, SemaphoreSlim>();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 读操作次数
        /// </summary>
        public long ReadOperations => _readOperations;

        /// <summary>
        /// 写操作次数
        /// </summary>
        public long WriteOperations => _writeOperations;

        /// <summary>
        /// 平均读取时间
        /// </summary>
        public double AverageReadTime => _readOperations > 0 ? _totalReadTime.TotalMilliseconds / _readOperations : 0;

        /// <summary>
        /// 平均写入时间
        /// </summary>
        public double AverageWriteTime => _writeOperations > 0 ? _totalWriteTime.TotalMilliseconds / _writeOperations : 0;

        #endregion

        #region 异步文件读取

        /// <summary>
        /// 异步读取文本文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件内容</returns>
        public async Task<string> ReadTextFileAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    using var reader = new StreamReader(filePath, bufferSize: _bufferSize);
                    var content = await reader.ReadToEndAsync();
                    
                    Interlocked.Increment(ref _readOperations);
                    _totalReadTime += DateTime.Now - startTime;
                    
                    return content;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        /// <summary>
        /// 异步读取二进制文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件数据</returns>
        public async Task<byte[]> ReadBinaryFileAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    var data = await File.ReadAllBytesAsync(filePath, cancellationToken);
                    
                    Interlocked.Increment(ref _readOperations);
                    _totalReadTime += DateTime.Now - startTime;
                    
                    return data;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        /// <summary>
        /// 异步流式读取大文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="processor">行处理器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task ReadLargeFileAsync(string filePath, Func<string, Task<bool>> processor, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    using var reader = new StreamReader(filePath, bufferSize: _bufferSize);
                    string line;
                    
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        
                        if (!await processor(line))
                        {
                            break; // 处理器返回false表示停止读取
                        }
                    }
                    
                    Interlocked.Increment(ref _readOperations);
                    _totalReadTime += DateTime.Now - startTime;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        #endregion

        #region 异步文件写入

        /// <summary>
        /// 异步写入文本文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        /// <param name="append">是否追加</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task WriteTextFileAsync(string filePath, string content, bool append = false, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    // 确保目录存在
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    using var writer = new StreamWriter(filePath, append, bufferSize: _bufferSize);
                    await writer.WriteAsync(content);
                    await writer.FlushAsync();
                    
                    Interlocked.Increment(ref _writeOperations);
                    _totalWriteTime += DateTime.Now - startTime;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        /// <summary>
        /// 异步写入二进制文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="data">文件数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task WriteBinaryFileAsync(string filePath, byte[] data, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            if (data == null)
                throw new ArgumentNullException(nameof(data));

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    // 确保目录存在
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    await File.WriteAllBytesAsync(filePath, data, cancellationToken);
                    
                    Interlocked.Increment(ref _writeOperations);
                    _totalWriteTime += DateTime.Now - startTime;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        /// <summary>
        /// 异步批量写入文本行
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="lines">文本行</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task WriteTextLinesAsync(string filePath, string[] lines, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            if (lines == null)
                throw new ArgumentNullException(nameof(lines));

            var startTime = DateTime.Now;
            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var fileLock = GetFileLock(filePath);
                await fileLock.WaitAsync(cancellationToken);

                try
                {
                    // 确保目录存在
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    await File.WriteAllLinesAsync(filePath, lines, cancellationToken);
                    
                    Interlocked.Increment(ref _writeOperations);
                    _totalWriteTime += DateTime.Now - startTime;
                }
                finally
                {
                    fileLock.Release();
                }
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        #endregion

        #region 文件操作

        /// <summary>
        /// 异步复制文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task CopyFileAsync(string sourcePath, string destinationPath, bool overwrite = false, CancellationToken cancellationToken = default)
        {
            if (!File.Exists(sourcePath))
                throw new FileNotFoundException($"Source file not found: {sourcePath}");

            if (File.Exists(destinationPath) && !overwrite)
                throw new InvalidOperationException($"Destination file already exists: {destinationPath}");

            await _fileSemaphore.WaitAsync(cancellationToken);

            try
            {
                var sourceData = await ReadBinaryFileAsync(sourcePath, cancellationToken);
                await WriteBinaryFileAsync(destinationPath, sourceData, cancellationToken);
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否存在</returns>
        public bool FileExists(string filePath)
        {
            return !string.IsNullOrEmpty(filePath) && File.Exists(filePath);
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小（字节）</returns>
        public long GetFileSize(string filePath)
        {
            if (!FileExists(filePath)) return 0;
            
            try
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取文件锁
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件锁</returns>
        private SemaphoreSlim GetFileLock(string filePath)
        {
            var normalizedPath = Path.GetFullPath(filePath).ToLowerInvariant();
            return _fileLocks.GetOrAdd(normalizedPath, _ => new SemaphoreSlim(1, 1));
        }

        #endregion

        #region 性能监控

        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <returns>性能统计</returns>
        public AsyncFileStats GetStats()
        {
            return new AsyncFileStats
            {
                ReadOperations = _readOperations,
                WriteOperations = _writeOperations,
                AverageReadTime = AverageReadTime,
                AverageWriteTime = AverageWriteTime,
                TotalReadTime = _totalReadTime.TotalMilliseconds,
                TotalWriteTime = _totalWriteTime.TotalMilliseconds,
                MaxConcurrentOperations = _maxConcurrentOperations,
                BufferSize = _bufferSize,
                ActiveFileLocks = _fileLocks.Count
            };
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetStats()
        {
            Interlocked.Exchange(ref _readOperations, 0);
            Interlocked.Exchange(ref _writeOperations, 0);
            _totalReadTime = TimeSpan.Zero;
            _totalWriteTime = TimeSpan.Zero;
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                _cancellationTokenSource.Cancel();
                _fileSemaphore?.Dispose();

                foreach (var fileLock in _fileLocks.Values)
                {
                    fileLock?.Dispose();
                }
                _fileLocks.Clear();

                _cancellationTokenSource?.Dispose();
                _isDisposed = true;

                Debug.WriteLine("AsyncFileManager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"AsyncFileManager dispose error: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 异步文件操作统计信息
    /// </summary>
    public class AsyncFileStats
    {
        public long ReadOperations { get; set; }
        public long WriteOperations { get; set; }
        public double AverageReadTime { get; set; }
        public double AverageWriteTime { get; set; }
        public double TotalReadTime { get; set; }
        public double TotalWriteTime { get; set; }
        public int MaxConcurrentOperations { get; set; }
        public int BufferSize { get; set; }
        public int ActiveFileLocks { get; set; }
        public long TotalOperations => ReadOperations + WriteOperations;
        public double AverageOperationTime => TotalOperations > 0 ? (TotalReadTime + TotalWriteTime) / TotalOperations : 0;
    }
}
