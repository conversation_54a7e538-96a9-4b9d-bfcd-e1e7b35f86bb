using System;
using System.Collections.Generic;
using System.Numerics;
using System.Windows.Forms;
using SkiaSharp;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Graphics;

namespace McLaser.EditViewerSk.Viewport
{
    /// <summary>
    /// 视图工具栏管理器
    /// 管理所有视图相关的工具栏功能，包括缩放、平移、视图历史等
    /// </summary>
    public class ViewToolbarManager
    {
        private readonly ViewBase _viewBase;
        private readonly ViewportManager _viewportManager;
        private Renderers.ZoomWindowRenderer _zoomWindowRenderer;

        // 视图模式
        private ViewToolMode _currentMode = ViewToolMode.None;
        
        // 缩放窗口相关
        private bool _isZoomWindowActive = false;
        private Vector2? _zoomWindowStartPoint = null;
        private Vector2? _zoomWindowEndPoint = null;
        
        // 实时缩放相关
        private bool _isRealtimeZoomActive = false;
        private Vector2? _lastZoomPoint = null;
        
        // 平移工具相关
        private bool _isPanToolActive = false;
        private Vector2? _lastPanPoint = null;
        
        // 视图历史
        private readonly Stack<ViewportState> _viewHistory;
        private readonly Stack<ViewportState> _viewForwardHistory;
        private const int MaxHistorySize = 50;
        
        // 事件
        public event EventHandler<ViewToolModeChangedEventArgs> ModeChanged;
        public event EventHandler<ZoomWindowEventArgs> ZoomWindowUpdated;
        
        public ViewToolbarManager(ViewBase viewBase, ViewportManager viewportManager)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _viewportManager = viewportManager ?? throw new ArgumentNullException(nameof(viewportManager));
            
            _viewHistory = new Stack<ViewportState>();
            _viewForwardHistory = new Stack<ViewportState>();
            
            // 订阅视图事件
            _viewBase.MouseDown += OnMouseDown;
            _viewBase.MouseMove += OnMouseMove;
            _viewBase.MouseUp += OnMouseUp;
            _viewBase.KeyDown += OnKeyDown;
        }
        
        #region 属性
        
        /// <summary>
        /// 当前视图工具模式
        /// </summary>
        public ViewToolMode CurrentMode
        {
            get => _currentMode;
            private set
            {
                if (_currentMode != value)
                {
                    var oldMode = _currentMode;
                    _currentMode = value;
                    OnModeChanged(oldMode, value);
                }
            }
        }
        
        /// <summary>
        /// 是否可以执行上一视图
        /// </summary>
        public bool CanGoToPreviousView => _viewHistory.Count > 0;
        
        /// <summary>
        /// 是否可以执行下一视图
        /// </summary>
        public bool CanGoToNextView => _viewForwardHistory.Count > 0;

        /// <summary>
        /// 设置缩放窗口渲染器
        /// </summary>
        public void SetZoomWindowRenderer(Renderers.ZoomWindowRenderer renderer)
        {
            _zoomWindowRenderer = renderer;
        }
        
        #endregion
        
        #region 视图工具激活方法
        
        /// <summary>
        /// 激活缩放窗口工具
        /// </summary>
        public void ActivateZoomWindow()
        {
            DeactivateCurrentTool();
            CurrentMode = ViewToolMode.ZoomWindow;
            _isZoomWindowActive = true;
            _zoomWindowStartPoint = null;
            _zoomWindowEndPoint = null;
            
            // 更改鼠标光标
            _viewBase.Viewer.Cursor = Cursors.Cross;
        }
        
        /// <summary>
        /// 激活实时缩放工具
        /// </summary>
        public void ActivateRealtimeZoom()
        {
            DeactivateCurrentTool();
            CurrentMode = ViewToolMode.RealtimeZoom;
            _isRealtimeZoomActive = true;
            _lastZoomPoint = null;
            
            // 更改鼠标光标
            _viewBase.Viewer.Cursor = Cursors.SizeNS;
        }
        
        /// <summary>
        /// 激活平移工具
        /// </summary>
        public void ActivatePanTool()
        {
            DeactivateCurrentTool();
            CurrentMode = ViewToolMode.Pan;
            _isPanToolActive = true;
            _lastPanPoint = null;
            
            // 更改鼠标光标
            _viewBase.Viewer.Cursor = Cursors.Hand;
        }
        
        /// <summary>
        /// 停用当前工具
        /// </summary>
        public void DeactivateCurrentTool()
        {
            _isZoomWindowActive = false;
            _isRealtimeZoomActive = false;
            _isPanToolActive = false;
            
            _zoomWindowStartPoint = null;
            _zoomWindowEndPoint = null;
            _lastZoomPoint = null;
            _lastPanPoint = null;
            
            CurrentMode = ViewToolMode.None;
            
            // 恢复默认鼠标光标
            _viewBase.Viewer.Cursor = Cursors.Default;
        }
        
        #endregion
        
        #region 视图历史管理
        
        /// <summary>
        /// 保存当前视图状态到历史
        /// </summary>
        public void SaveCurrentViewToHistory()
        {
            var currentState = _viewportManager.GetCurrentState();
            if (currentState != null)
            {
                _viewHistory.Push(currentState);
                
                // 限制历史大小
                if (_viewHistory.Count > MaxHistorySize)
                {
                    var tempStack = new Stack<ViewportState>();
                    for (int i = 0; i < MaxHistorySize - 1; i++)
                    {
                        tempStack.Push(_viewHistory.Pop());
                    }
                    _viewHistory.Clear();
                    while (tempStack.Count > 0)
                    {
                        _viewHistory.Push(tempStack.Pop());
                    }
                }
                
                // 清空前进历史
                _viewForwardHistory.Clear();
            }
        }
        
        /// <summary>
        /// 返回上一视图
        /// </summary>
        public void GoToPreviousView()
        {
            if (_viewHistory.Count > 0)
            {
                // 保存当前状态到前进历史
                var currentState = _viewportManager.GetCurrentState();
                if (currentState != null)
                {
                    _viewForwardHistory.Push(currentState);
                }
                
                // 恢复上一视图
                var previousState = _viewHistory.Pop();
                _viewportManager.RestoreState(previousState);
            }
        }
        
        /// <summary>
        /// 前进到下一视图
        /// </summary>
        public void GoToNextView()
        {
            if (_viewForwardHistory.Count > 0)
            {
                // 保存当前状态到历史
                var currentState = _viewportManager.GetCurrentState();
                if (currentState != null)
                {
                    _viewHistory.Push(currentState);
                }
                
                // 恢复下一视图
                var nextState = _viewForwardHistory.Pop();
                _viewportManager.RestoreState(nextState);
            }
        }
        
        #endregion
        
        #region 视图预设
        
        /// <summary>
        /// 切换到俯视图
        /// </summary>
        public void SetTopView()
        {
            SaveCurrentViewToHistory();
            _viewportManager.SetStandardView(StandardView.Top);
        }
        
        /// <summary>
        /// 切换到前视图
        /// </summary>
        public void SetFrontView()
        {
            SaveCurrentViewToHistory();
            _viewportManager.SetStandardView(StandardView.Front);
        }
        
        /// <summary>
        /// 切换到右视图
        /// </summary>
        public void SetRightView()
        {
            SaveCurrentViewToHistory();
            _viewportManager.SetStandardView(StandardView.Right);
        }
        
        /// <summary>
        /// 切换到等轴测视图
        /// </summary>
        public void SetIsometricView()
        {
            SaveCurrentViewToHistory();
            _viewportManager.SetStandardView(StandardView.Isometric);
        }
        
        /// <summary>
        /// 缩放到全部显示
        /// </summary>
        public void ZoomToFit()
        {
            SaveCurrentViewToHistory();
            _viewportManager.ZoomToFit();
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnMouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var point = new Vector2(e.X, e.Y);
            
            if (_isZoomWindowActive)
            {
                _zoomWindowStartPoint = point;
                _zoomWindowEndPoint = point;
                _zoomWindowRenderer?.StartZoomWindow(point);
            }
            else if (_isRealtimeZoomActive)
            {
                _lastZoomPoint = point;
            }
            else if (_isPanToolActive)
            {
                _lastPanPoint = point;
            }
        }
        
        private void OnMouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var point = new Vector2(e.X, e.Y);
            
            if (_isZoomWindowActive && _zoomWindowStartPoint.HasValue)
            {
                _zoomWindowEndPoint = point;
                _zoomWindowRenderer?.UpdateZoomWindow(point);
                OnZoomWindowUpdated();
            }
            else if (_isRealtimeZoomActive && _lastZoomPoint.HasValue && e.Button == MouseButtons.Left)
            {
                var deltaY = point.Y - _lastZoomPoint.Value.Y;
                var scaleFactor = deltaY > 0 ? 0.99f : 1.01f;
                _viewportManager.ZoomView(scaleFactor, point);
                _lastZoomPoint = point;
            }
            else if (_isPanToolActive && _lastPanPoint.HasValue && e.Button == MouseButtons.Left)
            {
                var delta = point - _lastPanPoint.Value;
                _viewportManager.PanView(delta.X, delta.Y);
                _lastPanPoint = point;
            }
        }
        
        private void OnMouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (_isZoomWindowActive && _zoomWindowStartPoint.HasValue && _zoomWindowEndPoint.HasValue)
            {
                // 执行缩放窗口
                var rect = new SKRect(
                    Math.Min(_zoomWindowStartPoint.Value.X, _zoomWindowEndPoint.Value.X),
                    Math.Min(_zoomWindowStartPoint.Value.Y, _zoomWindowEndPoint.Value.Y),
                    Math.Max(_zoomWindowStartPoint.Value.X, _zoomWindowEndPoint.Value.X),
                    Math.Max(_zoomWindowStartPoint.Value.Y, _zoomWindowEndPoint.Value.Y)
                );
                
                if (rect.Width > 10 && rect.Height > 10) // 最小尺寸检查
                {
                    SaveCurrentViewToHistory();
                    _viewportManager.ZoomToWindow(rect);
                    _zoomWindowRenderer?.EndZoomWindow();
                }
                else
                {
                    _zoomWindowRenderer?.CancelZoomWindow();
                }

                DeactivateCurrentTool();
            }
        }
        
        private void OnKeyDown(object sender, System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                if (_isZoomWindowActive)
                {
                    _zoomWindowRenderer?.CancelZoomWindow();
                }
                DeactivateCurrentTool();
            }
        }
        
        #endregion
        
        #region 事件触发
        
        private void OnModeChanged(ViewToolMode oldMode, ViewToolMode newMode)
        {
            ModeChanged?.Invoke(this, new ViewToolModeChangedEventArgs(oldMode, newMode));
        }
        
        private void OnZoomWindowUpdated()
        {
            if (_zoomWindowStartPoint.HasValue && _zoomWindowEndPoint.HasValue)
            {
                ZoomWindowUpdated?.Invoke(this, new ZoomWindowEventArgs(_zoomWindowStartPoint.Value, _zoomWindowEndPoint.Value));
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 视图工具模式
    /// </summary>
    public enum ViewToolMode
    {
        None,
        ZoomWindow,
        RealtimeZoom,
        Pan
    }
    
    /// <summary>
    /// 标准视图类型
    /// </summary>
    public enum StandardView
    {
        Top,
        Front,
        Right,
        Isometric
    }
    
    /// <summary>
    /// 视图工具模式变化事件参数
    /// </summary>
    public class ViewToolModeChangedEventArgs : EventArgs
    {
        public ViewToolMode OldMode { get; }
        public ViewToolMode NewMode { get; }
        
        public ViewToolModeChangedEventArgs(ViewToolMode oldMode, ViewToolMode newMode)
        {
            OldMode = oldMode;
            NewMode = newMode;
        }
    }
    
    /// <summary>
    /// 缩放窗口事件参数
    /// </summary>
    public class ZoomWindowEventArgs : EventArgs
    {
        public Vector2 StartPoint { get; }
        public Vector2 EndPoint { get; }
        
        public ZoomWindowEventArgs(Vector2 startPoint, Vector2 endPoint)
        {
            StartPoint = startPoint;
            EndPoint = endPoint;
        }
    }
}
