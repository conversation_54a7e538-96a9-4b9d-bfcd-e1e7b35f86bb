using McLaser.EditViewerSk.Managers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 线型管理器窗口
    /// </summary>
    public partial class LinetypeManagerWindow : Window
    {
        private LinetypeManager _linetypeManager;
        private object _selectedLinetype;
        private bool _isUpdating = false;

        public LinetypeManagerWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            try
            {
                _linetypeManager = LinetypeManager.Instance;
                LoadLinetypes();
                
                // 选择第一个线型
                if (LinetypeListBox.Items.Count > 0)
                {
                    LinetypeListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化线型管理器失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadLinetypes()
        {
            // 创建示例线型数据
            var linetypes = new List<LinetypeInfo>
            {
                new LinetypeInfo { Name = "实线", Description = "连续实线", Pattern = new double[0] },
                new LinetypeInfo { Name = "虚线", Description = "等间距虚线", Pattern = new double[] { 5, -5 } },
                new LinetypeInfo { Name = "点线", Description = "点划线", Pattern = new double[] { 5, -2, 1, -2 } },
                new LinetypeInfo { Name = "点划线", Description = "长划短划线", Pattern = new double[] { 10, -2, 1, -2, 1, -2 } },
                new LinetypeInfo { Name = "双点划线", Description = "双点划线", Pattern = new double[] { 10, -2, 1, -2, 1, -2, 1, -2 } }
            };
            
            LinetypeListBox.ItemsSource = linetypes;
        }

        private void LinetypeListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            _selectedLinetype = LinetypeListBox.SelectedItem;
            LoadLinetypeProperties();
        }

        private void LoadLinetypeProperties()
        {
            if (_selectedLinetype == null)
            {
                ClearPropertiesPanel();
                return;
            }

            _isUpdating = true;
            try
            {
                var linetype = _selectedLinetype as LinetypeInfo;
                if (linetype != null)
                {
                    LinetypeNameTextBox.Text = linetype.Name;
                    DescriptionTextBox.Text = linetype.Description;
                    PatternTextBox.Text = string.Join(", ", linetype.Pattern);
                    ScaleFactorTextBox.Text = "1.0";
                    LineWidthTextBox.Text = "1.0";
                    
                    UpdatePreview();
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void ClearPropertiesPanel()
        {
            LinetypeNameTextBox.Text = "";
            DescriptionTextBox.Text = "";
            PatternTextBox.Text = "";
            ScaleFactorTextBox.Text = "1.0";
            LineWidthTextBox.Text = "1.0";
        }

        private void NewLinetype_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newLinetype = new LinetypeInfo
                {
                    Name = "新线型",
                    Description = "自定义线型",
                    Pattern = new double[] { 5, -5 }
                };
                
                var linetypes = LinetypeListBox.ItemsSource as List<LinetypeInfo>;
                linetypes?.Add(newLinetype);
                
                LinetypeListBox.Items.Refresh();
                LinetypeListBox.SelectedItem = newLinetype;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建新线型失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteLinetype_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedLinetype == null) return;

            var linetype = _selectedLinetype as LinetypeInfo;
            var result = MessageBox.Show($"确定要删除线型 '{linetype?.Name}' 吗？", "确认删除", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var linetypes = LinetypeListBox.ItemsSource as List<LinetypeInfo>;
                    linetypes?.Remove(linetype);
                    
                    LinetypeListBox.Items.Refresh();
                    
                    // 选择第一个线型
                    if (LinetypeListBox.Items.Count > 0)
                    {
                        LinetypeListBox.SelectedIndex = 0;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除线型失败: {ex.Message}", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void LoadLinetype_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("从文件加载线型功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PresetPattern_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var pattern = button?.Tag as string;
            if (!string.IsNullOrEmpty(pattern))
            {
                PatternTextBox.Text = pattern;
                UpdatePreview();
            }
        }

        private void UpdatePreview_Click(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            try
            {
                // 解析线型模式
                var patternText = PatternTextBox.Text;
                if (string.IsNullOrWhiteSpace(patternText))
                {
                    // 实线
                    PreviewLine.StrokeDashArray = null;
                }
                else
                {
                    var patternValues = patternText.Split(',')
                        .Select(s => s.Trim())
                        .Where(s => !string.IsNullOrEmpty(s))
                        .Select(s => Math.Abs(double.Parse(s)))
                        .ToArray();
                    
                    if (patternValues.Length > 0)
                    {
                        PreviewLine.StrokeDashArray = new DoubleCollection(patternValues);
                    }
                }
                
                // 设置线宽
                if (double.TryParse(LineWidthTextBox.Text, out double lineWidth))
                {
                    PreviewLine.StrokeThickness = Math.Max(0.5, lineWidth);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新预览失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyToLayer_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("应用到图层功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Import_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导入线型功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导出线型功能将在后续版本中实现", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            SaveLinetypeProperties();
            MessageBox.Show("线型已应用", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveLinetypeProperties();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void SaveLinetypeProperties()
        {
            if (_selectedLinetype == null || _isUpdating) return;

            try
            {
                var linetype = _selectedLinetype as LinetypeInfo;
                if (linetype != null)
                {
                    linetype.Name = LinetypeNameTextBox.Text;
                    linetype.Description = DescriptionTextBox.Text;
                    
                    // 解析模式
                    var patternText = PatternTextBox.Text;
                    if (string.IsNullOrWhiteSpace(patternText))
                    {
                        linetype.Pattern = new double[0];
                    }
                    else
                    {
                        linetype.Pattern = patternText.Split(',')
                            .Select(s => s.Trim())
                            .Where(s => !string.IsNullOrEmpty(s))
                            .Select(double.Parse)
                            .ToArray();
                    }
                    
                    LinetypeListBox.Items.Refresh();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存线型属性失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // 线型信息类
    public class LinetypeInfo
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public double[] Pattern { get; set; }
    }
}
