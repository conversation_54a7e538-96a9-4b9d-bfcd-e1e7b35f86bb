﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Marker;
using McLaser.EditViewerSk.UndoRedo;
using McLaser.EditViewerSk.ViewModels;
using McLaser.EditViewerSk.Viewport;
using McLaser.EditViewerSk.Input;
using McLaser.EditViewerSk.UI;
using McLaser.EditViewerSk.Renderers;
using PropertyTools.Wpf;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;
using SaveFileDialog = Microsoft.Win32.SaveFileDialog;
using UserControl = System.Windows.Controls.UserControl;
using ViewBase = McLaser.EditViewerSk.Base.ViewBase;

namespace McLaser.EditViewerSk.Views
{

    public partial class SkEditor : UserControl
    {

        public SKTypeface Font;
        public DocumentBase doc;
        private ViewBase viewer;
        private IMarker _marker;

        public DocumentBase internalDoc;
        private SKGLControl _skglControl;

        // 新的管理器
        private ViewportManager _viewportManager;
        private ViewToolbarManager _viewToolbarManager;
        private InteractiveViewManager _interactiveViewManager;
        private EnhancedShortcutManager _shortcutManager;
        private EnhancedStatusBarManager _statusBarManager;
        private ZoomWindowRenderer _zoomWindowRenderer;

        public DocumentBase Document
        {
            get { return doc; }
            set
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (value == null || value.Equals((object)this.doc))
                        return;
                    else
                    {
                        this.doc = value;
                    }

                    if (viewer == null)
                        viewer = new ViewBase(this._skglControl, doc);
                    else
                        viewer.Document = doc;

                    doc.View = viewer;
                    doc.Action.viewer = viewer;

                    // 初始化统一命令管理器
                    try
                    {
                        var commandManager = Managers.UnifiedCommandManager.Instance;
                        commandManager.Initialize(viewer);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Failed to initialize UnifiedCommandManager: {ex.Message}");
                    }

                    DataContext = new SkEditorViewModel(doc);

                    //doc.Action.Init(doc, _viewer);
                    if (this.doc.Layers.Count == 0)
                        doc.Action.ActLayerNew();
                    doc.ActiveLayer = doc.Layers[0];
                    doc.SelectedEntityChanged -= Document_SelectedEntityChanged;
                    doc.SelectedEntityChanged += Document_SelectedEntityChanged;

                    // 初始化图层属性面板
                    LayerPropertiesPanel.Initialize(doc);

                    // 初始化动态输入系统
                    InitializeDynamicInput();

                    viewer.RepaintCanvas();
                });

            }
        }

        public SkEditor(IMarker marker)
        {
              RenderOptions.ProcessRenderMode = RenderMode.Default;
            InitializeComponent();
            _skglControl = new SKGLControl();
            _skglControl.Dock = System.Windows.Forms.DockStyle.Fill;
       
            //_skglControl.PaintSurface += OnPaintSurface;
            WinFormsHost.Child = _skglControl;

            //this.PreviewKeyDown += SkCanvas_PreviewKeyDown;
            //this.PreviewKeyUp += SkCanvas_PreviewKeyUp;

            internalDoc = new DocumentBase();
            Document = internalDoc;
            DataContext = new SkEditorViewModel(doc);


            marker.OnProgress += _marker_OnProgress;

            doc.Action.EventMarkingViewRegisted += Action_EventMarkingViewRegisted;
            wfpg.PropertyValueChanged += Wfpg_PropertyValueChanged;

            // 初始化新的管理器
            InitializeManagers();
        }

        private void _marker_OnProgress(IMarker sender, IMarkerArg markerArg)
        {
            viewer.DrawDot(markerArg.LaserDot.X, markerArg.LaserDot.Y);
        }

        private void Wfpg_PropertyValueChanged(object s, PropertyValueChangedEventArgs e)
        {
            viewer?.RepaintCanvas();
        }

        private void Action_EventMarkingViewRegisted(object sender, EventArgs e)
        {
            try
            {
                //if (sender is System.Windows.Forms.Control)
                //    this.MarkingControlView.Content = new WindowsFormsHost() { Child = (sender as System.Windows.Forms.Form) };
                //else if (sender is System.Windows.Controls.Control)
                //    this.MarkingControlView.Content = sender as System.Windows.Forms.Control;
            }
            catch (Exception ex)
            {

            }
        }

        private void Document_SelectedEntityChanged(object sender, EventArgs e)
        {
            try
            {
                if (Document.SelectedEntitys == null || Document.SelectedEntitys.Count == 0)
                {
                    wfpg.SelectedObjects = new object[] { Document.ActiveLayer };
                }
                else
                    wfpg.SelectedObjects = Document.SelectedEntitys.ToArray();
                ExpandAllProperties(wfpg);
            }
            catch (Exception ex)
            {

            }
        }

        private void ExpandAllProperties(System.Windows.Forms.PropertyGrid grid)
        {

            grid.Refresh();
            if (grid.SelectedObject != null && grid.Controls.Count > 0)
            {
                var propertyGridView = grid.Controls[0] as System.Windows.Forms.Control;
                if (propertyGridView != null)
                {
                    foreach (GridItem item in wfpg.SelectedGridItem.Parent?.GridItems ?? grid.SelectedGridItem.GridItems)
                    {
                        ExpandGridItem(item);
                    }
                }
            }
        }

        private void ExpandGridItem(GridItem item)
        {
            if (item.Expandable)
            {
                item.Expanded = true;
                foreach (GridItem childItem in item.GridItems)
                {
                    ExpandGridItem(childItem);
                }
            }
        }

        //private void SkCanvas_PreviewKeyUp(object sender, KeyEventArgs e)
        //{
        //    viewer.SkContainer_KeyUp(sender, e);
        //}

        //private void SkCanvas_PreviewKeyDown(object sender, KeyEventArgs e)
        //{
        //    viewer.SkContainer_KeyDown(sender, e);
        //}

        private void btnDrawLine_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActDrawLine();
        }

        private void btnLoadDxf_Click(object sender, RoutedEventArgs e)
        {

            doc.Action.ActReadDxf();
        }

        private void btnNew_Click(object sender, RoutedEventArgs e)
        {
            Document = new DocumentBase();

            doc.Action.ActLayerNew();
        }

        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择文件",
                Filter = "所有文件 (*.*)|*.*|mc文件(*.mc)|*.mc ",
                FilterIndex = 1,
                Multiselect = false // 设置是否允许选择多个文件
            };


            if (openFileDialog.ShowDialog() == true)
            {
                Document = Action.ActLoad(openFileDialog.FileName);
                Document.FileName = openFileDialog.FileName;
                Document.View.OnZoomFit();
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(doc.FileName))
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "mct marker data files (*.mc)|*.mc|All Files (*.*)|*.*";
                sfd.FileName = string.Empty;
                sfd.Title = "另存为 ...";
                if (sfd.ShowDialog() == false)
                    return;

                doc.FileName = sfd.FileName;
            }

            doc.Action.ActSave(doc, doc.FileName);

        }

        private void btnRedo_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActRedo();
        }

        private void btnUndo_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActUndo();
        }

        private void btnDelete_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityDelete(doc.SelectedEntitys);
        }

        private void btnGroup_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityGroup(doc.SelectedEntitys);
        }

        private void btnUnGroup_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityUnGroup(doc.SelectedEntitys);

        }

        private void btnSort_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntitySort(doc.SelectedEntitys, doc.ActiveLayer, EntitySort.LeftToRight);
        }

        private void btnReverse_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityReverse(doc.SelectedEntitys, doc.ActiveLayer);
        }

        private void tv_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            List<EntityBase> list = new List<EntityBase>();
            doc.SelectedEntitys.Clear();
            foreach (var item in tv.SelectedItems)
            {
                list.Add((EntityBase)item);
            }
            doc.SelectedEntitys = list;
            viewer.RepaintCanvas();
        }

        private void tv_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Up || e.Key == Key.Down)
            {
                if (e.IsUp)
                {
                    List<EntityBase> list = new List<EntityBase>();
                    doc.SelectedEntitys.Clear();
                    foreach (var item in tv.SelectedItems)
                    {
                        list.Add((EntityBase)item);
                    }
                    doc.SelectedEntitys = list;
                    viewer.RepaintCanvas();
                }
            }
        }

        private void btnCopy_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnCut_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnPaste_Click(object sender, RoutedEventArgs e)
        {

        }

        private void InsertEntityCmd_Click(object sender, RoutedEventArgs e)
        {
            //"插入测高使能"  
            //"插入C轴跟随使能"  
            //"插入Z轴跟随使能" 
            //"插入激光初始化" 
            //"插入清除当前补偿表"
            var btn = (sender as EnumMenuItem).Tag.ToString();
            int index = tv.SelectedIndex-1;
            switch (btn)
            {
                case "1":
                    doc.ActiveLayer.Insert(index, new EntityActionRange());
                    break;
                case "2":
                    doc.ActiveLayer.Insert(index, new EntityActionFollow());
                    break;
                case "3":
                    doc.ActiveLayer.Insert(index, new EntityActionCaliRange());
                    break;
                case "4":
                    doc.ActiveLayer.Insert(index, new EntityActionInitLaser());
                    break;
                case "5":
                    doc.ActiveLayer.Insert(index, new EntityActionClearCali());
                    break;
                case "6":
                    doc.ActiveLayer.Insert(index, new EntityActionDwell());
                    break;
                default:
                    break;
            }
        }

        #region 动态输入系统

        private void InitializeDynamicInput()
        {
            try
            {
                // 设置动态输入初始状态
                DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Hidden;

                // 监听鼠标移动事件来更新动态输入位置
                if (WinFormsHost.Child is System.Windows.Forms.Control winFormsControl)
                {
                    winFormsControl.MouseMove += WinFormsControl_MouseMove;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化动态输入系统失败: {ex.Message}");
            }
        }

        private void WinFormsControl_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                // 更新动态输入位置
                var screenPoint = new Point(e.X, e.Y);
                DynamicInputOverlay.SetPosition(screenPoint);

                // 更新当前坐标
                if (doc?.View != null)
                {
                    var modelPoint = doc.View.CanvasToModel(new System.Numerics.Vector2(e.X, e.Y));
                    DynamicInputOverlay.CurrentPoint = modelPoint;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新动态输入位置失败: {ex.Message}");
            }
        }

        private void DynamicInputOverlay_InputChanged(object sender, Views.DynamicInputEventArgs e)
        {
            try
            {
                // 处理动态输入变化
                if (doc?.View != null)
                {
                    // 更新绘图预览
                    // TODO: 通知绘图命令更新预览
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理动态输入变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化新的管理器
        /// </summary>
        private void InitializeManagers()
        {
            try
            {
                // 等待ViewBase初始化完成
                if (doc?.View is ViewBase viewBase)
                {
                    // 初始化视口管理器
                    _viewportManager = new ViewportManager(viewBase);

                    // 初始化视图工具栏管理器
                    _viewToolbarManager = new ViewToolbarManager(viewBase, _viewportManager);

                    // 初始化交互式视图管理器
                    _interactiveViewManager = new InteractiveViewManager(viewBase, _viewportManager);

                    // 初始化快捷键管理器
                    _shortcutManager = new EnhancedShortcutManager(viewBase, _viewToolbarManager);

                    // 初始化状态栏管理器
                    _statusBarManager = new EnhancedStatusBarManager(viewBase, _viewportManager);

                    // 初始化缩放窗口渲染器
                    _zoomWindowRenderer = new ZoomWindowRenderer(viewBase);

                    // 订阅事件
                    _viewToolbarManager.ZoomWindowUpdated += OnZoomWindowUpdated;
                    _shortcutManager.ShortcutExecuted += OnShortcutExecuted;

                    // 更新ViewModel的状态栏管理器引用
                    if (DataContext is SkEditorViewModel viewModel)
                    {
                        viewModel.StatusBarManager = _statusBarManager;
                    }

                    // 订阅键盘事件
                    this.KeyDown += OnKeyDown;
                    this.PreviewKeyDown += OnPreviewKeyDown;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化管理器失败: {ex.Message}");
            }
        }

        private void DynamicInputOverlay_InputConfirmed(object sender, Views.DynamicInputEventArgs e)
        {
            try
            {
                // 处理动态输入确认
                if (doc?.View != null)
                {
                    // 将确认的点传递给当前绘图命令
                    // TODO: 通知绘图命令使用确认的坐标

                    // 隐藏动态输入
                    DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Hidden;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理动态输入确认失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示坐标输入模式
        /// </summary>
        public void ShowCoordinateInput()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Coordinate;
            DynamicInputOverlay.ClearInputs();
        }

        /// <summary>
        /// 显示距离输入模式
        /// </summary>
        public void ShowDistanceInput()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Distance;
            DynamicInputOverlay.ClearInputs();
        }

        /// <summary>
        /// 显示角度输入模式
        /// </summary>
        public void ShowAngleInput()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Angle;
            DynamicInputOverlay.ClearInputs();
        }

        /// <summary>
        /// 显示极坐标输入模式
        /// </summary>
        public void ShowPolarInput()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.DistanceAngle;
            DynamicInputOverlay.ClearInputs();
        }

        /// <summary>
        /// 显示信息显示模式
        /// </summary>
        public void ShowInfoDisplay()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.InfoDisplay;
        }

        /// <summary>
        /// 隐藏动态输入
        /// </summary>
        public void HideDynamicInput()
        {
            DynamicInputOverlay.CurrentMode = Views.DynamicInputOverlay.InputMode.Hidden;
        }

        #endregion

        #region 视图工具栏事件处理

        private void btnZoomWindow_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.ActivateZoomWindow();
        }

        private void btnRealtimeZoom_Click(object sender, RoutedEventArgs e)
        {
            _interactiveViewManager?.ActivateRealtimeZoom();
        }

        private void btnPanTool_Click(object sender, RoutedEventArgs e)
        {
            _interactiveViewManager?.ActivatePanTool();
        }

        private void btnZoomToFit_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.ZoomToFit();
        }

        private void btnPreviousView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.GoToPreviousView();
        }

        private void btnNextView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.GoToNextView();
        }

        private void btnTopView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.SetTopView();
        }

        private void btnFrontView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.SetFrontView();
        }

        private void btnRightView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.SetRightView();
        }

        private void btnIsometricView_Click(object sender, RoutedEventArgs e)
        {
            _viewToolbarManager?.SetIsometricView();
        }

        #endregion

        #region 新增事件处理

        private void OnZoomWindowUpdated(object sender, ZoomWindowEventArgs e)
        {
            _zoomWindowRenderer?.UpdateZoomWindow(e.EndPoint);
        }

        private void OnShortcutExecuted(object sender, ShortcutExecutedEventArgs e)
        {
            _statusBarManager?.UpdateAllStatus();
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            var keys = (Keys)KeyInterop.VirtualKeyFromKey(e.Key);
            if (Control.ModifierKeys != Keys.None)
            {
                keys |= (Keys)Control.ModifierKeys;
            }

            if (_shortcutManager?.HandleShortcut(keys) == true)
            {
                e.Handled = true;
            }
        }

        private void OnPreviewKeyDown(object sender, KeyEventArgs e)
        {
            // 处理特殊键
            if (e.Key == Key.Escape || e.Key == Key.F8 || e.Key == Key.F9 ||
                e.Key == Key.F11 || e.Key == Key.F12 || e.Key == Key.Space)
            {
                var keys = (Keys)KeyInterop.VirtualKeyFromKey(e.Key);
                if (_shortcutManager?.HandleShortcut(keys) == true)
                {
                    e.Handled = true;
                }
            }
        }

        #endregion
    }
}
