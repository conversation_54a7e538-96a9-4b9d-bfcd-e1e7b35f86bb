using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.Tables;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 线型管理器
    /// 负责管理线型库、创建自定义线型、应用线型等操作
    /// </summary>
    public class LinetypeManager : ObservableObject
    {
        #region 私有字段

        private ObservableCollection<LinetypeInfo> _availableLinetypes;
        private ObservableCollection<LinetypeInfo> _customLinetypes;
        private LinetypeInfo _currentLinetype;
        private DocumentBase _document;
        private readonly Dictionary<string, LinetypePattern> _linetypePatterns;
        private readonly Dictionary<string, SKPaint> _penCache;

        #endregion

        #region 事件

        /// <summary>
        /// 线型添加事件
        /// </summary>
        public event EventHandler<LinetypeEventArgs> LinetypeAdded;

        /// <summary>
        /// 线型删除事件
        /// </summary>
        public event EventHandler<LinetypeEventArgs> LinetypeRemoved;

        /// <summary>
        /// 当前线型改变事件
        /// </summary>
        public event EventHandler<LinetypeEventArgs> CurrentLinetypeChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化线型管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public LinetypeManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _availableLinetypes = new ObservableCollection<LinetypeInfo>();
            _customLinetypes = new ObservableCollection<LinetypeInfo>();
            _linetypePatterns = new Dictionary<string, LinetypePattern>();
            _penCache = new Dictionary<string, SKPaint>();

            InitializeStandardLinetypes();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 可用线型集合
        /// </summary>
        public ObservableCollection<LinetypeInfo> AvailableLinetypes
        {
            get => _availableLinetypes;
            private set => SetProperty(ref _availableLinetypes, value);
        }

        /// <summary>
        /// 自定义线型集合
        /// </summary>
        public ObservableCollection<LinetypeInfo> CustomLinetypes
        {
            get => _customLinetypes;
            private set => SetProperty(ref _customLinetypes, value);
        }

        /// <summary>
        /// 当前线型
        /// </summary>
        public LinetypeInfo CurrentLinetype
        {
            get => _currentLinetype;
            set
            {
                if (SetProperty(ref _currentLinetype, value))
                {
                    OnCurrentLinetypeChanged(value);
                }
            }
        }

        /// <summary>
        /// 所有线型（标准 + 自定义）
        /// </summary>
        public IEnumerable<LinetypeInfo> AllLinetypes => _availableLinetypes.Concat(_customLinetypes);

        #endregion

        #region 线型操作方法

        /// <summary>
        /// 创建自定义线型
        /// </summary>
        /// <param name="name">线型名称</param>
        /// <param name="description">线型描述</param>
        /// <param name="pattern">线型图案</param>
        /// <returns>创建的线型信息</returns>
        public LinetypeInfo CreateCustomLinetype(string name, string description, LinetypePattern pattern)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("线型名称不能为空", nameof(name));

            if (LinetypeExists(name))
                throw new InvalidOperationException($"线型 '{name}' 已存在");

            var linetypeInfo = new LinetypeInfo
            {
                Name = name,
                Description = description,
                Pattern = pattern,
                IsCustom = true,
                IsEditable = true
            };

            _customLinetypes.Add(linetypeInfo);
            _linetypePatterns[name] = pattern;

            OnLinetypeAdded(linetypeInfo);
            return linetypeInfo;
        }

        /// <summary>
        /// 创建简单线型（虚线、点线等）
        /// </summary>
        /// <param name="name">线型名称</param>
        /// <param name="description">描述</param>
        /// <param name="dashPattern">破折号图案</param>
        /// <returns>创建的线型</returns>
        public LinetypeInfo CreateSimpleLinetype(string name, string description, float[] dashPattern)
        {
            var pattern = new LinetypePattern
            {
                Segments = dashPattern?.Select(d => new LinetypeSegmentInfo { Length = d, IsSpace = d < 0 }).ToList() 
                          ?? new List<LinetypeSegmentInfo>()
            };

            return CreateCustomLinetype(name, description, pattern);
        }

        /// <summary>
        /// 删除自定义线型
        /// </summary>
        /// <param name="linetype">要删除的线型</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteLinetype(LinetypeInfo linetype)
        {
            if (linetype == null || !linetype.IsCustom)
                return false;

            _customLinetypes.Remove(linetype);
            _linetypePatterns.Remove(linetype.Name);

            OnLinetypeRemoved(linetype);
            return true;
        }

        /// <summary>
        /// 修改线型
        /// </summary>
        /// <param name="linetype">要修改的线型</param>
        /// <param name="newPattern">新的线型图案</param>
        /// <returns>是否修改成功</returns>
        public bool ModifyLinetype(LinetypeInfo linetype, LinetypePattern newPattern)
        {
            if (linetype == null || !linetype.IsEditable)
                return false;

            linetype.Pattern = newPattern;
            _linetypePatterns[linetype.Name] = newPattern;

            return true;
        }

        /// <summary>
        /// 从文件加载线型库
        /// </summary>
        /// <param name="filePath">线型文件路径</param>
        /// <returns>加载的线型数量</returns>
        public int LoadLinetypesFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"线型文件不存在: {filePath}");

            var loadedCount = 0;
            try
            {
                var lines = File.ReadAllLines(filePath);
                foreach (var line in lines)
                {
                    if (TryParseLinetypeDefinition(line, out var linetypeInfo))
                    {
                        if (!LinetypeExists(linetypeInfo.Name))
                        {
                            _customLinetypes.Add(linetypeInfo);
                            _linetypePatterns[linetypeInfo.Name] = linetypeInfo.Pattern;
                            loadedCount++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载线型文件失败: {ex.Message}", ex);
            }

            return loadedCount;
        }

        /// <summary>
        /// 保存线型库到文件
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <param name="includeStandard">是否包含标准线型</param>
        public void SaveLinetypesToFile(string filePath, bool includeStandard = false)
        {
            var linesToSave = includeStandard ? AllLinetypes : _customLinetypes;
            
            var lines = new List<string>();
            foreach (var linetype in linesToSave)
            {
                lines.Add(FormatLinetypeDefinition(linetype));
            }

            File.WriteAllLines(filePath, lines);
        }

        /// <summary>
        /// 应用线型到实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="linetype">线型</param>
        public void ApplyLinetypeToEntity(EntityBase entity, LinetypeInfo linetype)
        {
            if (entity == null || linetype == null) return;

            // 使用缓存的Pen对象
            if (entity.Pen != null)
            {
                entity.Pen = GetCachedPenWithLinetype(entity.Pen, linetype);
                entity.IsNeedToRegen = true;
            }
        }

        /// <summary>
        /// 批量应用线型到多个实体（性能优化版本）
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="linetype">线型</param>
        public void BatchApplyLinetypeToEntities(IEnumerable<EntityBase> entities, LinetypeInfo linetype)
        {
            if (entities == null || linetype == null) return;

            try
            {
                // 预创建Pen对象缓存
                var penCache = new Dictionary<string, SKPaint>();

                foreach (var entity in entities)
                {
                    if (entity?.Pen != null)
                    {
                        var cacheKey = GetPenCacheKey(entity.Pen, linetype);

                        if (!penCache.TryGetValue(cacheKey, out var cachedPen))
                        {
                            cachedPen = CreatePenWithLinetype(entity.Pen, linetype);
                            penCache[cacheKey] = cachedPen;
                        }

                        entity.Pen = cachedPen;
                        entity.IsNeedToRegen = true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量应用线型失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取缓存的Pen对象
        /// </summary>
        /// <param name="basePen">基础Pen</param>
        /// <param name="linetype">线型</param>
        /// <returns>带线型的Pen对象</returns>
        private SKPaint GetCachedPenWithLinetype(SKPaint basePen, LinetypeInfo linetype)
        {
            var cacheKey = GetPenCacheKey(basePen, linetype);

            if (_penCache.TryGetValue(cacheKey, out var cachedPen))
            {
                return cachedPen;
            }

            // 创建新的Pen并缓存
            cachedPen = CreatePenWithLinetype(basePen, linetype);

            // 限制缓存大小
            if (_penCache.Count > 200)
            {
                ClearPenCache();
            }

            _penCache[cacheKey] = cachedPen;
            return cachedPen;
        }

        /// <summary>
        /// 生成Pen缓存键
        /// </summary>
        /// <param name="basePen">基础Pen</param>
        /// <param name="linetype">线型</param>
        /// <returns>缓存键</returns>
        private string GetPenCacheKey(SKPaint basePen, LinetypeInfo linetype)
        {
            return $"{basePen.Color}_{basePen.StrokeWidth}_{linetype.Name}";
        }

        /// <summary>
        /// 清空Pen缓存
        /// </summary>
        public void ClearPenCache()
        {
            try
            {
                foreach (var pen in _penCache.Values)
                {
                    pen?.Dispose();
                }
                _penCache.Clear();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空Pen缓存失败: {ex.Message}");
            }
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取线型
        /// </summary>
        /// <param name="name">线型名称</param>
        /// <returns>线型信息</returns>
        public LinetypeInfo GetLinetype(string name)
        {
            return AllLinetypes.FirstOrDefault(l => string.Equals(l.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查线型是否存在
        /// </summary>
        /// <param name="name">线型名称</param>
        /// <returns>是否存在</returns>
        public bool LinetypeExists(string name)
        {
            return AllLinetypes.Any(l => string.Equals(l.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取线型图案
        /// </summary>
        /// <param name="name">线型名称</param>
        /// <returns>线型图案</returns>
        public LinetypePattern GetLinetypePattern(string name)
        {
            _linetypePatterns.TryGetValue(name, out var pattern);
            return pattern;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化标准线型
        /// </summary>
        private void InitializeStandardLinetypes()
        {
            // 实线
            var continuous = new LinetypeInfo
            {
                Name = "Continuous",
                Description = "实线",
                Pattern = new LinetypePattern(),
                IsCustom = false,
                IsEditable = false
            };
            _availableLinetypes.Add(continuous);
            _linetypePatterns["Continuous"] = continuous.Pattern;

            // 虚线
            var dashed = new LinetypeInfo
            {
                Name = "Dashed",
                Description = "虚线",
                Pattern = CreateDashedPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _availableLinetypes.Add(dashed);
            _linetypePatterns["Dashed"] = dashed.Pattern;

            // 点线
            var dotted = new LinetypeInfo
            {
                Name = "Dotted",
                Description = "点线",
                Pattern = CreateDottedPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _availableLinetypes.Add(dotted);
            _linetypePatterns["Dotted"] = dotted.Pattern;

            // 点划线
            var dashDot = new LinetypeInfo
            {
                Name = "DashDot",
                Description = "点划线",
                Pattern = CreateDashDotPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _availableLinetypes.Add(dashDot);
            _linetypePatterns["DashDot"] = dashDot.Pattern;

            // 双点划线
            var dashDotDot = new LinetypeInfo
            {
                Name = "DashDotDot",
                Description = "双点划线",
                Pattern = CreateDashDotDotPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _availableLinetypes.Add(dashDotDot);
            _linetypePatterns["DashDotDot"] = dashDotDot.Pattern;

            // 设置默认线型
            CurrentLinetype = continuous;
        }

        /// <summary>
        /// 创建虚线图案
        /// </summary>
        private LinetypePattern CreateDashedPattern()
        {
            return new LinetypePattern
            {
                Segments = new List<LinetypeSegmentInfo>
                {
                    new LinetypeSegmentInfo { Length = 5.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 2.5f, IsSpace = true }
                }
            };
        }

        /// <summary>
        /// 创建点线图案
        /// </summary>
        private LinetypePattern CreateDottedPattern()
        {
            return new LinetypePattern
            {
                Segments = new List<LinetypeSegmentInfo>
                {
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true }
                }
            };
        }

        /// <summary>
        /// 创建点划线图案
        /// </summary>
        private LinetypePattern CreateDashDotPattern()
        {
            return new LinetypePattern
            {
                Segments = new List<LinetypeSegmentInfo>
                {
                    new LinetypeSegmentInfo { Length = 5.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true }
                }
            };
        }

        /// <summary>
        /// 创建双点划线图案
        /// </summary>
        private LinetypePattern CreateDashDotDotPattern()
        {
            return new LinetypePattern
            {
                Segments = new List<LinetypeSegmentInfo>
                {
                    new LinetypeSegmentInfo { Length = 5.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = false },
                    new LinetypeSegmentInfo { Length = 1.0f, IsSpace = true }
                }
            };
        }

        /// <summary>
        /// 解析线型定义
        /// </summary>
        private bool TryParseLinetypeDefinition(string line, out LinetypeInfo linetypeInfo)
        {
            linetypeInfo = null;
            // 简化的线型定义解析逻辑
            // 实际应用中需要更复杂的解析算法
            return false;
        }

        /// <summary>
        /// 格式化线型定义
        /// </summary>
        private string FormatLinetypeDefinition(LinetypeInfo linetype)
        {
            // 简化的线型定义格式化逻辑
            return $"{linetype.Name}, {linetype.Description}";
        }

        /// <summary>
        /// 创建带线型的画笔
        /// </summary>
        private EntityPen CreatePenWithLinetype(EntityPen originalPen, LinetypeInfo linetype)
        {
            var newPen = originalPen.Clone() as EntityPen;
            if (newPen != null && linetype.Pattern != null)
            {
                // 应用线型图案到画笔
                // 这里需要根据具体的渲染系统来实现
            }
            return newPen ?? originalPen;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发线型添加事件
        /// </summary>
        protected virtual void OnLinetypeAdded(LinetypeInfo linetype)
        {
            LinetypeAdded?.Invoke(this, new LinetypeEventArgs(linetype));
        }

        /// <summary>
        /// 触发线型删除事件
        /// </summary>
        protected virtual void OnLinetypeRemoved(LinetypeInfo linetype)
        {
            LinetypeRemoved?.Invoke(this, new LinetypeEventArgs(linetype));
        }

        /// <summary>
        /// 触发当前线型改变事件
        /// </summary>
        protected virtual void OnCurrentLinetypeChanged(LinetypeInfo linetype)
        {
            CurrentLinetypeChanged?.Invoke(this, new LinetypeEventArgs(linetype));
        }

        #endregion
    }

    #region 辅助类和结构

    /// <summary>
    /// 线型信息
    /// </summary>
    public class LinetypeInfo : ObservableObject
    {
        private string _name;
        private string _description;
        private LinetypePattern _pattern;
        private bool _isCustom;
        private bool _isEditable;

        /// <summary>线型名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>线型描述</summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>线型图案</summary>
        public LinetypePattern Pattern
        {
            get => _pattern;
            set => SetProperty(ref _pattern, value);
        }

        /// <summary>是否为自定义线型</summary>
        public bool IsCustom
        {
            get => _isCustom;
            set => SetProperty(ref _isCustom, value);
        }

        /// <summary>是否可编辑</summary>
        public bool IsEditable
        {
            get => _isEditable;
            set => SetProperty(ref _isEditable, value);
        }
    }

    /// <summary>
    /// 线型图案
    /// </summary>
    public class LinetypePattern
    {
        /// <summary>线型段集合</summary>
        public List<LinetypeSegmentInfo> Segments { get; set; } = new List<LinetypeSegmentInfo>();

        /// <summary>总长度</summary>
        public float TotalLength => Segments.Sum(s => Math.Abs(s.Length));
    }

    /// <summary>
    /// 线型段信息
    /// </summary>
    public class LinetypeSegmentInfo
    {
        /// <summary>段长度</summary>
        public float Length { get; set; }

        /// <summary>是否为空白段</summary>
        public bool IsSpace { get; set; }

        /// <summary>文本内容（用于文本段）</summary>
        public string Text { get; set; }

        /// <summary>形状名称（用于形状段）</summary>
        public string ShapeName { get; set; }
    }

    /// <summary>
    /// 线型事件参数
    /// </summary>
    public class LinetypeEventArgs : EventArgs
    {
        public LinetypeInfo Linetype { get; }

        public LinetypeEventArgs(LinetypeInfo linetype)
        {
            Linetype = linetype;
        }
    }

    #endregion
} 