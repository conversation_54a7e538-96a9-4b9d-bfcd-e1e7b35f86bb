using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 块引用管理器
    /// 负责管理块引用的插入、编辑、变换和删除等操作
    /// </summary>
    public class BlockReferenceManager : ObservableObject
    {
        #region 私有字段

        private ObservableCollection<BlockReference> _blockReferences;
        private BlockReference _currentBlockReference;
        private DocumentBase _document;
        private BlockDefinitionManager _blockDefinitionManager;
        private readonly Dictionary<string, int> _insertionCounts;

        #endregion

        #region 事件

        /// <summary>
        /// 块引用插入事件
        /// </summary>
        public event EventHandler<BlockReferenceEventArgs> BlockReferenceInserted;

        /// <summary>
        /// 块引用删除事件
        /// </summary>
        public event EventHandler<BlockReferenceEventArgs> BlockReferenceRemoved;

        /// <summary>
        /// 块引用变换事件
        /// </summary>
        public event EventHandler<BlockReferenceEventArgs> BlockReferenceTransformed;

        /// <summary>
        /// 当前块引用改变事件
        /// </summary>
        public event EventHandler<BlockReferenceEventArgs> CurrentBlockReferenceChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化块引用管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        /// <param name="blockDefinitionManager">块定义管理器</param>
        public BlockReferenceManager(DocumentBase document, BlockDefinitionManager blockDefinitionManager)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _blockDefinitionManager = blockDefinitionManager ?? throw new ArgumentNullException(nameof(blockDefinitionManager));
            _blockReferences = new ObservableCollection<BlockReference>();
            _insertionCounts = new Dictionary<string, int>();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 块引用集合
        /// </summary>
        public ObservableCollection<BlockReference> BlockReferences
        {
            get => _blockReferences;
            private set => SetProperty(ref _blockReferences, value);
        }

        /// <summary>
        /// 当前块引用
        /// </summary>
        public BlockReference CurrentBlockReference
        {
            get => _currentBlockReference;
            set
            {
                if (SetProperty(ref _currentBlockReference, value))
                {
                    OnCurrentBlockReferenceChanged(value);
                }
            }
        }

        /// <summary>
        /// 块引用数量
        /// </summary>
        public int BlockReferenceCount => _blockReferences.Count;

        #endregion

        #region 块引用操作方法

        /// <summary>
        /// 插入块引用
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <param name="insertionPoint">插入点</param>
        /// <param name="scale">缩放比例</param>
        /// <param name="rotation">旋转角度（弧度）</param>
        /// <param name="attributes">属性字典</param>
        /// <returns>插入的块引用</returns>
        public BlockReference InsertBlockReference(string blockDefinitionName, Vector2 insertionPoint,
            Vector2? scale = null, float rotation = 0.0f, Dictionary<string, object> attributes = null)
        {
            if (string.IsNullOrWhiteSpace(blockDefinitionName))
                throw new ArgumentException("块定义名称不能为空", nameof(blockDefinitionName));

            var blockDef = _blockDefinitionManager.GetBlockDefinition(blockDefinitionName);
            if (blockDef == null)
                throw new InvalidOperationException($"块定义 '{blockDefinitionName}' 不存在");

            var blockRef = new BlockReference
            {
                BlockDefinitionName = blockDefinitionName,
                InsertionPoint = insertionPoint,
                Scale = scale ?? Vector2.One,
                Rotation = rotation,
                Attributes = attributes ?? new Dictionary<string, object>(),
                Name = GenerateBlockReferenceName(blockDefinitionName),
                IsVisible = true,
                IsMarkerable = true
            };

            // 计算变换后的边界框
            CalculateTransformedBounds(blockRef, blockDef);

            _blockReferences.Add(blockRef);
            
            // 注册到块定义管理器
            _blockDefinitionManager.RegisterBlockReference(blockRef);

            // 更新插入计数
            UpdateInsertionCount(blockDefinitionName);

            OnBlockReferenceInserted(blockRef);
            return blockRef;
        }

        /// <summary>
        /// 插入多个块引用（阵列）
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <param name="basePoint">基点</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="rowSpacing">行间距</param>
        /// <param name="columnSpacing">列间距</param>
        /// <param name="scale">缩放比例</param>
        /// <param name="rotation">旋转角度</param>
        /// <returns>插入的块引用集合</returns>
        public IEnumerable<BlockReference> InsertBlockArray(string blockDefinitionName, Vector2 basePoint,
            int rows, int columns, float rowSpacing, float columnSpacing,
            Vector2? scale = null, float rotation = 0.0f)
        {
            if (rows <= 0 || columns <= 0)
                throw new ArgumentException("行数和列数必须大于0");

            var insertedReferences = new List<BlockReference>();

            for (int row = 0; row < rows; row++)
            {
                for (int col = 0; col < columns; col++)
                {
                    var insertionPoint = new Vector2(
                        basePoint.X + col * columnSpacing,
                        basePoint.Y + row * rowSpacing
                    );

                    var blockRef = InsertBlockReference(blockDefinitionName, insertionPoint, scale, rotation);
                    insertedReferences.Add(blockRef);
                }
            }

            return insertedReferences;
        }

        /// <summary>
        /// 删除块引用
        /// </summary>
        /// <param name="blockReference">要删除的块引用</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveBlockReference(BlockReference blockReference)
        {
            if (blockReference == null)
                return false;

            _blockReferences.Remove(blockReference);
            
            // 从块定义管理器中取消注册
            _blockDefinitionManager.UnregisterBlockReference(blockReference);

            // 更新插入计数
            if (_insertionCounts.ContainsKey(blockReference.BlockDefinitionName))
            {
                _insertionCounts[blockReference.BlockDefinitionName]--;
                if (_insertionCounts[blockReference.BlockDefinitionName] <= 0)
                {
                    _insertionCounts.Remove(blockReference.BlockDefinitionName);
                }
            }

            OnBlockReferenceRemoved(blockReference);
            return true;
        }

        /// <summary>
        /// 删除指定块定义的所有引用
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <returns>删除的引用数量</returns>
        public int RemoveAllReferences(string blockDefinitionName)
        {
            var referencesToRemove = _blockReferences
                .Where(br => br.BlockDefinitionName == blockDefinitionName)
                .ToList();

            foreach (var blockRef in referencesToRemove)
            {
                RemoveBlockReference(blockRef);
            }

            return referencesToRemove.Count;
        }

        /// <summary>
        /// 移动块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        /// <param name="translation">移动向量</param>
        public void MoveBlockReference(BlockReference blockReference, Vector2 translation)
        {
            if (blockReference == null) return;

            blockReference.InsertionPoint += translation;
            blockReference.IsNeedToRegen = true;

            OnBlockReferenceTransformed(blockReference);
        }

        /// <summary>
        /// 缩放块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        /// <param name="scaleCenter">缩放中心</param>
        /// <param name="scaleFactor">缩放因子</param>
        public void ScaleBlockReference(BlockReference blockReference, Vector2 scaleCenter, Vector2 scaleFactor)
        {
            if (blockReference == null) return;

            // 缩放插入点
            var translation = blockReference.InsertionPoint - scaleCenter;
            translation *= scaleFactor;
            blockReference.InsertionPoint = scaleCenter + translation;

            // 缩放块引用
            blockReference.Scale *= scaleFactor;
            blockReference.IsNeedToRegen = true;

            OnBlockReferenceTransformed(blockReference);
        }

        /// <summary>
        /// 旋转块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        /// <param name="rotationCenter">旋转中心</param>
        /// <param name="angle">旋转角度（弧度）</param>
        public void RotateBlockReference(BlockReference blockReference, Vector2 rotationCenter, float angle)
        {
            if (blockReference == null) return;

            // 旋转插入点
            var translation = blockReference.InsertionPoint - rotationCenter;
            var cos = (float)Math.Cos(angle);
            var sin = (float)Math.Sin(angle);
            
            var rotatedTranslation = new Vector2(
                translation.X * cos - translation.Y * sin,
                translation.X * sin + translation.Y * cos
            );
            
            blockReference.InsertionPoint = rotationCenter + rotatedTranslation;
            blockReference.Rotation += angle;
            blockReference.IsNeedToRegen = true;

            OnBlockReferenceTransformed(blockReference);
        }

        /// <summary>
        /// 镜像块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        /// <param name="mirrorLine">镜像线（两点定义）</param>
        public void MirrorBlockReference(BlockReference blockReference, (Vector2 Point1, Vector2 Point2) mirrorLine)
        {
            if (blockReference == null) return;

            // 计算镜像变换
            var mirrorPoint = MirrorPoint(blockReference.InsertionPoint, mirrorLine);
            blockReference.InsertionPoint = mirrorPoint;

            // 镜像缩放（X轴镜像）
            blockReference.Scale = new Vector2(-blockReference.Scale.X, blockReference.Scale.Y);
            blockReference.IsNeedToRegen = true;

            OnBlockReferenceTransformed(blockReference);
        }

        /// <summary>
        /// 复制块引用
        /// </summary>
        /// <param name="blockReference">源块引用</param>
        /// <param name="newInsertionPoint">新插入点</param>
        /// <returns>复制的块引用</returns>
        public BlockReference CopyBlockReference(BlockReference blockReference, Vector2 newInsertionPoint)
        {
            if (blockReference == null) return null;

            return InsertBlockReference(
                blockReference.BlockDefinitionName,
                newInsertionPoint,
                blockReference.Scale,
                blockReference.Rotation,
                new Dictionary<string, object>(blockReference.Attributes)
            );
        }

        /// <summary>
        /// 更新块引用属性
        /// </summary>
        /// <param name="blockReference">块引用</param>
        /// <param name="attributeName">属性名称</param>
        /// <param name="attributeValue">属性值</param>
        public void UpdateBlockAttribute(BlockReference blockReference, string attributeName, object attributeValue)
        {
            if (blockReference == null || string.IsNullOrWhiteSpace(attributeName)) return;

            blockReference.Attributes[attributeName] = attributeValue;
            blockReference.IsNeedToRegen = true;
        }

        /// <summary>
        /// 爆炸块引用（将块引用转换为独立实体）
        /// </summary>
        /// <param name="blockReference">要爆炸的块引用</param>
        /// <returns>爆炸后的实体集合</returns>
        public IEnumerable<EntityBase> ExplodeBlockReference(BlockReference blockReference)
        {
            if (blockReference == null) return Enumerable.Empty<EntityBase>();

            var blockDef = _blockDefinitionManager.GetBlockDefinition(blockReference.BlockDefinitionName);
            if (blockDef == null) return Enumerable.Empty<EntityBase>();

            var explodedEntities = new List<EntityBase>();

            foreach (var entity in blockDef.Entities)
            {
                var clonedEntity = entity.Clone() as EntityBase;
                if (clonedEntity != null)
                {
                    // 应用块引用的变换
                    ApplyTransformation(clonedEntity, blockReference);
                    explodedEntities.Add(clonedEntity);
                }
            }

            return explodedEntities;
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取指定块定义的所有引用
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <returns>块引用集合</returns>
        public IEnumerable<BlockReference> GetBlockReferences(string blockDefinitionName)
        {
            return _blockReferences.Where(br => br.BlockDefinitionName == blockDefinitionName);
        }

        /// <summary>
        /// 获取块引用的插入次数
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <returns>插入次数</returns>
        public int GetInsertionCount(string blockDefinitionName)
        {
            return _insertionCounts.ContainsKey(blockDefinitionName) 
                ? _insertionCounts[blockDefinitionName] : 0;
        }

        /// <summary>
        /// 搜索块引用
        /// </summary>
        /// <param name="searchCriteria">搜索条件</param>
        /// <returns>匹配的块引用</returns>
        public IEnumerable<BlockReference> SearchBlockReferences(BlockReferenceSearchCriteria searchCriteria)
        {
            var query = _blockReferences.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(searchCriteria.BlockDefinitionName))
            {
                query = query.Where(br => br.BlockDefinitionName.IndexOf(
                    searchCriteria.BlockDefinitionName, StringComparison.OrdinalIgnoreCase) >= 0);
            }

            if (searchCriteria.BoundingBox.HasValue)
            {
                query = query.Where(br => br.BoundingBox.Intersects(searchCriteria.BoundingBox.Value));
            }

            if (searchCriteria.AttributeFilters != null)
            {
                foreach (var filter in searchCriteria.AttributeFilters)
                {
                    query = query.Where(br => br.Attributes.ContainsKey(filter.Key) && 
                        br.Attributes[filter.Key]?.ToString() == filter.Value?.ToString());
                }
            }

            return query;
        }

        /// <summary>
        /// 获取在指定点附近的块引用
        /// </summary>
        /// <param name="point">检测点</param>
        /// <param name="tolerance">容差</param>
        /// <returns>附近的块引用</returns>
        public IEnumerable<BlockReference> GetBlockReferencesNearPoint(Vector2 point, float tolerance)
        {
            return _blockReferences.Where(br =>
            {
                var distance = Vector2.Distance(br.InsertionPoint, point);
                return distance <= tolerance;
            });
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 生成块引用名称
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        /// <returns>块引用名称</returns>
        private string GenerateBlockReferenceName(string blockDefinitionName)
        {
            var count = GetInsertionCount(blockDefinitionName) + 1;
            return $"{blockDefinitionName}_{count:D3}";
        }

        /// <summary>
        /// 更新插入计数
        /// </summary>
        /// <param name="blockDefinitionName">块定义名称</param>
        private void UpdateInsertionCount(string blockDefinitionName)
        {
            if (_insertionCounts.ContainsKey(blockDefinitionName))
            {
                _insertionCounts[blockDefinitionName]++;
            }
            else
            {
                _insertionCounts[blockDefinitionName] = 1;
            }
        }

        /// <summary>
        /// 计算变换后的边界框
        /// </summary>
        /// <param name="blockRef">块引用</param>
        /// <param name="blockDef">块定义</param>
        private void CalculateTransformedBounds(BlockReference blockRef, BlockDefinition blockDef)
        {
            if (blockDef.BoundingBox.IsEmpty)
            {
                blockRef.BoundingBox = BoundingBox.Empty;
                return;
            }

            // 获取原始边界框的顶点
            var originalBounds = blockDef.BoundingBox;
            var corners = new Vector2[]
            {
                new Vector2(originalBounds.MinX, originalBounds.MinY),
                new Vector2(originalBounds.MaxX, originalBounds.MinY),
                new Vector2(originalBounds.MaxX, originalBounds.MaxY),
                new Vector2(originalBounds.MinX, originalBounds.MaxY)
            };

            // 应用变换
            var transformedCorners = corners.Select(corner =>
                TransformPoint(corner, blockRef)).ToArray();

            // 计算新的边界框
            var newBounds = new BoundingBox();
            foreach (var corner in transformedCorners)
            {
                newBounds.Union(corner.X, corner.Y);
            }

            blockRef.BoundingBox = newBounds;
        }

        /// <summary>
        /// 变换点
        /// </summary>
        /// <param name="point">原始点</param>
        /// <param name="blockRef">块引用</param>
        /// <returns>变换后的点</returns>
        private Vector2 TransformPoint(Vector2 point, BlockReference blockRef)
        {
            // 1. 从块定义基点偏移
            var blockDef = _blockDefinitionManager.GetBlockDefinition(blockRef.BlockDefinitionName);
            point -= blockDef.BasePoint;

            // 2. 缩放
            point *= blockRef.Scale;

            // 3. 旋转
            if (Math.Abs(blockRef.Rotation) > 1e-6)
            {
                var cos = (float)Math.Cos(blockRef.Rotation);
                var sin = (float)Math.Sin(blockRef.Rotation);
                point = new Vector2(
                    point.X * cos - point.Y * sin,
                    point.X * sin + point.Y * cos
                );
            }

            // 4. 平移到插入点
            point += blockRef.InsertionPoint;

            return point;
        }

        /// <summary>
        /// 应用变换到实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="blockRef">块引用</param>
        private void ApplyTransformation(EntityBase entity, BlockReference blockRef)
        {
            // 获取块定义基点
            var blockDef = _blockDefinitionManager.GetBlockDefinition(blockRef.BlockDefinitionName);
            var basePoint = blockDef.BasePoint;

            // 平移到原点
            entity.Translate(-basePoint);

            // 缩放
            if (blockRef.Scale != Vector2.One)
            {
                // 实体的缩放实现需要根据具体实体类型来实现
            }

            // 旋转
            if (Math.Abs(blockRef.Rotation) > 1e-6)
            {
                // 实体的旋转实现需要根据具体实体类型来实现
            }

            // 平移到插入点
            entity.Translate(blockRef.InsertionPoint);
        }

        /// <summary>
        /// 镜像点
        /// </summary>
        /// <param name="point">原始点</param>
        /// <param name="mirrorLine">镜像线</param>
        /// <returns>镜像后的点</returns>
        private Vector2 MirrorPoint(Vector2 point, (Vector2 Point1, Vector2 Point2) mirrorLine)
        {
            var lineDir = Vector2.Normalize(mirrorLine.Point2 - mirrorLine.Point1);
            var pointToLine = point - mirrorLine.Point1;
            var projection = Vector2.Dot(pointToLine, lineDir) * lineDir;
            var perpendicular = pointToLine - projection;
            
            return point - 2 * perpendicular;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发块引用插入事件
        /// </summary>
        protected virtual void OnBlockReferenceInserted(BlockReference blockReference)
        {
            BlockReferenceInserted?.Invoke(this, new BlockReferenceEventArgs(blockReference));
        }

        /// <summary>
        /// 触发块引用删除事件
        /// </summary>
        protected virtual void OnBlockReferenceRemoved(BlockReference blockReference)
        {
            BlockReferenceRemoved?.Invoke(this, new BlockReferenceEventArgs(blockReference));
        }

        /// <summary>
        /// 触发块引用变换事件
        /// </summary>
        protected virtual void OnBlockReferenceTransformed(BlockReference blockReference)
        {
            BlockReferenceTransformed?.Invoke(this, new BlockReferenceEventArgs(blockReference));
        }

        /// <summary>
        /// 触发当前块引用改变事件
        /// </summary>
        protected virtual void OnCurrentBlockReferenceChanged(BlockReference blockReference)
        {
            CurrentBlockReferenceChanged?.Invoke(this, new BlockReferenceEventArgs(blockReference));
        }

        #endregion
    }

    #region 辅助类和结构

    /// <summary>
    /// 块引用搜索条件
    /// </summary>
    public class BlockReferenceSearchCriteria
    {
        /// <summary>块定义名称（支持部分匹配）</summary>
        public string BlockDefinitionName { get; set; }

        /// <summary>边界框过滤</summary>
        public BoundingBox? BoundingBox { get; set; }

        /// <summary>属性过滤器</summary>
        public Dictionary<string, object> AttributeFilters { get; set; }

        /// <summary>最小缩放</summary>
        public Vector2? MinScale { get; set; }

        /// <summary>最大缩放</summary>
        public Vector2? MaxScale { get; set; }
    }

    /// <summary>
    /// 块引用事件参数
    /// </summary>
    public class BlockReferenceEventArgs : EventArgs
    {
        public BlockReference BlockReference { get; }

        public BlockReferenceEventArgs(BlockReference blockReference)
        {
            BlockReference = blockReference;
        }
    }

    #endregion
} 