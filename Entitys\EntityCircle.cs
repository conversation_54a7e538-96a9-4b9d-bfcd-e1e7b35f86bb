﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Numerics;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityCircle : EntityBase
    {
        [JsonIgnore]
        private EntityLwPolyline lwPolyline = new EntityLwPolyline();

        public EntityCircle()
        {
            Name = "Circle";
            //Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/218.png"));
        }

        public EntityCircle(Vector2 center, double radius)
        {
            Name = "Circle";
            Center = center;
            Radius = radius;
            //Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/218.png"));

        }

        private Vector2 _center = new Vector2();
        [Category("基础"), DisplayName("圆心")]
        public Vector2 Center
        {
            get { return _center; }
            set { _center = value; this.IsNeedToRegen = true; OnPropertyChanged("Center"); }
        }


        private double _radius = 0.0;
        [Category("基础"), DisplayName("半径")]
        public double Radius
        {
            get { return _radius; }
            set { _radius = value; this.IsNeedToRegen = true; }
        }

      
        public double Diameter
        {
            get { return _radius * 2; }
        }



        [JsonIgnore]
        public override BoundingBox BoundingBox{ get; set; }

        private void RegenVertextList()
        {
            //this.lwPolyline.Clear();
            //this.lwPolyline.Color2 = this.color;
            //for (double startAngle = (double)this.StartAngle; startAngle < (double)this.StartAngle + 360.0; startAngle += (double)Config.AngleFactor)
            //    this.lwPolyline.Add(new LwPolyLineVertex((float)Math.Cos(startAngle * (Math.PI / 180.0)) * this.Radius + this.center.X, (float)Math.Sin(startAngle * (Math.PI / 180.0)) * this.Radius + this.center.Y));
            //this.lwPolyline.IsClosed = true;
            //this.lwPolyline.Owner = (IEntity)this;
            //this.lwPolyline.Regen();
        }

        private void RegenBoundRect()
        {
            double left = this.Center.X - this.Radius;
            double right = this.Center.X + this.Radius;
            double top = this.Center.Y + this.Radius;
            double bottom = this.Center.Y - this.Radius;
            this.BoundingBox = new BoundingBox(left, top, right, bottom);
        }

        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
        }


        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;

            (view as ViewBase).DrawCircle(_center, _radius, Pen);
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return MathHelper.IntersectCircleInRect(new BoundingBox(left, top, right, bottom), this.Center.X, this.Center.Y, this.Radius);
        }

        public override bool HitTest(double x, double y, double threshold)
        {
            if (!this.BoundingBox.HitTest(x, y, threshold))
                return false;
            int num = -1;
            if (this.lwPolyline.HitTest(x, y, threshold))
                num = 0;
            return num >= 0;
        }

        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!this.BoundingBox.HitTest(br, threshold))
                return false;
            int num = -1;
            if (this.lwPolyline.HitTest(br, threshold))
                num = 0;
            return num >= 0;
        }


        /// <summary>
        /// 对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            List<ObjectSnapPoint> snapPnts = new List<ObjectSnapPoint>
            {
                new ObjectSnapPoint(ObjectSnapMode.Center, _center),
                new ObjectSnapPoint(ObjectSnapMode.Quad, _center + new Vector2((float)_radius, 0)),
                new ObjectSnapPoint(ObjectSnapMode.Quad, _center + new Vector2(0, (float)_radius)),
                new ObjectSnapPoint(ObjectSnapMode.Quad, _center + new Vector2((float)-_radius, 0)),
                new ObjectSnapPoint(ObjectSnapMode.Quad, _center + new Vector2(0, (float)-_radius))
            };
            return snapPnts;
        }

        public override void Translate(Vector2 translation)
        {
            Center += translation;
        }


        public override object Clone() => (object)new EntityCircle()
        {
            Name = this.Name,
            Description = this.Description,
            Parent = this.Parent,
            IsSelected = this.IsSelected,
            IsVisible = this.IsVisible,
            IsMarkerable = this.IsMarkerable,
            IsLocked = this.IsLocked,
            color = this.color,
            BoundingBox = this.BoundingBox.Clone(),
            Repeats = this.Repeats,
            Center = this.Center,
            Radius = this.Radius,
            Tag = this.Tag,
            Index = this.Index,
            IsNeedToRegen = true
        };

    }
}
