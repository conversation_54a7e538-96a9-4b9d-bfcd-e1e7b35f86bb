﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    internal class PolylineCmd : DrawCmd
    {
        public EntityLine CurLine = null;
        private EntityLwPolyline _polyline = null;
        private DocumentBase doc;


        public PolylineCmd(DocumentBase doc)
        {
                this.doc = doc;
        }


        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityLwPolyline[1] { _polyline }; }
        }

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            Step1_SpecifyStartPoint = 1,
            Step2_SpecifyOtherPoint = 2,
        }
        private Step _step = Step.Step1_SpecifyStartPoint;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyStartPoint;
            this.pointer.Mode = IndicatorMode.Locate;
            this.pointer.Document.Prompt = "指定多段线的起点:";
        }


        protected override void Commit()
        {
            if (this.newEntities != null && this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }


        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (_step == Step.Step1_SpecifyStartPoint)
            {
                if (e.Button == MouseButtons.Left)
                {
                    _polyline = new EntityLwPolyline();
                    _polyline.Vertexes.Add(this.pointer.CurrentSnapPoint);
                    _polyline.LayerId = doc.ActiveLayer?.Id ?? 0;
                    _polyline.Color = doc.ActiveLayer?.Color ?? SkiaSharp.SKColors.White;

                    CurLine = new EntityLine();
                    CurLine.StartPoint = CurLine.EndPoint = this.pointer.CurrentSnapPoint;

                    this.newEntities.Add(_polyline);
                    _step = Step.Step2_SpecifyOtherPoint;
                    this.pointer.Document.Prompt = "指定下一点 (右键结束):";
                }
                else if (e.Button == MouseButtons.Right)
                {
                    _mgr.CancelCurrentCommand();
                }
            }
            else if (_step == Step.Step2_SpecifyOtherPoint)
            {
                if (e.Button == MouseButtons.Left)
                {
                    _polyline.Vertexes.Add(this.pointer.CurrentSnapPoint);
                    CurLine.StartPoint = this.pointer.CurrentSnapPoint;
                    this.pointer.Document.Prompt = "指定下一点 (右键结束):";
                }
                else if (e.Button == MouseButtons.Right)
                {
                    // 右键结束多段线绘制
                    if (_polyline.Vertexes.Count > 1)
                    {
                        _mgr.FinishCurrentCommand();
                    }
                    else
                    {
                        _mgr.CancelCurrentCommand();
                    }
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Middle)
            {
                return EventResult.Handled;
            }

            if (_step == Step.Step2_SpecifyOtherPoint)
            {
                if (CurLine != null)
                {
                    CurLine.EndPoint = this.pointer.CurrentSnapPoint;
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == System.Windows.Forms.Keys.Escape)
            {
                if (_polyline != null && _polyline.Vertexes.Count > 1)
                {
                    _mgr.FinishCurrentCommand();
                }
                else
                {
                    _mgr.CancelCurrentCommand();
                }
                return EventResult.Handled;
            }
            return EventResult.Unhandled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            if (CurLine != null)
            {
                CurLine.Render(_viewer);
            }
            if (_polyline != null)
            {
                ViewBase viewer = _mgr.Viewer as ViewBase;
                _polyline.Render(viewer);
            }
        }
    }
}
