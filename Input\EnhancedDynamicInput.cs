using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Tracking;
using SkiaSharp;
using System;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Input
{
    /// <summary>
    /// 增强的动态输入系统
    /// 集成极轴追踪、对象追踪和实时坐标反馈，提供专业CAD级别的输入体验
    /// </summary>
    public class EnhancedDynamicInput
    {
        private ViewBase _viewer;
        private DynamicInputer _baseInputer;
        private PolarTrackingSystem _polarTracking;
        private ObjectTrackingSystem _objectTracking;
        
        // 输入状态
        private DynamicInputMode _currentMode = DynamicInputMode.Coordinate;
        private Vector2? _basePoint;
        private Vector2 _currentPosition;
        private Vector2 _lastValidPosition;
        private bool _isVisible = false;
        private bool _inputLocked = false;
        
        // 输入值缓存
        private string _xInputBuffer = "";
        private string _yInputBuffer = "";
        private string _distanceInputBuffer = "";
        private string _angleInputBuffer = "";
        private bool _isEditingValue = false;
        private InputField _activeField = InputField.None;
        
        // 视觉样式
        private SKPaint _coordinateTextPaint;
        private SKPaint _editingTextPaint;
        private SKPaint _backgroundPaint;
        private SKPaint _borderPaint;
        private SKPaint _activeBorderPaint;
        
        // 布局配置
        private const float InputBoxWidth = 100;
        private const float InputBoxHeight = 20;
        private const float TextSize = 10;
        private const float Spacing = 5;
        
        public DynamicInputMode CurrentMode
        {
            get { return _currentMode; }
            set { _currentMode = value; }
        }
        
        public bool IsVisible
        {
            get { return _isVisible; }
            set { _isVisible = value; }
        }
        
        public Vector2? BasePoint
        {
            get { return _basePoint; }
            set { _basePoint = value; }
        }
        
        public bool IsEditingValue
        {
            get { return _isEditingValue; }
        }
        
        public EnhancedDynamicInput(ViewBase viewer, PolarTrackingSystem polarTracking, ObjectTrackingSystem objectTracking)
        {
            _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
            _baseInputer = new DynamicInputer(viewer);
            _polarTracking = polarTracking;
            _objectTracking = objectTracking;
            
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _coordinateTextPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = TextSize,
                Color = SKColors.Black,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Consolas", SKFontStyle.Normal)
            };
            
            _editingTextPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = TextSize,
                Color = SKColors.Blue,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Consolas", SKFontStyle.Bold)
            };
            
            _backgroundPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColor.FromArgb(240, 255, 255, 224), // 半透明浅黄色
                IsAntialias = true
            };
            
            _borderPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1,
                Color = SKColors.Gray,
                IsAntialias = true
            };
            
            _activeBorderPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2,
                Color = SKColors.Blue,
                IsAntialias = true
            };
        }
        
        /// <summary>
        /// 更新动态输入位置和内容
        /// </summary>
        public void UpdatePosition(Vector2 position)
        {
            if (!_isVisible) return;
            
            var originalPosition = position;
            
            // 如果没在编辑值，应用追踪增强
            if (!_isEditingValue)
            {
                // 应用极轴追踪
                if (_basePoint.HasValue && _polarTracking.IsEnabled)
                {
                    var trackingPoint = _polarTracking.UpdateTracking(position);
                    if (trackingPoint.HasValue)
                    {
                        position = trackingPoint.Value;
                    }
                }
                
                // 应用对象追踪
                if (_objectTracking.IsEnabled)
                {
                    var trackingResult = _objectTracking.UpdateTracking(position);
                    if (trackingResult.BestAlignmentPoint.HasValue)
                    {
                        position = trackingResult.BestAlignmentPoint.Value;
                    }
                }
            }
            
            _currentPosition = position;
            _lastValidPosition = originalPosition;
            
            // 更新输入缓存中的值
            UpdateInputBuffers();
        }
        
        /// <summary>
        /// 更新输入缓存
        /// </summary>
        private void UpdateInputBuffers()
        {
            if (_isEditingValue) return;
            
            switch (_currentMode)
            {
                case DynamicInputMode.Coordinate:
                    _xInputBuffer = _currentPosition.X.ToString("F3");
                    _yInputBuffer = _currentPosition.Y.ToString("F3");
                    break;
                    
                case DynamicInputMode.Distance:
                    if (_basePoint.HasValue)
                    {
                        var distance = Vector2.Distance(_basePoint.Value, _currentPosition);
                        _distanceInputBuffer = distance.ToString("F3");
                    }
                    break;
                    
                case DynamicInputMode.Angle:
                    if (_basePoint.HasValue)
                    {
                        var angle = CalculateAngle(_basePoint.Value, _currentPosition);
                        _angleInputBuffer = angle.ToString("F1");
                    }
                    break;
                    
                case DynamicInputMode.DistanceAngle:
                    if (_basePoint.HasValue)
                    {
                        var distance = Vector2.Distance(_basePoint.Value, _currentPosition);
                        var angle = CalculateAngle(_basePoint.Value, _currentPosition);
                        _distanceInputBuffer = distance.ToString("F3");
                        _angleInputBuffer = angle.ToString("F1");
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 计算角度（度）
        /// </summary>
        private float CalculateAngle(Vector2 from, Vector2 to)
        {
            var delta = to - from;
            var angleRad = Math.Atan2(delta.Y, delta.X);
            var angleDeg = angleRad * 180.0 / Math.PI;
            if (angleDeg < 0) angleDeg += 360;
            return (float)angleDeg;
        }
        
        /// <summary>
        /// 启动动态输入
        /// </summary>
        public void StartInput(Vector2 startPosition, DynamicInputMode mode = DynamicInputMode.Coordinate)
        {
            _basePoint = startPosition;
            _currentMode = mode;
            _isVisible = true;
            _isEditingValue = false;
            _activeField = InputField.None;
            
            // 清空输入缓存
            _xInputBuffer = "";
            _yInputBuffer = "";
            _distanceInputBuffer = "";
            _angleInputBuffer = "";
            
            // 设置极轴追踪基点
            if (_polarTracking.IsEnabled)
            {
                _polarTracking.SetBasePoint(startPosition);
            }
            
            // 添加对象追踪点
            if (_objectTracking.IsEnabled)
            {
                _objectTracking.AddTrackingPoint(startPosition, ObjectSnapMode.End);
            }
        }
        
        /// <summary>
        /// 结束动态输入
        /// </summary>
        public void EndInput()
        {
            _isVisible = false;
            _isEditingValue = false;
            _activeField = InputField.None;
            _basePoint = null;
            
            if (_polarTracking.IsEnabled)
            {
                _polarTracking.ClearBasePoint();
            }
        }
        
        /// <summary>
        /// 开始编辑指定字段
        /// </summary>
        public void StartEditingField(InputField field)
        {
            _isEditingValue = true;
            _activeField = field;
            _inputLocked = true;
        }
        
        /// <summary>
        /// 结束编辑字段
        /// </summary>
        public void EndEditingField()
        {
            _isEditingValue = false;
            _activeField = InputField.None;
            _inputLocked = false;
            
            // 根据输入值计算新位置
            RecalculatePositionFromInput();
        }
        
        /// <summary>
        /// 根据输入值重新计算位置
        /// </summary>
        private void RecalculatePositionFromInput()
        {
            if (!_basePoint.HasValue) return;
            
            try
            {
                switch (_currentMode)
                {
                    case DynamicInputMode.Coordinate:
                        if (float.TryParse(_xInputBuffer, out float x) && float.TryParse(_yInputBuffer, out float y))
                        {
                            _currentPosition = new Vector2(x, y);
                        }
                        break;
                        
                    case DynamicInputMode.Distance:
                        if (float.TryParse(_distanceInputBuffer, out float distance))
                        {
                            var angle = CalculateAngle(_basePoint.Value, _lastValidPosition);
                            var angleRad = angle * Math.PI / 180.0;
                            _currentPosition = _basePoint.Value + new Vector2(
                                (float)(distance * Math.Cos(angleRad)),
                                (float)(distance * Math.Sin(angleRad))
                            );
                        }
                        break;
                        
                    case DynamicInputMode.Angle:
                        if (float.TryParse(_angleInputBuffer, out float inputAngle))
                        {
                            var distance = Vector2.Distance(_basePoint.Value, _lastValidPosition);
                            var angleRad = inputAngle * Math.PI / 180.0;
                            _currentPosition = _basePoint.Value + new Vector2(
                                (float)(distance * Math.Cos(angleRad)),
                                (float)(distance * Math.Sin(angleRad))
                            );
                        }
                        break;
                        
                    case DynamicInputMode.DistanceAngle:
                        if (float.TryParse(_distanceInputBuffer, out float dist) && 
                            float.TryParse(_angleInputBuffer, out float ang))
                        {
                            var angleRad = ang * Math.PI / 180.0;
                            _currentPosition = _basePoint.Value + new Vector2(
                                (float)(dist * Math.Cos(angleRad)),
                                (float)(dist * Math.Sin(angleRad))
                            );
                        }
                        break;
                }
            }
            catch
            {
                // 解析失败，保持原位置
            }
        }
        
        /// <summary>
        /// 渲染动态输入
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            if (!_isVisible) return;
            
            var canvasPosition = _viewer.ModelToCanvas(_currentPosition);
            
            switch (_currentMode)
            {
                case DynamicInputMode.Coordinate:
                    RenderCoordinateInput(canvas, canvasPosition);
                    break;
                case DynamicInputMode.Distance:
                    RenderDistanceInput(canvas, canvasPosition);
                    break;
                case DynamicInputMode.Angle:
                    RenderAngleInput(canvas, canvasPosition);
                    break;
                case DynamicInputMode.DistanceAngle:
                    RenderDistanceAngleInput(canvas, canvasPosition);
                    break;
            }
        }
        
        private void RenderCoordinateInput(SKCanvas canvas, Vector2 canvasPosition)
        {
            var xText = $"X: {_xInputBuffer}";
            var yText = $"Y: {_yInputBuffer}";
            
            // X坐标输入框
            var xBoxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 45,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 45 + InputBoxHeight
            );
            
            DrawInputBox(canvas, xBoxRect, xText, _activeField == InputField.X);
            
            // Y坐标输入框
            var yBoxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 20,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 20 + InputBoxHeight
            );
            
            DrawInputBox(canvas, yBoxRect, yText, _activeField == InputField.Y);
        }
        
        private void RenderDistanceInput(SKCanvas canvas, Vector2 canvasPosition)
        {
            if (!_basePoint.HasValue) return;
            
            var distanceText = $"L: {_distanceInputBuffer}";
            
            var boxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 15,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 15 + InputBoxHeight
            );
            
            DrawInputBox(canvas, boxRect, distanceText, _activeField == InputField.Distance);
        }
        
        private void RenderAngleInput(SKCanvas canvas, Vector2 canvasPosition)
        {
            if (!_basePoint.HasValue) return;
            
            var angleText = $"∠: {_angleInputBuffer}°";
            
            var boxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 15,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 15 + InputBoxHeight
            );
            
            DrawInputBox(canvas, boxRect, angleText, _activeField == InputField.Angle);
        }
        
        private void RenderDistanceAngleInput(SKCanvas canvas, Vector2 canvasPosition)
        {
            if (!_basePoint.HasValue) return;
            
            var distanceText = $"L: {_distanceInputBuffer}";
            var angleText = $"∠: {_angleInputBuffer}°";
            
            // 距离输入框
            var distanceBoxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 45,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 45 + InputBoxHeight
            );
            
            DrawInputBox(canvas, distanceBoxRect, distanceText, _activeField == InputField.Distance);
            
            // 角度输入框
            var angleBoxRect = new SKRect(
                canvasPosition.X + 15,
                canvasPosition.Y - 20,
                canvasPosition.X + 15 + InputBoxWidth,
                canvasPosition.Y - 20 + InputBoxHeight
            );
            
            DrawInputBox(canvas, angleBoxRect, angleText, _activeField == InputField.Angle);
        }
        
        private void DrawInputBox(SKCanvas canvas, SKRect rect, string text, bool isActive)
        {
            // 绘制背景
            canvas.DrawRoundRect(rect, 3, 3, _backgroundPaint);
            
            // 绘制边框
            var borderPaint = isActive ? _activeBorderPaint : _borderPaint;
            canvas.DrawRoundRect(rect, 3, 3, borderPaint);
            
            // 绘制文本
            var textPaint = isActive && _isEditingValue ? _editingTextPaint : _coordinateTextPaint;
            var textBounds = new SKRect();
            textPaint.MeasureText(text, ref textBounds);
            
            var textX = rect.Left + 5;
            var textY = rect.Top + (rect.Height - textBounds.Height) / 2 - textBounds.Top;
            
            canvas.DrawText(text, textX, textY, textPaint);
            
            // 如果正在编辑，绘制光标
            if (isActive && _isEditingValue)
            {
                var cursorX = textX + textBounds.Width + 2;
                var cursorY1 = rect.Top + 3;
                var cursorY2 = rect.Bottom - 3;
                canvas.DrawLine(cursorX, cursorY1, cursorX, cursorY2, _editingTextPaint);
            }
        }
        
        /// <summary>
        /// 处理键盘输入
        /// </summary>
        public bool HandleKeyInput(KeyEventArgs e)
        {
            if (!_isVisible) return false;
            
            // 全局快捷键
            switch (e.KeyCode)
            {
                case Keys.Tab:
                    if (!_isEditingValue)
                    {
                        CycleDynamicInputMode();
                        return true;
                    }
                    else
                    {
                        CycleActiveField();
                        return true;
                    }
                    
                case Keys.F8:
                    // 切换极轴追踪
                    if (_polarTracking != null)
                    {
                        _polarTracking.IsEnabled = !_polarTracking.IsEnabled;
                    }
                    return true;
                    
                case Keys.F11:
                    // 切换对象追踪
                    if (_objectTracking != null)
                    {
                        _objectTracking.IsEnabled = !_objectTracking.IsEnabled;
                    }
                    return true;
                    
                case Keys.Enter:
                    if (_isEditingValue)
                    {
                        EndEditingField();
                        return true;
                    }
                    else
                    {
                        return ConfirmInput();
                    }
                    
                case Keys.Escape:
                    if (_isEditingValue)
                    {
                        EndEditingField();
                        return true;
                    }
                    else
                    {
                        EndInput();
                        return true;
                    }
            }
            
            // 字符输入处理
            if (_isEditingValue)
            {
                return HandleCharacterInput(e);
            }
            
            return false;
        }
        
        /// <summary>
        /// 处理字符输入
        /// </summary>
        private bool HandleCharacterInput(KeyEventArgs e)
        {
            var inputBuffer = GetActiveInputBuffer();
            if (inputBuffer == null) return false;
            
            if (e.KeyCode == Keys.Back)
            {
                if (inputBuffer.Length > 0)
                {
                    SetActiveInputBuffer(inputBuffer.Substring(0, inputBuffer.Length - 1));
                }
                return true;
            }
            
            // 数字和小数点输入
            if (char.IsDigit((char)e.KeyCode) || e.KeyCode == Keys.OemPeriod || e.KeyCode == Keys.Decimal)
            {
                var charToAdd = e.KeyCode == Keys.OemPeriod || e.KeyCode == Keys.Decimal ? "." : ((char)e.KeyCode).ToString();
                
                // 验证输入
                if (IsValidInput(inputBuffer + charToAdd))
                {
                    SetActiveInputBuffer(inputBuffer + charToAdd);
                }
                return true;
            }
            
            // 负号输入
            if (e.KeyCode == Keys.OemMinus || e.KeyCode == Keys.Subtract)
            {
                if (inputBuffer.Length == 0 || (inputBuffer.Length > 0 && inputBuffer[0] != '-'))
                {
                    SetActiveInputBuffer("-" + inputBuffer);
                }
                return true;
            }
            
            return false;
        }
        
        private string GetActiveInputBuffer()
        {
            switch (_activeField)
            {
                case InputField.X: return _xInputBuffer;
                case InputField.Y: return _yInputBuffer;
                case InputField.Distance: return _distanceInputBuffer;
                case InputField.Angle: return _angleInputBuffer;
                default: return null;
            }
        }
        
        private void SetActiveInputBuffer(string value)
        {
            switch (_activeField)
            {
                case InputField.X: _xInputBuffer = value; break;
                case InputField.Y: _yInputBuffer = value; break;
                case InputField.Distance: _distanceInputBuffer = value; break;
                case InputField.Angle: _angleInputBuffer = value; break;
            }
        }
        
        private bool IsValidInput(string input)
        {
            if (string.IsNullOrEmpty(input)) return true;
            
            // 基本数字验证
            if (float.TryParse(input, out float value))
            {
                // 距离不能为负数
                if (_activeField == InputField.Distance && value < 0)
                    return false;
                    
                // 角度范围0-360
                if (_activeField == InputField.Angle && (value < 0 || value >= 360))
                    return false;
                    
                return true;
            }
            
            // 允许部分输入（如"1."）
            return input.EndsWith(".") || input.StartsWith("-");
        }
        
        private void CycleDynamicInputMode()
        {
            var values = Enum.GetValues(typeof(DynamicInputMode));
            var currentIndex = Array.IndexOf(values, _currentMode);
            var nextIndex = (currentIndex + 1) % values.Length;
            _currentMode = (DynamicInputMode)values.GetValue(nextIndex);
            
            // 重置编辑状态
            _isEditingValue = false;
            _activeField = InputField.None;
        }
        
        private void CycleActiveField()
        {
            switch (_currentMode)
            {
                case DynamicInputMode.Coordinate:
                    _activeField = _activeField == InputField.X ? InputField.Y : InputField.X;
                    break;
                case DynamicInputMode.DistanceAngle:
                    _activeField = _activeField == InputField.Distance ? InputField.Angle : InputField.Distance;
                    break;
            }
        }
        
        private bool ConfirmInput()
        {
            // 触发坐标确认事件
            OnCoordinateConfirmed?.Invoke(_currentPosition);
            return true;
        }
        
        /// <summary>
        /// 获取增强的位置（应用所有追踪）
        /// </summary>
        public Vector2 GetEnhancedPosition(Vector2 rawPosition)
        {
            if (_inputLocked) return _currentPosition;
            
            var enhancedPosition = rawPosition;
            
            // 应用极轴追踪
            if (_basePoint.HasValue && _polarTracking.IsEnabled)
            {
                var trackingPoint = _polarTracking.UpdateTracking(rawPosition);
                if (trackingPoint.HasValue)
                {
                    enhancedPosition = trackingPoint.Value;
                }
            }
            
            // 应用对象追踪
            if (_objectTracking.IsEnabled)
            {
                var trackingResult = _objectTracking.UpdateTracking(rawPosition);
                if (trackingResult.BestAlignmentPoint.HasValue)
                {
                    enhancedPosition = trackingResult.BestAlignmentPoint.Value;
                }
            }
            
            return enhancedPosition;
        }
        
        /// <summary>
        /// 坐标确认事件
        /// </summary>
        public event Action<Vector2> OnCoordinateConfirmed;
        
        public void Dispose()
        {
            _coordinateTextPaint?.Dispose();
            _editingTextPaint?.Dispose();
            _backgroundPaint?.Dispose();
            _borderPaint?.Dispose();
            _activeBorderPaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 动态输入模式
    /// </summary>
    public enum DynamicInputMode
    {
        /// <summary>
        /// 坐标模式 (X, Y)
        /// </summary>
        Coordinate,
        
        /// <summary>
        /// 距离模式
        /// </summary>
        Distance,
        
        /// <summary>
        /// 角度模式
        /// </summary>
        Angle,
        
        /// <summary>
        /// 距离+角度模式
        /// </summary>
        DistanceAngle
    }
    
    /// <summary>
    /// 输入字段类型
    /// </summary>
    public enum InputField
    {
        None,
        X,
        Y,
        Distance,
        Angle
    }
} 