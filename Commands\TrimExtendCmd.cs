using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 修剪命令
    /// 实现专业CAD级别的修剪功能，支持直线、圆弧、多段线等的精确修剪
    /// </summary>
    public class TrimCmd : Command
    {
        private TrimState _currentState = TrimState.SelectCuttingEdges;
        private List<EntityBase> _cuttingEdges = new List<EntityBase>();
        private List<EntityBase> _entitiesToTrim = new List<EntityBase>();
        private List<IntersectionInfo> _intersections = new List<IntersectionInfo>();
        private TrimOptions _options = new TrimOptions();
        
        // 视觉样式
        private SKPaint _cuttingEdgePaint;
        private SKPaint _trimPreviewPaint;
        private SKPaint _intersectionPaint;
        
        public override string Name => "TRIM";
        public override string Description => "修剪对象到指定边界";
        
        public TrimCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _cuttingEdgePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Red,
                IsAntialias = true
            };
            
            _trimPreviewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 255, 255, 0), // 半透明黄色
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0),
                IsAntialias = true
            };
            
            _intersectionPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColors.Blue,
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = TrimState.SelectCuttingEdges;
            _cuttingEdges.Clear();
            _entitiesToTrim.Clear();
            _intersections.Clear();
            
            _viewer.Document.Prompt = "选择修剪边界（切边）或按Enter选择全部：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case TrimState.SelectCuttingEdges:
                    HandleCuttingEdgeSelection(currentPoint);
                    break;
                    
                case TrimState.SelectEntityToTrim:
                    HandleEntityTrimming(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            if (_currentState == TrimState.SelectEntityToTrim)
            {
                UpdateTrimPreview(currentPoint);
                _viewer.RepaintCanvas();
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == TrimState.SelectCuttingEdges)
                    {
                        if (_cuttingEdges.Count == 0)
                        {
                            // 选择全部作为切边
                            SelectAllAsCuttingEdges();
                        }
                        StartTrimming();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.A:
                    if (_currentState == TrimState.SelectCuttingEdges)
                    {
                        SelectAllAsCuttingEdges();
                        StartTrimming();
                    }
                    break;
                    
                case Keys.F:
                    // 切换围栏模式
                    _options.FenceMode = !_options.FenceMode;
                    _viewer.Document.Prompt += $" [围栏模式: {(_options.FenceMode ? "开" : "关")}]";
                    break;
            }
        }
        
        private void HandleCuttingEdgeSelection(Vector2 point)
        {
            var hitEntity = FindTrimmableEntity(point);
            
            if (hitEntity != null && !_cuttingEdges.Contains(hitEntity))
            {
                _cuttingEdges.Add(hitEntity);
                _viewer.Document.Prompt = $"已选择 {_cuttingEdges.Count} 个切边，继续选择或按Enter开始修剪：";
            }
        }
        
        private void HandleEntityTrimming(Vector2 point)
        {
            var hitEntity = FindTrimmableEntity(point);
            
            if (hitEntity != null && !_cuttingEdges.Contains(hitEntity))
            {
                PerformTrim(hitEntity, point);
            }
        }
        
        private void SelectAllAsCuttingEdges()
        {
            _cuttingEdges.Clear();
            _cuttingEdges.AddRange(_viewer.Document.ActiveLayer.Children.Where(IsTrimmableEntity));
            _viewer.Document.Prompt = $"已选择 {_cuttingEdges.Count} 个切边";
        }
        
        private void StartTrimming()
        {
            if (_cuttingEdges.Count == 0)
            {
                _viewer.Document.Prompt = "未选择任何切边";
                return;
            }
            
            CalculateIntersections();
            _currentState = TrimState.SelectEntityToTrim;
            _viewer.Document.Prompt = "选择要修剪的对象：";
        }
        
        private void CalculateIntersections()
        {
            _intersections.Clear();
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (_cuttingEdges.Contains(entity)) continue;
                if (!IsTrimmableEntity(entity)) continue;
                
                foreach (var cuttingEdge in _cuttingEdges)
                {
                    var intersections = FindIntersections(entity, cuttingEdge);
                    foreach (var intersection in intersections)
                    {
                        _intersections.Add(new IntersectionInfo
                        {
                            Point = intersection,
                            Entity = entity,
                            CuttingEdge = cuttingEdge
                        });
                    }
                }
            }
        }
        
        private void UpdateTrimPreview(Vector2 mousePoint)
        {
            _entitiesToTrim.Clear();
            
            var hitEntity = FindTrimmableEntity(mousePoint);
            if (hitEntity != null && !_cuttingEdges.Contains(hitEntity))
            {
                var trimSegments = CalculateTrimSegments(hitEntity, mousePoint);
                _entitiesToTrim.AddRange(trimSegments);
            }
        }
        
        private List<EntityBase> CalculateTrimSegments(EntityBase entity, Vector2 clickPoint)
        {
            var segments = new List<EntityBase>();
            
            // 找到与此实体相交的所有交点
            var entityIntersections = _intersections
                .Where(i => i.Entity == entity)
                .Select(i => i.Point)
                .OrderBy(p => GetParameterOnEntity(entity, p))
                .ToList();
            
            if (entityIntersections.Count == 0)
            {
                // 没有交点，不能修剪
                return segments;
            }
            
            // 根据实体类型创建修剪后的片段
            switch (entity)
            {
                case EntityLine line:
                    segments.AddRange(TrimLine(line, entityIntersections, clickPoint));
                    break;
                    
                case EntityArc arc:
                    segments.AddRange(TrimArc(arc, entityIntersections, clickPoint));
                    break;
                    
                case EntityCircle circle:
                    segments.AddRange(TrimCircle(circle, entityIntersections, clickPoint));
                    break;
                    
                case EntityLwPolyline polyline:
                    segments.AddRange(TrimPolyline(polyline, entityIntersections, clickPoint));
                    break;
            }
            
            return segments;
        }
        
        private List<EntityBase> TrimLine(EntityLine line, List<Vector2> intersections, Vector2 clickPoint)
        {
            var segments = new List<EntityBase>();
            
            if (intersections.Count == 0) return segments;
            
            // 找到点击点最近的线段
            var clickParameter = GetParameterOnLine(line, clickPoint);
            
            // 按参数排序交点
            var sortedIntersections = intersections
                .Select(p => new { Point = p, Parameter = GetParameterOnLine(line, p) })
                .OrderBy(x => x.Parameter)
                .ToList();
            
            // 找到包含点击点的线段并删除它
            for (int i = 0; i <= sortedIntersections.Count; i++)
            {
                float startParam = i == 0 ? 0 : sortedIntersections[i - 1].Parameter;
                float endParam = i == sortedIntersections.Count ? 1 : sortedIntersections[i].Parameter;
                
                if (clickParameter >= startParam && clickParameter <= endParam)
                {
                    // 这是要删除的线段，跳过
                    continue;
                }
                
                // 创建保留的线段
                var startPoint = i == 0 ? line.StartPoint : sortedIntersections[i - 1].Point;
                var endPoint = i == sortedIntersections.Count ? line.EndPoint : sortedIntersections[i].Point;
                
                if (Vector2.Distance(startPoint, endPoint) > 0.001f) // 避免零长度线段
                {
                    segments.Add(new EntityLine
                    {
                        StartPoint = startPoint,
                        EndPoint = endPoint,
                        LineType = line.LineType,
                        LineWeight = line.LineWeight,
                        Color = line.Color
                    });
                }
            }
            
            return segments;
        }
        
        private List<EntityBase> TrimArc(EntityArc arc, List<Vector2> intersections, Vector2 clickPoint)
        {
            var segments = new List<EntityBase>();
            
            if (intersections.Count == 0) return segments;
            
            // 将交点转换为角度
            var intersectionAngles = intersections
                .Select(p => CalculateAngle(arc.Center, p))
                .OrderBy(a => a)
                .ToList();
            
            var clickAngle = CalculateAngle(arc.Center, clickPoint);
            
            // 找到要删除的弧段
            for (int i = 0; i <= intersectionAngles.Count; i++)
            {
                float startAngle = i == 0 ? arc.StartAngle : intersectionAngles[i - 1];
                float endAngle = i == intersectionAngles.Count ? arc.EndAngle : intersectionAngles[i];
                
                if (IsAngleInRange(clickAngle, startAngle, endAngle))
                {
                    // 这是要删除的弧段
                    continue;
                }
                
                // 创建保留的弧段
                if (Math.Abs(endAngle - startAngle) > 0.001f)
                {
                    segments.Add(new EntityArc
                    {
                        Center = arc.Center,
                        Radius = arc.Radius,
                        StartAngle = startAngle,
                        EndAngle = endAngle,
                        LineType = arc.LineType,
                        LineWeight = arc.LineWeight,
                        Color = arc.Color
                    });
                }
            }
            
            return segments;
        }
        
        private List<EntityBase> TrimCircle(EntityCircle circle, List<Vector2> intersections, Vector2 clickPoint)
        {
            var segments = new List<EntityBase>();
            
            if (intersections.Count < 2) return segments; // 圆需要至少两个交点才能修剪
            
            // 将交点转换为角度
            var intersectionAngles = intersections
                .Select(p => CalculateAngle(circle.Center, p))
                .OrderBy(a => a)
                .ToList();
            
            var clickAngle = CalculateAngle(circle.Center, clickPoint);
            
            // 找到要删除的弧段并创建剩余的弧
            for (int i = 0; i < intersectionAngles.Count; i++)
            {
                int nextIndex = (i + 1) % intersectionAngles.Count;
                float startAngle = intersectionAngles[i];
                float endAngle = intersectionAngles[nextIndex];
                
                if (nextIndex == 0) endAngle += 360; // 处理跨越360度的情况
                
                if (!IsAngleInRange(clickAngle, startAngle, endAngle))
                {
                    // 这是要保留的弧段
                    segments.Add(new EntityArc
                    {
                        Center = circle.Center,
                        Radius = circle.Radius,
                        StartAngle = startAngle,
                        EndAngle = endAngle % 360,
                        LineType = circle.LineType,
                        LineWeight = circle.LineWeight,
                        Color = circle.Color
                    });
                }
            }
            
            return segments;
        }
        
        private List<EntityBase> TrimPolyline(EntityLwPolyline polyline, List<Vector2> intersections, Vector2 clickPoint)
        {
            var segments = new List<EntityBase>();

            try
            {
                if (polyline?.Vertexes == null || polyline.Vertexes.Count < 2)
                    return segments;

                // 找到最接近点击点的线段
                int clickSegmentIndex = FindNearestSegment(polyline, clickPoint);
                if (clickSegmentIndex < 0)
                    return segments;

                // 计算所有边界与多段线的交点
                var intersections = new List<PolylineIntersection>();

                for (int i = 0; i < polyline.Vertexes.Count - 1; i++)
                {
                    var segmentStart = polyline.Vertexes[i];
                    var segmentEnd = polyline.Vertexes[i + 1];
                    var segmentLine = new EntityLine { StartPoint = segmentStart, EndPoint = segmentEnd };

                    foreach (var boundary in boundaries)
                    {
                        var segmentIntersections = CalculateIntersections(segmentLine, boundary);
                        foreach (var intersection in segmentIntersections)
                        {
                            intersections.Add(new PolylineIntersection
                            {
                                Point = intersection,
                                SegmentIndex = i,
                                ParameterOnSegment = CalculateParameter(segmentStart, segmentEnd, intersection)
                            });
                        }
                    }
                }

                // 按线段索引和参数排序
                intersections.Sort((a, b) =>
                {
                    int segmentCompare = a.SegmentIndex.CompareTo(b.SegmentIndex);
                    return segmentCompare != 0 ? segmentCompare : a.ParameterOnSegment.CompareTo(b.ParameterOnSegment);
                });

                // 根据点击的线段确定修剪范围
                var trimStart = FindTrimStart(intersections, clickSegmentIndex, clickPoint);
                var trimEnd = FindTrimEnd(intersections, clickSegmentIndex, clickPoint);

                // 创建修剪后的线段
                if (trimStart.HasValue && trimEnd.HasValue)
                {
                    var trimmedPolyline = CreateTrimmedPolyline(polyline, trimStart.Value, trimEnd.Value);
                    if (trimmedPolyline != null)
                    {
                        segments.Add(trimmedPolyline);
                    }
                }
                else
                {
                    // 如果没有找到合适的修剪点，返回原多段线
                    segments.Add(polyline.Clone());
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"多段线修剪失败: {ex.Message}");
                // 出错时返回原多段线
                segments.Add(polyline.Clone());
            }

            return segments;
        }
        
        private void PerformTrim(EntityBase entity, Vector2 clickPoint)
        {
            try
            {
                var trimmedSegments = CalculateTrimSegments(entity, clickPoint);
                
                if (trimmedSegments.Count > 0)
                {
                    // 移除原实体
                    _viewer.Document.ActiveLayer.Children.Remove(entity);
                    
                    // 添加修剪后的片段
                    foreach (var segment in trimmedSegments)
                    {
                        _viewer.Document.ActiveLayer.Children.Add(segment);
                    }
                    
                    // 重新计算交点
                    CalculateIntersections();
                    
                    _viewer.Document.Prompt = "选择下一个要修剪的对象或按Escape结束：";
                    _viewer.RepaintCanvas();
                }
                else
                {
                    _viewer.Document.Prompt = "无法修剪该对象";
                }
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"修剪失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Trim error: {ex.Message}");
            }
        }
        
        private List<Vector2> FindIntersections(EntityBase entity1, EntityBase entity2)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 根据实体类型组合计算交点
                switch (entity1, entity2)
                {
                    case (EntityLine line1, EntityLine line2):
                        var intersection = CalculateLineLineIntersection(line1, line2);
                        if (intersection.HasValue) intersections.Add(intersection.Value);
                        break;

                    case (EntityLine line, EntityCircle circle):
                        intersections.AddRange(CalculateLineCircleIntersections(line, circle));
                        break;

                    case (EntityCircle circle, EntityLine line):
                        intersections.AddRange(CalculateLineCircleIntersections(line, circle));
                        break;

                    case (EntityCircle circle1, EntityCircle circle2):
                        intersections.AddRange(CalculateCircleCircleIntersections(circle1, circle2));
                        break;

                    case (EntityLine line, EntityArc arc):
                        intersections.AddRange(CalculateLineArcIntersections(line, arc));
                        break;

                    case (EntityArc arc, EntityLine line):
                        intersections.AddRange(CalculateLineArcIntersections(line, arc));
                        break;

                    case (EntityLine line, EntityEllipse ellipse):
                        intersections.AddRange(CalculateLineEllipseIntersections(line, ellipse));
                        break;

                    case (EntityEllipse ellipse, EntityLine line):
                        intersections.AddRange(CalculateLineEllipseIntersections(line, ellipse));
                        break;

                    case (EntityCircle circle, EntityArc arc):
                        intersections.AddRange(CalculateCircleArcIntersections(circle, arc));
                        break;

                    case (EntityArc arc, EntityCircle circle):
                        intersections.AddRange(CalculateCircleArcIntersections(circle, arc));
                        break;

                    case (EntityArc arc1, EntityArc arc2):
                        intersections.AddRange(CalculateArcArcIntersections(arc1, arc2));
                        break;

                    case (EntityLwPolyline polyline, EntityLine line):
                        intersections.AddRange(CalculatePolylineLineIntersections(polyline, line));
                        break;

                    case (EntityLine line, EntityLwPolyline polyline):
                        intersections.AddRange(CalculatePolylineLineIntersections(polyline, line));
                        break;

                    default:
                        // 对于不支持的组合，尝试通用方法
                        intersections.AddRange(CalculateGenericIntersections(entity1, entity2));
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Intersection calculation error: {ex.Message}");
            }

            return intersections;
        }
        
        private Vector2? CalculateLineLineIntersection(EntityLine line1, EntityLine line2)
        {
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;
            
            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-6) return null; // 平行线
            
            var t1 = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            var t2 = (d3.X * d1.Y - d3.Y * d1.X) / cross;
            
            if (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1)
            {
                return line1.StartPoint + t1 * d1;
            }
            
            return null;
        }
        
        private List<Vector2> CalculateLineCircleIntersections(EntityLine line, EntityCircle circle)
        {
            var intersections = new List<Vector2>();
            
            var d = line.EndPoint - line.StartPoint;
            var f = line.StartPoint - circle.Center;
            
            var a = Vector2.Dot(d, d);
            var b = 2 * Vector2.Dot(f, d);
            var c = Vector2.Dot(f, f) - circle.Radius * circle.Radius;
            
            var discriminant = b * b - 4 * a * c;
            
            if (discriminant >= 0)
            {
                discriminant = (float)Math.Sqrt(discriminant);
                
                var t1 = (-b - discriminant) / (2 * a);
                var t2 = (-b + discriminant) / (2 * a);
                
                if (t1 >= 0 && t1 <= 1)
                {
                    intersections.Add(line.StartPoint + t1 * d);
                }
                
                if (t2 >= 0 && t2 <= 1 && Math.Abs(t2 - t1) > 1e-6)
                {
                    intersections.Add(line.StartPoint + t2 * d);
                }
            }
            
            return intersections;
        }
        
        private List<Vector2> CalculateCircleCircleIntersections(EntityCircle circle1, EntityCircle circle2)
        {
            var intersections = new List<Vector2>();
            
            var d = Vector2.Distance(circle1.Center, circle2.Center);
            
            if (d > circle1.Radius + circle2.Radius || d < Math.Abs(circle1.Radius - circle2.Radius) || d == 0)
            {
                return intersections; // 无交点或重合
            }
            
            var a = (circle1.Radius * circle1.Radius - circle2.Radius * circle2.Radius + d * d) / (2 * d);
            var h = (float)Math.Sqrt(circle1.Radius * circle1.Radius - a * a);
            
            var p = circle1.Center + a * (circle2.Center - circle1.Center) / d;
            
            var offset = h * new Vector2(
                -(circle2.Center.Y - circle1.Center.Y) / d,
                (circle2.Center.X - circle1.Center.X) / d
            );
            
            intersections.Add(p + offset);
            if (h > 1e-6) // 两个不同的交点
            {
                intersections.Add(p - offset);
            }
            
            return intersections;
        }
        
        private float GetParameterOnEntity(EntityBase entity, Vector2 point)
        {
            switch (entity)
            {
                case EntityLine line:
                    return GetParameterOnLine(line, point);
                case EntityArc arc:
                    return GetParameterOnArc(arc, point);
                default:
                    return 0;
            }
        }
        
        private float GetParameterOnLine(EntityLine line, Vector2 point)
        {
            var lineVec = line.EndPoint - line.StartPoint;
            var pointVec = point - line.StartPoint;
            
            if (lineVec.LengthSquared() < 1e-6) return 0;
            
            return Vector2.Dot(pointVec, lineVec) / lineVec.LengthSquared();
        }
        
        private float GetParameterOnArc(EntityArc arc, Vector2 point)
        {
            var angle = CalculateAngle(arc.Center, point);
            var totalAngle = arc.EndAngle - arc.StartAngle;
            
            if (totalAngle != 0)
            {
                return (angle - arc.StartAngle) / totalAngle;
            }
            
            return 0;
        }
        
        private float CalculateAngle(Vector2 center, Vector2 point)
        {
            var delta = point - center;
            var angle = (float)(Math.Atan2(delta.Y, delta.X) * 180.0 / Math.PI);
            if (angle < 0) angle += 360;
            return angle;
        }
        
        private bool IsAngleInRange(float angle, float startAngle, float endAngle)
        {
            // 标准化角度
            while (angle < 0) angle += 360;
            while (angle >= 360) angle -= 360;
            while (startAngle < 0) startAngle += 360;
            while (startAngle >= 360) startAngle -= 360;
            while (endAngle < 0) endAngle += 360;
            while (endAngle >= 360) endAngle -= 360;
            
            if (startAngle <= endAngle)
            {
                return angle >= startAngle && angle <= endAngle;
            }
            else
            {
                return angle >= startAngle || angle <= endAngle;
            }
        }
        
        private EntityBase FindTrimmableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsTrimmableEntity(entity) && IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsTrimmableEntity(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityArc ||
                   entity is EntityCircle ||
                   entity is EntityLwPolyline ||
                   entity is EntityEllipse;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制切边高亮
            foreach (var edge in _cuttingEdges)
            {
                RenderCuttingEdgeHighlight(canvas, edge);
            }
            
            // 绘制交点
            foreach (var intersection in _intersections)
            {
                canvas.DrawCircle(intersection.Point.X, intersection.Point.Y, 3, _intersectionPaint);
            }
            
            // 绘制修剪预览
            foreach (var entity in _entitiesToTrim)
            {
                RenderTrimPreview(canvas, entity);
            }
        }
        
        private void RenderCuttingEdgeHighlight(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _cuttingEdgePaint);
            }
        }
        
        private void RenderTrimPreview(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _trimPreviewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _trimPreviewPaint);
                    break;
            }
        }
        
        public override void Cancel()
        {
            _cuttingEdges.Clear();
            _entitiesToTrim.Clear();
            _intersections.Clear();
            _currentState = TrimState.SelectCuttingEdges;
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _cuttingEdgePaint?.Dispose();
                _trimPreviewPaint?.Dispose();
                _intersectionPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    /// <summary>
    /// 延伸命令
    /// </summary>
    public class ExtendCmd : Command
    {
        private ExtendState _currentState = ExtendState.SelectBoundaryEdges;
        private List<EntityBase> _boundaryEdges = new List<EntityBase>();
        private List<EntityBase> _entitiesToExtend = new List<EntityBase>();
        private ExtendOptions _options = new ExtendOptions();
        
        // 视觉样式
        private SKPaint _boundaryEdgePaint;
        private SKPaint _extendPreviewPaint;
        
        public override string Name => "EXTEND";
        public override string Description => "延伸对象到指定边界";
        
        public ExtendCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _boundaryEdgePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Blue,
                IsAntialias = true
            };
            
            _extendPreviewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 0, 255, 0), // 半透明绿色
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 3 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = ExtendState.SelectBoundaryEdges;
            _boundaryEdges.Clear();
            _entitiesToExtend.Clear();
            
            _viewer.Document.Prompt = "选择边界边或按Enter选择全部：";
            _viewer.RepaintCanvas();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case ExtendState.SelectBoundaryEdges:
                    HandleBoundarySelection(currentPoint);
                    break;
                    
                case ExtendState.SelectEntityToExtend:
                    HandleEntityExtension(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            if (_currentState == ExtendState.SelectEntityToExtend)
            {
                UpdateExtendPreview(currentPoint);
                _viewer.RepaintCanvas();
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == ExtendState.SelectBoundaryEdges)
                    {
                        if (_boundaryEdges.Count == 0)
                        {
                            SelectAllAsBoundaryEdges();
                        }
                        StartExtending();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.A:
                    if (_currentState == ExtendState.SelectBoundaryEdges)
                    {
                        SelectAllAsBoundaryEdges();
                        StartExtending();
                    }
                    break;
                    
                case Keys.E:
                    // 切换延伸模式
                    _options.ExtendMode = _options.ExtendMode == ExtendMode.Extend ? 
                        ExtendMode.Lengthen : ExtendMode.Extend;
                    break;
            }
        }
        
        private void HandleBoundarySelection(Vector2 point)
        {
            var hitEntity = FindExtendableEntity(point);
            
            if (hitEntity != null && !_boundaryEdges.Contains(hitEntity))
            {
                _boundaryEdges.Add(hitEntity);
                _viewer.Document.Prompt = $"已选择 {_boundaryEdges.Count} 个边界，继续选择或按Enter开始延伸：";
            }
        }
        
        private void HandleEntityExtension(Vector2 point)
        {
            var hitEntity = FindExtendableEntity(point);
            
            if (hitEntity != null && !_boundaryEdges.Contains(hitEntity))
            {
                PerformExtend(hitEntity, point);
            }
        }
        
        private void SelectAllAsBoundaryEdges()
        {
            _boundaryEdges.Clear();
            _boundaryEdges.AddRange(_viewer.Document.ActiveLayer.Children.Where(IsExtendableEntity));
            _viewer.Document.Prompt = $"已选择 {_boundaryEdges.Count} 个边界";
        }
        
        private void StartExtending()
        {
            if (_boundaryEdges.Count == 0)
            {
                _viewer.Document.Prompt = "未选择任何边界";
                return;
            }
            
            _currentState = ExtendState.SelectEntityToExtend;
            _viewer.Document.Prompt = "选择要延伸的对象：";
        }
        
        private void UpdateExtendPreview(Vector2 mousePoint)
        {
            _entitiesToExtend.Clear();
            
            var hitEntity = FindExtendableEntity(mousePoint);
            if (hitEntity != null && !_boundaryEdges.Contains(hitEntity))
            {
                var extendedEntity = CalculateExtension(hitEntity, mousePoint);
                if (extendedEntity != null)
                {
                    _entitiesToExtend.Add(extendedEntity);
                }
            }
        }
        
        private EntityBase CalculateExtension(EntityBase entity, Vector2 clickPoint)
        {
            switch (entity)
            {
                case EntityLine line:
                    return ExtendLine(line, clickPoint);
                case EntityArc arc:
                    return ExtendArc(arc, clickPoint);
                default:
                    return null;
            }
        }
        
        private EntityBase ExtendLine(EntityLine line, Vector2 clickPoint)
        {
            // 确定延伸哪一端
            var distToStart = Vector2.Distance(clickPoint, line.StartPoint);
            var distToEnd = Vector2.Distance(clickPoint, line.EndPoint);
            
            bool extendFromStart = distToStart < distToEnd;
            
            // 查找与边界的交点
            Vector2? intersectionPoint = null;
            
            foreach (var boundary in _boundaryEdges)
            {
                var intersection = FindLineExtensionIntersection(line, boundary, extendFromStart);
                if (intersection.HasValue)
                {
                    if (intersectionPoint == null || 
                        Vector2.Distance(intersection.Value, extendFromStart ? line.StartPoint : line.EndPoint) <
                        Vector2.Distance(intersectionPoint.Value, extendFromStart ? line.StartPoint : line.EndPoint))
                    {
                        intersectionPoint = intersection;
                    }
                }
            }
            
            if (intersectionPoint.HasValue)
            {
                return new EntityLine
                {
                    StartPoint = extendFromStart ? intersectionPoint.Value : line.StartPoint,
                    EndPoint = extendFromStart ? line.EndPoint : intersectionPoint.Value,
                    LineType = line.LineType,
                    LineWeight = line.LineWeight,
                    Color = line.Color
                };
            }
            
            return null;
        }
        
        private EntityArc ExtendArc(EntityArc arc, Vector2 clickPoint)
        {
            // 弧的延伸需要更复杂的计算
            // 这里提供简化实现
            return null;
        }
        
        private Vector2? FindLineExtensionIntersection(EntityLine line, EntityBase boundary, bool extendFromStart)
        {
            // 创建延伸线
            var direction = Vector2.Normalize(line.EndPoint - line.StartPoint);
            if (extendFromStart) direction = -direction;
            
            var extendPoint = extendFromStart ? line.StartPoint : line.EndPoint;
            var farPoint = extendPoint + direction * 10000; // 延伸到很远的点
            
            var extendedLine = new EntityLine
            {
                StartPoint = extendPoint,
                EndPoint = farPoint
            };
            
            // 计算与边界的交点
            switch (boundary)
            {
                case EntityLine boundaryLine:
                    return CalculateLineLineIntersection(extendedLine, boundaryLine);
                case EntityCircle boundaryCircle:
                    var intersections = CalculateLineCircleIntersections(extendedLine, boundaryCircle);
                    return intersections.FirstOrDefault();
                default:
                    return null;
            }
        }
        
        private Vector2? CalculateLineLineIntersection(EntityLine line1, EntityLine line2)
        {
            // 重用Trim命令的交点计算方法
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;
            
            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-6) return null;
            
            var t1 = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            var t2 = (d3.X * d1.Y - d3.Y * d1.X) / cross;
            
            if (t1 >= 0 && t2 >= 0 && t2 <= 1)
            {
                return line1.StartPoint + t1 * d1;
            }
            
            return null;
        }
        
        private List<Vector2> CalculateLineCircleIntersections(EntityLine line, EntityCircle circle)
        {
            // 重用Trim命令的交点计算方法
            var intersections = new List<Vector2>();
            
            var d = line.EndPoint - line.StartPoint;
            var f = line.StartPoint - circle.Center;
            
            var a = Vector2.Dot(d, d);
            var b = 2 * Vector2.Dot(f, d);
            var c = Vector2.Dot(f, f) - circle.Radius * circle.Radius;
            
            var discriminant = b * b - 4 * a * c;
            
            if (discriminant >= 0)
            {
                discriminant = (float)Math.Sqrt(discriminant);
                
                var t1 = (-b - discriminant) / (2 * a);
                var t2 = (-b + discriminant) / (2 * a);
                
                if (t1 >= 0)
                {
                    intersections.Add(line.StartPoint + t1 * d);
                }
                
                if (t2 >= 0 && Math.Abs(t2 - t1) > 1e-6)
                {
                    intersections.Add(line.StartPoint + t2 * d);
                }
            }
            
            return intersections;
        }
        
        private void PerformExtend(EntityBase entity, Vector2 clickPoint)
        {
            try
            {
                var extendedEntity = CalculateExtension(entity, clickPoint);
                
                if (extendedEntity != null)
                {
                    // 替换原实体
                    var index = _viewer.Document.ActiveLayer.Children.IndexOf(entity);
                    if (index >= 0)
                    {
                        _viewer.Document.ActiveLayer.Children[index] = extendedEntity;
                    }
                    
                    _viewer.Document.Prompt = "选择下一个要延伸的对象或按Escape结束：";
                    _viewer.RepaintCanvas();
                }
                else
                {
                    _viewer.Document.Prompt = "无法延伸该对象到边界";
                }
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"延伸失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Extend error: {ex.Message}");
            }
        }
        
        private EntityBase FindExtendableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsExtendableEntity(entity) && IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsExtendableEntity(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityArc ||
                   entity is EntityLwPolyline;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制边界高亮
            foreach (var edge in _boundaryEdges)
            {
                RenderBoundaryHighlight(canvas, edge);
            }
            
            // 绘制延伸预览
            foreach (var entity in _entitiesToExtend)
            {
                RenderExtendPreview(canvas, entity);
            }
        }
        
        private void RenderBoundaryHighlight(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _boundaryEdgePaint);
            }
        }
        
        private void RenderExtendPreview(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _extendPreviewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _extendPreviewPaint);
                    break;
            }
        }
        
        public override void Cancel()
        {
            _boundaryEdges.Clear();
            _entitiesToExtend.Clear();
            _currentState = ExtendState.SelectBoundaryEdges;
            base.Cancel();
        }

        #region 新增的交点计算方法

        /// <summary>
        /// 计算直线与弧段的交点
        /// </summary>
        private List<Vector2> CalculateLineArcIntersections(EntityLine line, EntityArc arc)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 先计算直线与完整圆的交点
                var circle = new EntityCircle
                {
                    Center = arc.Center,
                    Radius = arc.Radius
                };

                var circleIntersections = CalculateLineCircleIntersections(line, circle);

                // 过滤出在弧段范围内的交点
                foreach (var point in circleIntersections)
                {
                    if (IsPointOnArc(point, arc))
                    {
                        intersections.Add(point);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Line-Arc intersection error: {ex.Message}");
            }

            return intersections;
        }

        /// <summary>
        /// 计算直线与椭圆的交点
        /// </summary>
        private List<Vector2> CalculateLineEllipseIntersections(EntityLine line, EntityEllipse ellipse)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 椭圆-直线交点计算较复杂，这里使用数值方法
                var lineDir = Vector2.Normalize(line.EndPoint - line.StartPoint);
                var lineStart = line.StartPoint - ellipse.Center;

                // 使用数值方法求解
                for (double t = 0; t < 2 * Math.PI; t += 0.01)
                {
                    var ellipsePoint = new Vector2(
                        (float)(ellipse.RadiusX * Math.Cos(t)),
                        (float)(ellipse.RadiusY * Math.Sin(t))
                    );

                    // 应用椭圆旋转
                    var rotatedPoint = RotatePoint(ellipsePoint, ellipse.Rotation);
                    var worldPoint = rotatedPoint + ellipse.Center;

                    // 检查点是否在直线上
                    if (IsPointOnLine(worldPoint, line, 0.1f))
                    {
                        intersections.Add(worldPoint);
                    }
                }

                // 去重
                intersections = RemoveDuplicatePoints(intersections, 0.1f);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Line-Ellipse intersection error: {ex.Message}");
            }

            return intersections;
        }

        /// <summary>
        /// 计算圆与弧段的交点
        /// </summary>
        private List<Vector2> CalculateCircleArcIntersections(EntityCircle circle, EntityArc arc)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 先计算两个完整圆的交点
                var fullCircle = new EntityCircle
                {
                    Center = arc.Center,
                    Radius = arc.Radius
                };

                var circleIntersections = CalculateCircleCircleIntersections(circle, fullCircle);

                // 过滤出在弧段范围内的交点
                foreach (var point in circleIntersections)
                {
                    if (IsPointOnArc(point, arc))
                    {
                        intersections.Add(point);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Circle-Arc intersection error: {ex.Message}");
            }

            return intersections;
        }

        /// <summary>
        /// 计算弧段与弧段的交点
        /// </summary>
        private List<Vector2> CalculateArcArcIntersections(EntityArc arc1, EntityArc arc2)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 先计算两个完整圆的交点
                var circle1 = new EntityCircle { Center = arc1.Center, Radius = arc1.Radius };
                var circle2 = new EntityCircle { Center = arc2.Center, Radius = arc2.Radius };

                var circleIntersections = CalculateCircleCircleIntersections(circle1, circle2);

                // 过滤出同时在两个弧段范围内的交点
                foreach (var point in circleIntersections)
                {
                    if (IsPointOnArc(point, arc1) && IsPointOnArc(point, arc2))
                    {
                        intersections.Add(point);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Arc-Arc intersection error: {ex.Message}");
            }

            return intersections;
        }

        /// <summary>
        /// 计算多段线与直线的交点
        /// </summary>
        private List<Vector2> CalculatePolylineLineIntersections(EntityLwPolyline polyline, EntityLine line)
        {
            var intersections = new List<Vector2>();

            try
            {
                for (int i = 0; i < polyline.Vertexs.Count; i++)
                {
                    var nextIndex = (i + 1) % polyline.Vertexs.Count;
                    if (!polyline.IsClosed && nextIndex == 0) break;

                    var segment = new EntityLine
                    {
                        StartPoint = polyline.Vertexs[i].Position,
                        EndPoint = polyline.Vertexs[nextIndex].Position
                    };

                    var segmentIntersection = CalculateLineLineIntersection(segment, line);
                    if (segmentIntersection.HasValue)
                    {
                        intersections.Add(segmentIntersection.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Polyline-Line intersection error: {ex.Message}");
            }

            return intersections;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查点是否在弧段上
        /// </summary>
        private bool IsPointOnArc(Vector2 point, EntityArc arc)
        {
            try
            {
                var vectorToPoint = point - arc.Center;
                var angle = Math.Atan2(vectorToPoint.Y, vectorToPoint.X) * 180 / Math.PI;

                // 标准化角度到0-360度
                if (angle < 0) angle += 360;

                var startAngle = arc.StartAngle;
                var endAngle = arc.EndAngle;

                // 处理跨越0度的情况
                if (startAngle > endAngle)
                {
                    return angle >= startAngle || angle <= endAngle;
                }
                else
                {
                    return angle >= startAngle && angle <= endAngle;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查点是否在直线上
        /// </summary>
        private bool IsPointOnLine(Vector2 point, EntityLine line, float tolerance)
        {
            try
            {
                var lineVec = line.EndPoint - line.StartPoint;
                var pointVec = point - line.StartPoint;

                var cross = Math.Abs(lineVec.X * pointVec.Y - lineVec.Y * pointVec.X);
                var lineLength = lineVec.Length();

                return cross / lineLength <= tolerance;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 旋转点
        /// </summary>
        private Vector2 RotatePoint(Vector2 point, double angle)
        {
            var cos = Math.Cos(angle);
            var sin = Math.Sin(angle);

            return new Vector2(
                (float)(point.X * cos - point.Y * sin),
                (float)(point.X * sin + point.Y * cos)
            );
        }

        /// <summary>
        /// 去除重复点
        /// </summary>
        private List<Vector2> RemoveDuplicatePoints(List<Vector2> points, float tolerance)
        {
            var result = new List<Vector2>();

            foreach (var point in points)
            {
                bool isDuplicate = false;
                foreach (var existing in result)
                {
                    if (Vector2.Distance(point, existing) <= tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    result.Add(point);
                }
            }

            return result;
        }

        /// <summary>
        /// 通用交点计算方法（用于不支持的图元组合）
        /// </summary>
        private List<Vector2> CalculateGenericIntersections(EntityBase entity1, EntityBase entity2)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 对于复杂图元，可以转换为多段线进行近似计算
                System.Diagnostics.Debug.WriteLine($"Generic intersection calculation for {entity1.GetType().Name} and {entity2.GetType().Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Generic intersection error: {ex.Message}");
            }

            return intersections;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查点是否在弧段上
        /// </summary>
        private bool IsPointOnArc(Vector2 point, EntityArc arc)
        {
            try
            {
                var vectorToPoint = point - arc.Center;
                var angle = Math.Atan2(vectorToPoint.Y, vectorToPoint.X) * 180 / Math.PI;

                // 标准化角度到0-360度
                if (angle < 0) angle += 360;

                var startAngle = arc.StartAngle;
                var endAngle = arc.EndAngle;

                // 处理跨越0度的情况
                if (startAngle > endAngle)
                {
                    return angle >= startAngle || angle <= endAngle;
                }
                else
                {
                    return angle >= startAngle && angle <= endAngle;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查点是否在直线上
        /// </summary>
        private bool IsPointOnLine(Vector2 point, EntityLine line, float tolerance)
        {
            try
            {
                var lineVec = line.EndPoint - line.StartPoint;
                var pointVec = point - line.StartPoint;

                var cross = Math.Abs(lineVec.X * pointVec.Y - lineVec.Y * pointVec.X);
                var lineLength = lineVec.Length();

                return cross / lineLength <= tolerance;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 旋转点
        /// </summary>
        private Vector2 RotatePoint(Vector2 point, double angle)
        {
            var cos = Math.Cos(angle);
            var sin = Math.Sin(angle);

            return new Vector2(
                (float)(point.X * cos - point.Y * sin),
                (float)(point.X * sin + point.Y * cos)
            );
        }

        /// <summary>
        /// 去除重复点
        /// </summary>
        private List<Vector2> RemoveDuplicatePoints(List<Vector2> points, float tolerance)
        {
            var result = new List<Vector2>();

            foreach (var point in points)
            {
                bool isDuplicate = false;
                foreach (var existing in result)
                {
                    if (Vector2.Distance(point, existing) <= tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    result.Add(point);
                }
            }

            return result;
        }

        /// <summary>
        /// 通用交点计算方法
        /// </summary>
        private List<Vector2> CalculateGenericIntersections(EntityBase entity1, EntityBase entity2)
        {
            var intersections = new List<Vector2>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"Generic intersection calculation for {entity1.GetType().Name} and {entity2.GetType().Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Generic intersection error: {ex.Message}");
            }

            return intersections;
        }

        /// <summary>
        /// 找到最接近点击点的线段
        /// </summary>
        private int FindNearestSegment(EntityLwPolyline polyline, Vector2 clickPoint)
        {
            int nearestIndex = -1;
            float minDistance = float.MaxValue;

            for (int i = 0; i < polyline.Vertexes.Count - 1; i++)
            {
                var segmentStart = polyline.Vertexes[i];
                var segmentEnd = polyline.Vertexes[i + 1];

                var distance = DistancePointToLineSegment(clickPoint, segmentStart, segmentEnd);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestIndex = i;
                }
            }

            return nearestIndex;
        }

        /// <summary>
        /// 计算点到线段的距离
        /// </summary>
        private float DistancePointToLineSegment(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            var lineVector = lineEnd - lineStart;
            var pointVector = point - lineStart;

            var lineLength = lineVector.LengthSquared();
            if (lineLength < 0.0001f)
                return (point - lineStart).Length();

            var t = Math.Max(0, Math.Min(1, Vector2.Dot(pointVector, lineVector) / lineLength));
            var projection = lineStart + (float)t * lineVector;

            return (point - projection).Length();
        }

        /// <summary>
        /// 计算参数
        /// </summary>
        private float CalculateParameter(Vector2 start, Vector2 end, Vector2 point)
        {
            var lineVector = end - start;
            var pointVector = point - start;

            var lineLength = lineVector.LengthSquared();
            if (lineLength < 0.0001f)
                return 0;

            return Vector2.Dot(pointVector, lineVector) / lineLength;
        }

        /// <summary>
        /// 找到修剪起点
        /// </summary>
        private PolylineIntersection? FindTrimStart(List<PolylineIntersection> intersections, int clickSegment, Vector2 clickPoint)
        {
            // 找到点击线段之前的最后一个交点
            for (int i = intersections.Count - 1; i >= 0; i--)
            {
                var intersection = intersections[i];
                if (intersection.SegmentIndex < clickSegment ||
                    (intersection.SegmentIndex == clickSegment &&
                     (intersection.Point - clickPoint).LengthSquared() > 0.01f))
                {
                    return intersection;
                }
            }
            return null;
        }

        /// <summary>
        /// 找到修剪终点
        /// </summary>
        private PolylineIntersection? FindTrimEnd(List<PolylineIntersection> intersections, int clickSegment, Vector2 clickPoint)
        {
            // 找到点击线段之后的第一个交点
            foreach (var intersection in intersections)
            {
                if (intersection.SegmentIndex > clickSegment ||
                    (intersection.SegmentIndex == clickSegment &&
                     (intersection.Point - clickPoint).LengthSquared() > 0.01f))
                {
                    return intersection;
                }
            }
            return null;
        }

        /// <summary>
        /// 创建修剪后的多段线
        /// </summary>
        private EntityLwPolyline CreateTrimmedPolyline(EntityLwPolyline original, PolylineIntersection trimStart, PolylineIntersection trimEnd)
        {
            var trimmed = new EntityLwPolyline();
            trimmed.Vertexes.Add(trimStart.Point);

            // 添加中间的顶点
            for (int i = trimStart.SegmentIndex + 1; i <= trimEnd.SegmentIndex; i++)
            {
                if (i < original.Vertexes.Count)
                {
                    trimmed.Vertexes.Add(original.Vertexes[i]);
                }
            }

            trimmed.Vertexes.Add(trimEnd.Point);

            // 复制属性
            trimmed.LayerId = original.LayerId;
            trimmed.Color = original.Color;
            trimmed.LineType = original.LineType;
            trimmed.LineWidth = original.LineWidth;

            return trimmed;
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _boundaryEdgePaint?.Dispose();
                _extendPreviewPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    #region 枚举和数据结构
    
    public enum TrimState
    {
        SelectCuttingEdges,
        SelectEntityToTrim
    }
    
    public enum ExtendState
    {
        SelectBoundaryEdges,
        SelectEntityToExtend
    }
    
    public enum ExtendMode
    {
        Extend,      // 延伸到边界
        Lengthen     // 按指定长度延伸
    }
    
    public class TrimOptions
    {
        public bool FenceMode { get; set; } = false;
        public bool ProjectMode { get; set; } = false;
        public bool EdgeMode { get; set; } = false;
    }
    
    public class ExtendOptions
    {
        public ExtendMode ExtendMode { get; set; } = ExtendMode.Extend;
        public bool ProjectMode { get; set; } = false;
        public bool EdgeMode { get; set; } = false;
    }
    
    public class IntersectionInfo
    {
        public Vector2 Point { get; set; }
        public EntityBase Entity { get; set; }
        public EntityBase CuttingEdge { get; set; }
    }

    public class PolylineIntersection
    {
        public Vector2 Point { get; set; }
        public int SegmentIndex { get; set; }
        public float ParameterOnSegment { get; set; }
    }

    #endregion
} 