using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Enums;
using McLaser.EditViewerSk.Input;
using McLaser.EditViewerSk.Selection;
using McLaser.EditViewerSk.Tracking;
using SkiaSharp;
using System;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 阶段2集成管理器
    /// 统一管理和协调所有阶段2的高级交互系统
    /// </summary>
    public class Phase2IntegrationManager
    {
        private ViewBase _viewer;
        private bool _isInitialized = false;
        
        // 子系统
        private PolarTrackingSystem _polarTracking;
        private ObjectTrackingSystem _objectTracking;
        private EnhancedDynamicInput _dynamicInput;
        private AdvancedSelectionSystem _selectionSystem;
        
        // 交互状态
        private Phase2InteractionState _currentState = Phase2InteractionState.Idle;
        private Vector2? _lastMousePosition;
        private bool _isCommandActive = false;
        
        // 配置
        private Phase2Configuration _configuration;
        
        public bool IsInitialized
        {
            get { return _isInitialized; }
        }
        
        public Phase2InteractionState CurrentState
        {
            get { return _currentState; }
        }
        
        public PolarTrackingSystem PolarTracking
        {
            get { return _polarTracking; }
        }
        
        public ObjectTrackingSystem ObjectTracking
        {
            get { return _objectTracking; }
        }
        
        public EnhancedDynamicInput DynamicInput
        {
            get { return _dynamicInput; }
        }
        
        public AdvancedSelectionSystem SelectionSystem
        {
            get { return _selectionSystem; }
        }
        
        public Phase2Configuration Configuration
        {
            get { return _configuration; }
            set { _configuration = value; ApplyConfiguration(); }
        }
        
        public Phase2IntegrationManager(ViewBase viewer)
        {
            try
            {
                _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
                _configuration = new Phase2Configuration();
                InitializeSystems();
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to initialize Phase2IntegrationManager: {ex.Message}");
                _isInitialized = false;
            }
        }
        
        private void InitializeSystems()
        {
            try
            {
                // 初始化追踪系统
                _polarTracking = new PolarTrackingSystem(_viewer);
                _objectTracking = new ObjectTrackingSystem(_viewer);
                
                // 初始化动态输入系统
                _dynamicInput = new EnhancedDynamicInput(_viewer, _polarTracking, _objectTracking);
                
                // 初始化选择系统
                _selectionSystem = new AdvancedSelectionSystem(_viewer);
                
                // 应用默认配置
                ApplyConfiguration();
                
                // 订阅事件
                SubscribeToEvents();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing Phase2 systems: {ex.Message}");
                throw;
            }
        }
        
        private void SubscribeToEvents()
        {
            if (_dynamicInput != null)
            {
                _dynamicInput.OnCoordinateConfirmed += OnCoordinateConfirmed;
            }
            
            if (_selectionSystem != null)
            {
                _selectionSystem.OnSelectionChanged += OnSelectionChanged;
            }
        }
        
        private void ApplyConfiguration()
        {
            if (!_isInitialized || _configuration == null) return;
            
            try
            {
                // 应用极轴追踪配置
                if (_polarTracking != null)
                {
                    _polarTracking.IsEnabled = _configuration.PolarTrackingEnabled;
                    if (_configuration.PolarTrackingAngles?.Count > 0)
                    {
                        _polarTracking.TrackingAngles = _configuration.PolarTrackingAngles;
                    }
                }
                
                // 应用对象追踪配置
                if (_objectTracking != null)
                {
                    _objectTracking.IsEnabled = _configuration.ObjectTrackingEnabled;
                }
                
                // 应用动态输入配置
                if (_dynamicInput != null)
                {
                    _dynamicInput.IsVisible = _configuration.DynamicInputEnabled;
                    _dynamicInput.CurrentMode = _configuration.DefaultDynamicInputMode;
                }
                
                // 应用选择系统配置
                if (_selectionSystem != null)
                {
                    _selectionSystem.CurrentMode = _configuration.DefaultSelectionMode;
                    _selectionSystem.Settings = _configuration.SelectionSettings;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying Phase2 configuration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理鼠标按下事件
        /// </summary>
        public bool HandleMouseDown(Vector2 position, MouseButtons button)
        {
            if (!_isInitialized) return false;
            
            try
            {
                _lastMousePosition = position;
                
                // 根据当前状态和按钮类型分发事件
                switch (button)
                {
                    case MouseButtons.Left:
                        return HandleLeftMouseDown(position);
                        
                    case MouseButtons.Right:
                        return HandleRightMouseDown(position);
                        
                    case MouseButtons.Middle:
                        return HandleMiddleMouseDown(position);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling mouse down: {ex.Message}");
                return false;
            }
        }
        
        private bool HandleLeftMouseDown(Vector2 position)
        {
            switch (_currentState)
            {
                case Phase2InteractionState.Idle:
                    return HandleIdleLeftMouseDown(position);
                    
                case Phase2InteractionState.Selecting:
                    return HandleSelectionLeftMouseDown(position);
                    
                case Phase2InteractionState.Drawing:
                    return HandleDrawingLeftMouseDown(position);
                    
                case Phase2InteractionState.DynamicInput:
                    return HandleDynamicInputLeftMouseDown(position);
                    
                default:
                    return false;
            }
        }
        
        private bool HandleIdleLeftMouseDown(Vector2 position)
        {
            // 如果没有活动命令，开始选择
            if (!_isCommandActive)
            {
                var modifierKeys = Control.ModifierKeys;
                
                // 根据修饰键确定选择模式
                var selectionMode = EntitySelectionMode.Point;
                if ((modifierKeys & Keys.Control) == Keys.Control)
                {
                    selectionMode = EntitySelectionMode.Window;
                }
                else if ((modifierKeys & Keys.Shift) == Keys.Shift)
                {
                    selectionMode = EntitySelectionMode.Cross;
                }
                
                _selectionSystem.StartSelection(position, selectionMode);
                _currentState = Phase2InteractionState.Selecting;
                return true;
            }
            
            return false;
        }
        
        private bool HandleSelectionLeftMouseDown(Vector2 position)
        {
            // 继续或完成选择操作
            if (_selectionSystem.IsDragging)
            {
                var result = _selectionSystem.CompleteSelection();
                if (result.IsCompleted)
                {
                    _currentState = Phase2InteractionState.Idle;
                }
                return true;
            }
            
            return false;
        }
        
        private bool HandleDrawingLeftMouseDown(Vector2 position)
        {
            // 处理绘制命令中的点击
            if (_dynamicInput.IsVisible)
            {
                // 确认动态输入的坐标
                var enhancedPosition = _dynamicInput.GetEnhancedPosition(position);
                _dynamicInput.UpdatePosition(enhancedPosition);
                return true;
            }
            
            return false;
        }
        
        private bool HandleDynamicInputLeftMouseDown(Vector2 position)
        {
            // 确认动态输入
            return _dynamicInput.HandleKeyInput(new KeyEventArgs(Keys.Enter));
        }
        
        private bool HandleRightMouseDown(Vector2 position)
        {
            // 右键通常用于取消或上下文菜单
            switch (_currentState)
            {
                case Phase2InteractionState.Selecting:
                    _selectionSystem.CancelSelection();
                    _currentState = Phase2InteractionState.Idle;
                    return true;
                    
                case Phase2InteractionState.Drawing:
                case Phase2InteractionState.DynamicInput:
                    _dynamicInput.EndInput();
                    _currentState = Phase2InteractionState.Idle;
                    return true;
                    
                default:
                    return false;
            }
        }
        
        private bool HandleMiddleMouseDown(Vector2 position)
        {
            // 中键通常用于平移，不处理
            return false;
        }
        
        /// <summary>
        /// 处理鼠标抬起事件
        /// </summary>
        public bool HandleMouseUp(Vector2 position, MouseButtons button)
        {
            if (!_isInitialized) return false;
            
            try
            {
                if (button == MouseButtons.Left && _currentState == Phase2InteractionState.Selecting)
                {
                    if (_selectionSystem.IsDragging)
                    {
                        var result = _selectionSystem.CompleteSelection();
                        if (result.IsCompleted)
                        {
                            _currentState = Phase2InteractionState.Idle;
                        }
                        return true;
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling mouse up: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 处理鼠标移动事件
        /// </summary>
        public bool HandleMouseMove(Vector2 position)
        {
            if (!_isInitialized) return false;
            
            try
            {
                _lastMousePosition = position;
                bool handled = false;
                
                // 更新追踪系统
                if (_polarTracking.IsEnabled || _objectTracking.IsEnabled)
                {
                    UpdateTrackingSystems(position);
                    handled = true;
                }
                
                // 更新动态输入
                if (_dynamicInput.IsVisible)
                {
                    _dynamicInput.UpdatePosition(position);
                    handled = true;
                }
                
                // 更新选择系统
                if (_currentState == Phase2InteractionState.Selecting)
                {
                    _selectionSystem.UpdateSelection(position);
                    handled = true;
                }
                
                return handled;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling mouse move: {ex.Message}");
                return false;
            }
        }
        
        private void UpdateTrackingSystems(Vector2 position)
        {
            // 更新对象追踪（需要在极轴追踪之前，因为它可能会影响追踪点）
            if (_objectTracking.IsEnabled)
            {
                _objectTracking.UpdateTracking(position);
            }
            
            // 更新极轴追踪
            if (_polarTracking.IsEnabled && _polarTracking.CurrentTrackingPoint.HasValue)
            {
                _polarTracking.UpdateTracking(position);
            }
        }
        
        /// <summary>
        /// 处理键盘按下事件
        /// </summary>
        public bool HandleKeyDown(KeyEventArgs e)
        {
            if (!_isInitialized) return false;
            
            try
            {
                // 优先让动态输入处理键盘事件
                if (_dynamicInput.IsVisible && _dynamicInput.HandleKeyInput(e))
                {
                    return true;
                }
                
                // 处理全局快捷键
                return HandleGlobalShortcuts(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling key down: {ex.Message}");
                return false;
            }
        }
        
        private bool HandleGlobalShortcuts(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F8:
                    // 切换极轴追踪
                    if (_polarTracking != null)
                    {
                        _polarTracking.IsEnabled = !_polarTracking.IsEnabled;
                        _configuration.PolarTrackingEnabled = _polarTracking.IsEnabled;
                        return true;
                    }
                    break;
                    
                case Keys.F11:
                    // 切换对象追踪
                    if (_objectTracking != null)
                    {
                        _objectTracking.IsEnabled = !_objectTracking.IsEnabled;
                        _configuration.ObjectTrackingEnabled = _objectTracking.IsEnabled;
                        return true;
                    }
                    break;
                    
                case Keys.F12:
                    // 切换动态输入
                    if (_dynamicInput != null)
                    {
                        _dynamicInput.IsVisible = !_dynamicInput.IsVisible;
                        _configuration.DynamicInputEnabled = _dynamicInput.IsVisible;
                        return true;
                    }
                    break;
                    
                case Keys.Escape:
                    // 取消当前操作
                    CancelCurrentOperation();
                    return true;
                    
                case Keys.A:
                    if (Control.ModifierKeys == Keys.Control)
                    {
                        // Ctrl+A 全选
                        _selectionSystem.SelectAllEntities();
                        return true;
                    }
                    break;
                    
                case Keys.Delete:
                    // 删除选中实体
                    if (_selectionSystem.SelectedEntities.Count > 0)
                    {
                        OnDeleteSelectedEntities?.Invoke(_selectionSystem.SelectedEntities);
                        return true;
                    }
                    break;
            }
            
            return false;
        }
        
        /// <summary>
        /// 取消当前操作
        /// </summary>
        public void CancelCurrentOperation()
        {
            try
            {
                switch (_currentState)
                {
                    case Phase2InteractionState.Selecting:
                        _selectionSystem.CancelSelection();
                        break;
                        
                    case Phase2InteractionState.Drawing:
                    case Phase2InteractionState.DynamicInput:
                        _dynamicInput.EndInput();
                        break;
                }
                
                _currentState = Phase2InteractionState.Idle;
                _isCommandActive = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error canceling operation: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 开始绘制命令
        /// </summary>
        public void StartDrawingCommand(Vector2 startPoint)
        {
            try
            {
                _isCommandActive = true;
                _currentState = Phase2InteractionState.Drawing;
                
                // 启动动态输入
                _dynamicInput.StartInput(startPoint, _configuration.DefaultDynamicInputMode);
                
                // 设置追踪基点
                if (_polarTracking.IsEnabled)
                {
                    _polarTracking.SetBasePoint(startPoint);
                }
                
                // 添加追踪点
                if (_objectTracking.IsEnabled)
                {
                    _objectTracking.AddTrackingPoint(startPoint, ObjectSnapMode.End);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error starting drawing command: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 结束绘制命令
        /// </summary>
        public void EndDrawingCommand()
        {
            try
            {
                _isCommandActive = false;
                _currentState = Phase2InteractionState.Idle;
                
                _dynamicInput.EndInput();
                
                if (_polarTracking.IsEnabled)
                {
                    _polarTracking.ClearBasePoint();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error ending drawing command: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 渲染所有系统
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            if (!_isInitialized) return;
            
            try
            {
                // 渲染顺序很重要：从底层到顶层
                
                // 1. 渲染对象追踪（最底层的参考线）
                _objectTracking?.Render(canvas);
                
                // 2. 渲染极轴追踪
                _polarTracking?.Render(canvas);
                
                // 3. 渲染选择系统
                _selectionSystem?.Render(canvas);
                
                // 4. 渲染动态输入（最顶层的UI）
                _dynamicInput?.Render(canvas);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error rendering Phase2 systems: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取增强的坐标（应用所有追踪和捕捉）
        /// </summary>
        public Vector2 GetEnhancedCoordinate(Vector2 rawCoordinate)
        {
            if (!_isInitialized) return rawCoordinate;

            try
            {
                return _dynamicInput?.GetEnhancedPosition(rawCoordinate) ?? rawCoordinate;
            }
            catch
            {
                return rawCoordinate;
            }
        }

        /// <summary>
        /// 设置极轴追踪启用状态
        /// </summary>
        public void SetPolarTrackingEnabled(bool enabled)
        {
            if (_polarTracking != null)
            {
                _polarTracking.IsEnabled = enabled;
                _configuration.PolarTrackingEnabled = enabled;
            }
        }

        /// <summary>
        /// 设置对象追踪启用状态
        /// </summary>
        public void SetObjectTrackingEnabled(bool enabled)
        {
            if (_objectTracking != null)
            {
                _objectTracking.IsEnabled = enabled;
                _configuration.ObjectTrackingEnabled = enabled;
            }
        }

        /// <summary>
        /// 设置动态输入启用状态
        /// </summary>
        public void SetDynamicInputEnabled(bool enabled)
        {
            if (_dynamicInput != null)
            {
                _dynamicInput.IsVisible = enabled;
                _configuration.DynamicInputEnabled = enabled;
            }
        }

        /// <summary>
        /// 获取极轴追踪启用状态
        /// </summary>
        public bool GetPolarTrackingEnabled()
        {
            return _polarTracking?.IsEnabled ?? false;
        }

        /// <summary>
        /// 获取对象追踪启用状态
        /// </summary>
        public bool GetObjectTrackingEnabled()
        {
            return _objectTracking?.IsEnabled ?? false;
        }

        /// <summary>
        /// 获取动态输入启用状态
        /// </summary>
        public bool GetDynamicInputEnabled()
        {
            return _dynamicInput?.IsVisible ?? false;
        }

        /// <summary>
        /// 获取极轴追踪设置
        /// </summary>
        public Tracking.PolarTrackingSettings GetPolarTrackingSettings()
        {
            var settings = new Tracking.PolarTrackingSettings();
            if (_polarTracking != null)
            {
                settings.IsEnabled = _polarTracking.IsEnabled;
                // TODO: 从PolarTrackingSystem获取其他设置
            }
            return settings;
        }

        /// <summary>
        /// 应用极轴追踪设置
        /// </summary>
        public void ApplyPolarTrackingSettings(Tracking.PolarTrackingSettings settings)
        {
            if (_polarTracking != null && settings != null)
            {
                _polarTracking.IsEnabled = settings.IsEnabled;
                _configuration.PolarTrackingEnabled = settings.IsEnabled;

                // TODO: 将其他设置应用到PolarTrackingSystem
                // _polarTracking.AngleTolerance = settings.AngleTolerance;
                // _polarTracking.TrackingAngles = settings.TrackingAngles;
            }
        }

        /// <summary>
        /// 获取对象捕捉设置
        /// </summary>
        public Base.ObjectSnapMode GetObjectSnapSettings()
        {
            return _viewer?._inputManager?.snapMgr?.RunningSnapModes ?? Base.ObjectSnapMode.Undefined;
        }

        /// <summary>
        /// 应用对象捕捉设置
        /// </summary>
        public void ApplyObjectSnapSettings(Base.ObjectSnapMode snapModes)
        {
            if (_viewer?._inputManager?.snapMgr != null)
            {
                _viewer._inputManager.snapMgr.RunningSnapModes = snapModes;
            }
        }
        
        /// <summary>
        /// 自动获取追踪点
        /// </summary>
        public void AutoAcquireTrackingPoints()
        {
            try
            {
                _objectTracking?.AutoAcquireTrackingPoints();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error auto-acquiring tracking points: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清理过期追踪点
        /// </summary>
        public void CleanupExpiredTrackingPoints()
        {
            try
            {
                _objectTracking?.ClearExpiredTrackingPoints(TimeSpan.FromMinutes(5));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning tracking points: {ex.Message}");
            }
        }
        
        #region 事件处理
        
        private void OnCoordinateConfirmed(Vector2 coordinate)
        {
            try
            {
                // 添加确认的坐标为追踪点
                if (_objectTracking.IsEnabled)
                {
                    _objectTracking.AddTrackingPoint(coordinate, ObjectSnapMode.End);
                }
                
                // 触发坐标确认事件
                OnCoordinateConfirmed_External?.Invoke(coordinate);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling coordinate confirmation: {ex.Message}");
            }
        }
        
        private void OnSelectionChanged(System.Collections.Generic.List<EntityBase> selectedEntities)
        {
            try
            {
                // 从选中实体自动获取追踪点
                if (_objectTracking.IsEnabled && selectedEntities.Count > 0)
                {
                    foreach (var entity in selectedEntities)
                    {
                        var snapPoints = entity.GetSnapPoints();
                        if (snapPoints != null)
                        {
                            foreach (var snapPoint in snapPoints)
                            {
                                _objectTracking.AddTrackingPoint(snapPoint.position, snapPoint.type, entity);
                            }
                        }
                    }
                }
                
                // 触发选择改变事件
                OnSelectionChanged_External?.Invoke(selectedEntities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling selection change: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 公共事件
        
        /// <summary>
        /// 坐标确认事件（对外）
        /// </summary>
        public event Action<Vector2> OnCoordinateConfirmed_External;
        
        /// <summary>
        /// 选择改变事件（对外）
        /// </summary>
        public event Action<System.Collections.Generic.List<EntityBase>> OnSelectionChanged_External;
        
        /// <summary>
        /// 删除选中实体事件
        /// </summary>
        public event Action<System.Collections.Generic.List<EntityBase>> OnDeleteSelectedEntities;
        
        #endregion
        
        public void Dispose()
        {
            try
            {
                _polarTracking?.Dispose();
                _objectTracking?.Dispose();
                _dynamicInput?.Dispose();
                _selectionSystem?.Dispose();
                
                _isInitialized = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing Phase2IntegrationManager: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 阶段2交互状态
    /// </summary>
    public enum Phase2InteractionState
    {
        Idle,           // 空闲状态
        Selecting,      // 选择状态
        Drawing,        // 绘制状态
        DynamicInput,   // 动态输入状态
        Tracking        // 追踪状态
    }
    
    /// <summary>
    /// 阶段2配置
    /// </summary>
    public class Phase2Configuration
    {
        public bool PolarTrackingEnabled { get; set; } = true;
        public bool ObjectTrackingEnabled { get; set; } = true;
        public bool DynamicInputEnabled { get; set; } = true;
        
        public System.Collections.Generic.List<float> PolarTrackingAngles { get; set; } = 
            new System.Collections.Generic.List<float> { 0, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330 };
        
        public DynamicInputMode DefaultDynamicInputMode { get; set; } = DynamicInputMode.Coordinate;
        public EntitySelectionMode DefaultSelectionMode { get; set; } = EntitySelectionMode.Point;
        
        public SelectionSettings SelectionSettings { get; set; } = new SelectionSettings();
    }
} 