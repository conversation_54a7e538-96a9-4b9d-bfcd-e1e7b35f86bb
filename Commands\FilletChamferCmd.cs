using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 圆角命令
    /// 实现专业CAD级别的圆角功能，支持多种类型的实体圆角处理
    /// </summary>
    public class FilletCmd : Command
    {
        private FilletState _currentState = FilletState.SetRadius;
        private float _radius = 10.0f;
        private EntityBase _firstEntity;
        private EntityBase _secondEntity;
        private Vector2 _firstClickPoint;
        private Vector2 _secondClickPoint;
        private List<EntityBase> _previewEntities = new List<EntityBase>();
        private FilletOptions _options = new FilletOptions();
        
        // 视觉样式
        private SKPaint _selectedPaint;
        private SKPaint _previewPaint;
        private SKPaint _radiusPaint;
        
        public override string Name => "FILLET";
        public override string Description => "在两个对象间创建圆角";
        
        public FilletCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 0, 255, 0), // 半透明绿色
                IsAntialias = true
            };
            
            _radiusPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 0.5f,
                Color = SKColors.Gray,
                PathEffect = SKPathEffect.CreateDash(new float[] { 3, 3 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = FilletState.SetRadius;
            _firstEntity = null;
            _secondEntity = null;
            _previewEntities.Clear();
            
            _viewer.Document.Prompt = $"当前圆角半径 = {_radius:F3}";
            ShowRadiusPrompt();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case FilletState.SetRadius:
                    if (_viewer._dynamicInputer?.GetResultNumber() != null)
                    {
                        _radius = Math.Max(0, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                        _viewer._dynamicInputer.EndInput();
                    }
                    StartEntitySelection();
                    goto case FilletState.SelectFirstEntity; // 继续执行选择逻辑
                    
                case FilletState.SelectFirstEntity:
                    HandleFirstEntitySelection(currentPoint);
                    break;
                    
                case FilletState.SelectSecondEntity:
                    HandleSecondEntitySelection(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            if (_currentState == FilletState.SelectSecondEntity && _firstEntity != null)
            {
                UpdateFilletPreview(currentPoint);
                _viewer.RepaintCanvas();
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == FilletState.SetRadius)
                    {
                        if (_viewer._dynamicInputer?.GetResultNumber() != null)
                        {
                            _radius = Math.Max(0, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                            _viewer._dynamicInputer.EndInput();
                        }
                        StartEntitySelection();
                    }
                    else if (_currentState == FilletState.SelectSecondEntity && _previewEntities.Count > 0)
                    {
                        CompleteFillet();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.R:
                    // 修改半径
                    _currentState = FilletState.SetRadius;
                    ShowRadiusPrompt();
                    break;
                    
                case Keys.T:
                    // 切换修剪模式
                    _options.TrimMode = !_options.TrimMode;
                    _viewer.Document.Prompt += $" [修剪: {(_options.TrimMode ? "开" : "关")}]";
                    break;
                    
                case Keys.M:
                    // 切换多重圆角模式
                    _options.Multiple = !_options.Multiple;
                    _viewer.Document.Prompt += $" [多重: {(_options.Multiple ? "开" : "关")}]";
                    break;
            }
        }
        
        private void ShowRadiusPrompt()
        {
            _viewer.Document.Prompt = $"指定圆角半径 [{_radius:F3}]：";
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.StartInput(Vector2.Zero, DynInputStatus.WaitForNumber);
            }
        }
        
        private void StartEntitySelection()
        {
            _currentState = FilletState.SelectFirstEntity;
            _viewer.Document.Prompt = "选择第一个对象或 [R]半径 [T]修剪 [M]多重：";
        }
        
        private void HandleFirstEntitySelection(Vector2 point)
        {
            var hitEntity = FindFilletableEntity(point);
            
            if (hitEntity != null)
            {
                _firstEntity = hitEntity;
                _firstClickPoint = point;
                _currentState = FilletState.SelectSecondEntity;
                _viewer.Document.Prompt = "选择第二个对象：";
            }
            else
            {
                _viewer.Document.Prompt = "未找到可圆角的对象，请重新选择：";
            }
        }
        
        private void HandleSecondEntitySelection(Vector2 point)
        {
            var hitEntity = FindFilletableEntity(point);
            
            if (hitEntity != null && hitEntity != _firstEntity)
            {
                _secondEntity = hitEntity;
                _secondClickPoint = point;
                
                var filletResult = CalculateFillet(_firstEntity, _secondEntity, _firstClickPoint, _secondClickPoint);
                
                if (filletResult.IsValid)
                {
                    ApplyFilletResult(filletResult);
                    
                    if (_options.Multiple)
                    {
                        // 多重模式，继续选择
                        _firstEntity = _secondEntity;
                        _firstClickPoint = _secondClickPoint;
                        _secondEntity = null;
                        _previewEntities.Clear();
                        _viewer.Document.Prompt = "选择下一个对象：";
                    }
                    else
                    {
                        Finish();
                    }
                }
                else
                {
                    _viewer.Document.Prompt = $"无法创建圆角: {filletResult.ErrorMessage}";
                }
            }
            else if (hitEntity == _firstEntity)
            {
                _viewer.Document.Prompt = "不能选择同一个对象，请选择另一个对象：";
            }
            else
            {
                _viewer.Document.Prompt = "未找到可圆角的对象，请重新选择：";
            }
        }
        
        private void UpdateFilletPreview(Vector2 mousePoint)
        {
            _previewEntities.Clear();
            
            var nearestEntity = FindFilletableEntity(mousePoint);
            if (nearestEntity != null && nearestEntity != _firstEntity)
            {
                var filletResult = CalculateFillet(_firstEntity, nearestEntity, _firstClickPoint, mousePoint);
                
                if (filletResult.IsValid)
                {
                    _previewEntities.AddRange(filletResult.ResultEntities);
                }
            }
        }
        
        private FilletResult CalculateFillet(EntityBase entity1, EntityBase entity2, Vector2 click1, Vector2 click2)
        {
            var result = new FilletResult();
            
            try
            {
                // 根据实体类型组合计算圆角
                if (entity1 is EntityLine line1 && entity2 is EntityLine line2)
                {
                    result = CalculateLineLineFillet(line1, line2, click1, click2);
                }
                else if (entity1 is EntityLine line && entity2 is EntityArc arc)
                {
                    result = CalculateLineArcFillet(line, arc, click1, click2);
                }
                else if (entity1 is EntityArc arc1 && entity2 is EntityArc arc2)
                {
                    result = CalculateArcArcFillet(arc1, arc2, click1, click2);
                }
                else if (entity1 is EntityCircle circle1 && entity2 is EntityCircle circle2)
                {
                    result = CalculateCircleCircleFillet(circle1, circle2, click1, click2);
                }
                else
                {
                    result.ErrorMessage = "不支持的实体类型组合";
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"Fillet calculation error: {ex.Message}");
            }
            
            return result;
        }
        
        private FilletResult CalculateLineLineFillet(EntityLine line1, EntityLine line2, Vector2 click1, Vector2 click2)
        {
            var result = new FilletResult();
            
            // 计算两直线的交点
            var intersection = CalculateLineIntersection(line1, line2, true);
            if (!intersection.HasValue)
            {
                result.ErrorMessage = "直线平行或不相交";
                return result;
            }
            
            // 如果半径为0，直接连接到交点
            if (_radius < 0.001f)
            {
                result = CreateZeroRadiusFillet(line1, line2, intersection.Value, click1, click2);
                return result;
            }
            
            // 计算圆角圆心和切点
            var filletInfo = CalculateLineLineFilletGeometry(line1, line2, intersection.Value, _radius, click1, click2);
            
            if (filletInfo != null)
            {
                result.ResultEntities.AddRange(CreateFilletEntities(line1, line2, filletInfo));
                result.IsValid = true;
            }
            else
            {
                result.ErrorMessage = "无法计算圆角几何";
            }
            
            return result;
        }
        
        private FilletResult CalculateLineArcFillet(EntityLine line, EntityArc arc, Vector2 clickLine, Vector2 clickArc)
        {
            var result = new FilletResult();

            try
            {
                // 线弧圆角的基本实现
                // 1. 计算直线与圆弧的交点
                var intersections = CalculateLineArcIntersections(line, arc);

                if (intersections.Count == 0)
                {
                    result.ErrorMessage = "直线与圆弧不相交，无法创建圆角";
                    return result;
                }

                // 2. 选择最接近点击点的交点
                var intersection = intersections.OrderBy(p =>
                    Math.Min((p - clickLine).LengthSquared(), (p - clickArc).LengthSquared())).First();

                // 3. 创建简化的圆角（使用零半径圆角）
                if (_filletRadius < 0.001f)
                {
                    // 零半径圆角：直接在交点处连接
                    result.IsSuccess = true;
                    result.TrimLine1Start = line.StartPoint;
                    result.TrimLine1End = intersection;
                    result.TrimArc1Start = intersection;
                    result.TrimArc1End = arc.EndPoint;
                }
                else
                {
                    // 简化实现：创建一个小的连接弧
                    result.FilletArc = CreateSimpleFilletArc(intersection, _filletRadius);
                    result.IsSuccess = true;
                }
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"线弧圆角计算失败: {ex.Message}";
            }

            return result;
        }
        
        private FilletResult CalculateArcArcFillet(EntityArc arc1, EntityArc arc2, Vector2 click1, Vector2 click2)
        {
            var result = new FilletResult();

            try
            {
                // 弧弧圆角的基本实现
                // 1. 计算两个圆弧的交点
                var intersections = CalculateArcArcIntersections(arc1, arc2);

                if (intersections.Count == 0)
                {
                    result.ErrorMessage = "两个圆弧不相交，无法创建圆角";
                    return result;
                }

                // 2. 选择最接近点击点的交点
                var intersection = intersections.OrderBy(p =>
                    Math.Min((p - click1).LengthSquared(), (p - click2).LengthSquared())).First();

                // 3. 创建简化的圆角
                if (_filletRadius < 0.001f)
                {
                    // 零半径圆角：直接在交点处连接
                    result.IsSuccess = true;
                    result.TrimArc1Start = arc1.StartPoint;
                    result.TrimArc1End = intersection;
                    result.TrimArc2Start = intersection;
                    result.TrimArc2End = arc2.EndPoint;
                }
                else
                {
                    // 简化实现：创建连接弧
                    result.FilletArc = CreateSimpleFilletArc(intersection, _filletRadius);
                    result.IsSuccess = true;
                }
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"弧弧圆角计算失败: {ex.Message}";
            }

            return result;
        }
        
        private FilletResult CalculateCircleCircleFillet(EntityCircle circle1, EntityCircle circle2, Vector2 click1, Vector2 click2)
        {
            var result = new FilletResult();

            try
            {
                // 圆圆圆角的基本实现
                // 1. 计算两个圆的交点
                var intersections = CalculateCircleCircleIntersections(circle1, circle2);

                if (intersections.Count == 0)
                {
                    result.ErrorMessage = "两个圆不相交，无法创建圆角";
                    return result;
                }

                // 2. 选择最接近点击点的交点
                var intersection = intersections.OrderBy(p =>
                    Math.Min((p - click1).LengthSquared(), (p - click2).LengthSquared())).First();

                // 3. 创建简化的圆角
                if (_filletRadius < 0.001f)
                {
                    // 零半径圆角：在交点处连接
                    result.IsSuccess = true;
                    // 对于圆形，我们创建连接线
                    result.ConnectingLine = new EntityLine
                    {
                        StartPoint = intersection,
                        EndPoint = intersection
                    };
                }
                else
                {
                    // 简化实现：创建连接弧
                    result.FilletArc = CreateSimpleFilletArc(intersection, _filletRadius);
                    result.IsSuccess = true;
                }
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"圆圆圆角计算失败: {ex.Message}";
            }

            return result;
        }
        
        private FilletResult CreateZeroRadiusFillet(EntityLine line1, EntityLine line2, Vector2 intersection, Vector2 click1, Vector2 click2)
        {
            var result = new FilletResult();
            
            // 创建延伸到交点的直线
            var newLine1 = new EntityLine
            {
                StartPoint = GetLineSegmentStart(line1, click1, intersection),
                EndPoint = intersection,
                LineType = line1.LineType,
                LineWeight = line1.LineWeight,
                Color = line1.Color
            };
            
            var newLine2 = new EntityLine
            {
                StartPoint = intersection,
                EndPoint = GetLineSegmentEnd(line2, click2, intersection),
                LineType = line2.LineType,
                LineWeight = line2.LineWeight,
                Color = line2.Color
            };
            
            result.ResultEntities.Add(newLine1);
            result.ResultEntities.Add(newLine2);
            result.EntitiesToRemove.Add(line1);
            result.EntitiesToRemove.Add(line2);
            result.IsValid = true;
            
            return result;
        }
        
        private LineLineFilletInfo CalculateLineLineFilletGeometry(EntityLine line1, EntityLine line2, Vector2 intersection, float radius, Vector2 click1, Vector2 click2)
        {
            // 计算两直线的方向向量
            var dir1 = Vector2.Normalize(line1.EndPoint - line1.StartPoint);
            var dir2 = Vector2.Normalize(line2.EndPoint - line2.StartPoint);
            
            // 根据点击位置确定线段方向
            if (Vector2.Dot(click1 - intersection, dir1) < 0) dir1 = -dir1;
            if (Vector2.Dot(click2 - intersection, dir2) < 0) dir2 = -dir2;
            
            // 计算角平分线
            var bisector = Vector2.Normalize(dir1 + dir2);
            
            // 计算内角
            var cosAngle = Vector2.Dot(dir1, dir2);
            var angle = Math.Acos(Math.Max(-1, Math.Min(1, cosAngle)));
            
            if (Math.Abs(angle) < 0.001f || Math.Abs(angle - Math.PI) < 0.001f)
            {
                return null; // 平行线
            }
            
            // 计算圆心距离
            var centerDistance = radius / Math.Sin(angle / 2);
            
            // 确定圆心位置（内角还是外角）
            var cross = dir1.X * dir2.Y - dir1.Y * dir2.X;
            var centerDirection = cross > 0 ? bisector : -bisector;
            
            var center = intersection + centerDirection * (float)centerDistance;
            
            // 计算切点
            var normal1 = new Vector2(-dir1.Y, dir1.X);
            var normal2 = new Vector2(-dir2.Y, dir2.X);
            
            if (Vector2.Dot(normal1, centerDirection) < 0) normal1 = -normal1;
            if (Vector2.Dot(normal2, centerDirection) < 0) normal2 = -normal2;
            
            var tangent1 = center + normal1 * radius;
            var tangent2 = center + normal2 * radius;
            
            // 计算圆弧角度
            var startAngle = Math.Atan2(tangent1.Y - center.Y, tangent1.X - center.X);
            var endAngle = Math.Atan2(tangent2.Y - center.Y, tangent2.X - center.X);
            
            return new LineLineFilletInfo
            {
                Center = center,
                Radius = radius,
                Tangent1 = tangent1,
                Tangent2 = tangent2,
                StartAngle = (float)(startAngle * 180 / Math.PI),
                EndAngle = (float)(endAngle * 180 / Math.PI),
                Line1 = line1,
                Line2 = line2,
                Click1 = click1,
                Click2 = click2
            };
        }
        
        private List<EntityBase> CreateFilletEntities(EntityLine line1, EntityLine line2, LineLineFilletInfo filletInfo)
        {
            var entities = new List<EntityBase>();
            
            // 创建圆弧
            var arc = new EntityArc
            {
                Center = filletInfo.Center,
                Radius = filletInfo.Radius,
                StartAngle = filletInfo.StartAngle,
                EndAngle = filletInfo.EndAngle,
                LineType = line1.LineType,
                LineWeight = line1.LineWeight,
                Color = line1.Color
            };
            entities.Add(arc);
            
            // 创建修剪后的直线
            if (_options.TrimMode)
            {
                var newLine1 = new EntityLine
                {
                    StartPoint = GetLineSegmentStart(line1, filletInfo.Click1, filletInfo.Tangent1),
                    EndPoint = filletInfo.Tangent1,
                    LineType = line1.LineType,
                    LineWeight = line1.LineWeight,
                    Color = line1.Color
                };
                
                var newLine2 = new EntityLine
                {
                    StartPoint = filletInfo.Tangent2,
                    EndPoint = GetLineSegmentEnd(line2, filletInfo.Click2, filletInfo.Tangent2),
                    LineType = line2.LineType,
                    LineWeight = line2.LineWeight,
                    Color = line2.Color
                };
                
                entities.Add(newLine1);
                entities.Add(newLine2);
            }
            
            return entities;
        }
        
        private Vector2 GetLineSegmentStart(EntityLine line, Vector2 clickPoint, Vector2 cutPoint)
        {
            // 确定保留线段的起点
            var distToStart = Vector2.Distance(clickPoint, line.StartPoint);
            var distToEnd = Vector2.Distance(clickPoint, line.EndPoint);
            
            return distToStart < distToEnd ? line.StartPoint : cutPoint;
        }
        
        private Vector2 GetLineSegmentEnd(EntityLine line, Vector2 clickPoint, Vector2 cutPoint)
        {
            // 确定保留线段的终点
            var distToStart = Vector2.Distance(clickPoint, line.StartPoint);
            var distToEnd = Vector2.Distance(clickPoint, line.EndPoint);
            
            return distToStart < distToEnd ? cutPoint : line.EndPoint;
        }
        
        private Vector2? CalculateLineIntersection(EntityLine line1, EntityLine line2, bool extendLines = false)
        {
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;
            
            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-6) return null; // 平行线
            
            var t1 = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            var t2 = (d3.X * d1.Y - d3.Y * d1.X) / cross;
            
            if (extendLines || (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1))
            {
                return line1.StartPoint + t1 * d1;
            }
            
            return null;
        }
        
        private void ApplyFilletResult(FilletResult result)
        {
            if (!result.IsValid) return;
            
            try
            {
                // 移除原实体
                if (_options.TrimMode)
                {
                    foreach (var entity in result.EntitiesToRemove)
                    {
                        _viewer.Document.ActiveLayer.Children.Remove(entity);
                    }
                    
                    if (result.EntitiesToRemove.Count == 0)
                    {
                        // 如果没有指定要移除的实体，移除原始实体
                        _viewer.Document.ActiveLayer.Children.Remove(_firstEntity);
                        _viewer.Document.ActiveLayer.Children.Remove(_secondEntity);
                    }
                }
                
                // 添加新实体
                foreach (var entity in result.ResultEntities)
                {
                    _viewer.Document.ActiveLayer.Children.Add(entity);
                }
                
                _viewer.RepaintCanvas();
                _viewer.Document.Prompt = "圆角创建成功";
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"应用圆角失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Apply fillet error: {ex.Message}");
            }
        }
        
        private void CompleteFillet()
        {
            if (_previewEntities.Count > 0)
            {
                // 将预览实体应用到文档
                var result = new FilletResult
                {
                    ResultEntities = new List<EntityBase>(_previewEntities),
                    IsValid = true
                };
                
                if (_options.TrimMode)
                {
                    result.EntitiesToRemove.Add(_firstEntity);
                    result.EntitiesToRemove.Add(_secondEntity);
                }
                
                ApplyFilletResult(result);
                Finish();
            }
        }
        
        private EntityBase FindFilletableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsFilletableEntity(entity) && IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsFilletableEntity(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityArc ||
                   entity is EntityCircle ||
                   entity is EntityLwPolyline;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制选中实体高亮
            if (_firstEntity != null)
            {
                RenderSelectedEntity(canvas, _firstEntity);
            }
            
            if (_secondEntity != null)
            {
                RenderSelectedEntity(canvas, _secondEntity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制半径指示
            if (_currentState == FilletState.SelectSecondEntity && _firstEntity != null)
            {
                RenderRadiusIndicator(canvas);
            }
        }
        
        private void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _selectedPaint);
            }
        }
        
        private void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
                    
                case EntityArc arc:
                    var rect = new SKRect(arc.Center.X - arc.Radius, arc.Center.Y - arc.Radius,
                                         arc.Center.X + arc.Radius, arc.Center.Y + arc.Radius);
                    canvas.DrawArc(rect, arc.StartAngle, arc.EndAngle - arc.StartAngle, false, _previewPaint);
                    break;
            }
        }
        
        private void RenderRadiusIndicator(SKCanvas canvas)
        {
            if (_firstEntity?.BoundingBox != null)
            {
                var center = _firstEntity.BoundingBox.Center;
                canvas.DrawCircle(center.X, center.Y, _radius, _radiusPaint);
                
                // 绘制半径文本
                var radiusText = $"R={_radius:F1}";
                var textPaint = new SKPaint
                {
                    Style = SKPaintStyle.Fill,
                    TextSize = 12,
                    Color = SKColors.Gray,
                    IsAntialias = true
                };
                
                canvas.DrawText(radiusText, center.X + _radius + 5, center.Y, textPaint);
                textPaint.Dispose();
            }
        }
        
        public override void Cancel()
        {
            _firstEntity = null;
            _secondEntity = null;
            _previewEntities.Clear();
            _currentState = FilletState.SetRadius;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _selectedPaint?.Dispose();
                _previewPaint?.Dispose();
                _radiusPaint?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
    
    /// <summary>
    /// 倒角命令
    /// </summary>
    public class ChamferCmd : Command
    {
        private ChamferState _currentState = ChamferState.SetDistances;
        private float _distance1 = 10.0f;
        private float _distance2 = 10.0f;
        private EntityBase _firstEntity;
        private EntityBase _secondEntity;
        private Vector2 _firstClickPoint;
        private Vector2 _secondClickPoint;
        private List<EntityBase> _previewEntities = new List<EntityBase>();
        private ChamferOptions _options = new ChamferOptions();
        
        // 视觉样式
        private SKPaint _selectedPaint;
        private SKPaint _previewPaint;
        private SKPaint _distancePaint;
        
        public override string Name => "CHAMFER";
        public override string Description => "在两个对象间创建倒角";
        
        public ChamferCmd()
        {
            InitializePaints();
        }
        
        private void InitializePaints()
        {
            _selectedPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Orange,
                IsAntialias = true
            };
            
            _previewPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColor.FromArgb(150, 255, 0, 255), // 半透明紫色
                IsAntialias = true
            };
            
            _distancePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 0.5f,
                Color = SKColors.Gray,
                PathEffect = SKPathEffect.CreateDash(new float[] { 3, 3 }, 0),
                IsAntialias = true
            };
        }
        
        public override void OnStart()
        {
            _currentState = ChamferState.SetDistances;
            _firstEntity = null;
            _secondEntity = null;
            _previewEntities.Clear();
            
            ShowDistancePrompt();
        }
        
        public override void OnMouseDown(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            switch (_currentState)
            {
                case ChamferState.SetDistances:
                    if (_viewer._dynamicInputer?.GetResultNumber() != null)
                    {
                        _distance1 = Math.Max(0, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                        _distance2 = _distance1; // 对称倒角
                        _viewer._dynamicInputer.EndInput();
                    }
                    StartEntitySelection();
                    goto case ChamferState.SelectFirstEntity;
                    
                case ChamferState.SelectFirstEntity:
                    HandleFirstEntitySelection(currentPoint);
                    break;
                    
                case ChamferState.SelectSecondEntity:
                    HandleSecondEntitySelection(currentPoint);
                    break;
            }
        }
        
        public override void OnMouseMove(MouseEventArgs e)
        {
            var currentPoint = _viewer.CanvasToModel(new Vector2(e.X, e.Y));
            
            if (_currentState == ChamferState.SelectSecondEntity && _firstEntity != null)
            {
                UpdateChamferPreview(currentPoint);
                _viewer.RepaintCanvas();
            }
        }
        
        public override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_currentState == ChamferState.SetDistances)
                    {
                        if (_viewer._dynamicInputer?.GetResultNumber() != null)
                        {
                            _distance1 = Math.Max(0, (float)_viewer._dynamicInputer.GetResultNumber().Value);
                            _distance2 = _distance1;
                            _viewer._dynamicInputer.EndInput();
                        }
                        StartEntitySelection();
                    }
                    else if (_currentState == ChamferState.SelectSecondEntity && _previewEntities.Count > 0)
                    {
                        CompleteChamfer();
                    }
                    break;
                    
                case Keys.Escape:
                    Cancel();
                    break;
                    
                case Keys.D:
                    // 修改距离
                    _currentState = ChamferState.SetDistances;
                    ShowDistancePrompt();
                    break;
                    
                case Keys.T:
                    // 切换修剪模式
                    _options.TrimMode = !_options.TrimMode;
                    _viewer.Document.Prompt += $" [修剪: {(_options.TrimMode ? "开" : "关")}]";
                    break;
                    
                case Keys.A:
                    // 切换角度模式
                    _options.AngleMode = !_options.AngleMode;
                    _viewer.Document.Prompt += $" [角度模式: {(_options.AngleMode ? "开" : "关")}]";
                    break;
            }
        }
        
        private void ShowDistancePrompt()
        {
            _viewer.Document.Prompt = $"指定第一个倒角距离 [{_distance1:F3}]：";
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.StartInput(Vector2.Zero, DynInputStatus.WaitForNumber);
            }
        }
        
        private void StartEntitySelection()
        {
            _currentState = ChamferState.SelectFirstEntity;
            _viewer.Document.Prompt = "选择第一个对象或 [D]距离 [T]修剪 [A]角度：";
        }
        
        private void HandleFirstEntitySelection(Vector2 point)
        {
            var hitEntity = FindChamferableEntity(point);
            
            if (hitEntity != null)
            {
                _firstEntity = hitEntity;
                _firstClickPoint = point;
                _currentState = ChamferState.SelectSecondEntity;
                _viewer.Document.Prompt = "选择第二个对象：";
            }
            else
            {
                _viewer.Document.Prompt = "未找到可倒角的对象，请重新选择：";
            }
        }
        
        private void HandleSecondEntitySelection(Vector2 point)
        {
            var hitEntity = FindChamferableEntity(point);
            
            if (hitEntity != null && hitEntity != _firstEntity)
            {
                _secondEntity = hitEntity;
                _secondClickPoint = point;
                
                var chamferResult = CalculateChamfer(_firstEntity, _secondEntity, _firstClickPoint, _secondClickPoint);
                
                if (chamferResult.IsValid)
                {
                    ApplyChamferResult(chamferResult);
                    Finish();
                }
                else
                {
                    _viewer.Document.Prompt = $"无法创建倒角: {chamferResult.ErrorMessage}";
                }
            }
            else if (hitEntity == _firstEntity)
            {
                _viewer.Document.Prompt = "不能选择同一个对象，请选择另一个对象：";
            }
            else
            {
                _viewer.Document.Prompt = "未找到可倒角的对象，请重新选择：";
            }
        }
        
        private void UpdateChamferPreview(Vector2 mousePoint)
        {
            _previewEntities.Clear();
            
            var nearestEntity = FindChamferableEntity(mousePoint);
            if (nearestEntity != null && nearestEntity != _firstEntity)
            {
                var chamferResult = CalculateChamfer(_firstEntity, nearestEntity, _firstClickPoint, mousePoint);
                
                if (chamferResult.IsValid)
                {
                    _previewEntities.AddRange(chamferResult.ResultEntities);
                }
            }
        }
        
        private ChamferResult CalculateChamfer(EntityBase entity1, EntityBase entity2, Vector2 click1, Vector2 click2)
        {
            var result = new ChamferResult();
            
            try
            {
                // 目前只支持直线间的倒角
                if (entity1 is EntityLine line1 && entity2 is EntityLine line2)
                {
                    result = CalculateLineLineChamfer(line1, line2, click1, click2);
                }
                else
                {
                    result.ErrorMessage = "暂时只支持直线间的倒角";
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"Chamfer calculation error: {ex.Message}");
            }
            
            return result;
        }
        
        private ChamferResult CalculateLineLineChamfer(EntityLine line1, EntityLine line2, Vector2 click1, Vector2 click2)
        {
            var result = new ChamferResult();
            
            // 计算两直线的交点
            var intersection = CalculateLineIntersection(line1, line2, true);
            if (!intersection.HasValue)
            {
                result.ErrorMessage = "直线平行或不相交";
                return result;
            }
            
            // 计算倒角点
            var chamferInfo = CalculateLineLineChamferGeometry(line1, line2, intersection.Value, _distance1, _distance2, click1, click2);
            
            if (chamferInfo != null)
            {
                result.ResultEntities.AddRange(CreateChamferEntities(line1, line2, chamferInfo));
                result.IsValid = true;
            }
            else
            {
                result.ErrorMessage = "无法计算倒角几何";
            }
            
            return result;
        }
        
        private LineLineChamferInfo CalculateLineLineChamferGeometry(EntityLine line1, EntityLine line2, Vector2 intersection, float dist1, float dist2, Vector2 click1, Vector2 click2)
        {
            // 计算两直线的方向向量
            var dir1 = Vector2.Normalize(line1.EndPoint - line1.StartPoint);
            var dir2 = Vector2.Normalize(line2.EndPoint - line2.StartPoint);
            
            // 根据点击位置确定线段方向
            if (Vector2.Dot(click1 - intersection, dir1) < 0) dir1 = -dir1;
            if (Vector2.Dot(click2 - intersection, dir2) < 0) dir2 = -dir2;
            
            // 计算倒角点
            var chamferPoint1 = intersection - dir1 * dist1;
            var chamferPoint2 = intersection - dir2 * dist2;
            
            return new LineLineChamferInfo
            {
                ChamferPoint1 = chamferPoint1,
                ChamferPoint2 = chamferPoint2,
                Line1 = line1,
                Line2 = line2,
                Click1 = click1,
                Click2 = click2
            };
        }
        
        private List<EntityBase> CreateChamferEntities(EntityLine line1, EntityLine line2, LineLineChamferInfo chamferInfo)
        {
            var entities = new List<EntityBase>();
            
            // 创建倒角线
            var chamferLine = new EntityLine
            {
                StartPoint = chamferInfo.ChamferPoint1,
                EndPoint = chamferInfo.ChamferPoint2,
                LineType = line1.LineType,
                LineWeight = line1.LineWeight,
                Color = line1.Color
            };
            entities.Add(chamferLine);
            
            // 创建修剪后的直线
            if (_options.TrimMode)
            {
                var newLine1 = new EntityLine
                {
                    StartPoint = GetLineSegmentStart(line1, chamferInfo.Click1, chamferInfo.ChamferPoint1),
                    EndPoint = chamferInfo.ChamferPoint1,
                    LineType = line1.LineType,
                    LineWeight = line1.LineWeight,
                    Color = line1.Color
                };
                
                var newLine2 = new EntityLine
                {
                    StartPoint = chamferInfo.ChamferPoint2,
                    EndPoint = GetLineSegmentEnd(line2, chamferInfo.Click2, chamferInfo.ChamferPoint2),
                    LineType = line2.LineType,
                    LineWeight = line2.LineWeight,
                    Color = line2.Color
                };
                
                entities.Add(newLine1);
                entities.Add(newLine2);
            }
            
            return entities;
        }
        
        private Vector2 GetLineSegmentStart(EntityLine line, Vector2 clickPoint, Vector2 cutPoint)
        {
            var distToStart = Vector2.Distance(clickPoint, line.StartPoint);
            var distToEnd = Vector2.Distance(clickPoint, line.EndPoint);
            
            return distToStart < distToEnd ? line.StartPoint : cutPoint;
        }
        
        private Vector2 GetLineSegmentEnd(EntityLine line, Vector2 clickPoint, Vector2 cutPoint)
        {
            var distToStart = Vector2.Distance(clickPoint, line.StartPoint);
            var distToEnd = Vector2.Distance(clickPoint, line.EndPoint);
            
            return distToStart < distToEnd ? cutPoint : line.EndPoint;
        }
        
        private Vector2? CalculateLineIntersection(EntityLine line1, EntityLine line2, bool extendLines = false)
        {
            var d1 = line1.EndPoint - line1.StartPoint;
            var d2 = line2.EndPoint - line2.StartPoint;
            var d3 = line1.StartPoint - line2.StartPoint;
            
            var cross = d1.X * d2.Y - d1.Y * d2.X;
            if (Math.Abs(cross) < 1e-6) return null;
            
            var t1 = (d3.X * d2.Y - d3.Y * d2.X) / cross;
            var t2 = (d3.X * d1.Y - d3.Y * d1.X) / cross;
            
            if (extendLines || (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1))
            {
                return line1.StartPoint + t1 * d1;
            }
            
            return null;
        }
        
        private void ApplyChamferResult(ChamferResult result)
        {
            if (!result.IsValid) return;
            
            try
            {
                // 移除原实体
                if (_options.TrimMode)
                {
                    _viewer.Document.ActiveLayer.Children.Remove(_firstEntity);
                    _viewer.Document.ActiveLayer.Children.Remove(_secondEntity);
                }
                
                // 添加新实体
                foreach (var entity in result.ResultEntities)
                {
                    _viewer.Document.ActiveLayer.Children.Add(entity);
                }
                
                _viewer.RepaintCanvas();
                _viewer.Document.Prompt = "倒角创建成功";
            }
            catch (Exception ex)
            {
                _viewer.Document.Prompt = $"应用倒角失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Apply chamfer error: {ex.Message}");
            }
        }
        
        private void CompleteChamfer()
        {
            if (_previewEntities.Count > 0)
            {
                var result = new ChamferResult
                {
                    ResultEntities = new List<EntityBase>(_previewEntities),
                    IsValid = true
                };
                
                ApplyChamferResult(result);
                Finish();
            }
        }
        
        private EntityBase FindChamferableEntity(Vector2 point)
        {
            const float tolerance = 5.0f;
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsChamferableEntity(entity) && IsPointNearEntity(point, entity, tolerance))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsChamferableEntity(EntityBase entity)
        {
            return entity is EntityLine ||
                   entity is EntityLwPolyline;
        }
        
        private bool IsPointNearEntity(Vector2 point, EntityBase entity, float tolerance)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null) return false;
            
            var expandedBounds = new BoundingBox(
                bounds.MinX - tolerance,
                bounds.MinY - tolerance,
                bounds.MaxX + tolerance,
                bounds.MaxY + tolerance
            );
            
            return expandedBounds.Contains(point.X, point.Y);
        }
        
        public override void OnRender(SKCanvas canvas)
        {
            // 绘制选中实体高亮
            if (_firstEntity != null)
            {
                RenderSelectedEntity(canvas, _firstEntity);
            }
            
            if (_secondEntity != null)
            {
                RenderSelectedEntity(canvas, _secondEntity);
            }
            
            // 绘制预览
            foreach (var entity in _previewEntities)
            {
                RenderPreviewEntity(canvas, entity);
            }
            
            // 绘制距离指示
            if (_currentState == ChamferState.SelectSecondEntity && _firstEntity != null)
            {
                RenderDistanceIndicator(canvas);
            }
        }
        
        private void RenderSelectedEntity(SKCanvas canvas, EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds != null && !bounds.IsEmpty)
            {
                canvas.DrawRect(bounds.MinX, bounds.MinY, bounds.Width, bounds.Height, _selectedPaint);
            }
        }
        
        private void RenderPreviewEntity(SKCanvas canvas, EntityBase entity)
        {
            switch (entity)
            {
                case EntityLine line:
                    canvas.DrawLine(line.StartPoint.X, line.StartPoint.Y,
                                  line.EndPoint.X, line.EndPoint.Y, _previewPaint);
                    break;
            }
        }
        
        private void RenderDistanceIndicator(SKCanvas canvas)
        {
            if (_firstEntity?.BoundingBox != null)
            {
                var center = _firstEntity.BoundingBox.Center;
                
                // 绘制距离文本
                var distanceText = $"D1={_distance1:F1} D2={_distance2:F1}";
                var textPaint = new SKPaint
                {
                    Style = SKPaintStyle.Fill,
                    TextSize = 12,
                    Color = SKColors.Gray,
                    IsAntialias = true
                };
                
                canvas.DrawText(distanceText, center.X + 10, center.Y, textPaint);
                textPaint.Dispose();
            }
        }
        
        public override void Cancel()
        {
            _firstEntity = null;
            _secondEntity = null;
            _previewEntities.Clear();
            _currentState = ChamferState.SetDistances;
            
            if (_viewer._dynamicInputer != null)
            {
                _viewer._dynamicInputer.EndInput();
            }
            
            base.Cancel();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _selectedPaint?.Dispose();
                _previewPaint?.Dispose();
                _distancePaint?.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 辅助方法

        /// <summary>
        /// 计算直线与圆弧的交点
        /// </summary>
        private List<Vector2> CalculateLineArcIntersections(EntityLine line, EntityArc arc)
        {
            var intersections = new List<Vector2>();

            try
            {
                // 简化实现：计算直线与圆的交点
                var lineDir = line.EndPoint - line.StartPoint;
                var lineToCenter = arc.Center - line.StartPoint;

                var a = Vector2.Dot(lineDir, lineDir);
                var b = 2 * Vector2.Dot(lineToCenter, lineDir);
                var c = Vector2.Dot(lineToCenter, lineToCenter) - arc.Radius * arc.Radius;

                var discriminant = b * b - 4 * a * c;

                if (discriminant >= 0)
                {
                    var sqrt = Math.Sqrt(discriminant);
                    var t1 = (-b + sqrt) / (2 * a);
                    var t2 = (-b - sqrt) / (2 * a);

                    if (t1 >= 0 && t1 <= 1)
                    {
                        intersections.Add(line.StartPoint + (float)t1 * lineDir);
                    }
                    if (t2 >= 0 && t2 <= 1 && Math.Abs(t1 - t2) > 0.001)
                    {
                        intersections.Add(line.StartPoint + (float)t2 * lineDir);
                    }
                }
            }
            catch
            {
                // 忽略计算错误
            }

            return intersections;
        }

        /// <summary>
        /// 计算两个圆弧的交点
        /// </summary>
        private List<Vector2> CalculateArcArcIntersections(EntityArc arc1, EntityArc arc2)
        {
            // 简化为圆圆交点计算
            return CalculateCircleCircleIntersections(
                new EntityCircle { Center = arc1.Center, Radius = arc1.Radius },
                new EntityCircle { Center = arc2.Center, Radius = arc2.Radius });
        }

        /// <summary>
        /// 计算两个圆的交点
        /// </summary>
        private List<Vector2> CalculateCircleCircleIntersections(EntityCircle circle1, EntityCircle circle2)
        {
            var intersections = new List<Vector2>();

            try
            {
                var d = (circle2.Center - circle1.Center).Length();
                var r1 = circle1.Radius;
                var r2 = circle2.Radius;

                // 检查是否有交点
                if (d > r1 + r2 || d < Math.Abs(r1 - r2) || d == 0)
                {
                    return intersections;
                }

                var a = (r1 * r1 - r2 * r2 + d * d) / (2 * d);
                var h = Math.Sqrt(r1 * r1 - a * a);

                var p = circle1.Center + (float)(a / d) * (circle2.Center - circle1.Center);
                var offset = (float)(h / d) * new Vector2(
                    -(circle2.Center.Y - circle1.Center.Y),
                    circle2.Center.X - circle1.Center.X);

                intersections.Add(p + offset);
                if (h > 0.001) // 避免重复点
                {
                    intersections.Add(p - offset);
                }
            }
            catch
            {
                // 忽略计算错误
            }

            return intersections;
        }

        /// <summary>
        /// 创建简单的圆角弧
        /// </summary>
        private EntityArc CreateSimpleFilletArc(Vector2 center, float radius)
        {
            return new EntityArc
            {
                Center = center,
                Radius = radius,
                StartAngle = 0,
                SweepAngle = 90 // 简化为90度弧
            };
        }

        #endregion
    }

    #region 枚举和数据结构
    
    public enum FilletState
    {
        SetRadius,
        SelectFirstEntity,
        SelectSecondEntity
    }
    
    public enum ChamferState
    {
        SetDistances,
        SelectFirstEntity,
        SelectSecondEntity
    }
    
    public class FilletOptions
    {
        public bool TrimMode { get; set; } = true;
        public bool Multiple { get; set; } = false;
        public bool Polyline { get; set; } = false;
    }
    
    public class ChamferOptions
    {
        public bool TrimMode { get; set; } = true;
        public bool AngleMode { get; set; } = false;
        public bool Polyline { get; set; } = false;
    }
    
    public class FilletResult
    {
        public bool IsValid { get; set; } = false;
        public bool IsSuccess { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<EntityBase> ResultEntities { get; set; } = new List<EntityBase>();
        public List<EntityBase> EntitiesToRemove { get; set; } = new List<EntityBase>();

        // 圆角弧
        public EntityArc FilletArc { get; set; }

        // 连接线（用于某些特殊情况）
        public EntityLine ConnectingLine { get; set; }

        // 修剪信息
        public Vector2 TrimLine1Start { get; set; }
        public Vector2 TrimLine1End { get; set; }
        public Vector2 TrimArc1Start { get; set; }
        public Vector2 TrimArc1End { get; set; }
        public Vector2 TrimArc2Start { get; set; }
        public Vector2 TrimArc2End { get; set; }
    }
    
    public class ChamferResult
    {
        public bool IsValid { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<EntityBase> ResultEntities { get; set; } = new List<EntityBase>();
    }
    
    public class LineLineFilletInfo
    {
        public Vector2 Center { get; set; }
        public float Radius { get; set; }
        public Vector2 Tangent1 { get; set; }
        public Vector2 Tangent2 { get; set; }
        public float StartAngle { get; set; }
        public float EndAngle { get; set; }
        public EntityLine Line1 { get; set; }
        public EntityLine Line2 { get; set; }
        public Vector2 Click1 { get; set; }
        public Vector2 Click2 { get; set; }
    }
    
    public class LineLineChamferInfo
    {
        public Vector2 ChamferPoint1 { get; set; }
        public Vector2 ChamferPoint2 { get; set; }
        public EntityLine Line1 { get; set; }
        public EntityLine Line2 { get; set; }
        public Vector2 Click1 { get; set; }
        public Vector2 Click2 { get; set; }
    }
    
    #endregion
} 