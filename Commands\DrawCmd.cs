﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Commands
{
    public abstract class DrawCmd : Command
    {
        protected abstract IEnumerable<EntityBase> newEntities { get; }

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            if (_mgr?.Viewer?.Selections != null)
            {
                _mgr.Viewer.Selections.Clear();
            }
            this.pointer.bIsShowAnchor = false;
        }

        /// <summary>
        /// 结束
        /// </summary>
        public override void Terminate()
        {
            if (_mgr?.Viewer?.Selections != null)
            {
                _mgr.Viewer.Selections.Clear();
            }

            base.Terminate();
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            if (this.newEntities != null)
            {
                foreach (EntityBase entity in this.newEntities)
                {
                    if (entity != null)
                    {
                        doc.Action.ActEntityAdd(entity);
                    }
                }
            }
        }

        
    }
}
