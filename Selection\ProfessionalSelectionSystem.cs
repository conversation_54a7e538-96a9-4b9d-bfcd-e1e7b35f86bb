using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Enums;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace McLaser.EditViewerSk.Selection
{
    /// <summary>
    /// 专业选择系统
    /// 实现专业CAD软件级别的选择功能
    /// </summary>
    public class ProfessionalSelectionSystem
    {
        private ViewBase _viewer;
        private EntitySelectionMode _currentMode = EntitySelectionMode.Point;
        private List<EntityBase> _selectedEntities;
        private List<EntityBase> _highlightedEntities;
        private SelectionBox _activeSelectionBox;
        
        // 选择配置
        private SelectionSettings _settings;
        
        // 视觉样式
        private Dictionary<EntitySelectionMode, SelectionBoxStyle> _selectionStyles;
        private SKPaint _selectedEntityPaint;
        private SKPaint _highlightedEntityPaint;
        
        public EntitySelectionMode CurrentMode
        {
            get { return _currentMode; }
            set { _currentMode = value; }
        }
        
        public List<EntityBase> SelectedEntities
        {
            get { return _selectedEntities; }
        }
        
        public SelectionSettings Settings
        {
            get { return _settings; }
            set { _settings = value; }
        }
        
        public ProfessionalSelectionSystem(ViewBase viewer)
        {
            _viewer = viewer;
            _selectedEntities = new List<EntityBase>();
            _highlightedEntities = new List<EntityBase>();
            _settings = new SelectionSettings();
            
            InitializeStyles();
        }
        
        private void InitializeStyles()
        {
            _selectionStyles = new Dictionary<EntitySelectionMode, SelectionBoxStyle>
            {
                [EntitySelectionMode.Window] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Blue,
                    FillColor = SKColor.FromArgb(30, 0, 0, 255),
                    StrokeWidth = 1.0f,
                    IsDashed = false
                },
                [EntitySelectionMode.Cross] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Green,
                    FillColor = SKColor.FromArgb(30, 0, 255, 0),
                    StrokeWidth = 1.0f,
                    IsDashed = true,
                    DashPattern = new float[] { 5, 5 }
                },
                [EntitySelectionMode.PolygonWindow] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Purple,
                    FillColor = SKColor.FromArgb(30, 128, 0, 128),
                    StrokeWidth = 1.0f,
                    IsDashed = false
                },
                [EntitySelectionMode.PolygonCross] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Orange,
                    FillColor = SKColor.FromArgb(30, 255, 165, 0),
                    StrokeWidth = 1.0f,
                    IsDashed = true,
                    DashPattern = new float[] { 3, 3 }
                }
            };
            
            _selectedEntityPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _highlightedEntityPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 3.0f,
                Color = SKColors.Cyan,
                IsAntialias = true
            };
        }
        
        /// <summary>
        /// 开始选择操作
        /// </summary>
        public void StartSelection(Vector2 startPoint, EntitySelectionMode mode = EntitySelectionMode.Point)
        {
            _currentMode = mode;
            
            switch (mode)
            {
                case EntitySelectionMode.Point:
                    PerformPointSelection(startPoint);
                    break;
                    
                case EntitySelectionMode.Window:
                case EntitySelectionMode.Cross:
                    StartBoxSelection(startPoint, mode);
                    break;
                    
                case EntitySelectionMode.All:
                    SelectAllEntities();
                    break;
                    
                case EntitySelectionMode.Invert:
                    InvertSelection();
                    break;
            }
        }
        
        /// <summary>
        /// 更新选择操作（用于拖拽选择）
        /// </summary>
        public void UpdateSelection(Vector2 currentPoint)
        {
            if (_activeSelectionBox == null) return;
            
            _activeSelectionBox.EndPoint = currentPoint;
            
            // 根据拖拽方向自动判断选择模式
            var dragDirection = currentPoint.X - _activeSelectionBox.StartPoint.X;
            if (dragDirection >= 0)
            {
                _activeSelectionBox.Mode = EntitySelectionMode.Window;
            }
            else
            {
                _activeSelectionBox.Mode = EntitySelectionMode.Cross;
            }
            
            // 预览选择结果
            PreviewBoxSelection(_activeSelectionBox);
        }
        
        /// <summary>
        /// 完成选择操作
        /// </summary>
        public void CompleteSelection()
        {
            if (_activeSelectionBox == null) return;
            
            PerformBoxSelection(_activeSelectionBox);
            _activeSelectionBox = null;
            ClearHighlight();
        }
        
        /// <summary>
        /// 取消选择操作
        /// </summary>
        public void CancelSelection()
        {
            _activeSelectionBox = null;
            ClearHighlight();
        }
        
        /// <summary>
        /// 点选
        /// </summary>
        private void PerformPointSelection(Vector2 point)
        {
            var hitEntity = HitTest(point);
            
            if (hitEntity != null)
            {
                if (_settings.MultiSelectEnabled && IsControlPressed())
                {
                    ToggleEntitySelection(hitEntity);
                }
                else if (_settings.MultiSelectEnabled && IsShiftPressed())
                {
                    AddEntityToSelection(hitEntity);
                }
                else
                {
                    SelectSingleEntity(hitEntity);
                }
            }
            else if (!_settings.PreserveSelectionOnMiss)
            {
                ClearSelection();
            }
        }
        
        /// <summary>
        /// 开始框选
        /// </summary>
        private void StartBoxSelection(Vector2 startPoint, EntitySelectionMode mode)
        {
            _activeSelectionBox = new SelectionBox
            {
                StartPoint = startPoint,
                EndPoint = startPoint,
                Mode = mode
            };
        }
        
        /// <summary>
        /// 执行框选
        /// </summary>
        private void PerformBoxSelection(SelectionBox selectionBox)
        {
            var selectedEntities = new List<EntityBase>();
            var selectionRect = GetSelectionRectangle(selectionBox);
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInSelection(entity, selectionRect, selectionBox.Mode))
                {
                    selectedEntities.Add(entity);
                }
            }
            
            if (_settings.MultiSelectEnabled && (IsControlPressed() || IsShiftPressed()))
            {
                foreach (var entity in selectedEntities)
                {
                    if (IsControlPressed())
                    {
                        ToggleEntitySelection(entity);
                    }
                    else
                    {
                        AddEntityToSelection(entity);
                    }
                }
            }
            else
            {
                SetSelectedEntities(selectedEntities);
            }
        }
        
        /// <summary>
        /// 预览框选结果
        /// </summary>
        private void PreviewBoxSelection(SelectionBox selectionBox)
        {
            _highlightedEntities.Clear();
            var selectionRect = GetSelectionRectangle(selectionBox);
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInSelection(entity, selectionRect, selectionBox.Mode))
                {
                    _highlightedEntities.Add(entity);
                }
            }
        }
        
        /// <summary>
        /// 判断实体是否在选择范围内
        /// </summary>
        private bool IsEntityInSelection(EntityBase entity, SKRect selectionRect, EntitySelectionMode mode)
        {
            var entityBounds = GetEntityBounds(entity);
            
            switch (mode)
            {
                case EntitySelectionMode.Window:
                    return selectionRect.Contains(entityBounds);
                    
                case EntitySelectionMode.Cross:
                    return selectionRect.IntersectsWith(entityBounds) || selectionRect.Contains(entityBounds);
                    
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 获取实体边界
        /// </summary>
        private SKRect GetEntityBounds(EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null || bounds.IsEmpty)
            {
                // 如果没有边界框，使用点边界
                return new SKRect(bounds.MinX - 1, bounds.MinY - 1, bounds.MaxX + 1, bounds.MaxY + 1);
            }
            
            return new SKRect(bounds.MinX, bounds.MinY, bounds.MaxX, bounds.MaxY);
        }
        
        /// <summary>
        /// 获取选择矩形
        /// </summary>
        private SKRect GetSelectionRectangle(SelectionBox selectionBox)
        {
            var left = Math.Min(selectionBox.StartPoint.X, selectionBox.EndPoint.X);
            var top = Math.Min(selectionBox.StartPoint.Y, selectionBox.EndPoint.Y);
            var right = Math.Max(selectionBox.StartPoint.X, selectionBox.EndPoint.X);
            var bottom = Math.Max(selectionBox.StartPoint.Y, selectionBox.EndPoint.Y);
            
            return new SKRect(left, top, right, bottom);
        }
        
        /// <summary>
        /// 命中测试
        /// </summary>
        private EntityBase HitTest(Vector2 point)
        {
            var tolerance = _settings.PickBoxSize;
            var testRect = new SKRect(
                point.X - tolerance,
                point.Y - tolerance,
                point.X + tolerance,
                point.Y + tolerance
            );
            
            // 按绘制顺序倒序查找（后绘制的在前）
            for (int i = _viewer.Document.ActiveLayer.Children.Count - 1; i >= 0; i--)
            {
                var entity = _viewer.Document.ActiveLayer.Children[i];
                if (IsEntityHit(entity, testRect))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsEntityHit(EntityBase entity, SKRect testRect)
        {
            var entityBounds = GetEntityBounds(entity);
            return testRect.IntersectsWith(entityBounds);
        }
        
        /// <summary>
        /// 选择单个实体
        /// </summary>
        public void SelectSingleEntity(EntityBase entity)
        {
            ClearSelection();
            AddEntityToSelection(entity);
        }
        
        /// <summary>
        /// 添加实体到选择集
        /// </summary>
        public void AddEntityToSelection(EntityBase entity)
        {
            if (entity != null && !_selectedEntities.Contains(entity))
            {
                _selectedEntities.Add(entity);
                entity.IsSelected = true;
                OnSelectionChanged?.Invoke(_selectedEntities);
            }
        }
        
        /// <summary>
        /// 从选择集移除实体
        /// </summary>
        public void RemoveEntityFromSelection(EntityBase entity)
        {
            if (_selectedEntities.Remove(entity))
            {
                entity.IsSelected = false;
                OnSelectionChanged?.Invoke(_selectedEntities);
            }
        }
        
        /// <summary>
        /// 切换实体选择状态
        /// </summary>
        public void ToggleEntitySelection(EntityBase entity)
        {
            if (_selectedEntities.Contains(entity))
            {
                RemoveEntityFromSelection(entity);
            }
            else
            {
                AddEntityToSelection(entity);
            }
        }
        
        /// <summary>
        /// 设置选择的实体列表
        /// </summary>
        public void SetSelectedEntities(List<EntityBase> entities)
        {
            ClearSelection();
            foreach (var entity in entities)
            {
                AddEntityToSelection(entity);
            }
        }
        
        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            foreach (var entity in _selectedEntities)
            {
                entity.IsSelected = false;
            }
            
            _selectedEntities.Clear();
            OnSelectionChanged?.Invoke(_selectedEntities);
        }
        
        /// <summary>
        /// 清除高亮
        /// </summary>
        public void ClearHighlight()
        {
            _highlightedEntities.Clear();
        }
        
        /// <summary>
        /// 全选
        /// </summary>
        public void SelectAllEntities()
        {
            var allEntities = _viewer.Document.ActiveLayer.Children.ToList();
            SetSelectedEntities(allEntities);
        }
        
        /// <summary>
        /// 反选
        /// </summary>
        public void InvertSelection()
        {
            var allEntities = _viewer.Document.ActiveLayer.Children.ToList();
            var newSelection = allEntities.Except(_selectedEntities).ToList();
            SetSelectedEntities(newSelection);
        }
        
        /// <summary>
        /// 按类型选择
        /// </summary>
        public void SelectByType(Type entityType)
        {
            var entitiesOfType = _viewer.Document.ActiveLayer.Children
                .Where(e => e.GetType() == entityType)
                .ToList();
            
            if (_settings.MultiSelectEnabled && (IsControlPressed() || IsShiftPressed()))
            {
                foreach (var entity in entitiesOfType)
                {
                    AddEntityToSelection(entity);
                }
            }
            else
            {
                SetSelectedEntities(entitiesOfType);
            }
        }
        
        /// <summary>
        /// 按图层选择
        /// </summary>
        public void SelectByLayer(EntityLayer layer)
        {
            var entitiesInLayer = _viewer.Document.ActiveLayer.Children
                .Where(e => e.Parent == layer)
                .ToList();
            
            SetSelectedEntities(entitiesInLayer);
        }
        
        /// <summary>
        /// 渲染选择系统
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            // 渲染选择框
            if (_activeSelectionBox != null)
            {
                RenderSelectionBox(canvas, _activeSelectionBox);
            }
            
            // 渲染选中实体的高亮
            RenderSelectedEntities(canvas);
            
            // 渲染预览高亮
            RenderHighlightedEntities(canvas);
        }
        
        private void RenderSelectionBox(SKCanvas canvas, SelectionBox selectionBox)
        {
            var style = _selectionStyles[selectionBox.Mode];
            var rect = GetSelectionRectangle(selectionBox);
            
            // 绘制填充
            var fillPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = style.FillColor,
                IsAntialias = true
            };
            canvas.DrawRect(rect, fillPaint);
            fillPaint.Dispose();
            
            // 绘制边框
            var strokePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = style.StrokeWidth,
                Color = style.StrokeColor,
                IsAntialias = true
            };
            
            if (style.IsDashed)
            {
                strokePaint.PathEffect = SKPathEffect.CreateDash(style.DashPattern, 0);
            }
            
            canvas.DrawRect(rect, strokePaint);
            strokePaint.Dispose();
        }
        
        private void RenderSelectedEntities(SKCanvas canvas)
        {
            foreach (var entity in _selectedEntities)
            {
                RenderEntityHighlight(canvas, entity, _selectedEntityPaint);
            }
        }
        
        private void RenderHighlightedEntities(SKCanvas canvas)
        {
            foreach (var entity in _highlightedEntities)
            {
                if (!_selectedEntities.Contains(entity))
                {
                    RenderEntityHighlight(canvas, entity, _highlightedEntityPaint);
                }
            }
        }
        
        private void RenderEntityHighlight(SKCanvas canvas, EntityBase entity, SKPaint paint)
        {
            var bounds = GetEntityBounds(entity);
            var canvasBounds = new SKRect(
                _viewer.ModelToCanvas(new Vector2(bounds.Left, bounds.Top)).X,
                _viewer.ModelToCanvas(new Vector2(bounds.Left, bounds.Top)).Y,
                _viewer.ModelToCanvas(new Vector2(bounds.Right, bounds.Bottom)).X,
                _viewer.ModelToCanvas(new Vector2(bounds.Right, bounds.Bottom)).Y
            );
            
            canvas.DrawRect(canvasBounds, paint);
        }
        
        private bool IsControlPressed()
        {
            return (Control.ModifierKeys & Keys.Control) == Keys.Control;
        }
        
        private bool IsShiftPressed()
        {
            return (Control.ModifierKeys & Keys.Shift) == Keys.Shift;
        }
        
        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event Action<List<EntityBase>> OnSelectionChanged;
        
        public void Dispose()
        {
            _selectedEntityPaint?.Dispose();
            _highlightedEntityPaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 选择框
    /// </summary>
    public class SelectionBox
    {
        public Vector2 StartPoint { get; set; }
        public Vector2 EndPoint { get; set; }
        public EntitySelectionMode Mode { get; set; }
    }
    
    /// <summary>
    /// 选择框样式
    /// </summary>
    public class SelectionBoxStyle
    {
        public SKColor StrokeColor { get; set; }
        public SKColor FillColor { get; set; }
        public float StrokeWidth { get; set; }
        public bool IsDashed { get; set; }
        public float[] DashPattern { get; set; }
    }
    
    /// <summary>
    /// 选择设置
    /// </summary>
    public class SelectionSettings
    {
        public bool MultiSelectEnabled { get; set; } = true;
        public bool PreserveSelectionOnMiss { get; set; } = false;
        public float PickBoxSize { get; set; } = 3.0f;
        public bool ShowSelectionPreview { get; set; } = true;
        public bool SnapToSelectionPoints { get; set; } = true;
    }
} 