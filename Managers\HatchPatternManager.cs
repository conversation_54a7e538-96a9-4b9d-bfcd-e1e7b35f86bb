using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 填充图案管理器
    /// 负责管理填充图案的定义、创建和应用
    /// </summary>
    public class HatchPatternManager : ObservableObject
    {
        #region 私有字段

        private ObservableCollection<HatchPatternInfo> _predefinedPatterns;
        private ObservableCollection<HatchPatternInfo> _customPatterns;
        private HatchPatternInfo _currentPattern;
        private DocumentBase _document;
        private readonly Dictionary<string, HatchPattern> _patternCache;
        private readonly string _patternsPath;

        #endregion

        #region 事件

        /// <summary>
        /// 填充图案添加事件
        /// </summary>
        public event EventHandler<HatchPatternEventArgs> PatternAdded;

        /// <summary>
        /// 填充图案删除事件
        /// </summary>
        public event EventHandler<HatchPatternEventArgs> PatternRemoved;

        /// <summary>
        /// 当前填充图案改变事件
        /// </summary>
        public event EventHandler<HatchPatternEventArgs> CurrentPatternChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化填充图案管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public HatchPatternManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _predefinedPatterns = new ObservableCollection<HatchPatternInfo>();
            _customPatterns = new ObservableCollection<HatchPatternInfo>();
            _patternCache = new Dictionary<string, HatchPattern>();
            _patternsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Patterns");

            InitializePredefinedPatterns();
            LoadCustomPatterns();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 预定义图案集合
        /// </summary>
        public ObservableCollection<HatchPatternInfo> PredefinedPatterns
        {
            get => _predefinedPatterns;
            private set => SetProperty(ref _predefinedPatterns, value);
        }

        /// <summary>
        /// 自定义图案集合
        /// </summary>
        public ObservableCollection<HatchPatternInfo> CustomPatterns
        {
            get => _customPatterns;
            private set => SetProperty(ref _customPatterns, value);
        }

        /// <summary>
        /// 当前填充图案
        /// </summary>
        public HatchPatternInfo CurrentPattern
        {
            get => _currentPattern;
            set
            {
                if (SetProperty(ref _currentPattern, value))
                {
                    OnCurrentPatternChanged(value);
                }
            }
        }

        /// <summary>
        /// 所有图案（预定义 + 自定义）
        /// </summary>
        public IEnumerable<HatchPatternInfo> AllPatterns => _predefinedPatterns.Concat(_customPatterns);

        #endregion

        #region 填充图案操作方法

        /// <summary>
        /// 创建自定义填充图案
        /// </summary>
        /// <param name="name">图案名称</param>
        /// <param name="description">图案描述</param>
        /// <param name="patternType">图案类型</param>
        /// <param name="lines">图案线条</param>
        /// <returns>创建的填充图案</returns>
        public HatchPatternInfo CreateCustomPattern(string name, string description, 
            HatchPatternType patternType, IEnumerable<HatchPatternLine> lines)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("图案名称不能为空", nameof(name));

            if (PatternExists(name))
                throw new InvalidOperationException($"填充图案 '{name}' 已存在");

            var pattern = new HatchPattern
            {
                Name = name,
                Description = description,
                Type = patternType,
                Lines = lines?.ToList() ?? new List<HatchPatternLine>(),
                IsCustom = true
            };

            var patternInfo = new HatchPatternInfo
            {
                Name = name,
                Description = description,
                Type = patternType,
                Pattern = pattern,
                IsCustom = true,
                IsEditable = true
            };

            _customPatterns.Add(patternInfo);
            _patternCache[name] = pattern;

            OnPatternAdded(patternInfo);
            return patternInfo;
        }

        /// <summary>
        /// 创建简单几何图案
        /// </summary>
        /// <param name="name">图案名称</param>
        /// <param name="description">描述</param>
        /// <param name="angle">角度</param>
        /// <param name="spacing">间距</param>
        /// <param name="secondAngle">第二角度（用于交叉图案）</param>
        /// <param name="secondSpacing">第二间距</param>
        /// <returns>创建的填充图案</returns>
        public HatchPatternInfo CreateSimplePattern(string name, string description,
            float angle, float spacing, float? secondAngle = null, float? secondSpacing = null)
        {
            var lines = new List<HatchPatternLine>
            {
                new HatchPatternLine
                {
                    Angle = angle,
                    BasePoint = Vector2.Zero,
                    Offset = new Vector2(0, spacing),
                    DashLengths = new float[0] // 实线
                }
            };

            // 如果提供了第二角度，创建交叉图案
            if (secondAngle.HasValue)
            {
                lines.Add(new HatchPatternLine
                {
                    Angle = secondAngle.Value,
                    BasePoint = Vector2.Zero,
                    Offset = new Vector2(0, secondSpacing ?? spacing),
                    DashLengths = new float[0] // 实线
                });
            }

            return CreateCustomPattern(name, description, HatchPatternType.UserDefined, lines);
        }

        /// <summary>
        /// 创建点状图案
        /// </summary>
        /// <param name="name">图案名称</param>
        /// <param name="description">描述</param>
        /// <param name="dotSpacing">点间距</param>
        /// <param name="dotSize">点大小</param>
        /// <returns>创建的填充图案</returns>
        public HatchPatternInfo CreateDotPattern(string name, string description, 
            float dotSpacing, float dotSize = 1.0f)
        {
            var lines = new List<HatchPatternLine>
            {
                new HatchPatternLine
                {
                    Angle = 0,
                    BasePoint = Vector2.Zero,
                    Offset = new Vector2(dotSpacing, dotSpacing),
                    DashLengths = new float[] { dotSize, dotSpacing - dotSize }
                }
            };

            return CreateCustomPattern(name, description, HatchPatternType.UserDefined, lines);
        }

        /// <summary>
        /// 删除自定义图案
        /// </summary>
        /// <param name="pattern">要删除的图案</param>
        /// <returns>是否删除成功</returns>
        public bool DeletePattern(HatchPatternInfo pattern)
        {
            if (pattern == null || !pattern.IsCustom)
                return false;

            _customPatterns.Remove(pattern);
            _patternCache.Remove(pattern.Name);

            OnPatternRemoved(pattern);
            return true;
        }

        /// <summary>
        /// 修改自定义图案
        /// </summary>
        /// <param name="pattern">要修改的图案</param>
        /// <param name="newDescription">新描述</param>
        /// <param name="newLines">新的图案线条</param>
        /// <returns>是否修改成功</returns>
        public bool ModifyPattern(HatchPatternInfo pattern, string newDescription = null, 
            IEnumerable<HatchPatternLine> newLines = null)
        {
            if (pattern == null || !pattern.IsEditable)
                return false;

            if (!string.IsNullOrWhiteSpace(newDescription))
            {
                pattern.Description = newDescription;
                pattern.Pattern.Description = newDescription;
            }

            if (newLines != null)
            {
                pattern.Pattern.Lines = newLines.ToList();
            }

            return true;
        }

        /// <summary>
        /// 从文件加载图案
        /// </summary>
        /// <param name="filePath">图案文件路径</param>
        /// <returns>加载的图案数量</returns>
        public int LoadPatternsFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"图案文件不存在: {filePath}");

            var loadedCount = 0;
            try
            {
                var lines = File.ReadAllLines(filePath);
                var currentPattern = new List<string>();
                
                foreach (var line in lines)
                {
                    if (line.StartsWith("*")) // 图案开始标记
                    {
                        if (currentPattern.Count > 0)
                        {
                            if (TryParsePattern(currentPattern, out var pattern))
                            {
                                if (!PatternExists(pattern.Name))
                                {
                                    var patternInfo = new HatchPatternInfo
                                    {
                                        Name = pattern.Name,
                                        Description = pattern.Description,
                                        Type = pattern.Type,
                                        Pattern = pattern,
                                        IsCustom = true,
                                        IsEditable = true
                                    };

                                    _customPatterns.Add(patternInfo);
                                    _patternCache[pattern.Name] = pattern;
                                    loadedCount++;
                                }
                            }
                            currentPattern.Clear();
                        }
                        currentPattern.Add(line);
                    }
                    else if (!string.IsNullOrWhiteSpace(line))
                    {
                        currentPattern.Add(line);
                    }
                }

                // 处理最后一个图案
                if (currentPattern.Count > 0)
                {
                    if (TryParsePattern(currentPattern, out var pattern))
                    {
                        if (!PatternExists(pattern.Name))
                        {
                            var patternInfo = new HatchPatternInfo
                            {
                                Name = pattern.Name,
                                Description = pattern.Description,
                                Type = pattern.Type,
                                Pattern = pattern,
                                IsCustom = true,
                                IsEditable = true
                            };

                            _customPatterns.Add(patternInfo);
                            _patternCache[pattern.Name] = pattern;
                            loadedCount++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载图案文件失败: {ex.Message}", ex);
            }

            return loadedCount;
        }

        /// <summary>
        /// 保存图案到文件
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <param name="includeBuiltIn">是否包含内置图案</param>
        public void SavePatternsToFile(string filePath, bool includeBuiltIn = false)
        {
            var patternsToSave = includeBuiltIn ? AllPatterns : _customPatterns;
            
            var lines = new List<string>();
            foreach (var patternInfo in patternsToSave)
            {
                lines.AddRange(FormatPatternDefinition(patternInfo.Pattern));
                lines.Add(""); // 空行分隔
            }

            File.WriteAllLines(filePath, lines);
        }

        /// <summary>
        /// 应用填充图案到实体
        /// </summary>
        /// <param name="entity">目标实体</param>
        /// <param name="pattern">填充图案</param>
        /// <param name="scale">缩放比例</param>
        /// <param name="angle">旋转角度</param>
        /// <param name="color">填充颜色</param>
        public void ApplyHatchToEntity(EntityBase entity, HatchPatternInfo pattern, 
            float scale = 1.0f, float angle = 0.0f, SKColor? color = null)
        {
            if (entity == null || pattern == null) return;

            // 创建填充实体
            var hatchEntity = CreateHatchEntity(entity, pattern, scale, angle, color);
            
            // 将填充实体添加到父容器
            if (entity.Parent != null)
            {
                entity.Parent.Add(hatchEntity);
            }
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取填充图案
        /// </summary>
        /// <param name="name">图案名称</param>
        /// <returns>填充图案信息</returns>
        public HatchPatternInfo GetPattern(string name)
        {
            return AllPatterns.FirstOrDefault(p => 
                string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查图案是否存在
        /// </summary>
        /// <param name="name">图案名称</param>
        /// <returns>是否存在</returns>
        public bool PatternExists(string name)
        {
            return AllPatterns.Any(p => 
                string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 按类型获取图案
        /// </summary>
        /// <param name="patternType">图案类型</param>
        /// <returns>指定类型的图案</returns>
        public IEnumerable<HatchPatternInfo> GetPatternsByType(HatchPatternType patternType)
        {
            return AllPatterns.Where(p => p.Type == patternType);
        }

        /// <summary>
        /// 搜索填充图案
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <returns>匹配的图案</returns>
        public IEnumerable<HatchPatternInfo> SearchPatterns(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return AllPatterns;

            return AllPatterns.Where(p => 
                p.Name.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                p.Description.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化预定义图案
        /// </summary>
        private void InitializePredefinedPatterns()
        {
            // 实心填充
            var solid = new HatchPatternInfo
            {
                Name = "SOLID",
                Description = "实心填充",
                Type = HatchPatternType.Predefined,
                Pattern = new HatchPattern { Name = "SOLID", Type = HatchPatternType.Predefined },
                IsCustom = false,
                IsEditable = false
            };
            _predefinedPatterns.Add(solid);
            _patternCache["SOLID"] = solid.Pattern;

            // 角度线填充
            var angle = new HatchPatternInfo
            {
                Name = "ANGLE",
                Description = "角度线填充",
                Type = HatchPatternType.Predefined,
                Pattern = CreateAnglePattern(),
                IsCustom = false,
                IsEditable = false
            };
            _predefinedPatterns.Add(angle);
            _patternCache["ANGLE"] = angle.Pattern;

            // 砖块图案
            var brick = new HatchPatternInfo
            {
                Name = "BRICK",
                Description = "砖块图案",
                Type = HatchPatternType.Predefined,
                Pattern = CreateBrickPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _predefinedPatterns.Add(brick);
            _patternCache["BRICK"] = brick.Pattern;

            // 交叉线填充
            var cross = new HatchPatternInfo
            {
                Name = "CROSS",
                Description = "交叉线填充",
                Type = HatchPatternType.Predefined,
                Pattern = CreateCrossPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _predefinedPatterns.Add(cross);
            _patternCache["CROSS"] = cross.Pattern;

            // 点状填充
            var dots = new HatchPatternInfo
            {
                Name = "DOTS",
                Description = "点状填充",
                Type = HatchPatternType.Predefined,
                Pattern = CreateDotsPattern(),
                IsCustom = false,
                IsEditable = false
            };
            _predefinedPatterns.Add(dots);
            _patternCache["DOTS"] = dots.Pattern;

            // 设置默认图案
            CurrentPattern = solid;
        }

        /// <summary>
        /// 加载自定义图案
        /// </summary>
        private void LoadCustomPatterns()
        {
            if (!Directory.Exists(_patternsPath))
            {
                Directory.CreateDirectory(_patternsPath);
                return;
            }

            try
            {
                var patternFiles = Directory.GetFiles(_patternsPath, "*.pat");
                foreach (var file in patternFiles)
                {
                    try
                    {
                        LoadPatternsFromFile(file);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"加载图案文件 {file} 失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载自定义图案失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建角度线图案
        /// </summary>
        private HatchPattern CreateAnglePattern()
        {
            return new HatchPattern
            {
                Name = "ANGLE",
                Description = "角度线填充",
                Type = HatchPatternType.Predefined,
                Lines = new List<HatchPatternLine>
                {
                    new HatchPatternLine
                    {
                        Angle = 45.0f,
                        BasePoint = Vector2.Zero,
                        Offset = new Vector2(0, 0.125f),
                        DashLengths = new float[0]
                    }
                }
            };
        }

        /// <summary>
        /// 创建砖块图案
        /// </summary>
        private HatchPattern CreateBrickPattern()
        {
            return new HatchPattern
            {
                Name = "BRICK",
                Description = "砖块图案",
                Type = HatchPatternType.Predefined,
                Lines = new List<HatchPatternLine>
                {
                    new HatchPatternLine
                    {
                        Angle = 0.0f,
                        BasePoint = Vector2.Zero,
                        Offset = new Vector2(0, 0.25f),
                        DashLengths = new float[0]
                    },
                    new HatchPatternLine
                    {
                        Angle = 90.0f,
                        BasePoint = new Vector2(0, 0.125f),
                        Offset = new Vector2(0.5f, 0),
                        DashLengths = new float[] { 0.25f, 0.25f }
                    }
                }
            };
        }

        /// <summary>
        /// 创建交叉线图案
        /// </summary>
        private HatchPattern CreateCrossPattern()
        {
            return new HatchPattern
            {
                Name = "CROSS",
                Description = "交叉线填充",
                Type = HatchPatternType.Predefined,
                Lines = new List<HatchPatternLine>
                {
                    new HatchPatternLine
                    {
                        Angle = 0.0f,
                        BasePoint = Vector2.Zero,
                        Offset = new Vector2(0, 0.125f),
                        DashLengths = new float[0]
                    },
                    new HatchPatternLine
                    {
                        Angle = 90.0f,
                        BasePoint = Vector2.Zero,
                        Offset = new Vector2(0.125f, 0),
                        DashLengths = new float[0]
                    }
                }
            };
        }

        /// <summary>
        /// 创建点状图案
        /// </summary>
        private HatchPattern CreateDotsPattern()
        {
            return new HatchPattern
            {
                Name = "DOTS",
                Description = "点状填充",
                Type = HatchPatternType.Predefined,
                Lines = new List<HatchPatternLine>
                {
                    new HatchPatternLine
                    {
                        Angle = 0.0f,
                        BasePoint = Vector2.Zero,
                        Offset = new Vector2(0.0625f, 0.0625f),
                        DashLengths = new float[] { 0, 0.0625f }
                    }
                }
            };
        }

        /// <summary>
        /// 解析图案定义
        /// </summary>
        private bool TryParsePattern(List<string> lines, out HatchPattern pattern)
        {
            pattern = null;
            if (lines.Count == 0) return false;

            try
            {
                // 解析图案头部
                var headerLine = lines[0];
                if (!headerLine.StartsWith("*")) return false;

                var parts = headerLine.Substring(1).Split(',');
                if (parts.Length < 1) return false;

                pattern = new HatchPattern
                {
                    Name = parts[0].Trim(),
                    Description = parts.Length > 1 ? parts[1].Trim() : "",
                    Type = HatchPatternType.UserDefined,
                    Lines = new List<HatchPatternLine>(),
                    IsCustom = true
                };

                // 解析图案线条
                for (int i = 1; i < lines.Count; i++)
                {
                    if (TryParsePatternLine(lines[i], out var patternLine))
                    {
                        pattern.Lines.Add(patternLine);
                    }
                }

                return pattern.Lines.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 解析图案线条
        /// </summary>
        private bool TryParsePatternLine(string line, out HatchPatternLine patternLine)
        {
            patternLine = null;
            try
            {
                var parts = line.Split(',');
                if (parts.Length < 4) return false;

                patternLine = new HatchPatternLine
                {
                    Angle = float.Parse(parts[0].Trim()),
                    BasePoint = new Vector2(float.Parse(parts[1].Trim()), float.Parse(parts[2].Trim())),
                    Offset = new Vector2(float.Parse(parts[3].Trim()), 
                                       parts.Length > 4 ? float.Parse(parts[4].Trim()) : 0),
                    DashLengths = parts.Length > 5 ? 
                        parts.Skip(5).Select(p => float.Parse(p.Trim())).ToArray() : 
                        new float[0]
                };

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 格式化图案定义
        /// </summary>
        private IEnumerable<string> FormatPatternDefinition(HatchPattern pattern)
        {
            var lines = new List<string>
            {
                $"*{pattern.Name}, {pattern.Description}"
            };

            foreach (var line in pattern.Lines)
            {
                var parts = new List<string>
                {
                    line.Angle.ToString("F6"),
                    line.BasePoint.X.ToString("F6"),
                    line.BasePoint.Y.ToString("F6"),
                    line.Offset.X.ToString("F6"),
                    line.Offset.Y.ToString("F6")
                };

                if (line.DashLengths != null && line.DashLengths.Length > 0)
                {
                    parts.AddRange(line.DashLengths.Select(d => d.ToString("F6")));
                }

                lines.Add(string.Join(",", parts));
            }

            return lines;
        }

        /// <summary>
        /// 创建填充实体
        /// </summary>
        private EntityBase CreateHatchEntity(EntityBase boundaryEntity, HatchPatternInfo pattern,
            float scale, float angle, SKColor? color)
        {
            // 这里需要根据具体的实体类型创建对应的填充实体
            // 简化实现，返回一个基础的填充实体
            return new EntityBase(); // 需要实现具体的填充实体类
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发图案添加事件
        /// </summary>
        protected virtual void OnPatternAdded(HatchPatternInfo pattern)
        {
            PatternAdded?.Invoke(this, new HatchPatternEventArgs(pattern));
        }

        /// <summary>
        /// 触发图案删除事件
        /// </summary>
        protected virtual void OnPatternRemoved(HatchPatternInfo pattern)
        {
            PatternRemoved?.Invoke(this, new HatchPatternEventArgs(pattern));
        }

        /// <summary>
        /// 触发当前图案改变事件
        /// </summary>
        protected virtual void OnCurrentPatternChanged(HatchPatternInfo pattern)
        {
            CurrentPatternChanged?.Invoke(this, new HatchPatternEventArgs(pattern));
        }

        #endregion
    }

    #region 辅助类和枚举

    /// <summary>
    /// 填充图案类型
    /// </summary>
    public enum HatchPatternType
    {
        /// <summary>预定义图案</summary>
        Predefined,
        /// <summary>用户定义图案</summary>
        UserDefined,
        /// <summary>自定义图案</summary>
        Custom
    }

    /// <summary>
    /// 填充图案信息
    /// </summary>
    public class HatchPatternInfo : ObservableObject
    {
        private string _name;
        private string _description;
        private HatchPatternType _type;
        private HatchPattern _pattern;
        private bool _isCustom;
        private bool _isEditable;

        /// <summary>图案名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>图案描述</summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>图案类型</summary>
        public HatchPatternType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>图案定义</summary>
        public HatchPattern Pattern
        {
            get => _pattern;
            set => SetProperty(ref _pattern, value);
        }

        /// <summary>是否为自定义图案</summary>
        public bool IsCustom
        {
            get => _isCustom;
            set => SetProperty(ref _isCustom, value);
        }

        /// <summary>是否可编辑</summary>
        public bool IsEditable
        {
            get => _isEditable;
            set => SetProperty(ref _isEditable, value);
        }
    }

    /// <summary>
    /// 填充图案定义
    /// </summary>
    public class HatchPattern
    {
        /// <summary>图案名称</summary>
        public string Name { get; set; }

        /// <summary>图案描述</summary>
        public string Description { get; set; }

        /// <summary>图案类型</summary>
        public HatchPatternType Type { get; set; }

        /// <summary>图案线条集合</summary>
        public List<HatchPatternLine> Lines { get; set; } = new List<HatchPatternLine>();

        /// <summary>是否为自定义图案</summary>
        public bool IsCustom { get; set; }
    }

    /// <summary>
    /// 填充图案线条
    /// </summary>
    public class HatchPatternLine
    {
        /// <summary>线条角度（度）</summary>
        public float Angle { get; set; }

        /// <summary>基点</summary>
        public Vector2 BasePoint { get; set; }

        /// <summary>偏移量</summary>
        public Vector2 Offset { get; set; }

        /// <summary>破折号长度数组</summary>
        public float[] DashLengths { get; set; } = new float[0];
    }

    /// <summary>
    /// 填充图案事件参数
    /// </summary>
    public class HatchPatternEventArgs : EventArgs
    {
        public HatchPatternInfo Pattern { get; }

        public HatchPatternEventArgs(HatchPatternInfo pattern)
        {
            Pattern = pattern;
        }
    }

    #endregion
} 