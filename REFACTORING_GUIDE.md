# CAD鼠标操作系统重构指南

## 重构概述

本次重构将原来的`MgrIndicator`类拆分为多个职责单一的组件，提高了代码的可维护性、可扩展性和可测试性。

## 新架构组件

### 1. 坐标转换服务 (`ICoordinateService`)
- **职责**：统一管理所有坐标系转换
- **位置**：`Interfaces/ICoordinateService.cs`, `Services/CoordinateService.cs`
- **优势**：消除坐标转换错误，提供一致的转换接口

### 2. 交互状态枚举 (`InteractionState` & `EntitySelectionMode`)
- **职责**：定义清晰的交互状态和选择模式
- **位置**：`Enums/InteractionState.cs`
- **优势**：简化状态管理，消除状态混乱，避免命名冲突

### 3. 选择框渲染器 (`SelectionRenderer`)
- **职责**：专门负责选择框的渲染
- **位置**：`Renderers/SelectionRenderer.cs`
- **优势**：分离渲染逻辑，支持不同选择模式的视觉反馈

### 4. 事件处理器系统 (`MouseInputHandler`)
- **职责**：使用职责链模式处理鼠标事件
- **位置**：`Handlers/MouseInputHandler.cs`, `Handlers/SelectionHandler.cs`
- **优势**：支持扩展，处理逻辑清晰

### 5. 输入管理器 (`InputManager`)
- **职责**：统一管理所有输入事件
- **位置**：`Managers/InputManager.cs`
- **优势**：替代`MgrIndicator`，架构更清晰

## 新旧系统对比

| 方面 | 旧系统 (MgrIndicator) | 新系统 (InputManager) |
|------|---------------------|---------------------|
| **职责** | 单一类承担多个职责 | 职责分离到专门的类 |
| **状态管理** | 两套重叠的状态系统 | 统一的InteractionState |
| **坐标处理** | 分散在各处，容易出错 | 统一的CoordinateService |
| **扩展性** | 难以添加新功能 | 通过注册处理器轻松扩展 |
| **测试** | 难以单独测试 | 各组件可独立测试 |
| **维护** | 代码耦合严重 | 松耦合，易于维护 |

## 使用指南

### 新系统的使用

```csharp
// 1. 创建坐标转换服务
var coordinateService = new CoordinateService(viewBase);

// 2. 创建渲染器
var selectionRenderer = new SelectionRenderer(coordinateService);

// 3. 创建处理器
var selectionHandler = new SelectionHandler(selectionRenderer);

// 4. 创建输入管理器
var inputManager = new InputManager(viewBase);

// 5. 注册自定义处理器
inputManager.RegisterHandler(new CustomHandler());
```

### 扩展新功能

```csharp
// 创建自定义处理器
public class PanHandler : MouseInputHandler
{
    public PanHandler()
    {
        Priority = 50; // 设置优先级
    }

    protected override bool CanHandle(MouseEventContext context)
    {
        return context.EventArgs.Button == MouseButtons.Middle;
    }

    protected override MouseEventResult DoHandle(MouseEventContext context)
    {
        // 实现平移逻辑
        return MouseEventResult.WithStateChange(InteractionState.Panning);
    }
}

// 注册到输入管理器
inputManager.RegisterHandler(new PanHandler());
```

## 迁移路径

### 阶段1：并行运行（当前状态）
- 新旧系统同时运行
- 新系统优先处理事件
- 保持完全向后兼容

### 阶段2：功能迁移
```csharp
// 逐步将功能从MgrIndicator迁移到专门的处理器
// 例如：捕捉功能
public class SnapHandler : MouseInputHandler
{
    // 将MgrSnap的逻辑迁移到这里
}
```

### 阶段3：完全替换
- 移除MgrIndicator的调用
- 完全使用新的InputManager
- 清理旧代码

## API兼容性

为确保平滑迁移，新系统提供了兼容接口：

```csharp
// 兼容原有的CurrentSnapPoint接口
public Vector2 CurrentSnapPoint => _inputManager.CurrentSnapPoint;
```

## 性能优化

新架构带来的性能改进：

1. **按需处理**：只有相关处理器才会处理事件
2. **优先级排序**：高优先级处理器优先处理
3. **早期退出**：处理器处理完成后立即返回
4. **内存优化**：减少不必要的对象创建

## 测试策略

### 单元测试示例

```csharp
[Test]
public void SelectionHandler_ShouldStartSelection_WhenLeftMouseDown()
{
    // Arrange
    var mockRenderer = new Mock<SelectionRenderer>();
    var handler = new SelectionHandler(mockRenderer.Object);
    var context = new MouseEventContext 
    { 
        EventType = MouseEventType.Down,
        EventArgs = new MouseEventArgs(MouseButtons.Left, 1, 0, 0, 0)
    };

    // Act
    var result = handler.Handle(context);

    // Assert
    Assert.IsTrue(result.Handled);
    Assert.AreEqual(InteractionState.Selecting, result.NewState);
}
```

## 故障排除

### 常见问题

1. **框选不工作**
   - 检查SelectionHandler是否正确注册
   - 确认坐标转换是否正确

2. **事件不响应**
   - 检查处理器的CanHandle方法
   - 确认优先级设置是否正确

3. **状态混乱**
   - 确保只使用InteractionState枚举
   - 检查状态转换逻辑

### 调试技巧

```csharp
// 在InputManager中添加日志
public MouseEventResult ProcessMouseEvent(MouseEventContext context)
{
    Console.WriteLine($"Processing {context.EventType} in state {context.CurrentState}");
    // ... 处理逻辑
}
```

## 最佳实践

1. **处理器设计**
   - 保持处理器职责单一
   - 使用合适的优先级
   - 在CanHandle中做快速检查

2. **状态管理**
   - 使用明确的状态转换
   - 避免状态混乱
   - 在状态变化时通知相关组件

3. **坐标处理**
   - 始终使用CoordinateService
   - 明确指定坐标系类型
   - 在转换前验证坐标有效性

4. **性能考虑**
   - 避免在事件处理中进行重计算
   - 使用缓存减少重复计算
   - 及时释放资源

## 未来扩展

新架构为以下功能提供了基础：

1. **手势识别**：通过事件处理链支持复杂手势
2. **多点触控**：扩展事件上下文支持触控
3. **插件系统**：通过注册机制支持插件
4. **自定义交互**：用户可以定义自己的交互模式
5. **宏录制**：事件处理链天然支持宏录制和回放

## 总结

本次重构大幅提升了代码质量和系统架构：

- ✅ **单一职责**：每个类只负责一个功能
- ✅ **松耦合**：组件间依赖最小化
- ✅ **高内聚**：相关功能集中在一起
- ✅ **可扩展**：易于添加新功能
- ✅ **可测试**：支持单元测试
- ✅ **向后兼容**：不影响现有功能

新架构为CAD软件的长期发展奠定了坚实的基础。 