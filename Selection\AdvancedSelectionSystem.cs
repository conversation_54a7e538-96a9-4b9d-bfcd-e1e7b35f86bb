using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Enums;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Selection
{
    /// <summary>
    /// 高级选择系统
    /// 实现专业CAD软件级别的多种选择模式和可视化反馈
    /// </summary>
    public class AdvancedSelectionSystem
    {
        private ViewBase _viewer;
        private EntitySelectionMode _currentMode = EntitySelectionMode.Point;
        private List<EntityBase> _selectedEntities;
        private List<EntityBase> _highlightedEntities;
        private SelectionBox _activeSelectionBox;
        private List<Vector2> _polygonPoints;
        private bool _isDragging = false;
        
        // 选择配置
        private SelectionSettings _settings;
        
        // 视觉样式
        private Dictionary<EntitySelectionMode, SelectionBoxStyle> _selectionStyles;
        private SKPaint _selectedEntityPaint;
        private SKPaint _highlightedEntityPaint;
        private SKPaint _polygonSelectionPaint;
        
        public EntitySelectionMode CurrentMode
        {
            get { return _currentMode; }
            set { _currentMode = value; }
        }
        
        public List<EntityBase> SelectedEntities
        {
            get { return _selectedEntities; }
        }
        
        public List<EntityBase> HighlightedEntities
        {
            get { return _highlightedEntities; }
        }
        
        public SelectionSettings Settings
        {
            get { return _settings; }
            set { _settings = value; }
        }
        
        public bool IsDragging
        {
            get { return _isDragging; }
        }
        
        public AdvancedSelectionSystem(ViewBase viewer)
        {
            _viewer = viewer ?? throw new ArgumentNullException(nameof(viewer));
            _selectedEntities = new List<EntityBase>();
            _highlightedEntities = new List<EntityBase>();
            _polygonPoints = new List<Vector2>();
            _settings = new SelectionSettings();
            
            InitializeStyles();
        }
        
        private void InitializeStyles()
        {
            _selectionStyles = new Dictionary<EntitySelectionMode, SelectionBoxStyle>
            {
                [EntitySelectionMode.Window] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Blue,
                    FillColor = SKColor.FromArgb(30, 0, 0, 255),
                    StrokeWidth = 1.0f,
                    IsDashed = false
                },
                [EntitySelectionMode.Cross] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Green,
                    FillColor = SKColor.FromArgb(30, 0, 255, 0),
                    StrokeWidth = 1.0f,
                    IsDashed = true,
                    DashPattern = new float[] { 5, 5 }
                },
                [EntitySelectionMode.PolygonWindow] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Purple,
                    FillColor = SKColor.FromArgb(30, 128, 0, 128),
                    StrokeWidth = 1.5f,
                    IsDashed = false
                },
                [EntitySelectionMode.PolygonCross] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Orange,
                    FillColor = SKColor.FromArgb(30, 255, 165, 0),
                    StrokeWidth = 1.5f,
                    IsDashed = true,
                    DashPattern = new float[] { 4, 4 }
                },
                [EntitySelectionMode.CircleWindow] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Cyan,
                    FillColor = SKColor.FromArgb(30, 0, 255, 255),
                    StrokeWidth = 1.5f,
                    IsDashed = false
                },
                [EntitySelectionMode.CircleCross] = new SelectionBoxStyle
                {
                    StrokeColor = SKColors.Yellow,
                    FillColor = SKColor.FromArgb(30, 255, 255, 0),
                    StrokeWidth = 1.5f,
                    IsDashed = true,
                    DashPattern = new float[] { 3, 3 }
                }
            };
            
            _selectedEntityPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                Color = SKColors.Yellow,
                IsAntialias = true
            };
            
            _highlightedEntityPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 3.0f,
                Color = SKColor.FromArgb(180, 0, 255, 255), // 半透明青色
                IsAntialias = true
            };
            
            _polygonSelectionPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColors.Magenta,
                PathEffect = SKPathEffect.CreateDash(new float[] { 6, 3 }, 0),
                IsAntialias = true
            };
        }
        
        /// <summary>
        /// 开始选择操作
        /// </summary>
        public SelectionResult StartSelection(Vector2 startPoint, EntitySelectionMode mode = EntitySelectionMode.Point)
        {
            _currentMode = mode;
            var result = new SelectionResult();
            
            switch (mode)
            {
                case EntitySelectionMode.Point:
                    result = PerformPointSelection(startPoint);
                    break;
                    
                case EntitySelectionMode.Window:
                case EntitySelectionMode.Cross:
                    StartBoxSelection(startPoint, mode);
                    result.IsCompleted = false;
                    break;
                    
                case EntitySelectionMode.PolygonWindow:
                case EntitySelectionMode.PolygonCross:
                    StartPolygonSelection(startPoint, mode);
                    result.IsCompleted = false;
                    break;
                    
                case EntitySelectionMode.CircleWindow:
                case EntitySelectionMode.CircleCross:
                    StartCircleSelection(startPoint, mode);
                    result.IsCompleted = false;
                    break;
                    
                case EntitySelectionMode.All:
                    result = SelectAllEntities();
                    break;
                    
                case EntitySelectionMode.Invert:
                    result = InvertSelection();
                    break;
                    
                case EntitySelectionMode.Fence:
                    StartFenceSelection(startPoint);
                    result.IsCompleted = false;
                    break;
            }
            
            return result;
        }
        
        /// <summary>
        /// 更新选择操作（用于拖拽选择）
        /// </summary>
        public SelectionResult UpdateSelection(Vector2 currentPoint)
        {
            _isDragging = true;
            var result = new SelectionResult { IsCompleted = false };
            
            if (_activeSelectionBox != null)
            {
                _activeSelectionBox.EndPoint = currentPoint;
                
                // 根据拖拽方向自动判断选择模式（仅适用于矩形选择）
                if (_currentMode == EntitySelectionMode.Window || _currentMode == EntitySelectionMode.Cross)
                {
                    var dragDirection = currentPoint.X - _activeSelectionBox.StartPoint.X;
                    _activeSelectionBox.Mode = dragDirection >= 0 ? EntitySelectionMode.Window : EntitySelectionMode.Cross;
                }
                
                // 预览选择结果
                PreviewBoxSelection(_activeSelectionBox);
            }
            else if (_polygonPoints.Count > 0)
            {
                // 更新多边形选择
                if (_polygonPoints.Count > 0)
                {
                    _polygonPoints[_polygonPoints.Count - 1] = currentPoint;
                }
                PreviewPolygonSelection();
            }
            
            return result;
        }
        
        /// <summary>
        /// 完成选择操作
        /// </summary>
        public SelectionResult CompleteSelection()
        {
            _isDragging = false;
            var result = new SelectionResult();
            
            if (_activeSelectionBox != null)
            {
                result = PerformBoxSelection(_activeSelectionBox);
                _activeSelectionBox = null;
            }
            else if (_polygonPoints.Count > 2)
            {
                result = PerformPolygonSelection();
                _polygonPoints.Clear();
            }
            
            ClearHighlight();
            return result;
        }
        
        /// <summary>
        /// 取消选择操作
        /// </summary>
        public void CancelSelection()
        {
            _isDragging = false;
            _activeSelectionBox = null;
            _polygonPoints.Clear();
            ClearHighlight();
        }
        
        /// <summary>
        /// 添加多边形选择点
        /// </summary>
        public void AddPolygonPoint(Vector2 point)
        {
            _polygonPoints.Add(point);
        }
        
        /// <summary>
        /// 点选
        /// </summary>
        private SelectionResult PerformPointSelection(Vector2 point)
        {
            var result = new SelectionResult { IsCompleted = true };
            var hitEntity = HitTest(point);
            
            if (hitEntity != null)
            {
                var isControlPressed = (Control.ModifierKeys & Keys.Control) == Keys.Control;
                var isShiftPressed = (Control.ModifierKeys & Keys.Shift) == Keys.Shift;
                
                if (_settings.MultiSelectEnabled && isControlPressed)
                {
                    ToggleEntitySelection(hitEntity);
                }
                else if (_settings.MultiSelectEnabled && isShiftPressed)
                {
                    AddEntityToSelection(hitEntity);
                }
                else
                {
                    SelectSingleEntity(hitEntity);
                }
                
                result.SelectedEntities.Add(hitEntity);
                result.SelectionCount = _selectedEntities.Count;
            }
            else if (!_settings.PreserveSelectionOnMiss)
            {
                ClearSelection();
            }
            
            return result;
        }
        
        /// <summary>
        /// 开始框选
        /// </summary>
        private void StartBoxSelection(Vector2 startPoint, EntitySelectionMode mode)
        {
            _activeSelectionBox = new SelectionBox
            {
                StartPoint = startPoint,
                EndPoint = startPoint,
                Mode = mode
            };
        }
        
        /// <summary>
        /// 开始多边形选择
        /// </summary>
        private void StartPolygonSelection(Vector2 startPoint, EntitySelectionMode mode)
        {
            _polygonPoints.Clear();
            _polygonPoints.Add(startPoint);
            _currentMode = mode;
        }
        
        /// <summary>
        /// 开始圆形选择
        /// </summary>
        private void StartCircleSelection(Vector2 startPoint, EntitySelectionMode mode)
        {
            _activeSelectionBox = new SelectionBox
            {
                StartPoint = startPoint,
                EndPoint = startPoint,
                Mode = mode
            };
        }
        
        /// <summary>
        /// 开始围栏选择
        /// </summary>
        private void StartFenceSelection(Vector2 startPoint)
        {
            _polygonPoints.Clear();
            _polygonPoints.Add(startPoint);
        }
        
        /// <summary>
        /// 执行框选
        /// </summary>
        private SelectionResult PerformBoxSelection(SelectionBox selectionBox)
        {
            var result = new SelectionResult { IsCompleted = true };
            var selectedEntities = new List<EntityBase>();
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInBoxSelection(entity, selectionBox))
                {
                    selectedEntities.Add(entity);
                }
            }
            
            result.SelectedEntities = selectedEntities;
            ApplySelectionResult(selectedEntities);
            
            return result;
        }
        
        /// <summary>
        /// 执行多边形选择
        /// </summary>
        private SelectionResult PerformPolygonSelection()
        {
            var result = new SelectionResult { IsCompleted = true };
            var selectedEntities = new List<EntityBase>();
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInPolygonSelection(entity))
                {
                    selectedEntities.Add(entity);
                }
            }
            
            result.SelectedEntities = selectedEntities;
            ApplySelectionResult(selectedEntities);
            
            return result;
        }
        
        /// <summary>
        /// 应用选择结果
        /// </summary>
        private void ApplySelectionResult(List<EntityBase> selectedEntities)
        {
            var isControlPressed = (Control.ModifierKeys & Keys.Control) == Keys.Control;
            var isShiftPressed = (Control.ModifierKeys & Keys.Shift) == Keys.Shift;
            
            if (_settings.MultiSelectEnabled && (isControlPressed || isShiftPressed))
            {
                foreach (var entity in selectedEntities)
                {
                    if (isControlPressed)
                    {
                        ToggleEntitySelection(entity);
                    }
                    else
                    {
                        AddEntityToSelection(entity);
                    }
                }
            }
            else
            {
                SetSelectedEntities(selectedEntities);
            }
        }
        
        /// <summary>
        /// 预览框选结果
        /// </summary>
        private void PreviewBoxSelection(SelectionBox selectionBox)
        {
            if (!_settings.ShowSelectionPreview) return;
            
            _highlightedEntities.Clear();
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInBoxSelection(entity, selectionBox))
                {
                    _highlightedEntities.Add(entity);
                }
            }
        }
        
        /// <summary>
        /// 预览多边形选择结果
        /// </summary>
        private void PreviewPolygonSelection()
        {
            if (!_settings.ShowSelectionPreview || _polygonPoints.Count < 3) return;
            
            _highlightedEntities.Clear();
            
            foreach (var entity in _viewer.Document.ActiveLayer.Children)
            {
                if (IsEntityInPolygonSelection(entity))
                {
                    _highlightedEntities.Add(entity);
                }
            }
        }
        
        /// <summary>
        /// 判断实体是否在框选范围内
        /// </summary>
        private bool IsEntityInBoxSelection(EntityBase entity, SelectionBox selectionBox)
        {
            var entityBounds = GetEntityBounds(entity);
            
            if (selectionBox.Mode == EntitySelectionMode.CircleWindow || selectionBox.Mode == EntitySelectionMode.CircleCross)
            {
                return IsEntityInCircleSelection(entity, selectionBox);
            }
            
            var selectionRect = GetSelectionRectangle(selectionBox);
            
            switch (selectionBox.Mode)
            {
                case EntitySelectionMode.Window:
                    return selectionRect.Contains(entityBounds);
                    
                case EntitySelectionMode.Cross:
                    return selectionRect.IntersectsWith(entityBounds) || selectionRect.Contains(entityBounds);
                    
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 判断实体是否在圆形选择范围内
        /// </summary>
        private bool IsEntityInCircleSelection(EntityBase entity, SelectionBox selectionBox)
        {
            var center = selectionBox.StartPoint;
            var radius = Vector2.Distance(selectionBox.StartPoint, selectionBox.EndPoint);
            var entityCenter = GetEntityCenter(entity);
            var distanceToCenter = Vector2.Distance(center, entityCenter);
            
            if (selectionBox.Mode == EntitySelectionMode.CircleWindow)
            {
                return distanceToCenter <= radius;
            }
            else // CircleCross
            {
                var entityBounds = GetEntityBounds(entity);
                // 检查圆形是否与实体边界相交或包含
                return distanceToCenter <= radius + Math.Max(entityBounds.Width, entityBounds.Height) / 2;
            }
        }
        
        /// <summary>
        /// 判断实体是否在多边形选择范围内
        /// </summary>
        private bool IsEntityInPolygonSelection(EntityBase entity)
        {
            if (_polygonPoints.Count < 3) return false;
            
            var entityCenter = GetEntityCenter(entity);
            
            if (_currentMode == EntitySelectionMode.PolygonWindow)
            {
                return IsPointInPolygon(entityCenter, _polygonPoints);
            }
            else if (_currentMode == EntitySelectionMode.PolygonCross)
            {
                // 检查实体是否与多边形相交或被包含
                return IsPointInPolygon(entityCenter, _polygonPoints) || EntityIntersectsPolygon(entity, _polygonPoints);
            }
            else if (_currentMode == EntitySelectionMode.Fence)
            {
                return EntityIntersectsFence(entity, _polygonPoints);
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查点是否在多边形内
        /// </summary>
        private bool IsPointInPolygon(Vector2 point, List<Vector2> polygon)
        {
            bool inside = false;
            int j = polygon.Count - 1;
            
            for (int i = 0; i < polygon.Count; i++)
            {
                if (((polygon[i].Y > point.Y) != (polygon[j].Y > point.Y)) &&
                    (point.X < (polygon[j].X - polygon[i].X) * (point.Y - polygon[i].Y) / (polygon[j].Y - polygon[i].Y) + polygon[i].X))
                {
                    inside = !inside;
                }
                j = i;
            }
            
            return inside;
        }
        
        /// <summary>
        /// 检查实体是否与多边形相交
        /// </summary>
        private bool EntityIntersectsPolygon(EntityBase entity, List<Vector2> polygon)
        {
            // 简化实现：检查实体边界框与多边形的相交
            var bounds = GetEntityBounds(entity);
            var corners = new Vector2[]
            {
                new Vector2(bounds.Left, bounds.Top),
                new Vector2(bounds.Right, bounds.Top),
                new Vector2(bounds.Right, bounds.Bottom),
                new Vector2(bounds.Left, bounds.Bottom)
            };
            
            // 检查任何一个角点是否在多边形内
            foreach (var corner in corners)
            {
                if (IsPointInPolygon(corner, polygon))
                    return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查实体是否与围栏相交
        /// </summary>
        private bool EntityIntersectsFence(EntityBase entity, List<Vector2> fencePoints)
        {
            var bounds = GetEntityBounds(entity);
            
            for (int i = 0; i < fencePoints.Count - 1; i++)
            {
                if (LineIntersectsRectangle(fencePoints[i], fencePoints[i + 1], bounds))
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查线段是否与矩形相交
        /// </summary>
        private bool LineIntersectsRectangle(Vector2 lineStart, Vector2 lineEnd, SKRect rect)
        {
            // 使用Cohen-Sutherland算法的简化版本
            return rect.Contains(lineStart.X, lineStart.Y) || 
                   rect.Contains(lineEnd.X, lineEnd.Y) ||
                   LineIntersectsLine(lineStart, lineEnd, new Vector2(rect.Left, rect.Top), new Vector2(rect.Right, rect.Top)) ||
                   LineIntersectsLine(lineStart, lineEnd, new Vector2(rect.Right, rect.Top), new Vector2(rect.Right, rect.Bottom)) ||
                   LineIntersectsLine(lineStart, lineEnd, new Vector2(rect.Right, rect.Bottom), new Vector2(rect.Left, rect.Bottom)) ||
                   LineIntersectsLine(lineStart, lineEnd, new Vector2(rect.Left, rect.Bottom), new Vector2(rect.Left, rect.Top));
        }
        
        /// <summary>
        /// 检查两条线段是否相交
        /// </summary>
        private bool LineIntersectsLine(Vector2 line1Start, Vector2 line1End, Vector2 line2Start, Vector2 line2End)
        {
            var d1 = CrossProduct(line2End - line2Start, line1Start - line2Start);
            var d2 = CrossProduct(line2End - line2Start, line1End - line2Start);
            var d3 = CrossProduct(line1End - line1Start, line2Start - line1Start);
            var d4 = CrossProduct(line1End - line1Start, line2End - line1Start);
            
            return ((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) && ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0));
        }
        
        private float CrossProduct(Vector2 a, Vector2 b)
        {
            return a.X * b.Y - a.Y * b.X;
        }
        
        /// <summary>
        /// 获取实体边界
        /// </summary>
        private SKRect GetEntityBounds(EntityBase entity)
        {
            var bounds = entity.BoundingBox;
            if (bounds == null || bounds.IsEmpty)
            {
                return new SKRect(0, 0, 1, 1);
            }
            
            return new SKRect(bounds.MinX, bounds.MinY, bounds.MaxX, bounds.MaxY);
        }
        
        /// <summary>
        /// 获取实体中心点
        /// </summary>
        private Vector2 GetEntityCenter(EntityBase entity)
        {
            var bounds = GetEntityBounds(entity);
            return new Vector2(bounds.MidX, bounds.MidY);
        }
        
        /// <summary>
        /// 获取选择矩形
        /// </summary>
        private SKRect GetSelectionRectangle(SelectionBox selectionBox)
        {
            var left = Math.Min(selectionBox.StartPoint.X, selectionBox.EndPoint.X);
            var top = Math.Min(selectionBox.StartPoint.Y, selectionBox.EndPoint.Y);
            var right = Math.Max(selectionBox.StartPoint.X, selectionBox.EndPoint.X);
            var bottom = Math.Max(selectionBox.StartPoint.Y, selectionBox.EndPoint.Y);
            
            return new SKRect(left, top, right, bottom);
        }
        
        /// <summary>
        /// 命中测试
        /// </summary>
        private EntityBase HitTest(Vector2 point)
        {
            var tolerance = _settings.PickBoxSize;
            var testRect = new SKRect(
                point.X - tolerance,
                point.Y - tolerance,
                point.X + tolerance,
                point.Y + tolerance
            );
            
            // 按绘制顺序倒序查找（后绘制的在前）
            for (int i = _viewer.Document.ActiveLayer.Children.Count - 1; i >= 0; i--)
            {
                var entity = _viewer.Document.ActiveLayer.Children[i];
                if (IsEntityHit(entity, testRect))
                {
                    return entity;
                }
            }
            
            return null;
        }
        
        private bool IsEntityHit(EntityBase entity, SKRect testRect)
        {
            var entityBounds = GetEntityBounds(entity);
            return testRect.IntersectsWith(entityBounds);
        }
        
        /// <summary>
        /// 选择单个实体
        /// </summary>
        public void SelectSingleEntity(EntityBase entity)
        {
            ClearSelection();
            AddEntityToSelection(entity);
        }
        
        /// <summary>
        /// 添加实体到选择集
        /// </summary>
        public void AddEntityToSelection(EntityBase entity)
        {
            if (entity != null && !_selectedEntities.Contains(entity))
            {
                _selectedEntities.Add(entity);
                entity.IsSelected = true;
                OnSelectionChanged?.Invoke(_selectedEntities);
            }
        }
        
        /// <summary>
        /// 从选择集移除实体
        /// </summary>
        public void RemoveEntityFromSelection(EntityBase entity)
        {
            if (_selectedEntities.Remove(entity))
            {
                entity.IsSelected = false;
                OnSelectionChanged?.Invoke(_selectedEntities);
            }
        }
        
        /// <summary>
        /// 切换实体选择状态
        /// </summary>
        public void ToggleEntitySelection(EntityBase entity)
        {
            if (_selectedEntities.Contains(entity))
            {
                RemoveEntityFromSelection(entity);
            }
            else
            {
                AddEntityToSelection(entity);
            }
        }
        
        /// <summary>
        /// 设置选择的实体列表
        /// </summary>
        public void SetSelectedEntities(List<EntityBase> entities)
        {
            ClearSelection();
            foreach (var entity in entities)
            {
                AddEntityToSelection(entity);
            }
        }
        
        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            foreach (var entity in _selectedEntities)
            {
                entity.IsSelected = false;
            }
            
            _selectedEntities.Clear();
            OnSelectionChanged?.Invoke(_selectedEntities);
        }
        
        /// <summary>
        /// 清除高亮
        /// </summary>
        public void ClearHighlight()
        {
            _highlightedEntities.Clear();
        }
        
        /// <summary>
        /// 全选
        /// </summary>
        public SelectionResult SelectAllEntities()
        {
            var allEntities = _viewer.Document.ActiveLayer.Children.ToList();
            SetSelectedEntities(allEntities);
            
            return new SelectionResult
            {
                IsCompleted = true,
                SelectedEntities = allEntities,
                SelectionCount = allEntities.Count
            };
        }
        
        /// <summary>
        /// 反选
        /// </summary>
        public SelectionResult InvertSelection()
        {
            var allEntities = _viewer.Document.ActiveLayer.Children.ToList();
            var newSelection = allEntities.Except(_selectedEntities).ToList();
            SetSelectedEntities(newSelection);
            
            return new SelectionResult
            {
                IsCompleted = true,
                SelectedEntities = newSelection,
                SelectionCount = newSelection.Count
            };
        }
        
        /// <summary>
        /// 按类型选择
        /// </summary>
        public SelectionResult SelectByType(Type entityType)
        {
            var entitiesOfType = _viewer.Document.ActiveLayer.Children
                .Where(e => e.GetType() == entityType)
                .ToList();
            
            var isControlPressed = (Control.ModifierKeys & Keys.Control) == Keys.Control;
            var isShiftPressed = (Control.ModifierKeys & Keys.Shift) == Keys.Shift;
            
            if (_settings.MultiSelectEnabled && (isControlPressed || isShiftPressed))
            {
                foreach (var entity in entitiesOfType)
                {
                    AddEntityToSelection(entity);
                }
            }
            else
            {
                SetSelectedEntities(entitiesOfType);
            }
            
            return new SelectionResult
            {
                IsCompleted = true,
                SelectedEntities = entitiesOfType,
                SelectionCount = entitiesOfType.Count
            };
        }
        
        /// <summary>
        /// 渲染选择系统
        /// </summary>
        public void Render(SKCanvas canvas)
        {
            // 渲染选择框
            if (_activeSelectionBox != null)
            {
                RenderSelectionBox(canvas, _activeSelectionBox);
            }
            
            // 渲染多边形选择
            if (_polygonPoints.Count > 1)
            {
                RenderPolygonSelection(canvas);
            }
            
            // 渲染选中实体的高亮
            RenderSelectedEntities(canvas);
            
            // 渲染预览高亮
            RenderHighlightedEntities(canvas);
        }
        
        private void RenderSelectionBox(SKCanvas canvas, SelectionBox selectionBox)
        {
            if (!_selectionStyles.TryGetValue(selectionBox.Mode, out var style))
                return;
            
            if (selectionBox.Mode == EntitySelectionMode.CircleWindow || selectionBox.Mode == EntitySelectionMode.CircleCross)
            {
                RenderCircleSelection(canvas, selectionBox, style);
            }
            else
            {
                RenderRectangleSelection(canvas, selectionBox, style);
            }
        }
        
        private void RenderRectangleSelection(SKCanvas canvas, SelectionBox selectionBox, SelectionBoxStyle style)
        {
            var rect = GetSelectionRectangle(selectionBox);
            
            // 绘制填充
            var fillPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = style.FillColor,
                IsAntialias = true
            };
            canvas.DrawRect(rect, fillPaint);
            fillPaint.Dispose();
            
            // 绘制边框
            var strokePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = style.StrokeWidth,
                Color = style.StrokeColor,
                IsAntialias = true
            };
            
            if (style.IsDashed)
            {
                strokePaint.PathEffect = SKPathEffect.CreateDash(style.DashPattern, 0);
            }
            
            canvas.DrawRect(rect, strokePaint);
            strokePaint.Dispose();
        }
        
        private void RenderCircleSelection(SKCanvas canvas, SelectionBox selectionBox, SelectionBoxStyle style)
        {
            var center = selectionBox.StartPoint;
            var radius = Vector2.Distance(selectionBox.StartPoint, selectionBox.EndPoint);
            
            // 绘制填充
            var fillPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = style.FillColor,
                IsAntialias = true
            };
            canvas.DrawCircle(center.X, center.Y, radius, fillPaint);
            fillPaint.Dispose();
            
            // 绘制边框
            var strokePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = style.StrokeWidth,
                Color = style.StrokeColor,
                IsAntialias = true
            };
            
            if (style.IsDashed)
            {
                strokePaint.PathEffect = SKPathEffect.CreateDash(style.DashPattern, 0);
            }
            
            canvas.DrawCircle(center.X, center.Y, radius, strokePaint);
            strokePaint.Dispose();
        }
        
        private void RenderPolygonSelection(SKCanvas canvas)
        {
            if (_polygonPoints.Count < 2) return;
            
            var path = new SKPath();
            path.MoveTo(_polygonPoints[0].X, _polygonPoints[0].Y);
            
            for (int i = 1; i < _polygonPoints.Count; i++)
            {
                path.LineTo(_polygonPoints[i].X, _polygonPoints[i].Y);
            }
            
            if (_polygonPoints.Count > 2 && !_isDragging)
            {
                path.Close();
            }
            
            canvas.DrawPath(path, _polygonSelectionPaint);
            path.Dispose();
        }
        
        private void RenderSelectedEntities(SKCanvas canvas)
        {
            foreach (var entity in _selectedEntities)
            {
                RenderEntityHighlight(canvas, entity, _selectedEntityPaint);
            }
        }
        
        private void RenderHighlightedEntities(SKCanvas canvas)
        {
            foreach (var entity in _highlightedEntities)
            {
                if (!_selectedEntities.Contains(entity))
                {
                    RenderEntityHighlight(canvas, entity, _highlightedEntityPaint);
                }
            }
        }
        
        private void RenderEntityHighlight(SKCanvas canvas, EntityBase entity, SKPaint paint)
        {
            var bounds = GetEntityBounds(entity);
            canvas.DrawRect(bounds, paint);
        }
        
        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event Action<List<EntityBase>> OnSelectionChanged;
        
        public void Dispose()
        {
            _selectedEntityPaint?.Dispose();
            _highlightedEntityPaint?.Dispose();
            _polygonSelectionPaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 选择框
    /// </summary>
    public class SelectionBox
    {
        public Vector2 StartPoint { get; set; }
        public Vector2 EndPoint { get; set; }
        public EntitySelectionMode Mode { get; set; }
    }
    
    /// <summary>
    /// 选择框样式
    /// </summary>
    public class SelectionBoxStyle
    {
        public SKColor StrokeColor { get; set; }
        public SKColor FillColor { get; set; }
        public float StrokeWidth { get; set; }
        public bool IsDashed { get; set; }
        public float[] DashPattern { get; set; }
    }
    
    /// <summary>
    /// 选择设置
    /// </summary>
    public class SelectionSettings
    {
        public bool MultiSelectEnabled { get; set; } = true;
        public bool PreserveSelectionOnMiss { get; set; } = false;
        public float PickBoxSize { get; set; } = 3.0f;
        public bool ShowSelectionPreview { get; set; } = true;
        public bool SnapToSelectionPoints { get; set; } = true;
    }
    
    /// <summary>
    /// 选择结果
    /// </summary>
    public class SelectionResult
    {
        public bool IsCompleted { get; set; }
        public List<EntityBase> SelectedEntities { get; set; } = new List<EntityBase>();
        public int SelectionCount { get; set; }
        public EntitySelectionMode SelectionMode { get; set; }
    }
} 