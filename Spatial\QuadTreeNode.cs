using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace McLaser.EditViewerSk.Spatial
{
    /// <summary>
    /// 四叉树节点
    /// </summary>
    public class QuadTreeNode : IDisposable
    {
        #region 私有字段

        private BoundingBox _bounds;
        private List<EntityBase> _entities;
        private QuadTreeNode[] _children;
        private readonly int _depth;
        private readonly int _maxDepth;
        private readonly int _maxEntitiesPerNode;
        private bool _isLeaf;
        private bool _isDisposed;

        #endregion

        #region 构造函数

        /// <summary>
        /// 创建四叉树节点
        /// </summary>
        /// <param name="bounds">节点边界</param>
        /// <param name="depth">当前深度</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="maxEntitiesPerNode">每个节点最大实体数</param>
        public QuadTreeNode(BoundingBox bounds, int depth, int maxDepth, int maxEntitiesPerNode)
        {
            _bounds = bounds;
            _depth = depth;
            _maxDepth = maxDepth;
            _maxEntitiesPerNode = maxEntitiesPerNode;
            _entities = new List<EntityBase>();
            _children = null;
            _isLeaf = true;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 节点边界
        /// </summary>
        public BoundingBox Bounds => _bounds;

        /// <summary>
        /// 当前深度
        /// </summary>
        public int Depth => _depth;

        /// <summary>
        /// 是否为叶子节点
        /// </summary>
        public bool IsLeaf => _isLeaf;

        /// <summary>
        /// 实体数量
        /// </summary>
        public int EntityCount => _entities?.Count ?? 0;

        /// <summary>
        /// 子节点
        /// </summary>
        public QuadTreeNode[] Children => _children;

        /// <summary>
        /// 实体列表
        /// </summary>
        public IReadOnlyList<EntityBase> Entities => _entities?.AsReadOnly();

        #endregion

        #region 公共方法

        /// <summary>
        /// 插入实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="entityBounds">实体边界</param>
        /// <returns>插入的节点</returns>
        public QuadTreeNode Insert(EntityBase entity, BoundingBox entityBounds)
        {
            if (_isDisposed || entity == null || entityBounds.IsEmpty) return null;

            try
            {
                // 检查实体是否在节点边界内
                if (!_bounds.HitTest(entityBounds, 0.001))
                {
                    return null;
                }

                // 如果是叶子节点且未达到分割条件
                if (_isLeaf)
                {
                    _entities.Add(entity);

                    // 检查是否需要分割
                    if (_entities.Count > _maxEntitiesPerNode && _depth < _maxDepth)
                    {
                        Subdivide();
                        
                        // 重新分配实体到子节点
                        var entitiesToRedistribute = new List<EntityBase>(_entities);
                        _entities.Clear();

                        foreach (var e in entitiesToRedistribute)
                        {
                            var eBounds = GetEntityBounds(e);
                            var insertedNode = InsertIntoChildren(e, eBounds);
                            
                            // 如果无法插入子节点，保留在当前节点
                            if (insertedNode == null)
                            {
                                _entities.Add(e);
                            }
                        }
                    }

                    return this;
                }
                else
                {
                    // 尝试插入子节点
                    var insertedNode = InsertIntoChildren(entity, entityBounds);
                    
                    // 如果无法插入子节点，插入当前节点
                    if (insertedNode == null)
                    {
                        _entities.Add(entity);
                        return this;
                    }

                    return insertedNode;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode insert error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 移除实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>是否成功移除</returns>
        public bool Remove(EntityBase entity)
        {
            if (_isDisposed || entity == null) return false;

            try
            {
                // 从当前节点移除
                if (_entities.Remove(entity))
                {
                    return true;
                }

                // 从子节点移除
                if (!_isLeaf && _children != null)
                {
                    foreach (var child in _children)
                    {
                        if (child.Remove(entity))
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode remove error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查询指定区域内的实体
        /// </summary>
        /// <param name="queryBounds">查询区域</param>
        /// <param name="results">结果列表</param>
        public void Query(BoundingBox queryBounds, List<EntityBase> results)
        {
            if (_isDisposed || queryBounds.IsEmpty || results == null) return;

            try
            {
                // 检查查询区域是否与节点相交
                if (!_bounds.HitTest(queryBounds, 0.001))
                {
                    return;
                }

                // 添加当前节点的实体
                foreach (var entity in _entities)
                {
                    var entityBounds = GetEntityBounds(entity);
                    if (entityBounds.HitTest(queryBounds, 0.001))
                    {
                        results.Add(entity);
                    }
                }

                // 查询子节点
                if (!_isLeaf && _children != null)
                {
                    foreach (var child in _children)
                    {
                        child.Query(queryBounds, results);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode query error: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空节点
        /// </summary>
        public void Clear()
        {
            if (_isDisposed) return;

            try
            {
                _entities?.Clear();

                if (_children != null)
                {
                    foreach (var child in _children)
                    {
                        child?.Clear();
                        child?.Dispose();
                    }
                    _children = null;
                }

                _isLeaf = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode clear error: {ex.Message}");
            }
        }

        /// <summary>
        /// 收集统计信息
        /// </summary>
        /// <param name="stats">统计信息</param>
        public void CollectStats(QuadTreeStats stats)
        {
            if (_isDisposed || stats == null) return;

            try
            {
                stats.TotalNodes++;

                if (_isLeaf)
                {
                    stats.LeafNodes++;
                    if (_entities.Count == 0)
                    {
                        stats.EmptyNodes++;
                    }
                }

                stats.ActualMaxDepth = Math.Max(stats.ActualMaxDepth, _depth);

                if (_children != null)
                {
                    foreach (var child in _children)
                    {
                        child.CollectStats(stats);
                    }
                }

                // 计算平均实体数（仅在根节点计算）
                if (_depth == 0 && stats.LeafNodes > 0)
                {
                    stats.AverageEntitiesPerLeaf = (double)stats.TotalEntities / stats.LeafNodes;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode stats error: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 分割节点
        /// </summary>
        private void Subdivide()
        {
            if (_isDisposed || !_isLeaf || _depth >= _maxDepth) return;

            try
            {
                var halfWidth = _bounds.Width / 2;
                var halfHeight = _bounds.Height / 2;
                var centerX = _bounds.Left + halfWidth;
                var centerY = _bounds.Bottom + halfHeight;

                _children = new QuadTreeNode[4];

                // 西北象限
                _children[0] = new QuadTreeNode(
                    new BoundingBox(_bounds.Left, _bounds.Top, centerX, centerY),
                    _depth + 1, _maxDepth, _maxEntitiesPerNode);

                // 东北象限
                _children[1] = new QuadTreeNode(
                    new BoundingBox(centerX, _bounds.Top, _bounds.Right, centerY),
                    _depth + 1, _maxDepth, _maxEntitiesPerNode);

                // 西南象限
                _children[2] = new QuadTreeNode(
                    new BoundingBox(_bounds.Left, centerY, centerX, _bounds.Bottom),
                    _depth + 1, _maxDepth, _maxEntitiesPerNode);

                // 东南象限
                _children[3] = new QuadTreeNode(
                    new BoundingBox(centerX, centerY, _bounds.Right, _bounds.Bottom),
                    _depth + 1, _maxDepth, _maxEntitiesPerNode);

                _isLeaf = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode subdivide error: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入实体到子节点
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="entityBounds">实体边界</param>
        /// <returns>插入的节点</returns>
        private QuadTreeNode InsertIntoChildren(EntityBase entity, BoundingBox entityBounds)
        {
            if (_children == null) return null;

            try
            {
                foreach (var child in _children)
                {
                    var insertedNode = child.Insert(entity, entityBounds);
                    if (insertedNode != null)
                    {
                        return insertedNode;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode insert into children error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取实体包围盒
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>包围盒</returns>
        private BoundingBox GetEntityBounds(EntityBase entity)
        {
            try
            {
                if (entity.BBox != null && !entity.BBox.IsEmpty)
                {
                    return entity.BBox;
                }

                entity.UpdateBoundingBox();
                return entity.BBox ?? BoundingBox.Empty;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting entity bounds: {ex.Message}");
                return BoundingBox.Empty;
            }
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                Clear();
                _entities = null;
                _isDisposed = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"QuadTreeNode dispose error: {ex.Message}");
            }
        }

        #endregion
    }
}
