using System.Windows.Media;
using SkiaSharp;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 图形相关扩展方法
    /// </summary>
    public static class GraphicsExtensions
    {
        /// <summary>
        /// 将WPF Color转换为SKColor
        /// </summary>
        public static SKColor ToSKColor(this Color color)
        {
            return new SKColor(color.R, color.G, color.B, color.A);
        }

        /// <summary>
        /// 将SKColor转换为WPF Color
        /// </summary>
        public static Color ToWpfColor(this SKColor color)
        {
            return Color.FromArgb(color.Alpha, color.Red, color.Green, color.Blue);
        }

        /// <summary>
        /// 创建标准的CAD线型画笔
        /// </summary>
        public static SKPaint CreateLinePaint(Color color, float strokeWidth = 1.0f)
        {
            return new SKPaint()
            {
                Color = color.ToSKColor(),
                StrokeWidth = strokeWidth,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true
            };
        }

        /// <summary>
        /// 创建填充画笔
        /// </summary>
        public static SKPaint CreateFillPaint(Color color)
        {
            return new SKPaint()
            {
                Color = color.ToSKColor(),
                Style = SKPaintStyle.Fill,
                IsAntialias = true
            };
        }

        /// <summary>
        /// 创建文本画笔
        /// </summary>
        public static SKPaint CreateTextPaint(Color color, float textSize = 12.0f, string fontFamily = "Arial")
        {
            return new SKPaint()
            {
                Color = color.ToSKColor(),
                TextSize = textSize,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName(fontFamily)
            };
        }

        /// <summary>
        /// 创建虚线画笔
        /// </summary>
        public static SKPaint CreateDashPaint(Color color, float strokeWidth = 1.0f, float[] dashPattern = null)
        {
            var paint = CreateLinePaint(color, strokeWidth);
            dashPattern = dashPattern ?? new float[] { 5, 5 };
            paint.PathEffect = SKPathEffect.CreateDash(dashPattern, 0);
            return paint;
        }
    }
} 