using System;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Managers;

namespace McLaser.EditViewerSk.Commands
{
    public class RadiusDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForCircleSelection,
            WaitingForLeaderPosition
        }

        private State currentState = State.WaitingForCircleSelection;
        private EntityBase selectedEntity;
        private EntityRadiusDimension previewDimension;

        public RadiusDimensionCmd(IView view) : base(view)
        {
            CommandName = "半径标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    var entity = Document.FindEntityAt(point, 5.0);
                    if (entity is EntityCircle circle)
                    {
                        selectedEntity = circle;
                        previewDimension = EntityRadiusDimension.CreateFromCircle(circle, point);
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                        
                        currentState = State.WaitingForLeaderPosition;
                        DynamicInputManager.AddInput("请指定引线终点位置:");
                        return true;
                    }
                    else if (entity is EntityArc arc)
                    {
                        selectedEntity = arc;
                        previewDimension = EntityRadiusDimension.CreateFromArc(arc, point);
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                        
                        currentState = State.WaitingForLeaderPosition;
                        DynamicInputManager.AddInput("请指定引线终点位置:");
                        return true;
                    }
                    else
                    {
                        DynamicInputManager.AddError("请选择圆或圆弧");
                        return false;
                    }

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.LeaderEndPoint = point;
                        
                        // 添加到文档
                        Document.AddEntity(previewDimension);
                        
                        // 创建关联
                        DimensionAssociationManager.Instance.CreateAssociation(previewDimension, selectedEntity);
                        
                        // 添加到撤销系统
                        Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                        
                        Finish();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    // 高亮可选择的圆形实体
                    InvalidateView();
                    return true;

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.LeaderEndPoint = point;
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            if (key == System.Windows.Input.Key.Escape)
            {
                Cancel();
                return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    // 高亮鼠标下的圆形实体
                    if (View.LastMousePosition.HasValue)
                    {
                        var entity = Document.FindEntityAt(View.LastMousePosition.Value, 5.0);
                        if (entity is EntityCircle || entity is EntityArc)
                        {
                            var highlightPaint = CreateHighlightPaint();
                            entity.Render(renderer, CoordinateSpace.Model);
                        }
                    }
                    break;

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    return "选择圆弧或圆:";
                case State.WaitingForLeaderPosition:
                    return "请指定标注位置或 [文字(M)/角度(A)]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            selectedEntity = null;
            currentState = State.WaitingForCircleSelection;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            selectedEntity = null;
            currentState = State.WaitingForCircleSelection;
            base.Finish();
        }
    }

    public class DiameterDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForCircleSelection,
            WaitingForLeaderPosition
        }

        private State currentState = State.WaitingForCircleSelection;
        private EntityBase selectedEntity;
        private EntityDiameterDimension previewDimension;

        public DiameterDimensionCmd(IView view) : base(view)
        {
            CommandName = "直径标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    var entity = Document.FindEntityAt(point, 5.0);
                    if (entity is EntityCircle circle)
                    {
                        selectedEntity = circle;
                        previewDimension = EntityDiameterDimension.CreateFromCircle(circle, point);
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                        
                        currentState = State.WaitingForLeaderPosition;
                        DynamicInputManager.AddInput("请指定引线终点位置:");
                        return true;
                    }
                    else if (entity is EntityArc arc)
                    {
                        selectedEntity = arc;
                        previewDimension = EntityDiameterDimension.CreateFromArc(arc, point);
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                        
                        currentState = State.WaitingForLeaderPosition;
                        DynamicInputManager.AddInput("请指定引线终点位置:");
                        return true;
                    }
                    else
                    {
                        DynamicInputManager.AddError("请选择圆或圆弧");
                        return false;
                    }

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.LeaderEndPoint = point;
                        
                        // 添加到文档
                        Document.AddEntity(previewDimension);
                        
                        // 创建关联
                        DimensionAssociationManager.Instance.CreateAssociation(previewDimension, selectedEntity);
                        
                        // 添加到撤销系统
                        Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                        
                        Finish();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    InvalidateView();
                    return true;

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.LeaderEndPoint = point;
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            if (key == System.Windows.Input.Key.Escape)
            {
                Cancel();
                return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    // 高亮鼠标下的圆形实体
                    if (View.LastMousePosition.HasValue)
                    {
                        var entity = Document.FindEntityAt(View.LastMousePosition.Value, 5.0);
                        if (entity is EntityCircle || entity is EntityArc)
                        {
                            var highlightPaint = CreateHighlightPaint();
                            entity.Render(renderer, CoordinateSpace.Model);
                        }
                    }
                    break;

                case State.WaitingForLeaderPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForCircleSelection:
                    return "选择圆弧或圆:";
                case State.WaitingForLeaderPosition:
                    return "请指定标注位置或 [文字(M)/角度(A)]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            selectedEntity = null;
            currentState = State.WaitingForCircleSelection;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            selectedEntity = null;
            currentState = State.WaitingForCircleSelection;
            base.Finish();
        }
    }
} 