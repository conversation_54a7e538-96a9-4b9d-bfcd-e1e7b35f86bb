using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 专业级顶点类，支持弧段
    /// </summary>
    [TypeConverter(typeof(ExpandableObjectConverter))]
    public class PolylineVertex : IEquatable<PolylineVertex>, ICloneable
    {
        /// <summary>
        /// X坐标
        /// </summary>
        [Category("坐标"), DisplayName("X"), Browsable(true)]
        public double X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        [Category("坐标"), DisplayName("Y"), Browsable(true)]
        public double Y { get; set; }

        /// <summary>
        /// 弧度因子（Bulge）- 用于定义弧段
        /// Bulge = tan(θ/4)，其中θ是弧的角度
        /// 0 = 直线段，正值 = 逆时针弧，负值 = 顺时针弧
        /// </summary>
        [Category("弧段"), DisplayName("弧度因子"), Browsable(true)]
        public double Bulge { get; set; }

        /// <summary>
        /// 起始线宽
        /// </summary>
        [Category("线宽"), DisplayName("起始宽度"), Browsable(true)]
        public double StartWidth { get; set; }

        /// <summary>
        /// 结束线宽
        /// </summary>
        [Category("线宽"), DisplayName("结束宽度"), Browsable(true)]
        public double EndWidth { get; set; }

        /// <summary>
        /// 是否为弧段
        /// </summary>
        [JsonIgnore, Browsable(false)]
        public bool IsArc => Math.Abs(Bulge) > double.Epsilon;

        /// <summary>
        /// 弧段角度（度）
        /// </summary>
        [JsonIgnore, Category("计算属性"), DisplayName("弧角度"), ReadOnly(true)]
        public double ArcAngle => IsArc ? Math.Atan(Math.Abs(Bulge)) * 4.0 * 180.0 / Math.PI : 0;

        /// <summary>
        /// 位置向量
        /// </summary>
        [JsonIgnore, Browsable(false)]
        public Vector2 Position
        {
            get => new Vector2((float)X, (float)Y);
            set { X = value.X; Y = value.Y; }
        }

        public PolylineVertex() { }

        public PolylineVertex(double x, double y, double bulge = 0.0, double startWidth = 0.0, double endWidth = 0.0)
        {
            X = x;
            Y = y;
            Bulge = bulge;
            StartWidth = startWidth;
            EndWidth = endWidth;
        }

        public PolylineVertex(Vector2 position, double bulge = 0.0, double startWidth = 0.0, double endWidth = 0.0)
            : this(position.X, position.Y, bulge, startWidth, endWidth) { }

        public bool Equals(PolylineVertex other)
        {
            if (other == null) return false;
            return Math.Abs(X - other.X) < double.Epsilon &&
                   Math.Abs(Y - other.Y) < double.Epsilon &&
                   Math.Abs(Bulge - other.Bulge) < double.Epsilon;
        }

        public object Clone()
        {
            return new PolylineVertex(X, Y, Bulge, StartWidth, EndWidth);
        }

        public override string ToString()
        {
            return $"({X:F3}, {Y:F3}) Bulge:{Bulge:F3}";
        }
    }

    /// <summary>
    /// 专业高级多段线图元类
    /// 支持直线段和弧段的混合，基于CAD行业标准实现
    /// </summary>
    public class EntityPolylineAdvanced : EntityBase
    {
        #region 私有字段
        private List<PolylineVertex> _vertices = new List<PolylineVertex>();
        private bool _isClosed = false;
        private double _globalWidth = 0.0; // 全局线宽
        private bool _useGlobalWidth = true; // 是否使用全局线宽
        
        [JsonIgnore]
        private SKPath _polylinePath; // 缓存的多段线路径
        private bool _pathNeedsUpdate = true; // 路径是否需要更新
        
        [JsonIgnore]
        private List<LineSegmentInfo> _segments; // 缓存的线段信息
        private bool _segmentsNeedUpdate = true; // 线段信息是否需要更新
        #endregion

        #region 内部结构
        /// <summary>
        /// 线段信息结构
        /// </summary>
        private struct LineSegmentInfo
        {
            public Vector2 StartPoint;
            public Vector2 EndPoint;
            public bool IsArc;
            public Vector2 ArcCenter;
            public double ArcRadius;
            public double StartAngle;
            public double SweepAngle;
            public bool IsClockwise;
        }
        #endregion

        #region 公共属性
        /// <summary>
        /// 顶点列表
        /// </summary>
        [Category("几何"), DisplayName("顶点"), Browsable(true)]
        [TypeConverter(typeof(CollectionConverter))]
        public List<PolylineVertex> Vertices
        {
            get => _vertices;
            set
            {
                if (_vertices != value)
                {
                    _vertices = value ?? new List<PolylineVertex>();
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    _segmentsNeedUpdate = true;
                    OnPropertyChanged(nameof(Vertices));
                    OnPropertyChanged(nameof(Length));
                    OnPropertyChanged(nameof(Area));
                }
            }
        }

        /// <summary>
        /// 是否闭合
        /// </summary>
        [Category("几何"), DisplayName("闭合"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public bool IsClosed
        {
            get => _isClosed;
            set
            {
                if (_isClosed != value)
                {
                    _isClosed = value;
                    IsNeedToRegen = true;
                    _pathNeedsUpdate = true;
                    OnPropertyChanged(nameof(IsClosed));
                    OnPropertyChanged(nameof(Length));
                    OnPropertyChanged(nameof(Area));
                }
            }
        }

        /// <summary>
        /// 全局线宽
        /// </summary>
        [Category("外观"), DisplayName("全局线宽"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double GlobalWidth
        {
            get => _globalWidth;
            set
            {
                if (Math.Abs(_globalWidth - value) > double.Epsilon)
                {
                    _globalWidth = Math.Max(0, value);
                    OnPropertyChanged(nameof(GlobalWidth));
                }
            }
        }

        /// <summary>
        /// 是否使用全局线宽
        /// </summary>
        [Category("外观"), DisplayName("使用全局线宽"), Browsable(true)]
        public bool UseGlobalWidth
        {
            get => _useGlobalWidth;
            set
            {
                if (_useGlobalWidth != value)
                {
                    _useGlobalWidth = value;
                    OnPropertyChanged(nameof(UseGlobalWidth));
                }
            }
        }

        /// <summary>
        /// 顶点数量
        /// </summary>
        [Category("计算属性"), DisplayName("顶点数"), Browsable(true), ReadOnly(true)]
        public int VertexCount => _vertices?.Count ?? 0;

        /// <summary>
        /// 线段数量
        /// </summary>
        [Category("计算属性"), DisplayName("线段数"), Browsable(true), ReadOnly(true)]
        public int SegmentCount
        {
            get
            {
                var count = Math.Max(0, VertexCount - 1);
                if (_isClosed && VertexCount > 2) count++;
                return count;
            }
        }

        /// <summary>
        /// 弧段数量
        /// </summary>
        [Category("计算属性"), DisplayName("弧段数"), Browsable(true), ReadOnly(true)]
        public int ArcSegmentCount
        {
            get
            {
                if (_vertices == null) return 0;
                
                int count = 0;
                for (int i = 0; i < _vertices.Count - 1; i++)
                {
                    if (_vertices[i].IsArc) count++;
                }
                if (_isClosed && _vertices.Count > 2 && _vertices[_vertices.Count - 1].IsArc)
                    count++;
                
                return count;
            }
        }

        /// <summary>
        /// 总长度
        /// </summary>
        [Category("计算属性"), DisplayName("长度"), Browsable(true), ReadOnly(true)]
        public double Length
        {
            get
            {
                UpdateSegments();
                if (_segments == null) return 0;
                
                double totalLength = 0;
                foreach (var segment in _segments)
                {
                    if (segment.IsArc)
                    {
                        // 弧段长度 = 半径 × 弧度
                        totalLength += segment.ArcRadius * Math.Abs(segment.SweepAngle);
                    }
                    else
                    {
                        // 直线段长度
                        totalLength += Vector2.Distance(segment.StartPoint, segment.EndPoint);
                    }
                }
                return totalLength;
            }
        }

        /// <summary>
        /// 面积（仅对闭合多段线有效）
        /// </summary>
        [Category("计算属性"), DisplayName("面积"), Browsable(true), ReadOnly(true)]
        public double Area
        {
            get
            {
                if (!_isClosed || _vertices == null || _vertices.Count < 3) return 0;
                
                // 使用精确的面积计算算法（包含弧段）
                return CalculatePolygonArea();
            }
        }
        #endregion

        #region 构造函数
        public EntityPolylineAdvanced()
        {
            Name = "AdvancedPolyline";
            _polylinePath = new SKPath();
            // Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/polyline_advanced.png"));
        }

        public EntityPolylineAdvanced(IEnumerable<Vector2> points, bool isClosed = false) : this()
        {
            if (points != null)
            {
                _vertices = points.Select(p => new PolylineVertex(p)).ToList();
            }
            _isClosed = isClosed;
        }

        public EntityPolylineAdvanced(IEnumerable<PolylineVertex> vertices, bool isClosed = false) : this()
        {
            _vertices = vertices?.ToList() ?? new List<PolylineVertex>();
            _isClosed = isClosed;
        }
        #endregion

        #region 重写方法
        [JsonIgnore]
        public override BoundingBox BoundingBox { get; set; } = BoundingBox.Empty;

        /// <summary>
        /// 重新生成多段线数据
        /// </summary>
        public override void Regen()
        {
            UpdateSegments();
            UpdatePolylinePath();
            RegenBoundRect();
            IsNeedToRegen = false;
        }

        /// <summary>
        /// 渲染多段线
        /// </summary>
        public override void Render(IView view)
        {
            if (view == null || !IsRenderable || _vertices == null || _vertices.Count < 2) return;
            if (IsNeedToRegen) Regen();

            // 使用新的集成渲染系统
            this.IntegratePolylineRendering(view);
        }

        /// <summary>
        /// 平移变换
        /// </summary>
        public override void Translate(Vector2 delta)
        {
            if (IsLocked || delta == Vector2.Zero || _vertices == null) return;
            
            for (int i = 0; i < _vertices.Count; i++)
            {
                _vertices[i].Position += delta;
            }
            
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
            BoundingBox?.Transit(delta);
        }

        /// <summary>
        /// 旋转变换
        /// </summary>
        public override void Rotate(double angle, Vector2 rotateCenter)
        {
            if (IsLocked || MathHelper.IsZero(angle) || _vertices == null) return;
            
            var matrix = Matrix3.CreateRotation((float)(angle * Math.PI / 180.0), rotateCenter);
            
            for (int i = 0; i < _vertices.Count; i++)
            {
                _vertices[i].Position = Vector2.Transform(_vertices[i].Position, matrix);
            }
            
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
        }

        /// <summary>
        /// 缩放变换
        /// </summary>
        public override void Scale(Vector2 scale, Vector2 scaleCenter)
        {
            if (IsLocked || scale == Vector2.Zero || scale == Vector2.One || _vertices == null) return;
            
            for (int i = 0; i < _vertices.Count; i++)
            {
                _vertices[i].Position = (_vertices[i].Position - scaleCenter) * scale + scaleCenter;
            }
            
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
        }

        /// <summary>
        /// 点击测试
        /// </summary>
        public override bool HitTest(double x, double y, double threshold)
        {
            if (!BoundingBox.HitTest(x, y, threshold)) return false;
            
            return IsPointOnPolyline(new Vector2((float)x, (float)y), threshold);
        }

        /// <summary>
        /// 矩形区域碰撞测试
        /// </summary>
        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!BoundingBox.HitTest(br, threshold)) return false;
            
            return IsPolylineIntersectRect(br);
        }

        /// <summary>
        /// 获取对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            var snapPoints = new List<ObjectSnapPoint>();
            
            if (_vertices == null || _vertices.Count == 0) return snapPoints;
            
            // 顶点
            foreach (var vertex in _vertices)
            {
                snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.End, vertex.Position));
            }
            
            // 线段中点
            UpdateSegments();
            if (_segments != null)
            {
                foreach (var segment in _segments)
                {
                    if (segment.IsArc)
                    {
                        // 弧段中点
                        var midAngle = segment.StartAngle + segment.SweepAngle * 0.5;
                        var midPoint = segment.ArcCenter + segment.ArcRadius * new Vector2(
                            (float)Math.Cos(midAngle), (float)Math.Sin(midAngle));
                        snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Mid, midPoint));
                        
                        // 弧心
                        snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Center, segment.ArcCenter));
                    }
                    else
                    {
                        // 直线段中点
                        var midPoint = (segment.StartPoint + segment.EndPoint) * 0.5f;
                        snapPoints.Add(new ObjectSnapPoint(ObjectSnapMode.Mid, midPoint));
                    }
                }
            }
            
            return snapPoints;
        }

        /// <summary>
        /// 克隆对象
        /// </summary>
        public override object Clone()
        {
            var clonedVertices = _vertices?.Select(v => v.Clone() as PolylineVertex).ToList();
            
            return new EntityPolylineAdvanced
            {
                Name = Name,
                Description = Description,
                Parent = Parent,
                IsSelected = IsSelected,
                IsVisible = IsVisible,
                IsRenderable = IsRenderable,
                IsMarkerable = IsMarkerable,
                IsLocked = IsLocked,
                Color = Color,
                Vertices = clonedVertices,
                IsClosed = _isClosed,
                GlobalWidth = _globalWidth,
                UseGlobalWidth = _useGlobalWidth,
                BoundingBox = BoundingBox?.Clone(),
                Tag = Tag,
                Index = Index,
                IsNeedToRegen = true
            };
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 添加顶点
        /// </summary>
        public void AddVertex(PolylineVertex vertex)
        {
            if (vertex == null) return;
            
            _vertices.Add(vertex);
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
        }

        /// <summary>
        /// 添加直线顶点
        /// </summary>
        public void AddVertex(Vector2 position)
        {
            AddVertex(new PolylineVertex(position));
        }

        /// <summary>
        /// 添加弧线顶点
        /// </summary>
        public void AddArcVertex(Vector2 position, double bulge)
        {
            AddVertex(new PolylineVertex(position, bulge));
        }

        /// <summary>
        /// 插入顶点
        /// </summary>
        public void InsertVertex(int index, PolylineVertex vertex)
        {
            if (vertex == null || index < 0 || index > _vertices.Count) return;
            
            _vertices.Insert(index, vertex);
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
        }

        /// <summary>
        /// 移除顶点
        /// </summary>
        public bool RemoveVertex(int index)
        {
            if (index < 0 || index >= _vertices.Count) return false;
            
            _vertices.RemoveAt(index);
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
            return true;
        }

        /// <summary>
        /// 移除顶点
        /// </summary>
        public bool RemoveVertex(PolylineVertex vertex)
        {
            if (vertex == null) return false;
            
            var removed = _vertices.Remove(vertex);
            if (removed)
            {
                IsNeedToRegen = true;
                _pathNeedsUpdate = true;
                _segmentsNeedUpdate = true;
            }
            return removed;
        }

        /// <summary>
        /// 清除所有顶点
        /// </summary>
        public void ClearVertices()
        {
            _vertices.Clear();
            IsNeedToRegen = true;
            _pathNeedsUpdate = true;
            _segmentsNeedUpdate = true;
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 更新线段信息
        /// </summary>
        private void UpdateSegments()
        {
            if (!_segmentsNeedUpdate || _vertices == null || _vertices.Count < 2) return;
            
            _segments = new List<LineSegmentInfo>();
            
            for (int i = 0; i < _vertices.Count - 1; i++)
            {
                var segment = CreateSegment(_vertices[i], _vertices[i + 1]);
                _segments.Add(segment);
            }
            
            // 闭合线段
            if (_isClosed && _vertices.Count > 2)
            {
                var segment = CreateSegment(_vertices[_vertices.Count - 1], _vertices[0]);
                _segments.Add(segment);
            }
            
            _segmentsNeedUpdate = false;
        }

        /// <summary>
        /// 创建线段信息
        /// </summary>
        private LineSegmentInfo CreateSegment(PolylineVertex startVertex, PolylineVertex endVertex)
        {
            var segment = new LineSegmentInfo
            {
                StartPoint = startVertex.Position,
                EndPoint = endVertex.Position,
                IsArc = startVertex.IsArc
            };
            
            if (startVertex.IsArc)
            {
                // 计算弧段参数
                CalculateArcParameters(startVertex, endVertex, out segment.ArcCenter, 
                    out segment.ArcRadius, out segment.StartAngle, out segment.SweepAngle, out segment.IsClockwise);
            }
            
            return segment;
        }

        /// <summary>
        /// 计算弧段参数（基于Bulge值）
        /// </summary>
        private void CalculateArcParameters(PolylineVertex startVertex, PolylineVertex endVertex,
            out Vector2 center, out double radius, out double startAngle, out double sweepAngle, out bool isClockwise)
        {
            var p1 = startVertex.Position;
            var p2 = endVertex.Position;
            var bulge = startVertex.Bulge;
            
            // 弦长
            var chordLength = Vector2.Distance(p1, p2);
            
            // 弦中点
            var midPoint = (p1 + p2) * 0.5f;
            
            // 弓高（sagitta）
            var sagitta = Math.Abs(bulge) * chordLength * 0.5;
            
            // 半径计算
            radius = (chordLength * chordLength * 0.25 + sagitta * sagitta) / (2.0 * sagitta);
            
            // 弦方向向量
            var chordVector = p2 - p1;
            var chordAngle = Math.Atan2(chordVector.Y, chordVector.X);
            
            // 垂直于弦的方向（指向圆心）
            var perpAngle = chordAngle + (bulge > 0 ? Math.PI * 0.5 : -Math.PI * 0.5);
            var distanceToCenter = radius - sagitta;
            
            // 圆心位置
            center = midPoint + (float)distanceToCenter * new Vector2((float)Math.Cos(perpAngle), (float)Math.Sin(perpAngle));
            
            // 起始角度和扫描角度
            var startVector = p1 - center;
            var endVector = p2 - center;
            
            startAngle = Math.Atan2(startVector.Y, startVector.X);
            var endAngle = Math.Atan2(endVector.Y, endVector.X);
            
            sweepAngle = endAngle - startAngle;
            isClockwise = bulge < 0;
            
            // 标准化角度
            if (isClockwise)
            {
                if (sweepAngle > 0) sweepAngle -= 2.0 * Math.PI;
            }
            else
            {
                if (sweepAngle < 0) sweepAngle += 2.0 * Math.PI;
            }
        }

        /// <summary>
        /// 更新多段线路径
        /// </summary>
        private void UpdatePolylinePath()
        {
            if (!_pathNeedsUpdate || _vertices == null || _vertices.Count < 2) return;
            
            _polylinePath?.Reset();
            
            UpdateSegments();
            if (_segments == null || _segments.Count == 0) return;
            
            // 移动到第一个点
            _polylinePath.MoveTo(_segments[0].StartPoint.X, _segments[0].StartPoint.Y);
            
            // 绘制所有线段
            foreach (var segment in _segments)
            {
                if (segment.IsArc)
                {
                    // 弧段
                    var rect = new SKRect(
                        (float)(segment.ArcCenter.X - segment.ArcRadius),
                        (float)(segment.ArcCenter.Y - segment.ArcRadius),
                        (float)(segment.ArcCenter.X + segment.ArcRadius),
                        (float)(segment.ArcCenter.Y + segment.ArcRadius)
                    );
                    
                    var startAngleDeg = (float)(segment.StartAngle * 180.0 / Math.PI);
                    var sweepAngleDeg = (float)(segment.SweepAngle * 180.0 / Math.PI);
                    
                    _polylinePath.ArcTo(rect, startAngleDeg, sweepAngleDeg, false);
                }
                else
                {
                    // 直线段
                    _polylinePath.LineTo(segment.EndPoint.X, segment.EndPoint.Y);
                }
            }
            
            // 如果是闭合的，闭合路径
            if (_isClosed)
            {
                _polylinePath.Close();
            }
            
            _pathNeedsUpdate = false;
        }

        /// <summary>
        /// 重新计算包围盒
        /// </summary>
        private void RegenBoundRect()
        {
            if (_vertices == null || _vertices.Count == 0)
            {
                BoundingBox = BoundingBox.Empty;
                return;
            }
            
            var minX = _vertices.Min(v => v.X);
            var maxX = _vertices.Max(v => v.X);
            var minY = _vertices.Min(v => v.Y);
            var maxY = _vertices.Max(v => v.Y);
            
            // 对于弧段，需要扩展包围盒以包含弧的极值点
            UpdateSegments();
            if (_segments != null)
            {
                foreach (var segment in _segments)
                {
                    if (segment.IsArc)
                    {
                        // 扩展包围盒以包含弧段
                        var arcMinX = segment.ArcCenter.X - segment.ArcRadius;
                        var arcMaxX = segment.ArcCenter.X + segment.ArcRadius;
                        var arcMinY = segment.ArcCenter.Y - segment.ArcRadius;
                        var arcMaxY = segment.ArcCenter.Y + segment.ArcRadius;
                        
                        minX = Math.Min(minX, arcMinX);
                        maxX = Math.Max(maxX, arcMaxX);
                        minY = Math.Min(minY, arcMinY);
                        maxY = Math.Max(maxY, arcMaxY);
                    }
                }
            }
            
            BoundingBox = new BoundingBox(minX, maxY, maxX, minY);
        }

        /// <summary>
        /// 基础方法渲染（降级方案）
        /// </summary>
        private void RenderWithBasicMethod(ViewBase viewBase)
        {
            UpdateSegments();
            if (_segments == null) return;
            
            foreach (var segment in _segments)
            {
                if (segment.IsArc)
                {
                    // 弧段：用直线逼近
                    var arcPoints = GetArcPoints(segment, 16);
                    for (int i = 0; i < arcPoints.Count - 1; i++)
                    {
                        viewBase.DrawLine(arcPoints[i], arcPoints[i + 1], Pen);
                    }
                }
                else
                {
                    // 直线段
                    viewBase.DrawLine(segment.StartPoint, segment.EndPoint, Pen);
                }
            }
        }

        /// <summary>
        /// 获取弧段上的点
        /// </summary>
        private List<Vector2> GetArcPoints(LineSegmentInfo arcSegment, int segments)
        {
            var points = new List<Vector2>();
            var angleStep = arcSegment.SweepAngle / segments;
            
            for (int i = 0; i <= segments; i++)
            {
                var angle = arcSegment.StartAngle + i * angleStep;
                var point = arcSegment.ArcCenter + (float)arcSegment.ArcRadius * 
                    new Vector2((float)Math.Cos(angle), (float)Math.Sin(angle));
                points.Add(point);
            }
            
            return points;
        }

        /// <summary>
        /// 渲染控制点
        /// </summary>
        private void RenderControlPoints(ViewBase viewBase)
        {
            if (_vertices == null) return;
            
            var controlPaint = new SKPaint
            {
                Color = SKColors.Blue,
                StrokeWidth = 1.0f,
                Style = SKPaintStyle.Fill
            };
            
            var arcControlPaint = new SKPaint
            {
                Color = SKColors.Green,
                StrokeWidth = 1.0f,
                Style = SKPaintStyle.Fill
            };

            // 顶点
            foreach (var vertex in _vertices)
            {
                var paint = vertex.IsArc ? arcControlPaint : controlPaint;
                viewBase.DrawRectangle(vertex.Position - new Vector2(2, 2), 4, 4, paint);
            }
        }

        /// <summary>
        /// 判断点是否在多段线上
        /// </summary>
        private bool IsPointOnPolyline(Vector2 point, double threshold)
        {
            UpdateSegments();
            if (_segments == null) return false;
            
            foreach (var segment in _segments)
            {
                if (segment.IsArc)
                {
                    // 弧段碰撞检测
                    var distanceToCenter = Vector2.Distance(point, segment.ArcCenter);
                    if (Math.Abs(distanceToCenter - segment.ArcRadius) <= threshold)
                    {
                        // 检查点是否在弧段的角度范围内
                        var pointAngle = Math.Atan2(point.Y - segment.ArcCenter.Y, point.X - segment.ArcCenter.X);
                        return IsAngleInRange(pointAngle, segment.StartAngle, segment.SweepAngle);
                    }
                }
                else
                {
                    // 直线段碰撞检测
                    if (MathHelper.IntersectPointInLine(
                        segment.StartPoint.X, segment.StartPoint.Y,
                        segment.EndPoint.X, segment.EndPoint.Y,
                        point.X, point.Y, threshold))
                    {
                        return true;
                    }
                }
            }
            
            return false;
        }

        /// <summary>
        /// 检查角度是否在范围内
        /// </summary>
        private bool IsAngleInRange(double angle, double startAngle, double sweepAngle)
        {
            // 标准化角度到[0, 2π]范围
            while (angle < 0) angle += 2.0 * Math.PI;
            while (angle >= 2.0 * Math.PI) angle -= 2.0 * Math.PI;
            while (startAngle < 0) startAngle += 2.0 * Math.PI;
            while (startAngle >= 2.0 * Math.PI) startAngle -= 2.0 * Math.PI;
            
            var endAngle = startAngle + sweepAngle;
            
            if (sweepAngle > 0)
            {
                return angle >= startAngle && angle <= endAngle;
            }
            else
            {
                return angle <= startAngle && angle >= endAngle;
            }
        }

        /// <summary>
        /// 多段线与矩形相交测试
        /// </summary>
        private bool IsPolylineIntersectRect(BoundingBox rect)
        {
            // 简化实现：检查多段线的包围盒是否与给定矩形相交
            return BoundingBox.HitTest(rect, 0);
        }

        /// <summary>
        /// 计算多边形面积（包含弧段）
        /// </summary>
        private double CalculatePolygonArea()
        {
            UpdateSegments();
            if (_segments == null || _segments.Count == 0) return 0;
            
            double area = 0;
            
            foreach (var segment in _segments)
            {
                if (segment.IsArc)
                {
                    // 弧段面积 = 扇形面积 - 三角形面积
                    var sectorArea = 0.5 * segment.ArcRadius * segment.ArcRadius * Math.Abs(segment.SweepAngle);
                    var triangleArea = 0.5 * segment.ArcRadius * segment.ArcRadius * Math.Sin(Math.Abs(segment.SweepAngle));
                    area += (sectorArea - triangleArea) * Math.Sign(segment.SweepAngle);
                }
                else
                {
                    // 直线段：使用shoelace公式
                    area += (segment.StartPoint.X * segment.EndPoint.Y - segment.EndPoint.X * segment.StartPoint.Y);
                }
            }
            
            return Math.Abs(area) * 0.5;
        }
        #endregion

        #region IDisposable
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _polylinePath?.Dispose();
                _polylinePath = null;
                _segments?.Clear();
                _segments = null;
            }
            base.Dispose(disposing);
        }
        #endregion
    }
} 