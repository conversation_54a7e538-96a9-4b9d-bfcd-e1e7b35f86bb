# McLaser CAD UI集成测试指南

## 🎯 测试目标
验证所有后端功能都有完整的UI访问方式，确保100%功能可访问性。

## 📋 测试清单

### ✅ 第一阶段：核心功能工具栏测试

#### 标注工具栏测试
- [ ] **线性标注按钮** - 点击后应执行DIMLINEAR命令
- [ ] **对齐标注按钮** - 点击后应执行DIMALIGNED命令  
- [ ] **半径标注按钮** - 点击后应执行DIMRADIUS命令
- [ ] **直径标注按钮** - 点击后应执行DIMDIAMETER命令
- [ ] **角度标注按钮** - 点击后应执行DIMANGULAR命令
- [ ] **引线标注按钮** - 点击后应执行LEADER命令
- [ ] **标注样式管理器按钮** - 点击后应打开标注样式管理器窗口

#### 编辑工具栏测试
- [ ] **偏移按钮** - 点击后应执行OFFSET命令
- [ ] **阵列按钮** - 点击后应执行ARRAY命令
- [ ] **修剪按钮** - 点击后应执行TRIM命令
- [ ] **延伸按钮** - 点击后应执行EXTEND命令
- [ ] **圆角按钮** - 点击后应执行FILLET命令
- [ ] **倒角按钮** - 点击后应执行CHAMFER命令
- [ ] **缩放按钮** - 点击后应执行SCALE命令
- [ ] **旋转按钮** - 点击后应执行ROTATE命令

### ✅ 第二阶段：高级交互系统测试

#### 对象捕捉控制面板测试
- [ ] **端点捕捉切换** - 切换应影响捕捉行为
- [ ] **中点捕捉切换** - 切换应影响捕捉行为
- [ ] **中心点捕捉切换** - 切换应影响捕捉行为
- [ ] **交点捕捉切换** - 切换应影响捕捉行为
- [ ] **垂足捕捉切换** - 切换应影响捕捉行为
- [ ] **切点捕捉切换** - 切换应影响捕捉行为
- [ ] **象限点捕捉切换** - 切换应影响捕捉行为
- [ ] **最近点捕捉切换** - 切换应影响捕捉行为
- [ ] **网格捕捉切换** - 切换应影响捕捉行为

#### 高级交互控制测试
- [ ] **极轴追踪切换** - 切换应影响极轴追踪状态
- [ ] **对象追踪切换** - 切换应影响对象追踪状态
- [ ] **动态输入切换** - 切换应影响动态输入状态
- [ ] **极轴追踪设置按钮** - 点击后应打开极轴追踪设置窗口
- [ ] **对象捕捉设置按钮** - 点击后应打开对象捕捉设置窗口

### ✅ 第三阶段：符号系统管理测试

#### 图层管理面板测试
- [ ] **新建图层按钮** - 应创建新图层
- [ ] **删除图层按钮** - 应删除选中图层
- [ ] **复制图层按钮** - 应复制选中图层
- [ ] **图层可见性切换** - 应切换图层可见性
- [ ] **图层锁定切换** - 应切换图层锁定状态
- [ ] **图层可标记切换** - 应切换图层可标记状态
- [ ] **图层属性编辑** - 应能编辑图层名称、描述等

#### 线型管理器测试
- [ ] **打开线型管理器** - 通过命令应能打开线型管理器
- [ ] **新建线型** - 应能创建新线型
- [ ] **删除线型** - 应能删除选中线型
- [ ] **编辑线型属性** - 应能编辑线型名称、描述、模式
- [ ] **预设线型模式** - 应能应用预设线型模式
- [ ] **线型预览** - 应能实时预览线型效果

### ✅ 第四阶段：系统设置测试

#### 系统设置窗口测试
- [ ] **打开系统设置** - 通过工具栏按钮应能打开系统设置
- [ ] **常规设置** - 应能修改界面语言、主题等
- [ ] **显示设置** - 应能修改背景颜色、网格设置等
- [ ] **绘图设置** - 应能修改默认线宽、颜色等
- [ ] **快捷键设置** - 应能查看和修改快捷键
- [ ] **性能设置** - 应能修改撤销步数、自动保存等

## 🔧 测试步骤

### 启动测试
1. 编译并运行McLaser.EditViewerSk项目
2. 确保主界面正常显示
3. 检查所有工具栏是否正确显示

### 功能测试
1. **逐一点击每个工具栏按钮**
   - 验证按钮响应
   - 检查命令是否正确执行
   - 确认无异常抛出

2. **测试设置窗口**
   - 打开每个设置窗口
   - 验证界面显示正确
   - 测试设置保存和应用

3. **测试状态同步**
   - 修改设置后检查状态栏显示
   - 验证UI状态与后端状态同步
   - 测试快捷键与UI按钮的一致性

### 集成测试
1. **命令执行测试**
   - 通过UI执行标注命令
   - 通过UI执行编辑命令
   - 验证命令参数正确传递

2. **状态管理测试**
   - 测试对象捕捉状态切换
   - 测试高级交互系统状态
   - 验证状态持久化

## ✅ 高优先级缺陷修复验证

### 颜色选择器系统测试
- [ ] **标注样式管理器颜色选择** - 点击文本颜色按钮应打开颜色选择器
- [ ] **图层管理颜色选择** - 点击图层颜色应打开颜色选择器
- [ ] **系统设置颜色选择** - 背景色、网格色、默认色按钮应正常工作
- [ ] **颜色选择器功能** - HSV滑块、RGB输入、预设颜色、最近使用颜色
- [ ] **颜色应用** - 选择的颜色应正确应用到相应组件

### 动态输入界面系统测试
- [ ] **动态输入显示** - 鼠标移动时应显示浮动输入界面
- [ ] **坐标输入模式** - 应能输入X、Y坐标值
- [ ] **距离输入模式** - 应能输入距离值
- [ ] **角度输入模式** - 应能输入角度值
- [ ] **极坐标输入模式** - 应能同时输入距离和角度
- [ ] **信息显示模式** - 应显示当前坐标、距离、角度信息
- [ ] **键盘导航** - Tab键切换、Enter确认、Escape取消应正常工作

### Phase2集成系统测试
- [ ] **极轴追踪设置** - 设置窗口的更改应实时影响绘图行为
- [ ] **对象捕捉设置** - 捕捉模式更改应立即生效
- [ ] **状态同步** - UI状态应与后端状态保持同步
- [ ] **设置持久化** - 设置应能正确保存和加载

## 🐛 常见问题排查

### 按钮无响应
1. 检查Command绑定是否正确
2. 验证UnifiedCommandManager是否正确初始化
3. 检查命令是否正确注册

### 颜色选择器问题
1. 检查ColorPickerDialog是否正确实例化
2. 验证颜色转换是否正确
3. 检查颜色应用逻辑

### 动态输入问题
1. 检查DynamicInputOverlay是否正确添加到Canvas
2. 验证鼠标事件是否正确绑定
3. 检查坐标转换是否正确

### 设置窗口无法打开
1. 检查窗口类是否正确实例化
2. 验证依赖项是否正确传递
3. 检查异常日志

### 状态不同步
1. 检查属性绑定是否正确
2. 验证PropertyChanged事件是否触发
3. 检查后端状态更新方法
4. 验证Phase2IntegrationManager连接

## 📊 测试结果记录

### 功能可访问性统计
- **标注功能**: ___/7 (___%) - 目标：100%
- **编辑命令**: ___/8 (___%) - 目标：100%
- **对象捕捉**: ___/9 (___%) - 目标：100%
- **高级交互**: ___/3 (___%) - 目标：100%
- **图层管理**: ___/7 (___%) - 目标：100%
- **线型管理**: ___/6 (___%) - 目标：100%
- **系统设置**: ___/6 (___%) - 目标：100%
- **颜色选择**: ___/4 (___%) - 目标：100% (新增)
- **动态输入**: ___/7 (___%) - 目标：100% (新增)
- **设置集成**: ___/4 (___%) - 目标：100% (新增)

### 总体评估
- **UI集成完整度**: ___% - 目标：≥95%
- **用户体验质量**: ___分 (1-10分) - 目标：≥9分
- **专业CAD标准符合度**: ___分 (1-10分) - 目标：≥9分
- **缺陷修复完成度**: ___% - 目标：100%

## ✅ 验收标准

### 必须达到的标准
- [ ] 所有后端功能都有UI访问方式 (100%)
- [ ] 所有工具栏按钮都能正常工作
- [ ] 所有设置窗口都能正常打开和使用
- [ ] UI状态与后端状态完全同步
- [ ] 无严重异常或崩溃

### 推荐达到的标准
- [ ] 用户体验评分 ≥ 8分
- [ ] 专业CAD标准符合度 ≥ 8分
- [ ] 响应时间 < 500ms
- [ ] 界面美观度良好

## 📝 测试报告模板

```
测试日期: ___________
测试人员: ___________
软件版本: ___________

测试结果:
- 通过项目: ___/46
- 失败项目: ___/46
- 总体通过率: ___%

主要问题:
1. ________________
2. ________________
3. ________________

改进建议:
1. ________________
2. ________________
3. ________________

结论: □ 通过验收 □ 需要改进 □ 重大问题
```
