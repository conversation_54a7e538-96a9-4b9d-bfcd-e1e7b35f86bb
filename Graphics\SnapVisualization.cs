using McLaser.EditViewerSk.Base;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Numerics;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 对象捕捉可视化系统
    /// 实现专业CAD级别的捕捉点视觉反馈
    /// </summary>
    public class SnapVisualization
    {
        private ViewBase _viewer;
        private SKPaint _markerPaint;
        private SKPaint _tooltipPaint;
        private SKPaint _tooltipBackgroundPaint;
        private SKPaint _magnetLinePaint;
        
        // 捕捉标记大小
        private const float MarkerSize = 8.0f;
        private const float TooltipFontSize = 12.0f;
        
        // 捕捉标记颜色配置
        private readonly Dictionary<ObjectSnapMode, SnapMarkerInfo> _markerStyles;
        
        public SnapVisualization(ViewBase viewer)
        {
            _viewer = viewer;
            InitializePaints();
            _markerStyles = InitializeMarkerStyles();
        }
        
        private void InitializePaints()
        {
            _markerPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2.0f,
                IsAntialias = true,
                Color = SKColors.Yellow
            };
            
            _tooltipPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                TextSize = TooltipFontSize,
                IsAntialias = true,
                Color = SKColors.Black,
                Typeface = SKTypeface.FromFamilyName("Arial")
            };
            
            _tooltipBackgroundPaint = new SKPaint
            {
                Style = SKPaintStyle.Fill,
                Color = SKColor.FromArgb(220, 255, 255, 224), // 半透明黄色背景
                IsAntialias = true
            };
            
            _magnetLinePaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColors.Green,
                PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0),
                IsAntialias = true
            };
        }
        
        private Dictionary<ObjectSnapMode, SnapMarkerInfo> InitializeMarkerStyles()
        {
            return new Dictionary<ObjectSnapMode, SnapMarkerInfo>
            {
                { ObjectSnapMode.End, new SnapMarkerInfo("端点", SKColors.Red, SnapMarkerShape.Square) },
                { ObjectSnapMode.Mid, new SnapMarkerInfo("中点", SKColors.Green, SnapMarkerShape.Triangle) },
                { ObjectSnapMode.Center, new SnapMarkerInfo("中心", SKColors.Blue, SnapMarkerShape.Circle) },
                { ObjectSnapMode.Intersection, new SnapMarkerInfo("交点", SKColors.Orange, SnapMarkerShape.Cross) },
                { ObjectSnapMode.Perpendicular, new SnapMarkerInfo("垂足", SKColors.Purple, SnapMarkerShape.Perpendicular) },
                { ObjectSnapMode.Tangent, new SnapMarkerInfo("切点", SKColors.Cyan, SnapMarkerShape.Tangent) },
                { ObjectSnapMode.Quad, new SnapMarkerInfo("象限", SKColors.Magenta, SnapMarkerShape.Diamond) },
                { ObjectSnapMode.Near, new SnapMarkerInfo("最近点", SKColors.Gray, SnapMarkerShape.Star) },
                { ObjectSnapMode.GeometricCenter, new SnapMarkerInfo("几何中心", SKColors.DarkBlue, SnapMarkerShape.Plus) },
                { ObjectSnapMode.Extension, new SnapMarkerInfo("延伸", SKColors.LightGreen, SnapMarkerShape.Extension) },
                { ObjectSnapMode.Parallel, new SnapMarkerInfo("平行", SKColors.Brown, SnapMarkerShape.Parallel) },
                { ObjectSnapMode.ApparentIntersection, new SnapMarkerInfo("虚交点", SKColors.Pink, SnapMarkerShape.ApparentCross) },
                { ObjectSnapMode.Node, new SnapMarkerInfo("节点", SKColors.DarkGreen, SnapMarkerShape.Node) },
                { ObjectSnapMode.Ins, new SnapMarkerInfo("插入点", SKColors.DarkRed, SnapMarkerShape.Insert) },
                { ObjectSnapMode.Grid, new SnapMarkerInfo("网格", SKColors.LightGray, SnapMarkerShape.Grid) }
            };
        }
        
        /// <summary>
        /// 渲染捕捉点标记和提示
        /// </summary>
        public void RenderSnapPoint(SKCanvas canvas, ObjectSnapPoint snapPoint, Vector2 canvasPosition)
        {
            if (snapPoint == null) return;
            
            var markerInfo = _markerStyles.GetValueOrDefault(snapPoint.type);
            if (markerInfo == null) return;
            
            // 设置标记颜色
            _markerPaint.Color = markerInfo.Color;
            
            // 绘制标记
            DrawMarker(canvas, canvasPosition, markerInfo.Shape);
            
            // 绘制工具提示
            DrawTooltip(canvas, canvasPosition, markerInfo.Name);
        }
        
        /// <summary>
        /// 绘制磁力线效果
        /// </summary>
        public void RenderMagnetEffect(SKCanvas canvas, Vector2 cursorPosition, Vector2 snapPosition)
        {
            if (Vector2.Distance(cursorPosition, snapPosition) < 50) // 在50像素范围内显示磁力线
            {
                canvas.DrawLine(cursorPosition.X, cursorPosition.Y, snapPosition.X, snapPosition.Y, _magnetLinePaint);
            }
        }
        
        /// <summary>
        /// 绘制追踪线
        /// </summary>
        public void RenderTrackingLines(SKCanvas canvas, List<Vector2> trackingPoints)
        {
            if (trackingPoints == null || trackingPoints.Count < 2) return;
            
            var trackingPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 1.0f,
                Color = SKColors.Green,
                PathEffect = SKPathEffect.CreateDash(new float[] { 3, 3 }, 0),
                IsAntialias = true
            };
            
            for (int i = 1; i < trackingPoints.Count; i++)
            {
                canvas.DrawLine(trackingPoints[i-1].X, trackingPoints[i-1].Y, 
                              trackingPoints[i].X, trackingPoints[i].Y, trackingPaint);
            }
            
            trackingPaint.Dispose();
        }
        
        private void DrawMarker(SKCanvas canvas, Vector2 position, SnapMarkerShape shape)
        {
            var centerX = position.X;
            var centerY = position.Y;
            var halfSize = MarkerSize / 2;
            
            switch (shape)
            {
                case SnapMarkerShape.Square:
                    canvas.DrawRect(centerX - halfSize, centerY - halfSize, MarkerSize, MarkerSize, _markerPaint);
                    break;
                    
                case SnapMarkerShape.Circle:
                    canvas.DrawCircle(centerX, centerY, halfSize, _markerPaint);
                    break;
                    
                case SnapMarkerShape.Triangle:
                    DrawTriangle(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Diamond:
                    DrawDiamond(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Cross:
                    DrawCross(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Plus:
                    DrawPlus(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Star:
                    DrawStar(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Perpendicular:
                    DrawPerpendicular(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Tangent:
                    DrawTangent(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Extension:
                    DrawExtension(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Parallel:
                    DrawParallel(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.ApparentCross:
                    DrawApparentCross(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Node:
                    DrawNode(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Insert:
                    DrawInsert(canvas, position, halfSize);
                    break;
                    
                case SnapMarkerShape.Grid:
                    DrawGrid(canvas, position, halfSize);
                    break;
            }
        }
        
        private void DrawTriangle(SKCanvas canvas, Vector2 position, float size)
        {
            var path = new SKPath();
            path.MoveTo(position.X, position.Y - size);
            path.LineTo(position.X - size, position.Y + size);
            path.LineTo(position.X + size, position.Y + size);
            path.Close();
            canvas.DrawPath(path, _markerPaint);
            path.Dispose();
        }
        
        private void DrawDiamond(SKCanvas canvas, Vector2 position, float size)
        {
            var path = new SKPath();
            path.MoveTo(position.X, position.Y - size);
            path.LineTo(position.X + size, position.Y);
            path.LineTo(position.X, position.Y + size);
            path.LineTo(position.X - size, position.Y);
            path.Close();
            canvas.DrawPath(path, _markerPaint);
            path.Dispose();
        }
        
        private void DrawCross(SKCanvas canvas, Vector2 position, float size)
        {
            canvas.DrawLine(position.X - size, position.Y - size, position.X + size, position.Y + size, _markerPaint);
            canvas.DrawLine(position.X - size, position.Y + size, position.X + size, position.Y - size, _markerPaint);
        }
        
        private void DrawPlus(SKCanvas canvas, Vector2 position, float size)
        {
            canvas.DrawLine(position.X - size, position.Y, position.X + size, position.Y, _markerPaint);
            canvas.DrawLine(position.X, position.Y - size, position.X, position.Y + size, _markerPaint);
        }
        
        private void DrawStar(SKCanvas canvas, Vector2 position, float size)
        {
            var path = new SKPath();
            for (int i = 0; i < 5; i++)
            {
                var angle = i * Math.PI * 2 / 5 - Math.PI / 2;
                var outerRadius = size;
                var innerRadius = size * 0.4f;
                
                var x1 = position.X + Math.Cos(angle) * outerRadius;
                var y1 = position.Y + Math.Sin(angle) * outerRadius;
                
                var innerAngle = angle + Math.PI / 5;
                var x2 = position.X + Math.Cos(innerAngle) * innerRadius;
                var y2 = position.Y + Math.Sin(innerAngle) * innerRadius;
                
                if (i == 0)
                    path.MoveTo((float)x1, (float)y1);
                else
                    path.LineTo((float)x1, (float)y1);
                
                path.LineTo((float)x2, (float)y2);
            }
            path.Close();
            canvas.DrawPath(path, _markerPaint);
            path.Dispose();
        }
        
        private void DrawPerpendicular(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制垂直符号
            canvas.DrawLine(position.X - size, position.Y + size, position.X + size, position.Y + size, _markerPaint);
            canvas.DrawLine(position.X + size, position.Y - size, position.X + size, position.Y + size, _markerPaint);
            canvas.DrawRect(position.X + size - 3, position.Y + size - 3, 3, 3, _markerPaint);
        }
        
        private void DrawTangent(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制切线符号：圆加切线
            canvas.DrawCircle(position.X, position.Y, size * 0.6f, _markerPaint);
            canvas.DrawLine(position.X - size, position.Y - size, position.X + size, position.Y - size, _markerPaint);
        }
        
        private void DrawExtension(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制延伸符号：虚线加箭头
            var dashPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = _markerPaint.StrokeWidth,
                Color = _markerPaint.Color,
                PathEffect = SKPathEffect.CreateDash(new float[] { 2, 2 }, 0),
                IsAntialias = true
            };
            
            canvas.DrawLine(position.X - size, position.Y, position.X + size, position.Y, dashPaint);
            
            // 箭头
            canvas.DrawLine(position.X + size - 3, position.Y - 2, position.X + size, position.Y, _markerPaint);
            canvas.DrawLine(position.X + size - 3, position.Y + 2, position.X + size, position.Y, _markerPaint);
            
            dashPaint.Dispose();
        }
        
        private void DrawParallel(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制平行符号：两条平行线
            canvas.DrawLine(position.X - size, position.Y - 2, position.X + size, position.Y - 2, _markerPaint);
            canvas.DrawLine(position.X - size, position.Y + 2, position.X + size, position.Y + 2, _markerPaint);
        }
        
        private void DrawApparentCross(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制虚交点：虚线交叉
            var dashPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = _markerPaint.StrokeWidth,
                Color = _markerPaint.Color,
                PathEffect = SKPathEffect.CreateDash(new float[] { 2, 2 }, 0),
                IsAntialias = true
            };
            
            canvas.DrawLine(position.X - size, position.Y - size, position.X + size, position.Y + size, dashPaint);
            canvas.DrawLine(position.X - size, position.Y + size, position.X + size, position.Y - size, dashPaint);
            
            dashPaint.Dispose();
        }
        
        private void DrawNode(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制节点：圆加十字
            canvas.DrawCircle(position.X, position.Y, size * 0.8f, _markerPaint);
            DrawPlus(canvas, position, size * 0.5f);
        }
        
        private void DrawInsert(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制插入点：两个错位的小正方形
            canvas.DrawRect(position.X - size, position.Y - size, size, size, _markerPaint);
            canvas.DrawRect(position.X - size/2, position.Y - size/2, size, size, _markerPaint);
        }
        
        private void DrawGrid(SKCanvas canvas, Vector2 position, float size)
        {
            // 绘制网格点：小正方形
            canvas.DrawRect(position.X - size/2, position.Y - size/2, size, size, _markerPaint);
        }
        
        private void DrawTooltip(SKCanvas canvas, Vector2 position, string text)
        {
            if (string.IsNullOrEmpty(text)) return;
            
            var bounds = new SKRect();
            _tooltipPaint.MeasureText(text, ref bounds);
            
            var tooltipX = position.X + 15;
            var tooltipY = position.Y - 15;
            var padding = 4;
            
            // 绘制背景
            var backgroundRect = new SKRect(
                tooltipX - padding,
                tooltipY - bounds.Height - padding,
                tooltipX + bounds.Width + padding,
                tooltipY + padding
            );
            
            canvas.DrawRoundRect(backgroundRect, 3, 3, _tooltipBackgroundPaint);
            
            // 绘制文本
            canvas.DrawText(text, tooltipX, tooltipY, _tooltipPaint);
        }
        
        public void Dispose()
        {
            _markerPaint?.Dispose();
            _tooltipPaint?.Dispose();
            _tooltipBackgroundPaint?.Dispose();
            _magnetLinePaint?.Dispose();
        }
    }
    
    /// <summary>
    /// 捕捉标记信息
    /// </summary>
    public class SnapMarkerInfo
    {
        public string Name { get; }
        public SKColor Color { get; }
        public SnapMarkerShape Shape { get; }
        
        public SnapMarkerInfo(string name, SKColor color, SnapMarkerShape shape)
        {
            Name = name;
            Color = color;
            Shape = shape;
        }
    }
    
    /// <summary>
    /// 捕捉标记形状
    /// </summary>
    public enum SnapMarkerShape
    {
        Square,
        Circle,
        Triangle,
        Diamond,
        Cross,
        Plus,
        Star,
        Perpendicular,
        Tangent,
        Extension,
        Parallel,
        ApparentCross,
        Node,
        Insert,
        Grid
    }
} 