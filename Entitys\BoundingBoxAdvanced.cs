using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.ComponentModel;
using Newtonsoft.Json;
using McLaser.EditViewerSk.Common;
using SkiaSharp;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 专业包围盒计算器
    /// 提供高精度的包围盒计算和优化算法
    /// </summary>
    public static class BoundingBoxCalculator
    {
        #region 常量
        private const double EPSILON = 1e-10;
        private const int ARC_SUBDIVISION_COUNT = 32; // 弧段细分数量
        #endregion

        #region 基础几何包围盒计算
        /// <summary>
        /// 计算点的包围盒
        /// </summary>
        public static BoundingBox CalculatePointBounds(Vector2 point, double pointSize = 1.0)
        {
            var halfSize = pointSize * 0.5;
            return new BoundingBox(
                point.X - halfSize, 
                point.Y + halfSize, 
                point.X + halfSize, 
                point.Y - halfSize
            );
        }

        /// <summary>
        /// 计算直线段的包围盒
        /// </summary>
        public static BoundingBox CalculateLineBounds(Vector2 startPoint, Vector2 endPoint, double lineWidth = 0.0)
        {
            var minX = Math.Min(startPoint.X, endPoint.X);
            var maxX = Math.Max(startPoint.X, endPoint.X);
            var minY = Math.Min(startPoint.Y, endPoint.Y);
            var maxY = Math.Max(startPoint.Y, endPoint.Y);

            // 考虑线宽
            var halfWidth = lineWidth * 0.5;
            return new BoundingBox(
                minX - halfWidth, 
                maxY + halfWidth, 
                maxX + halfWidth, 
                minY - halfWidth
            );
        }

        /// <summary>
        /// 计算圆形的包围盒
        /// </summary>
        public static BoundingBox CalculateCircleBounds(Vector2 center, double radius)
        {
            return new BoundingBox(
                center.X - radius,
                center.Y + radius,
                center.X + radius,
                center.Y - radius
            );
        }

        /// <summary>
        /// 计算椭圆的包围盒（考虑旋转）
        /// </summary>
        public static BoundingBox CalculateEllipseBounds(Vector2 center, double radiusX, double radiusY, double rotationDegrees = 0.0)
        {
            if (Math.Abs(rotationDegrees) < EPSILON)
            {
                // 未旋转的椭圆
                return new BoundingBox(
                    center.X - radiusX,
                    center.Y + radiusY,
                    center.X + radiusX,
                    center.Y - radiusY
                );
            }

            // 旋转椭圆的包围盒计算
            var rotationRad = rotationDegrees * Math.PI / 180.0;
            var cos = Math.Cos(rotationRad);
            var sin = Math.Sin(rotationRad);

            // 计算旋转后的轴向包围盒
            var ux = radiusX * cos;
            var uy = radiusX * sin;
            var vx = radiusY * -sin;
            var vy = radiusY * cos;

            var halfWidth = Math.Sqrt(ux * ux + vx * vx);
            var halfHeight = Math.Sqrt(uy * uy + vy * vy);

            return new BoundingBox(
                center.X - halfWidth,
                center.Y + halfHeight,
                center.X + halfWidth,
                center.Y - halfHeight
            );
        }

        /// <summary>
        /// 计算圆弧的包围盒
        /// </summary>
        public static BoundingBox CalculateArcBounds(Vector2 center, double radius, double startAngleDeg, double endAngleDeg)
        {
            var startAngle = startAngleDeg * Math.PI / 180.0;
            var endAngle = endAngleDeg * Math.PI / 180.0;

            // 弧的起点和终点
            var startPoint = center + radius * new Vector2((float)Math.Cos(startAngle), (float)Math.Sin(startAngle));
            var endPoint = center + radius * new Vector2((float)Math.Cos(endAngle), (float)Math.Sin(endAngle));

            var minX = Math.Min(startPoint.X, endPoint.X);
            var maxX = Math.Max(startPoint.X, endPoint.X);
            var minY = Math.Min(startPoint.Y, endPoint.Y);
            var maxY = Math.Max(startPoint.Y, endPoint.Y);

            // 检查是否包含象限点（0°, 90°, 180°, 270°）
            var normalizedStart = NormalizeAngle(startAngleDeg);
            var normalizedEnd = NormalizeAngle(endAngleDeg);
            
            if (normalizedEnd < normalizedStart)
                normalizedEnd += 360.0;

            var quadrantAngles = new[] { 0.0, 90.0, 180.0, 270.0 };
            
            foreach (var quadrantAngle in quadrantAngles)
            {
                if (IsAngleInArc(quadrantAngle, normalizedStart, normalizedEnd))
                {
                    var quadrantRad = quadrantAngle * Math.PI / 180.0;
                    var quadrantPoint = center + radius * new Vector2((float)Math.Cos(quadrantRad), (float)Math.Sin(quadrantRad));
                    
                    minX = Math.Min(minX, quadrantPoint.X);
                    maxX = Math.Max(maxX, quadrantPoint.X);
                    minY = Math.Min(minY, quadrantPoint.Y);
                    maxY = Math.Max(maxY, quadrantPoint.Y);
                }
            }

            return new BoundingBox(minX, maxY, maxX, minY);
        }

        /// <summary>
        /// 计算椭圆弧的包围盒
        /// </summary>
        public static BoundingBox CalculateEllipticalArcBounds(Vector2 center, double radiusX, double radiusY, 
            double startAngleDeg, double endAngleDeg, double rotationDegrees = 0.0)
        {
            // 对于椭圆弧，使用采样点方法
            var points = SampleEllipticalArc(center, radiusX, radiusY, startAngleDeg, endAngleDeg, rotationDegrees, ARC_SUBDIVISION_COUNT);
            return CalculatePointsBounds(points);
        }

        /// <summary>
        /// 计算多边形的包围盒
        /// </summary>
        public static BoundingBox CalculatePolygonBounds(IEnumerable<Vector2> vertices)
        {
            if (vertices == null || !vertices.Any())
                return BoundingBox.Empty;

            return CalculatePointsBounds(vertices);
        }

        /// <summary>
        /// 计算多段线的包围盒（支持弧段）
        /// </summary>
        public static BoundingBox CalculatePolylineBounds(IEnumerable<PolylineVertex> vertices, bool isClosed = false)
        {
            if (vertices == null || !vertices.Any())
                return BoundingBox.Empty;

            var vertexList = vertices.ToList();
            if (vertexList.Count < 2)
                return CalculatePointBounds(vertexList[0].Position);

            var bounds = BoundingBox.Empty;

            // 计算每个线段的包围盒
            for (int i = 0; i < vertexList.Count - 1; i++)
            {
                var segmentBounds = CalculatePolylineSegmentBounds(vertexList[i], vertexList[i + 1]);
                bounds = UnionBounds(bounds, segmentBounds);
            }

            // 闭合线段
            if (isClosed && vertexList.Count > 2)
            {
                var segmentBounds = CalculatePolylineSegmentBounds(vertexList[vertexList.Count - 1], vertexList[0]);
                bounds = UnionBounds(bounds, segmentBounds);
            }

            return bounds;
        }

        /// <summary>
        /// 计算贝塞尔曲线的包围盒
        /// </summary>
        public static BoundingBox CalculateBezierBounds(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3)
        {
            // 三次贝塞尔曲线的包围盒计算
            var bounds = new BoundingBox(
                Math.Min(p0.X, p3.X),
                Math.Max(p0.Y, p3.Y),
                Math.Max(p0.X, p3.X),
                Math.Min(p0.Y, p3.Y)
            );

            // 计算导数零点
            var extremaX = FindBezierExtrema(p0.X, p1.X, p2.X, p3.X);
            var extremaY = FindBezierExtrema(p0.Y, p1.Y, p2.Y, p3.Y);

            foreach (var t in extremaX.Concat(extremaY).Where(t => t >= 0 && t <= 1))
            {
                var point = EvaluateBezier(p0, p1, p2, p3, t);
                bounds.Union(point);
            }

            return bounds;
        }

        /// <summary>
        /// 计算文本的包围盒
        /// </summary>
        public static BoundingBox CalculateTextBounds(string text, Vector2 position, SKPaint textPaint)
        {
            if (string.IsNullOrEmpty(text) || textPaint == null)
                return CalculatePointBounds(position);

            var bounds = new SKRect();
            textPaint.MeasureText(text, ref bounds);

            return new BoundingBox(
                position.X + bounds.Left,
                position.Y + bounds.Top,
                position.X + bounds.Right,
                position.Y + bounds.Bottom
            );
        }
        #endregion

        #region 复合包围盒操作
        /// <summary>
        /// 合并两个包围盒
        /// </summary>
        public static BoundingBox UnionBounds(BoundingBox bounds1, BoundingBox bounds2)
        {
            if (bounds1.IsEmpty) return bounds2;
            if (bounds2.IsEmpty) return bounds1;

            return new BoundingBox(
                Math.Min(bounds1.Left, bounds2.Left),
                Math.Max(bounds1.Top, bounds2.Top),
                Math.Max(bounds1.Right, bounds2.Right),
                Math.Min(bounds1.Bottom, bounds2.Bottom)
            );
        }

        /// <summary>
        /// 合并多个包围盒
        /// </summary>
        public static BoundingBox UnionBounds(IEnumerable<BoundingBox> boundsList)
        {
            if (boundsList == null || !boundsList.Any())
                return BoundingBox.Empty;

            return boundsList.Aggregate(BoundingBox.Empty, UnionBounds);
        }

        /// <summary>
        /// 扩展包围盒
        /// </summary>
        public static BoundingBox ExpandBounds(BoundingBox bounds, double margin)
        {
            if (bounds.IsEmpty) return bounds;

            return new BoundingBox(
                bounds.Left - margin,
                bounds.Top + margin,
                bounds.Right + margin,
                bounds.Bottom - margin
            );
        }

        /// <summary>
        /// 变换包围盒
        /// </summary>
        public static BoundingBox TransformBounds(BoundingBox bounds, SKMatrix transform)
        {
            if (bounds.IsEmpty) return bounds;

            var corners = new[]
            {
                new SKPoint((float)bounds.Left, (float)bounds.Top),
                new SKPoint((float)bounds.Right, (float)bounds.Top),
                new SKPoint((float)bounds.Right, (float)bounds.Bottom),
                new SKPoint((float)bounds.Left, (float)bounds.Bottom)
            };

            transform.MapPoints(corners);

            var minX = corners.Min(p => p.X);
            var maxX = corners.Max(p => p.X);
            var minY = corners.Min(p => p.Y);
            var maxY = corners.Max(p => p.Y);

            return new BoundingBox(minX, maxY, maxX, minY);
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 计算点集的包围盒
        /// </summary>
        private static BoundingBox CalculatePointsBounds(IEnumerable<Vector2> points)
        {
            var pointsList = points.ToList();
            if (!pointsList.Any())
                return BoundingBox.Empty;

            var minX = pointsList.Min(p => p.X);
            var maxX = pointsList.Max(p => p.X);
            var minY = pointsList.Min(p => p.Y);
            var maxY = pointsList.Max(p => p.Y);

            return new BoundingBox(minX, maxY, maxX, minY);
        }

        /// <summary>
        /// 计算多段线线段的包围盒
        /// </summary>
        private static BoundingBox CalculatePolylineSegmentBounds(PolylineVertex startVertex, PolylineVertex endVertex)
        {
            if (!startVertex.IsArc)
            {
                // 直线段
                return CalculateLineBounds(startVertex.Position, endVertex.Position);
            }
            else
            {
                // 弧段，基于bulge值计算
                return CalculateArcBoundsFromBulge(startVertex, endVertex);
            }
        }

        /// <summary>
        /// 基于Bulge值计算弧段包围盒
        /// </summary>
        private static BoundingBox CalculateArcBoundsFromBulge(PolylineVertex startVertex, PolylineVertex endVertex)
        {
            var p1 = startVertex.Position;
            var p2 = endVertex.Position;
            var bulge = startVertex.Bulge;

            if (Math.Abs(bulge) < EPSILON)
            {
                // 实际上是直线段
                return CalculateLineBounds(p1, p2);
            }

            // 计算弧的参数
            var chordLength = Vector2.Distance(p1, p2);
            var sagitta = Math.Abs(bulge) * chordLength * 0.5;
            var radius = (chordLength * chordLength * 0.25 + sagitta * sagitta) / (2.0 * sagitta);

            // 弦中点
            var midPoint = (p1 + p2) * 0.5f;

            // 弦方向向量
            var chordVector = p2 - p1;
            var chordAngle = Math.Atan2(chordVector.Y, chordVector.X);

            // 垂直于弦的方向（指向圆心）
            var perpAngle = chordAngle + (bulge > 0 ? Math.PI * 0.5 : -Math.PI * 0.5);
            var distanceToCenter = radius - sagitta;

            // 圆心位置
            var center = midPoint + (float)distanceToCenter * new Vector2((float)Math.Cos(perpAngle), (float)Math.Sin(perpAngle));

            // 计算弧的角度范围
            var startAngle = Math.Atan2(p1.Y - center.Y, p1.X - center.X) * 180.0 / Math.PI;
            var endAngle = Math.Atan2(p2.Y - center.Y, p2.X - center.X) * 180.0 / Math.PI;

            return CalculateArcBounds(center, radius, startAngle, endAngle);
        }

        /// <summary>
        /// 采样椭圆弧点
        /// </summary>
        private static List<Vector2> SampleEllipticalArc(Vector2 center, double radiusX, double radiusY, 
            double startAngleDeg, double endAngleDeg, double rotationDeg, int sampleCount)
        {
            var points = new List<Vector2>();
            var startAngle = startAngleDeg * Math.PI / 180.0;
            var endAngle = endAngleDeg * Math.PI / 180.0;
            var rotation = rotationDeg * Math.PI / 180.0;

            var angleStep = (endAngle - startAngle) / sampleCount;

            for (int i = 0; i <= sampleCount; i++)
            {
                var angle = startAngle + i * angleStep;
                
                // 椭圆参数方程
                var x = radiusX * Math.Cos(angle);
                var y = radiusY * Math.Sin(angle);

                // 应用旋转
                if (Math.Abs(rotation) > EPSILON)
                {
                    var cosRot = Math.Cos(rotation);
                    var sinRot = Math.Sin(rotation);
                    var rotatedX = x * cosRot - y * sinRot;
                    var rotatedY = x * sinRot + y * cosRot;
                    x = rotatedX;
                    y = rotatedY;
                }

                points.Add(new Vector2((float)(center.X + x), (float)(center.Y + y)));
            }

            return points;
        }

        /// <summary>
        /// 查找贝塞尔曲线的极值点
        /// </summary>
        private static List<double> FindBezierExtrema(double p0, double p1, double p2, double p3)
        {
            var extrema = new List<double>();

            // 一阶导数系数
            var a = 3 * (-p0 + 3 * p1 - 3 * p2 + p3);
            var b = 6 * (p0 - 2 * p1 + p2);
            var c = 3 * (-p0 + p1);

            if (Math.Abs(a) < EPSILON)
            {
                // 退化为二次方程
                if (Math.Abs(b) > EPSILON)
                {
                    var t = -c / b;
                    if (t >= 0 && t <= 1)
                        extrema.Add(t);
                }
            }
            else
            {
                // 二次方程求解
                var discriminant = b * b - 4 * a * c;
                if (discriminant >= 0)
                {
                    var sqrtD = Math.Sqrt(discriminant);
                    var t1 = (-b + sqrtD) / (2 * a);
                    var t2 = (-b - sqrtD) / (2 * a);

                    if (t1 >= 0 && t1 <= 1) extrema.Add(t1);
                    if (t2 >= 0 && t2 <= 1) extrema.Add(t2);
                }
            }

            return extrema;
        }

        /// <summary>
        /// 计算贝塞尔曲线上的点
        /// </summary>
        private static Vector2 EvaluateBezier(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, double t)
        {
            var u = 1.0 - t;
            var tt = t * t;
            var uu = u * u;
            var uuu = uu * u;
            var ttt = tt * t;

            var x = uuu * p0.X + 3 * uu * t * p1.X + 3 * u * tt * p2.X + ttt * p3.X;
            var y = uuu * p0.Y + 3 * uu * t * p1.Y + 3 * u * tt * p2.Y + ttt * p3.Y;

            return new Vector2((float)x, (float)y);
        }

        /// <summary>
        /// 标准化角度到[0, 360]范围
        /// </summary>
        private static double NormalizeAngle(double angleDeg)
        {
            while (angleDeg < 0) angleDeg += 360.0;
            while (angleDeg >= 360.0) angleDeg -= 360.0;
            return angleDeg;
        }

        /// <summary>
        /// 检查角度是否在弧的范围内
        /// </summary>
        private static bool IsAngleInArc(double angleDeg, double startAngleDeg, double endAngleDeg)
        {
            var angle = NormalizeAngle(angleDeg);
            var start = NormalizeAngle(startAngleDeg);
            var end = NormalizeAngle(endAngleDeg);

            if (end < start)
                end += 360.0;

            if (angle < start)
                angle += 360.0;

            return angle >= start && angle <= end;
        }
        #endregion
    }

    /// <summary>
    /// 包围盒扩展方法
    /// </summary>
    public static class BoundingBoxExtensions
    {
        /// <summary>
        /// 使用高精度计算器更新包围盒
        /// </summary>
        public static void UpdateBounds(this BoundingBox boundingBox, EntityBase entity)
        {
            if (entity == null) return;

            BoundingBox newBounds = BoundingBox.Empty;

            switch (entity)
            {
                case EntityLine line:
                    newBounds = BoundingBoxCalculator.CalculateLineBounds(line.StartPoint, line.EndPoint);
                    break;
                case EntityCircle circle:
                    newBounds = BoundingBoxCalculator.CalculateCircleBounds(circle.Center, circle.Radius);
                    break;
                case EntityEllipse ellipse:
                    newBounds = BoundingBoxCalculator.CalculateEllipseBounds(ellipse.Center, ellipse.RadiusX, ellipse.RadiusY, ellipse.Rotation);
                    break;
                case EntityPolygon polygon:
                    newBounds = BoundingBoxCalculator.CalculatePolygonBounds(polygon.Vertices);
                    break;
                case EntityPolylineAdvanced polyline:
                    newBounds = BoundingBoxCalculator.CalculatePolylineBounds(polyline.Vertices, polyline.IsClosed);
                    break;
                case EntityPoint point:
                    // 假设EntityPoint有Position属性
                    // newBounds = BoundingBoxCalculator.CalculatePointBounds(point.Position);
                    break;
            }

            if (!newBounds.IsEmpty)
            {
                boundingBox.Left = newBounds.Left;
                boundingBox.Right = newBounds.Right;
                boundingBox.Top = newBounds.Top;
                boundingBox.Bottom = newBounds.Bottom;
                boundingBox.IsEmpty = false;
            }
        }

        /// <summary>
        /// 检查包围盒是否与视口相交
        /// </summary>
        public static bool IntersectsViewport(this BoundingBox boundingBox, BoundingBox viewport, double tolerance = 0.0)
        {
            if (boundingBox.IsEmpty || viewport.IsEmpty) return false;

            return !(boundingBox.Right + tolerance < viewport.Left ||
                     boundingBox.Left - tolerance > viewport.Right ||
                     boundingBox.Top + tolerance < viewport.Bottom ||
                     boundingBox.Bottom - tolerance > viewport.Top);
        }

        /// <summary>
        /// 计算包围盒的对角线长度
        /// </summary>
        public static double GetDiagonalLength(this BoundingBox boundingBox)
        {
            if (boundingBox.IsEmpty) return 0;
            return Math.Sqrt(boundingBox.Width * boundingBox.Width + boundingBox.Height * boundingBox.Height);
        }

        /// <summary>
        /// 获取包围盒的面积
        /// </summary>
        public static double GetArea(this BoundingBox boundingBox)
        {
            if (boundingBox.IsEmpty) return 0;
            return boundingBox.Width * boundingBox.Height;
        }

        /// <summary>
        /// 检查点是否在包围盒内（考虑容差）
        /// </summary>
        public static bool ContainsPoint(this BoundingBox boundingBox, Vector2 point, double tolerance = 0.0)
        {
            if (boundingBox.IsEmpty) return false;

            return point.X >= boundingBox.Left - tolerance &&
                   point.X <= boundingBox.Right + tolerance &&
                   point.Y >= boundingBox.Bottom - tolerance &&
                   point.Y <= boundingBox.Top + tolerance;
        }
    }
} 