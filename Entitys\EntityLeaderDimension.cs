using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Numerics;
using PropertyTools;
using System.Linq;

namespace McLaser.EditViewerSk.Entitys
{
    /// <summary>
    /// 引线标注实体
    /// </summary>
    [Browsable(true)]
    [DisplayName("引线标注")]
    public class EntityLeaderDimension : EntityDimension
    {
        private List<Vector2> leaderPoints = new List<Vector2>();
        private string annotationText = "";
        private bool showArrowhead = true;
        private bool useSpline = false;

        #region 构造函数

        public EntityLeaderDimension() : base(DimensionType.Leader)
        {
            leaderPoints.Add(Vector2.Zero);
            leaderPoints.Add(new Vector2(10, 0));
            leaderPoints.Add(new Vector2(15, 5));
            annotationText = "注释文本";
            Update();
        }

        public EntityLeaderDimension(IEnumerable<Vector2> points, string text) : base(DimensionType.Leader)
        {
            leaderPoints.AddRange(points);
            annotationText = text ?? "";
            
            // 确保至少有两个点
            if (leaderPoints.Count < 2)
            {
                leaderPoints.Clear();
                leaderPoints.Add(Vector2.Zero);
                leaderPoints.Add(new Vector2(10, 5));
            }
            
            Update();
        }

        #endregion

        #region 属性

        [Category("几何")]
        [DisplayName("引线点")]
        public List<Vector2> LeaderPoints
        {
            get => leaderPoints;
            set
            {
                if (value != null && value.Count >= 2)
                {
                    leaderPoints = new List<Vector2>(value);
                    OnPropertyChanged(nameof(LeaderPoints));
                    Update();
                }
            }
        }

        [Category("文本")]
        [DisplayName("注释文本")]
        public string AnnotationText
        {
            get => annotationText;
            set
            {
                if (annotationText != value)
                {
                    annotationText = value ?? "";
                    OnPropertyChanged(nameof(AnnotationText));
                    InvalidateVisual();
                }
            }
        }

        [Category("显示")]
        [DisplayName("显示箭头")]
        public bool ShowArrowhead
        {
            get => showArrowhead;
            set
            {
                if (showArrowhead != value)
                {
                    showArrowhead = value;
                    OnPropertyChanged(nameof(ShowArrowhead));
                    InvalidateVisual();
                }
            }
        }

        [Category("显示")]
        [DisplayName("使用样条线")]
        public bool UseSpline
        {
            get => useSpline;
            set
            {
                if (useSpline != value)
                {
                    useSpline = value;
                    OnPropertyChanged(nameof(UseSpline));
                    InvalidateVisual();
                }
            }
        }

        [Category("几何")]
        [DisplayName("引线长度")]
        [ReadOnly(true)]
        public double TotalLength
        {
            get
            {
                double length = 0;
                for (int i = 0; i < leaderPoints.Count - 1; i++)
                {
                    length += Vector2.Distance(leaderPoints[i], leaderPoints[i + 1]);
                }
                return length;
            }
        }

        [Category("几何")]
        [DisplayName("起点")]
        public Vector2 StartPoint
        {
            get => leaderPoints.Count > 0 ? leaderPoints[0] : Vector2.Zero;
            set
            {
                if (leaderPoints.Count > 0 && leaderPoints[0] != value)
                {
                    leaderPoints[0] = value;
                    OnPropertyChanged(nameof(StartPoint));
                    Update();
                }
            }
        }

        [Category("几何")]
        [DisplayName("终点")]
        public Vector2 EndPoint
        {
            get => leaderPoints.Count > 0 ? leaderPoints[leaderPoints.Count - 1] : Vector2.Zero;
            set
            {
                if (leaderPoints.Count > 0 && leaderPoints[leaderPoints.Count - 1] != value)
                {
                    leaderPoints[leaderPoints.Count - 1] = value;
                    OnPropertyChanged(nameof(EndPoint));
                    Update();
                }
            }
        }

        #endregion

        #region 重写方法

        protected override void CalculateMeasurement()
        {
            ActualMeasurement = TotalLength;
        }

        public override DimensionGeometry GetDimensionGeometry()
        {
            var geometry = new DimensionGeometry();

            if (leaderPoints.Count < 2) return geometry;

            if (useSpline && leaderPoints.Count > 2)
            {
                // 生成样条曲线点
                var splinePoints = GenerateSplinePoints(leaderPoints, 20);
                for (int i = 0; i < splinePoints.Length - 1; i++)
                {
                    geometry.ExtensionLines.Add(new LineGeometry(splinePoints[i], splinePoints[i + 1]));
                }
            }
            else
            {
                // 生成直线段
                for (int i = 0; i < leaderPoints.Count - 1; i++)
                {
                    geometry.ExtensionLines.Add(new LineGeometry(leaderPoints[i], leaderPoints[i + 1]));
                }
            }

            // 添加箭头（在起点）
            if (showArrowhead && leaderPoints.Count >= 2)
            {
                var direction = Vector2.Normalize(leaderPoints[1] - leaderPoints[0]);
                geometry.Arrows.Add(new ArrowGeometry(leaderPoints[0], direction, Style.ArrowSize));
            }

            // 设置文本位置（在终点附近）
            if (leaderPoints.Count > 0)
            {
                var lastPoint = leaderPoints[leaderPoints.Count - 1];
                // 偏移文本以避免与引线重叠
                geometry.TextPosition = lastPoint + new Vector2(Style.TextHeight * 0.5f, Style.TextHeight * 0.5f);
            }

            return geometry;
        }

        protected override string GetDisplayText()
        {
            return !string.IsNullOrEmpty(annotationText) ? annotationText : base.GetDisplayText();
        }

        public override EntityBase Clone()
        {
            var clone = new EntityLeaderDimension(leaderPoints, annotationText)
            {
                ShowArrowhead = showArrowhead,
                UseSpline = useSpline
            };
            CopyPropertiesTo(clone);
            return clone;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加引线点
        /// </summary>
        public void AddPoint(Vector2 point)
        {
            leaderPoints.Add(point);
            OnPropertyChanged(nameof(LeaderPoints));
            Update();
        }

        /// <summary>
        /// 插入引线点
        /// </summary>
        public void InsertPoint(int index, Vector2 point)
        {
            if (index >= 0 && index <= leaderPoints.Count)
            {
                leaderPoints.Insert(index, point);
                OnPropertyChanged(nameof(LeaderPoints));
                Update();
            }
        }

        /// <summary>
        /// 移除引线点
        /// </summary>
        public bool RemovePoint(int index)
        {
            if (index >= 0 && index < leaderPoints.Count && leaderPoints.Count > 2)
            {
                leaderPoints.RemoveAt(index);
                OnPropertyChanged(nameof(LeaderPoints));
                Update();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 移动引线点
        /// </summary>
        public void MovePoint(int index, Vector2 newPosition)
        {
            if (index >= 0 && index < leaderPoints.Count)
            {
                leaderPoints[index] = newPosition;
                OnPropertyChanged(nameof(LeaderPoints));
                Update();
            }
        }

        /// <summary>
        /// 简化引线路径（移除共线点）
        /// </summary>
        public void SimplifyPath(double tolerance = 1e-6)
        {
            if (leaderPoints.Count <= 2) return;

            var simplified = new List<Vector2> { leaderPoints[0] };

            for (int i = 1; i < leaderPoints.Count - 1; i++)
            {
                var prev = leaderPoints[i - 1];
                var current = leaderPoints[i];
                var next = leaderPoints[i + 1];

                // 检查是否共线
                if (!AreCollinear(prev, current, next, tolerance))
                {
                    simplified.Add(current);
                }
            }

            simplified.Add(leaderPoints[leaderPoints.Count - 1]);

            if (simplified.Count != leaderPoints.Count)
            {
                leaderPoints = simplified;
                OnPropertyChanged(nameof(LeaderPoints));
                Update();
            }
        }

        #endregion

        #region 私有方法

        private Vector2[] GenerateSplinePoints(List<Vector2> controlPoints, int segments)
        {
            if (controlPoints.Count < 3)
            {
                return controlPoints.ToArray();
            }

            // 简单的Catmull-Rom样条实现
            var points = new List<Vector2>();
            
            for (int i = 0; i < controlPoints.Count - 1; i++)
            {
                var p0 = i > 0 ? controlPoints[i - 1] : controlPoints[i];
                var p1 = controlPoints[i];
                var p2 = controlPoints[i + 1];
                var p3 = i < controlPoints.Count - 2 ? controlPoints[i + 2] : controlPoints[i + 1];

                for (int j = 0; j < segments; j++)
                {
                    var t = (float)j / segments;
                    var point = CatmullRom(p0, p1, p2, p3, t);
                    points.Add(point);
                }
            }

            points.Add(controlPoints[controlPoints.Count - 1]);
            return points.ToArray();
        }

        private Vector2 CatmullRom(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            var t2 = t * t;
            var t3 = t2 * t;

            return 0.5f * (
                (2.0f * p1) +
                (-p0 + p2) * t +
                (2.0f * p0 - 5.0f * p1 + 4.0f * p2 - p3) * t2 +
                (-p0 + 3.0f * p1 - 3.0f * p2 + p3) * t3
            );
        }

        private bool AreCollinear(Vector2 p1, Vector2 p2, Vector2 p3, double tolerance)
        {
            // 使用叉积判断是否共线
            var cross = (p2.X - p1.X) * (p3.Y - p1.Y) - (p2.Y - p1.Y) * (p3.X - p1.X);
            return Math.Abs(cross) < tolerance;
        }

        #endregion

        #region 工厂方法

        /// <summary>
        /// 创建简单的两点引线
        /// </summary>
        public static EntityLeaderDimension CreateSimple(Vector2 start, Vector2 end, string text)
        {
            return new EntityLeaderDimension(new[] { start, end }, text);
        }

        /// <summary>
        /// 创建带肘部的引线
        /// </summary>
        public static EntityLeaderDimension CreateWithElbow(Vector2 start, Vector2 elbow, Vector2 end, string text)
        {
            return new EntityLeaderDimension(new[] { start, elbow, end }, text);
        }

        /// <summary>
        /// 从现有实体创建引线
        /// </summary>
        public static EntityLeaderDimension CreateFromEntity(EntityBase entity, Vector2 leaderEnd, string text)
        {
            var center = entity.BoundingBox?.Center ?? Vector2.Zero;
            return CreateSimple(center, leaderEnd, text);
        }

        #endregion
    }
} 