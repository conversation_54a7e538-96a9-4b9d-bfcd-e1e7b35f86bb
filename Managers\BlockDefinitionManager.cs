using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;

namespace McLaser.EditViewerSk.Managers
{
    /// <summary>
    /// 块定义管理器
    /// 负责管理块的定义、创建、编辑和删除等操作
    /// </summary>
    public class BlockDefinitionManager : ObservableObject
    {
        #region 私有字段

        private ObservableCollection<BlockDefinition> _blockDefinitions;
        private BlockDefinition _currentBlockDefinition;
        private DocumentBase _document;
        private readonly Dictionary<string, List<BlockReference>> _blockReferences;

        #endregion

        #region 事件

        /// <summary>
        /// 块定义添加事件
        /// </summary>
        public event EventHandler<BlockDefinitionEventArgs> BlockDefinitionAdded;

        /// <summary>
        /// 块定义删除事件
        /// </summary>
        public event EventHandler<BlockDefinitionEventArgs> BlockDefinitionRemoved;

        /// <summary>
        /// 块定义修改事件
        /// </summary>
        public event EventHandler<BlockDefinitionEventArgs> BlockDefinitionModified;

        /// <summary>
        /// 当前块定义改变事件
        /// </summary>
        public event EventHandler<BlockDefinitionEventArgs> CurrentBlockDefinitionChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化块定义管理器
        /// </summary>
        /// <param name="document">文档对象</param>
        public BlockDefinitionManager(DocumentBase document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _blockDefinitions = new ObservableCollection<BlockDefinition>();
            _blockReferences = new Dictionary<string, List<BlockReference>>();

            CreateStandardBlocks();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 块定义集合
        /// </summary>
        public ObservableCollection<BlockDefinition> BlockDefinitions
        {
            get => _blockDefinitions;
            private set => SetProperty(ref _blockDefinitions, value);
        }

        /// <summary>
        /// 当前块定义
        /// </summary>
        public BlockDefinition CurrentBlockDefinition
        {
            get => _currentBlockDefinition;
            set
            {
                if (SetProperty(ref _currentBlockDefinition, value))
                {
                    OnCurrentBlockDefinitionChanged(value);
                }
            }
        }

        /// <summary>
        /// 块定义数量
        /// </summary>
        public int BlockDefinitionCount => _blockDefinitions.Count;

        #endregion

        #region 块定义操作方法

        /// <summary>
        /// 创建块定义
        /// </summary>
        /// <param name="name">块名称</param>
        /// <param name="basePoint">基点</param>
        /// <param name="entities">实体集合</param>
        /// <param name="description">描述</param>
        /// <returns>创建的块定义</returns>
        public BlockDefinition CreateBlockDefinition(string name, Vector2 basePoint, 
            IEnumerable<EntityBase> entities, string description = "")
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("块名称不能为空", nameof(name));

            if (BlockDefinitionExists(name))
                throw new InvalidOperationException($"块定义 '{name}' 已存在");

            var blockDef = new BlockDefinition
            {
                Name = name,
                Description = description,
                BasePoint = basePoint,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                IsEditable = true
            };

            // 添加实体到块定义（检查循环引用）
            if (entities != null)
            {
                foreach (var entity in entities)
                {
                    // 检查循环引用
                    if (HasCircularReference(entity, name))
                    {
                        throw new InvalidOperationException($"检测到循环引用：实体引用了正在创建的块定义 '{name}'");
                    }

                    var clonedEntity = entity.Clone() as EntityBase;
                    if (clonedEntity != null)
                    {
                        blockDef.Entities.Add(clonedEntity);
                    }
                }
            }

            // 计算边界框
            blockDef.RecalculateBounds();

            _blockDefinitions.Add(blockDef);
            _blockReferences[name] = new List<BlockReference>();

            OnBlockDefinitionAdded(blockDef);
            return blockDef;
        }

        /// <summary>
        /// 从选中的实体创建块定义
        /// </summary>
        /// <param name="name">块名称</param>
        /// <param name="basePoint">基点</param>
        /// <param name="selectedEntities">选中的实体</param>
        /// <param name="deleteOriginal">是否删除原始实体</param>
        /// <param name="description">描述</param>
        /// <returns>创建的块定义</returns>
        public BlockDefinition CreateBlockFromSelection(string name, Vector2 basePoint, 
            IEnumerable<EntityBase> selectedEntities, bool deleteOriginal = false, string description = "")
        {
            var blockDef = CreateBlockDefinition(name, basePoint, selectedEntities, description);

            if (deleteOriginal && selectedEntities != null)
            {
                // 删除原始实体的逻辑需要与文档编辑器配合
                // 这里只是标记，实际删除由调用方处理
                foreach (var entity in selectedEntities)
                {
                    entity.Tag = "ToBeDeleted";
                }
            }

            return blockDef;
        }

        /// <summary>
        /// 编辑块定义
        /// </summary>
        /// <param name="blockDefinition">要编辑的块定义</param>
        /// <param name="newEntities">新的实体集合</param>
        /// <param name="newBasePoint">新的基点</param>
        /// <returns>是否编辑成功</returns>
        public bool EditBlockDefinition(BlockDefinition blockDefinition, 
            IEnumerable<EntityBase> newEntities = null, Vector2? newBasePoint = null)
        {
            if (blockDefinition == null || !blockDefinition.IsEditable)
                return false;

            // 更新基点
            if (newBasePoint.HasValue)
            {
                blockDefinition.BasePoint = newBasePoint.Value;
            }

            // 更新实体
            if (newEntities != null)
            {
                blockDefinition.Entities.Clear();
                foreach (var entity in newEntities)
                {
                    var clonedEntity = entity.Clone() as EntityBase;
                    if (clonedEntity != null)
                    {
                        blockDefinition.Entities.Add(clonedEntity);
                    }
                }
            }

            blockDefinition.ModifiedDate = DateTime.Now;
            blockDefinition.RecalculateBounds();

            // 更新所有引用此块定义的块引用
            UpdateBlockReferences(blockDefinition.Name);

            OnBlockDefinitionModified(blockDefinition);
            return true;
        }

        /// <summary>
        /// 删除块定义
        /// </summary>
        /// <param name="blockDefinition">要删除的块定义</param>
        /// <param name="forceDelete">是否强制删除（即使有引用）</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteBlockDefinition(BlockDefinition blockDefinition, bool forceDelete = false)
        {
            if (blockDefinition == null)
                return false;

            if (blockDefinition.IsSystem && !forceDelete)
                throw new InvalidOperationException("不能删除系统块定义");

            // 检查是否有引用
            var referenceCount = GetReferenceCount(blockDefinition.Name);
            if (referenceCount > 0 && !forceDelete)
            {
                throw new InvalidOperationException($"块定义 '{blockDefinition.Name}' 有 {referenceCount} 个引用，无法删除");
            }

            // 删除所有引用
            if (forceDelete && _blockReferences.ContainsKey(blockDefinition.Name))
            {
                _blockReferences[blockDefinition.Name].Clear();
            }

            _blockDefinitions.Remove(blockDefinition);
            _blockReferences.Remove(blockDefinition.Name);

            OnBlockDefinitionRemoved(blockDefinition);
            return true;
        }

        /// <summary>
        /// 重命名块定义
        /// </summary>
        /// <param name="blockDefinition">块定义</param>
        /// <param name="newName">新名称</param>
        /// <returns>是否重命名成功</returns>
        public bool RenameBlockDefinition(BlockDefinition blockDefinition, string newName)
        {
            if (blockDefinition == null || string.IsNullOrWhiteSpace(newName))
                return false;

            if (BlockDefinitionExists(newName))
                throw new InvalidOperationException($"块名称 '{newName}' 已存在");

            var oldName = blockDefinition.Name;
            blockDefinition.Name = newName;
            blockDefinition.ModifiedDate = DateTime.Now;

            // 更新引用字典
            if (_blockReferences.ContainsKey(oldName))
            {
                _blockReferences[newName] = _blockReferences[oldName];
                _blockReferences.Remove(oldName);

                // 更新所有块引用的名称
                foreach (var reference in _blockReferences[newName])
                {
                    reference.BlockDefinitionName = newName;
                }
            }

            OnBlockDefinitionModified(blockDefinition);
            return true;
        }

        /// <summary>
        /// 复制块定义
        /// </summary>
        /// <param name="sourceBlock">源块定义</param>
        /// <param name="newName">新名称</param>
        /// <returns>复制的块定义</returns>
        public BlockDefinition CopyBlockDefinition(BlockDefinition sourceBlock, string newName)
        {
            if (sourceBlock == null || string.IsNullOrWhiteSpace(newName))
                return null;

            if (BlockDefinitionExists(newName))
                throw new InvalidOperationException($"块名称 '{newName}' 已存在");

            var copiedBlock = new BlockDefinition
            {
                Name = newName,
                Description = sourceBlock.Description + " (副本)",
                BasePoint = sourceBlock.BasePoint,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                IsEditable = true,
                IsSystem = false
            };

            // 复制实体
            foreach (var entity in sourceBlock.Entities)
            {
                var clonedEntity = entity.Clone() as EntityBase;
                if (clonedEntity != null)
                {
                    copiedBlock.Entities.Add(clonedEntity);
                }
            }

            copiedBlock.RecalculateBounds();

            _blockDefinitions.Add(copiedBlock);
            _blockReferences[newName] = new List<BlockReference>();

            OnBlockDefinitionAdded(copiedBlock);
            return copiedBlock;
        }

        #endregion

        #region 块引用管理

        /// <summary>
        /// 注册块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        public void RegisterBlockReference(BlockReference blockReference)
        {
            if (blockReference == null) return;

            var blockName = blockReference.BlockDefinitionName;
            if (!_blockReferences.ContainsKey(blockName))
            {
                _blockReferences[blockName] = new List<BlockReference>();
            }

            if (!_blockReferences[blockName].Contains(blockReference))
            {
                _blockReferences[blockName].Add(blockReference);
            }
        }

        /// <summary>
        /// 取消注册块引用
        /// </summary>
        /// <param name="blockReference">块引用</param>
        public void UnregisterBlockReference(BlockReference blockReference)
        {
            if (blockReference == null) return;

            var blockName = blockReference.BlockDefinitionName;
            if (_blockReferences.ContainsKey(blockName))
            {
                _blockReferences[blockName].Remove(blockReference);
            }
        }

        /// <summary>
        /// 获取块定义的引用数量
        /// </summary>
        /// <param name="blockName">块名称</param>
        /// <returns>引用数量</returns>
        public int GetReferenceCount(string blockName)
        {
            return _blockReferences.ContainsKey(blockName) ? _blockReferences[blockName].Count : 0;
        }

        /// <summary>
        /// 获取块定义的所有引用
        /// </summary>
        /// <param name="blockName">块名称</param>
        /// <returns>块引用列表</returns>
        public IEnumerable<BlockReference> GetBlockReferences(string blockName)
        {
            return _blockReferences.ContainsKey(blockName) 
                ? _blockReferences[blockName].AsEnumerable() 
                : Enumerable.Empty<BlockReference>();
        }

        /// <summary>
        /// 更新块引用
        /// </summary>
        /// <param name="blockName">块名称</param>
        private void UpdateBlockReferences(string blockName)
        {
            if (!_blockReferences.ContainsKey(blockName)) return;

            foreach (var reference in _blockReferences[blockName])
            {
                reference.IsNeedToRegen = true;
                // 触发引用的重新生成
                reference.Regen();
            }
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取块定义
        /// </summary>
        /// <param name="name">块名称</param>
        /// <returns>块定义</returns>
        public BlockDefinition GetBlockDefinition(string name)
        {
            return _blockDefinitions.FirstOrDefault(b => 
                string.Equals(b.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查块定义是否存在
        /// </summary>
        /// <param name="name">块名称</param>
        /// <returns>是否存在</returns>
        public bool BlockDefinitionExists(string name)
        {
            return _blockDefinitions.Any(b => 
                string.Equals(b.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取可编辑的块定义
        /// </summary>
        /// <returns>可编辑的块定义列表</returns>
        public IEnumerable<BlockDefinition> GetEditableBlockDefinitions()
        {
            return _blockDefinitions.Where(b => b.IsEditable);
        }

        /// <summary>
        /// 获取用户定义的块
        /// </summary>
        /// <returns>用户定义的块列表</returns>
        public IEnumerable<BlockDefinition> GetUserDefinedBlocks()
        {
            return _blockDefinitions.Where(b => !b.IsSystem);
        }

        /// <summary>
        /// 搜索块定义
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <returns>匹配的块定义</returns>
        public IEnumerable<BlockDefinition> SearchBlockDefinitions(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return _blockDefinitions;

            return _blockDefinitions.Where(b => 
                b.Name.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                b.Description.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建标准块
        /// </summary>
        private void CreateStandardBlocks()
        {
            // 创建一些常用的标准块定义
            CreateArrowBlock();
            CreateNorthSymbolBlock();
            CreateTitleBlockBorder();
        }

        /// <summary>
        /// 创建箭头块
        /// </summary>
        private void CreateArrowBlock()
        {
            var entities = new List<EntityBase>();
            
            // 创建箭头形状的实体
            // 这里简化为一个三角形
            var triangle = new EntityPolygon();
            // 添加三角形顶点...

            var arrowBlock = new BlockDefinition
            {
                Name = "Arrow",
                Description = "标准箭头",
                BasePoint = Vector2.Zero,
                IsSystem = true,
                IsEditable = false,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            arrowBlock.Entities.AddRange(entities);
            arrowBlock.RecalculateBounds();

            _blockDefinitions.Add(arrowBlock);
            _blockReferences[arrowBlock.Name] = new List<BlockReference>();
        }

        /// <summary>
        /// 创建指北针块
        /// </summary>
        private void CreateNorthSymbolBlock()
        {
            var entities = new List<EntityBase>();
            
            // 创建指北针符号
            // 添加圆圈和箭头...

            var northBlock = new BlockDefinition
            {
                Name = "NorthSymbol",
                Description = "指北针符号",
                BasePoint = Vector2.Zero,
                IsSystem = true,
                IsEditable = false,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            northBlock.Entities.AddRange(entities);
            northBlock.RecalculateBounds();

            _blockDefinitions.Add(northBlock);
            _blockReferences[northBlock.Name] = new List<BlockReference>();
        }

        /// <summary>
        /// 创建标题栏边框块
        /// </summary>
        private void CreateTitleBlockBorder()
        {
            var entities = new List<EntityBase>();
            
            // 创建标题栏边框
            // 添加矩形和分隔线...

            var titleBlock = new BlockDefinition
            {
                Name = "TitleBlockBorder",
                Description = "标题栏边框",
                BasePoint = Vector2.Zero,
                IsSystem = true,
                IsEditable = false,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            titleBlock.Entities.AddRange(entities);
            titleBlock.RecalculateBounds();

            _blockDefinitions.Add(titleBlock);
            _blockReferences[titleBlock.Name] = new List<BlockReference>();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发块定义添加事件
        /// </summary>
        protected virtual void OnBlockDefinitionAdded(BlockDefinition blockDefinition)
        {
            BlockDefinitionAdded?.Invoke(this, new BlockDefinitionEventArgs(blockDefinition));
        }

        /// <summary>
        /// 触发块定义删除事件
        /// </summary>
        protected virtual void OnBlockDefinitionRemoved(BlockDefinition blockDefinition)
        {
            BlockDefinitionRemoved?.Invoke(this, new BlockDefinitionEventArgs(blockDefinition));
        }

        /// <summary>
        /// 触发块定义修改事件
        /// </summary>
        protected virtual void OnBlockDefinitionModified(BlockDefinition blockDefinition)
        {
            BlockDefinitionModified?.Invoke(this, new BlockDefinitionEventArgs(blockDefinition));
        }

        /// <summary>
        /// 触发当前块定义改变事件
        /// </summary>
        protected virtual void OnCurrentBlockDefinitionChanged(BlockDefinition blockDefinition)
        {
            CurrentBlockDefinitionChanged?.Invoke(this, new BlockDefinitionEventArgs(blockDefinition));
        }

        #endregion

        #region 循环引用检查

        /// <summary>
        /// 检查是否存在循环引用
        /// </summary>
        /// <param name="entity">要检查的实体</param>
        /// <param name="blockName">正在创建的块名称</param>
        /// <returns>是否存在循环引用</returns>
        private bool HasCircularReference(EntityBase entity, string blockName)
        {
            try
            {
                return CheckCircularReferenceRecursive(entity, blockName, new HashSet<string>());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"循环引用检查失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 递归检查循环引用
        /// </summary>
        /// <param name="entity">当前实体</param>
        /// <param name="targetBlockName">目标块名称</param>
        /// <param name="visitedBlocks">已访问的块名称集合</param>
        /// <returns>是否存在循环引用</returns>
        private bool CheckCircularReferenceRecursive(EntityBase entity, string targetBlockName, HashSet<string> visitedBlocks)
        {
            // 如果实体是块引用
            if (entity is BlockReference blockRef)
            {
                var blockName = blockRef.BlockDefinitionName;

                // 如果引用的是目标块，则存在循环引用
                if (string.Equals(blockName, targetBlockName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                // 如果已经访问过这个块，避免无限递归
                if (visitedBlocks.Contains(blockName))
                {
                    return false;
                }

                // 获取块定义并递归检查其内部实体
                var blockDef = GetBlockDefinition(blockName);
                if (blockDef != null)
                {
                    visitedBlocks.Add(blockName);

                    foreach (var innerEntity in blockDef.Entities)
                    {
                        if (CheckCircularReferenceRecursive(innerEntity, targetBlockName, visitedBlocks))
                        {
                            return true;
                        }
                    }

                    visitedBlocks.Remove(blockName);
                }
            }

            return false;
        }

        /// <summary>
        /// 验证块定义的完整性
        /// </summary>
        /// <param name="blockDefinition">块定义</param>
        /// <returns>验证结果</returns>
        public bool ValidateBlockDefinition(BlockDefinition blockDefinition)
        {
            if (blockDefinition == null) return false;

            try
            {
                // 检查块定义内部是否存在循环引用
                var visitedBlocks = new HashSet<string>();

                foreach (var entity in blockDefinition.Entities)
                {
                    if (CheckCircularReferenceRecursive(entity, blockDefinition.Name, visitedBlocks))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"块定义验证失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    #region 辅助类和结构

    /// <summary>
    /// 块定义
    /// </summary>
    public class BlockDefinition : ObservableObject
    {
        private string _name;
        private string _description;
        private Vector2 _basePoint;
        private ObservableCollection<EntityBase> _entities;
        private BoundingBox _boundingBox;
        private bool _isEditable;
        private bool _isSystem;
        private DateTime _createdDate;
        private DateTime _modifiedDate;

        public BlockDefinition()
        {
            _entities = new ObservableCollection<EntityBase>();
            _boundingBox = BoundingBox.Empty;
        }

        /// <summary>块名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>块描述</summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>基点</summary>
        public Vector2 BasePoint
        {
            get => _basePoint;
            set => SetProperty(ref _basePoint, value);
        }

        /// <summary>实体集合</summary>
        public ObservableCollection<EntityBase> Entities
        {
            get => _entities;
            set => SetProperty(ref _entities, value);
        }

        /// <summary>边界框</summary>
        public BoundingBox BoundingBox
        {
            get => _boundingBox;
            private set => SetProperty(ref _boundingBox, value);
        }

        /// <summary>是否可编辑</summary>
        public bool IsEditable
        {
            get => _isEditable;
            set => SetProperty(ref _isEditable, value);
        }

        /// <summary>是否为系统块</summary>
        public bool IsSystem
        {
            get => _isSystem;
            set => SetProperty(ref _isSystem, value);
        }

        /// <summary>创建日期</summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        /// <summary>修改日期</summary>
        public DateTime ModifiedDate
        {
            get => _modifiedDate;
            set => SetProperty(ref _modifiedDate, value);
        }

        /// <summary>
        /// 重新计算边界框
        /// </summary>
        public void RecalculateBounds()
        {
            _boundingBox.Clear();
            foreach (var entity in _entities)
            {
                _boundingBox.Union(entity.BoundingBox);
            }
        }
    }

    /// <summary>
    /// 块引用
    /// </summary>
    public class BlockReference : EntityBase
    {
        private string _blockDefinitionName;
        private Vector2 _insertionPoint;
        private Vector2 _scale;
        private float _rotation;
        private Dictionary<string, object> _attributes;

        public BlockReference()
        {
            _scale = Vector2.One;
            _attributes = new Dictionary<string, object>();
        }

        /// <summary>块定义名称</summary>
        public string BlockDefinitionName
        {
            get => _blockDefinitionName;
            set => _blockDefinitionName = value;
        }

        /// <summary>插入点</summary>
        public Vector2 InsertionPoint
        {
            get => _insertionPoint;
            set => _insertionPoint = value;
        }

        /// <summary>缩放比例</summary>
        public Vector2 Scale
        {
            get => _scale;
            set => _scale = value;
        }

        /// <summary>旋转角度（弧度）</summary>
        public float Rotation
        {
            get => _rotation;
            set => _rotation = value;
        }

        /// <summary>属性字典</summary>
        public Dictionary<string, object> Attributes
        {
            get => _attributes;
            set => _attributes = value ?? new Dictionary<string, object>();
        }

        public override void Render(IView view)
        {
            // 块引用的渲染逻辑
            // 需要获取块定义并应用变换矩阵渲染
        }

        public override void Regen()
        {
            // 重新生成块引用的几何信息
        }

        public override object Clone()
        {
            var clone = new BlockReference
            {
                BlockDefinitionName = this.BlockDefinitionName,
                InsertionPoint = this.InsertionPoint,
                Scale = this.Scale,
                Rotation = this.Rotation,
                Attributes = new Dictionary<string, object>(this.Attributes)
            };
            return clone;
        }

        public override void Translate(Vector2 translation)
        {
            InsertionPoint += translation;
        }
    }

    /// <summary>
    /// 块定义事件参数
    /// </summary>
    public class BlockDefinitionEventArgs : EventArgs
    {
        public BlockDefinition BlockDefinition { get; }

        public BlockDefinitionEventArgs(BlockDefinition blockDefinition)
        {
            BlockDefinition = blockDefinition;
        }
    }

    #endregion
} 