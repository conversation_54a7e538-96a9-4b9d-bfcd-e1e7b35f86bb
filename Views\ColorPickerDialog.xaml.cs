using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SkiaSharp;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// 颜色选择器对话框
    /// </summary>
    public partial class ColorPickerDialog : Window
    {
        private bool _isUpdating = false;
        private List<string> _recentColors = new List<string>();
        
        public Color SelectedColor { get; private set; } = Colors.Red;
        public SKColor SelectedSKColor => new SKColor(SelectedColor.R, SelectedColor.G, SelectedColor.B, SelectedColor.A);

        public ColorPickerDialog()
        {
            InitializeComponent();
            LoadRecentColors();
            UpdateColorPreview();
        }

        public ColorPickerDialog(Color initialColor) : this()
        {
            SelectedColor = initialColor;
            SetColorFromRgb(initialColor.R, initialColor.G, initialColor.B);
        }

        public ColorPickerDialog(SKColor initialColor) : this()
        {
            SelectedColor = Color.FromRgb(initialColor.Red, initialColor.Green, initialColor.Blue);
            SetColorFromRgb(initialColor.Red, initialColor.Green, initialColor.Blue);
        }

        private void LoadRecentColors()
        {
            // TODO: 从配置文件加载最近使用的颜色
            _recentColors = new List<string>
            {
                "#FF0000", "#00FF00", "#0000FF", "#FFFF00",
                "#FF00FF", "#00FFFF", "#000000", "#FFFFFF"
            };
            
            UpdateRecentColorsPanel();
        }

        private void UpdateRecentColorsPanel()
        {
            RecentColorsPanel.Children.Clear();
            
            foreach (var colorHex in _recentColors.Take(16))
            {
                var button = new Button
                {
                    Style = (Style)FindResource("ColorButtonStyle"),
                    Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(colorHex)),
                    Tag = colorHex
                };
                button.Click += PresetColor_Click;
                RecentColorsPanel.Children.Add(button);
            }
        }

        private void HueSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_isUpdating) return;
            UpdateColorFromHsv();
        }

        private void SaturationSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_isUpdating) return;
            UpdateColorFromHsv();
        }

        private void ValueSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_isUpdating) return;
            UpdateColorFromHsv();
        }

        private void UpdateColorFromHsv()
        {
            _isUpdating = true;
            try
            {
                var hue = HueSlider.Value;
                var saturation = SaturationSlider.Value / 100.0;
                var value = ValueSlider.Value / 100.0;

                var color = HsvToRgb(hue, saturation, value);
                SelectedColor = color;

                // 更新RGB输入框
                RedTextBox.Text = color.R.ToString();
                GreenTextBox.Text = color.G.ToString();
                BlueTextBox.Text = color.B.ToString();
                HexTextBox.Text = $"#{color.R:X2}{color.G:X2}{color.B:X2}";

                // 更新滑块值显示
                HueValueText.Text = $"{hue:F0}°";
                SaturationValueText.Text = $"{SaturationSlider.Value:F0}%";
                ValueValueText.Text = $"{ValueSlider.Value:F0}%";

                UpdateColorPreview();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void RgbTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            UpdateColorFromRgb();
        }

        private void UpdateColorFromRgb()
        {
            _isUpdating = true;
            try
            {
                if (byte.TryParse(RedTextBox.Text, out byte r) &&
                    byte.TryParse(GreenTextBox.Text, out byte g) &&
                    byte.TryParse(BlueTextBox.Text, out byte b))
                {
                    SetColorFromRgb(r, g, b);
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void HexTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdating) return;
            
            _isUpdating = true;
            try
            {
                var hex = HexTextBox.Text;
                if (hex.StartsWith("#") && hex.Length == 7)
                {
                    try
                    {
                        var color = (Color)ColorConverter.ConvertFromString(hex);
                        SetColorFromRgb(color.R, color.G, color.B);
                    }
                    catch
                    {
                        // 忽略无效的十六进制颜色
                    }
                }
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void SetColorFromRgb(byte r, byte g, byte b)
        {
            SelectedColor = Color.FromRgb(r, g, b);
            
            // 更新RGB输入框
            RedTextBox.Text = r.ToString();
            GreenTextBox.Text = g.ToString();
            BlueTextBox.Text = b.ToString();
            HexTextBox.Text = $"#{r:X2}{g:X2}{b:X2}";
            
            // 更新HSV滑块
            var (h, s, v) = RgbToHsv(r, g, b);
            HueSlider.Value = h;
            SaturationSlider.Value = s * 100;
            ValueSlider.Value = v * 100;
            
            // 更新滑块值显示
            HueValueText.Text = $"{h:F0}°";
            SaturationValueText.Text = $"{s * 100:F0}%";
            ValueValueText.Text = $"{v * 100:F0}%";
            
            UpdateColorPreview();
        }

        private void UpdateColorPreview()
        {
            ColorPreviewBorder.Background = new SolidColorBrush(SelectedColor);
        }

        private void PresetColor_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var colorHex = button?.Tag as string;
            
            if (!string.IsNullOrEmpty(colorHex))
            {
                try
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorHex);
                    SetColorFromRgb(color.R, color.G, color.B);
                }
                catch
                {
                    // 忽略无效颜色
                }
            }
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            // 添加到最近使用的颜色
            var hexColor = $"#{SelectedColor.R:X2}{SelectedColor.G:X2}{SelectedColor.B:X2}";
            if (!_recentColors.Contains(hexColor))
            {
                _recentColors.Insert(0, hexColor);
                if (_recentColors.Count > 16)
                {
                    _recentColors.RemoveAt(_recentColors.Count - 1);
                }
                // TODO: 保存到配置文件
            }
            
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // HSV到RGB转换
        private Color HsvToRgb(double hue, double saturation, double value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            double f = hue / 60 - Math.Floor(hue / 60);

            value = value * 255;
            byte v = Convert.ToByte(value);
            byte p = Convert.ToByte(value * (1 - saturation));
            byte q = Convert.ToByte(value * (1 - f * saturation));
            byte t = Convert.ToByte(value * (1 - (1 - f) * saturation));

            return hi switch
            {
                0 => Color.FromRgb(v, t, p),
                1 => Color.FromRgb(q, v, p),
                2 => Color.FromRgb(p, v, t),
                3 => Color.FromRgb(p, q, v),
                4 => Color.FromRgb(t, p, v),
                _ => Color.FromRgb(v, p, q),
            };
        }

        // RGB到HSV转换
        private (double h, double s, double v) RgbToHsv(byte r, byte g, byte b)
        {
            double rd = r / 255.0;
            double gd = g / 255.0;
            double bd = b / 255.0;

            double max = Math.Max(rd, Math.Max(gd, bd));
            double min = Math.Min(rd, Math.Min(gd, bd));
            double delta = max - min;

            double h = 0;
            if (delta != 0)
            {
                if (max == rd)
                    h = 60 * (((gd - bd) / delta) % 6);
                else if (max == gd)
                    h = 60 * (((bd - rd) / delta) + 2);
                else if (max == bd)
                    h = 60 * (((rd - gd) / delta) + 4);
            }

            if (h < 0) h += 360;

            double s = max == 0 ? 0 : delta / max;
            double v = max;

            return (h, s, v);
        }
    }
}
