using System;
using System.Numerics;
using System.Windows.Forms;
using SkiaSharp;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Viewport
{
    /// <summary>
    /// 交互式视图管理器
    /// 处理实时缩放、平移等交互操作
    /// </summary>
    public class InteractiveViewManager
    {
        private readonly ViewBase _viewBase;
        private readonly ViewportManager _viewportManager;
        
        // 实时缩放相关
        private bool _isRealtimeZoomActive = false;
        private Vector2? _zoomStartPoint = null;
        private float _zoomSensitivity = 0.01f;
        
        // 平移工具相关
        private bool _isPanToolActive = false;
        private Vector2? _panStartPoint = null;
        private bool _isPanning = false;
        
        // 鼠标滚轮缩放
        private bool _isWheelZoomEnabled = true;
        private float _wheelZoomFactor = 1.1f;
        
        // 中键平移
        private bool _isMiddleButtonPanEnabled = true;
        private bool _isMiddleButtonPanning = false;
        private Vector2? _middlePanStartPoint = null;
        
        // 光标管理
        private Cursor _originalCursor;
        
        public InteractiveViewManager(ViewBase viewBase, ViewportManager viewportManager)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _viewportManager = viewportManager ?? throw new ArgumentNullException(nameof(viewportManager));
            
            _originalCursor = _viewBase.Viewer.Cursor;
            
            // 订阅事件
            _viewBase.MouseDown += OnMouseDown;
            _viewBase.MouseMove += OnMouseMove;
            _viewBase.MouseUp += OnMouseUp;
            _viewBase.MouseWheel += OnMouseWheel;
        }
        
        #region 属性
        
        /// <summary>
        /// 是否启用鼠标滚轮缩放
        /// </summary>
        public bool IsWheelZoomEnabled
        {
            get => _isWheelZoomEnabled;
            set => _isWheelZoomEnabled = value;
        }
        
        /// <summary>
        /// 滚轮缩放因子
        /// </summary>
        public float WheelZoomFactor
        {
            get => _wheelZoomFactor;
            set => _wheelZoomFactor = Math.Max(1.01f, Math.Min(2.0f, value));
        }
        
        /// <summary>
        /// 是否启用中键平移
        /// </summary>
        public bool IsMiddleButtonPanEnabled
        {
            get => _isMiddleButtonPanEnabled;
            set => _isMiddleButtonPanEnabled = value;
        }
        
        /// <summary>
        /// 缩放敏感度
        /// </summary>
        public float ZoomSensitivity
        {
            get => _zoomSensitivity;
            set => _zoomSensitivity = Math.Max(0.001f, Math.Min(0.1f, value));
        }
        
        /// <summary>
        /// 是否正在进行实时缩放
        /// </summary>
        public bool IsRealtimeZoomActive => _isRealtimeZoomActive;
        
        /// <summary>
        /// 是否正在进行平移
        /// </summary>
        public bool IsPanToolActive => _isPanToolActive;
        
        #endregion
        
        #region 工具激活/停用
        
        /// <summary>
        /// 激活实时缩放工具
        /// </summary>
        public void ActivateRealtimeZoom()
        {
            DeactivateAllTools();
            _isRealtimeZoomActive = true;
            _zoomStartPoint = null;
            
            // 更改光标
            _viewBase.Viewer.Cursor = Cursors.SizeNS;
        }
        
        /// <summary>
        /// 激活平移工具
        /// </summary>
        public void ActivatePanTool()
        {
            DeactivateAllTools();
            _isPanToolActive = true;
            _panStartPoint = null;
            _isPanning = false;
            
            // 更改光标
            _viewBase.Viewer.Cursor = Cursors.Hand;
        }
        
        /// <summary>
        /// 停用所有工具
        /// </summary>
        public void DeactivateAllTools()
        {
            _isRealtimeZoomActive = false;
            _isPanToolActive = false;
            _isPanning = false;
            _isMiddleButtonPanning = false;
            
            _zoomStartPoint = null;
            _panStartPoint = null;
            _middlePanStartPoint = null;
            
            // 恢复原始光标
            _viewBase.Viewer.Cursor = _originalCursor;
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnMouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var point = new Vector2(e.X, e.Y);
            
            if (e.Button == MouseButtons.Left)
            {
                if (_isRealtimeZoomActive)
                {
                    _zoomStartPoint = point;
                }
                else if (_isPanToolActive)
                {
                    _panStartPoint = point;
                    _isPanning = true;
                    _viewBase.Viewer.Cursor = Cursors.SizeAll;
                }
            }
            else if (e.Button == MouseButtons.Middle && _isMiddleButtonPanEnabled)
            {
                _middlePanStartPoint = point;
                _isMiddleButtonPanning = true;
                _viewBase.Viewer.Cursor = Cursors.SizeAll;
            }
        }
        
        private void OnMouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var point = new Vector2(e.X, e.Y);
            
            // 实时缩放
            if (_isRealtimeZoomActive && _zoomStartPoint.HasValue && e.Button == MouseButtons.Left)
            {
                var deltaY = point.Y - _zoomStartPoint.Value.Y;
                var scaleFactor = 1.0f + (deltaY * _zoomSensitivity);
                
                // 限制缩放范围
                scaleFactor = Math.Max(0.5f, Math.Min(2.0f, scaleFactor));
                
                _viewportManager.ZoomView(scaleFactor, _zoomStartPoint);
                _zoomStartPoint = point;
            }
            // 平移工具
            else if (_isPanning && _panStartPoint.HasValue)
            {
                var delta = point - _panStartPoint.Value;
                _viewportManager.PanView(delta.X, delta.Y);
                _panStartPoint = point;
            }
            // 中键平移
            else if (_isMiddleButtonPanning && _middlePanStartPoint.HasValue)
            {
                var delta = point - _middlePanStartPoint.Value;
                _viewportManager.PanView(delta.X, delta.Y);
                _middlePanStartPoint = point;
            }
        }
        
        private void OnMouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (_isRealtimeZoomActive)
                {
                    _zoomStartPoint = null;
                }
                else if (_isPanning)
                {
                    _isPanning = false;
                    _panStartPoint = null;
                    
                    // 恢复平移工具光标
                    if (_isPanToolActive)
                    {
                        _viewBase.Viewer.Cursor = Cursors.Hand;
                    }
                }
            }
            else if (e.Button == MouseButtons.Middle)
            {
                _isMiddleButtonPanning = false;
                _middlePanStartPoint = null;
                
                // 恢复光标
                if (_isPanToolActive)
                {
                    _viewBase.Viewer.Cursor = Cursors.Hand;
                }
                else if (_isRealtimeZoomActive)
                {
                    _viewBase.Viewer.Cursor = Cursors.SizeNS;
                }
                else
                {
                    _viewBase.Viewer.Cursor = _originalCursor;
                }
            }
        }
        
        private void OnMouseWheel(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (_isWheelZoomEnabled)
            {
                var point = new Vector2(e.X, e.Y);
                var scaleFactor = e.Delta > 0 ? _wheelZoomFactor : 1.0f / _wheelZoomFactor;
                
                _viewportManager.ZoomView(scaleFactor, point);
            }
        }
        
        #endregion
        
        #region 快捷操作
        
        /// <summary>
        /// 执行快速缩放
        /// </summary>
        /// <param name="scaleFactor">缩放因子</param>
        /// <param name="centerPoint">缩放中心点，null表示视图中心</param>
        public void QuickZoom(float scaleFactor, Vector2? centerPoint = null)
        {
            _viewportManager.ZoomView(scaleFactor, centerPoint);
        }
        
        /// <summary>
        /// 执行快速平移
        /// </summary>
        /// <param name="deltaX">X轴平移量</param>
        /// <param name="deltaY">Y轴平移量</param>
        public void QuickPan(float deltaX, float deltaY)
        {
            _viewportManager.PanView(deltaX, deltaY);
        }
        
        /// <summary>
        /// 重置视图到默认状态
        /// </summary>
        public void ResetView()
        {
            _viewportManager.ResetView();
        }
        
        /// <summary>
        /// 缩放到适合窗口
        /// </summary>
        public void ZoomToFit()
        {
            _viewportManager.ZoomToFit();
        }
        
        #endregion
        
        #region 资源清理
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 取消事件订阅
            _viewBase.MouseDown -= OnMouseDown;
            _viewBase.MouseMove -= OnMouseMove;
            _viewBase.MouseUp -= OnMouseUp;
            _viewBase.MouseWheel -= OnMouseWheel;
            
            // 恢复光标
            _viewBase.Viewer.Cursor = _originalCursor;
        }
        
        #endregion
    }
}
