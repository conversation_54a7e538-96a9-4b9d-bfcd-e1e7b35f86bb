using System;
using System.Collections.Generic;
using System.Numerics;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Managers;

namespace McLaser.EditViewerSk.Commands
{
    public class AngularDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForFirstLine,
            WaitingForSecondLine,
            WaitingForArcPosition
        }

        private State currentState = State.WaitingForFirstLine;
        private EntityLine firstLine;
        private EntityLine secondLine;
        private EntityAngularDimension previewDimension;

        public AngularDimensionCmd(IView view) : base(view)
        {
            CommandName = "角度标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForFirstLine:
                    var entity1 = Document.FindEntityAt(point, 5.0);
                    if (entity1 is EntityLine line1)
                    {
                        firstLine = line1;
                        currentState = State.WaitingForSecondLine;
                        DynamicInputManager.AddInput("请选择第二条线:");
                        return true;
                    }
                    else
                    {
                        DynamicInputManager.AddError("请选择直线");
                        return false;
                    }

                case State.WaitingForSecondLine:
                    var entity2 = Document.FindEntityAt(point, 5.0);
                    if (entity2 is EntityLine line2 && line2 != firstLine)
                    {
                        secondLine = line2;
                        
                        // 创建预览标注
                        previewDimension = EntityAngularDimension.CreateFromTwoLines(firstLine, secondLine);
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                        
                        currentState = State.WaitingForArcPosition;
                        DynamicInputManager.AddInput("请指定弧线位置:");
                        return true;
                    }
                    else
                    {
                        DynamicInputManager.AddError("请选择另一条直线");
                        return false;
                    }

                case State.WaitingForArcPosition:
                    if (previewDimension != null)
                    {
                        // 根据点击位置调整弧线半径
                        var distance = Vector2.Distance(point, previewDimension.Vertex);
                        previewDimension.ArcRadius = Math.Max(5.0, distance);
                        
                        // 添加到文档
                        Document.AddEntity(previewDimension);
                        
                        // 创建关联
                        DimensionAssociationManager.Instance.CreateAssociation(previewDimension, firstLine);
                        DimensionAssociationManager.Instance.CreateAssociation(previewDimension, secondLine);
                        
                        // 添加到撤销系统
                        Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                        
                        Finish();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForFirstLine:
                case State.WaitingForSecondLine:
                    InvalidateView();
                    return true;

                case State.WaitingForArcPosition:
                    if (previewDimension != null)
                    {
                        var distance = Vector2.Distance(point, previewDimension.Vertex);
                        previewDimension.ArcRadius = Math.Max(5.0, distance);
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            if (key == System.Windows.Input.Key.Escape)
            {
                Cancel();
                return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForFirstLine:
                    // 高亮鼠标下的直线
                    if (View.LastMousePosition.HasValue)
                    {
                        var entity = Document.FindEntityAt(View.LastMousePosition.Value, 5.0);
                        if (entity is EntityLine)
                        {
                            var highlightPaint = CreateHighlightPaint();
                            entity.Render(renderer, CoordinateSpace.Model);
                        }
                    }
                    break;

                case State.WaitingForSecondLine:
                    // 高亮第一条线和鼠标下的线
                    if (firstLine != null)
                    {
                        var selectedPaint = CreateSelectedPaint();
                        firstLine.Render(renderer, CoordinateSpace.Model);
                    }
                    
                    if (View.LastMousePosition.HasValue)
                    {
                        var entity = Document.FindEntityAt(View.LastMousePosition.Value, 5.0);
                        if (entity is EntityLine && entity != firstLine)
                        {
                            var highlightPaint = CreateHighlightPaint();
                            entity.Render(renderer, CoordinateSpace.Model);
                        }
                    }
                    break;

                case State.WaitingForArcPosition:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForFirstLine:
                    return "选择第一条线:";
                case State.WaitingForSecondLine:
                    return "选择第二条线:";
                case State.WaitingForArcPosition:
                    return "请指定标注弧线位置或 [文字(M)]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            firstLine = null;
            secondLine = null;
            currentState = State.WaitingForFirstLine;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            firstLine = null;
            secondLine = null;
            currentState = State.WaitingForFirstLine;
            base.Finish();
        }
    }

    public class LeaderDimensionCmd : CommandBase, ICommand
    {
        private enum State
        {
            WaitingForStartPoint,
            WaitingForNextPoint,
            WaitingForText
        }

        private State currentState = State.WaitingForStartPoint;
        private List<Vector2> leaderPoints = new List<Vector2>();
        private EntityLeaderDimension previewDimension;
        private string annotationText = "";

        public LeaderDimensionCmd(IView view) : base(view)
        {
            CommandName = "引线标注";
        }

        public override bool OnMouseDown(Vector2 point, int mouseButton)
        {
            switch (currentState)
            {
                case State.WaitingForStartPoint:
                    leaderPoints.Add(point);
                    currentState = State.WaitingForNextPoint;
                    DynamicInputManager.AddInput("请指定下一点 (Enter 结束, Esc 取消):");
                    return true;

                case State.WaitingForNextPoint:
                    leaderPoints.Add(point);
                    
                    // 更新预览
                    if (previewDimension != null)
                    {
                        previewDimension.LeaderPoints = new List<Vector2>(leaderPoints);
                    }
                    else
                    {
                        previewDimension = new EntityLeaderDimension(leaderPoints, "");
                        previewDimension.Style = DimensionStyleManager.Instance.CurrentStyle;
                    }
                    
                    return true;

                case State.WaitingForText:
                    // 文本输入在键盘事件中处理
                    return true;
            }

            return false;
        }

        public override bool OnMouseMove(Vector2 point)
        {
            switch (currentState)
            {
                case State.WaitingForNextPoint:
                    if (leaderPoints.Count > 0)
                    {
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnKeyDown(System.Windows.Input.Key key)
        {
            switch (key)
            {
                case System.Windows.Input.Key.Enter:
                    if (currentState == State.WaitingForNextPoint && leaderPoints.Count >= 2)
                    {
                        currentState = State.WaitingForText;
                        DynamicInputManager.AddInput("请输入注释文本:");
                        return true;
                    }
                    else if (currentState == State.WaitingForText)
                    {
                        FinishLeader();
                        return true;
                    }
                    break;

                case System.Windows.Input.Key.Escape:
                    Cancel();
                    return true;

                case System.Windows.Input.Key.Back:
                    if (currentState == State.WaitingForNextPoint && leaderPoints.Count > 1)
                    {
                        leaderPoints.RemoveAt(leaderPoints.Count - 1);
                        if (previewDimension != null)
                        {
                            previewDimension.LeaderPoints = new List<Vector2>(leaderPoints);
                        }
                        InvalidateView();
                        return true;
                    }
                    break;
            }

            return false;
        }

        public override bool OnTextInput(string text)
        {
            if (currentState == State.WaitingForText)
            {
                annotationText += text;
                if (previewDimension != null)
                {
                    previewDimension.AnnotationText = annotationText;
                    InvalidateView();
                }
                return true;
            }

            return false;
        }

        public override void OnDraw(IGraphicsRenderer renderer)
        {
            if (renderer == null) return;

            var paint = CreatePreviewPaint();

            switch (currentState)
            {
                case State.WaitingForNextPoint:
                    if (leaderPoints.Count > 0)
                    {
                        // 绘制已确定的点和线段
                        for (int i = 0; i < leaderPoints.Count - 1; i++)
                        {
                            renderer.DrawLine(leaderPoints[i], leaderPoints[i + 1], paint);
                        }

                        // 绘制到鼠标位置的预览线
                        if (View.LastMousePosition.HasValue)
                        {
                            var dashPaint = CreateDashPaint();
                            renderer.DrawLine(leaderPoints[leaderPoints.Count - 1], View.LastMousePosition.Value, dashPaint);
                        }

                        // 绘制点
                        foreach (var point in leaderPoints)
                        {
                            DrawPoint(renderer, point, paint);
                        }
                    }
                    break;

                case State.WaitingForText:
                    if (previewDimension != null)
                    {
                        previewDimension.Render(renderer, CoordinateSpace.Model);
                        
                        // 显示文本输入提示
                        if (!string.IsNullOrEmpty(annotationText))
                        {
                            var textPaint = CreateTextPaint();
                            var textPos = leaderPoints[leaderPoints.Count - 1];
                            renderer.DrawText(annotationText + "_", textPos, textPaint);
                        }
                    }
                    break;
            }
        }

        public override string GetCommandPrompt()
        {
            switch (currentState)
            {
                case State.WaitingForStartPoint:
                    return "请指定引线起点:";
                case State.WaitingForNextPoint:
                    return $"请指定下一点 (已有 {leaderPoints.Count} 点) [Enter 结束, Backspace 撤销上一点]:";
                case State.WaitingForText:
                    return $"请输入注释文本 (当前: '{annotationText}') [Enter 完成]:";
                default:
                    return "";
            }
        }

        public override void Cancel()
        {
            previewDimension = null;
            leaderPoints.Clear();
            annotationText = "";
            currentState = State.WaitingForStartPoint;
            base.Cancel();
        }

        public override void Finish()
        {
            previewDimension = null;
            leaderPoints.Clear();
            annotationText = "";
            currentState = State.WaitingForStartPoint;
            base.Finish();
        }

        private void FinishLeader()
        {
            if (previewDimension != null && leaderPoints.Count >= 2)
            {
                previewDimension.AnnotationText = annotationText;
                
                // 添加到文档
                Document.AddEntity(previewDimension);
                
                // 添加到撤销系统
                Document.UndoRedoManager.AddCommand(new UndoRedoEntityAdd(Document, previewDimension));
                
                Finish();
            }
        }

        private void DrawPoint(IGraphicsRenderer renderer, Vector2 point, SkiaSharp.SKPaint paint)
        {
            var size = 2.0f;
            renderer.DrawLine(
                new Vector2(point.X - size, point.Y),
                new Vector2(point.X + size, point.Y), 
                paint);
            renderer.DrawLine(
                new Vector2(point.X, point.Y - size),
                new Vector2(point.X, point.Y + size), 
                paint);
        }

        private SkiaSharp.SKPaint CreateDashPaint()
        {
            var paint = CreatePreviewPaint();
            paint.PathEffect = SkiaSharp.SKPathEffect.CreateDash(new float[] { 5, 5 }, 0);
            return paint;
        }

        private SkiaSharp.SKPaint CreateTextPaint()
        {
            return new SkiaSharp.SKPaint()
            {
                Color = SkiaSharp.SKColors.Blue,
                TextSize = 12,
                IsAntialias = true
            };
        }
    }
} 