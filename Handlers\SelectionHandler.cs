using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Enums;
using McLaser.EditViewerSk.Renderers;
using System;
using System.Drawing;
using System.Numerics;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Handlers
{
    /// <summary>
    /// 选择处理器
    /// 负责处理所有选择相关的鼠标事件
    /// </summary>
    public class SelectionHandler : MouseInputHandler
    {
        private SelectRectangle _currentSelection;
        private readonly SelectionRenderer _renderer;
        private Vector2 _selectionStartPoint;
        private bool _isSelecting;

        public SelectionHandler(SelectionRenderer renderer)
        {
            _renderer = renderer ?? throw new ArgumentNullException(nameof(renderer));
            Priority = 100; // 高优先级
        }

        protected override bool CanHandle(MouseEventContext context)
        {
            // 可以处理空闲状态或选择状态的事件
            return context.CurrentState == InteractionState.Idle || 
                   context.CurrentState == InteractionState.Selecting ||
                   _isSelecting;
        }

        protected override MouseEventResult DoHandle(MouseEventContext context)
        {
            switch (context.EventType)
            {
                case MouseEventType.Down:
                    return HandleMouseDown(context);
                case MouseEventType.Move:
                    return HandleMouseMove(context);
                case MouseEventType.Up:
                    return HandleMouseUp(context);
                default:
                    return MouseEventResult.Unhandled;
            }
        }

        private MouseEventResult HandleMouseDown(MouseEventContext context)
        {
            if (context.EventArgs.Button != MouseButtons.Left)
                return MouseEventResult.Unhandled;

            // 开始选择操作
            _isSelecting = true;
            _selectionStartPoint = context.ModelPosition;
            
            _currentSelection = new SelectRectangle(context.View)
            {
                startPoint = context.ModelPosition,
                endPoint = context.ModelPosition
            };

            return MouseEventResult.WithStateChange(InteractionState.Selecting);
        }

        private MouseEventResult HandleMouseMove(MouseEventContext context)
        {
            if (!_isSelecting || _currentSelection == null)
                return MouseEventResult.Unhandled;

            // 更新选择框终点
            _currentSelection.endPoint = context.ModelPosition;
            
            // 触发重绘
            context.View.RepaintCanvas();

            return MouseEventResult.HandledResult;
        }

        private MouseEventResult HandleMouseUp(MouseEventContext context)
        {
            if (!_isSelecting || _currentSelection == null)
                return MouseEventResult.Unhandled;

            if (context.EventArgs.Button != MouseButtons.Left)
                return MouseEventResult.Unhandled;

            try
            {
                // 执行选择操作
                var startCanvas = context.CoordinateService.ModelToCanvas(_selectionStartPoint);
                var endCanvas = context.CoordinateService.ModelToCanvas(context.ModelPosition);
                
                // 调用原有的HitTest方法
                int count = context.View.HitTest(
                    new Point((int)startCanvas.X, (int)startCanvas.Y),
                    new Point((int)endCanvas.X, (int)endCanvas.Y),
                    out var hitEntities);

                // 更新选择
                if (hitEntities != null && hitEntities.Count > 0)
                {
                    context.View.Document.SelectedEntitys = hitEntities;
                }

                // 触发重绘
                context.View.RepaintCanvas();

                return MouseEventResult.WithStateChange(InteractionState.Idle);
            }
            finally
            {
                // 清理选择状态
                _isSelecting = false;
                _currentSelection = null;
            }
        }

        /// <summary>
        /// 渲染当前选择框
        /// </summary>
        public void Render(ViewBase view)
        {
            if (_currentSelection != null && _isSelecting)
            {
                var selectionMode = _currentSelection.selectMode == SelectRectangle.SelectMode.Window 
                    ? EntitySelectionMode.Window 
                    : EntitySelectionMode.Cross;

                _renderer.RenderSelectionRectangle(view, 
                    _currentSelection.startPoint, 
                    _currentSelection.endPoint, 
                    selectionMode);
            }
        }
    }
} 