<UserControl x:Class="McLaser.EditViewerSk.Views.DynamicInputOverlay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="120" d:DesignWidth="250"
             Background="Transparent" IsHitTestVisible="True">
    
    <UserControl.Resources>
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="25"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="BorderBrush" Value="#4A90E2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="Width" Value="20"/>
        </Style>
        
        <Style x:Key="InputPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#4A90E2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="2" 
                                      BlurRadius="4" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <Grid Name="MainGrid" Visibility="Collapsed">
        <!-- 坐标输入模式 -->
        <Border Name="CoordinateInputPanel" Style="{StaticResource InputPanelStyle}" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="坐标输入" FontWeight="Bold" FontSize="11" Margin="0,0,0,5" 
                           Foreground="#4A90E2" HorizontalAlignment="Center"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="X:" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelTextStyle}"/>
                    <TextBox Name="XCoordinateTextBox" Grid.Row="0" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="80"
                             KeyDown="InputTextBox_KeyDown" TextChanged="CoordinateTextBox_TextChanged"/>
                    
                    <TextBlock Text="Y:" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelTextStyle}"/>
                    <TextBox Name="YCoordinateTextBox" Grid.Row="1" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="80"
                             KeyDown="InputTextBox_KeyDown" TextChanged="CoordinateTextBox_TextChanged"/>
                </Grid>
                
                <TextBlock Name="CoordinateHintText" Text="输入坐标值，按Tab切换，Enter确认" 
                           FontSize="9" Foreground="Gray" Margin="0,3,0,0" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- 距离输入模式 -->
        <Border Name="DistanceInputPanel" Style="{StaticResource InputPanelStyle}" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="距离输入" FontWeight="Bold" FontSize="11" Margin="0,0,0,5" 
                           Foreground="#4A90E2" HorizontalAlignment="Center"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="距离:" Grid.Column="0" Style="{StaticResource LabelTextStyle}" Width="35"/>
                    <TextBox Name="DistanceTextBox" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="100"
                             KeyDown="InputTextBox_KeyDown" TextChanged="DistanceTextBox_TextChanged"/>
                </Grid>
                
                <TextBlock Name="DistanceHintText" Text="输入距离值，Enter确认" 
                           FontSize="9" Foreground="Gray" Margin="0,3,0,0" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- 角度输入模式 -->
        <Border Name="AngleInputPanel" Style="{StaticResource InputPanelStyle}" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="角度输入" FontWeight="Bold" FontSize="11" Margin="0,0,0,5" 
                           Foreground="#4A90E2" HorizontalAlignment="Center"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="角度:" Grid.Column="0" Style="{StaticResource LabelTextStyle}" Width="35"/>
                    <TextBox Name="AngleTextBox" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="80"
                             KeyDown="InputTextBox_KeyDown" TextChanged="AngleTextBox_TextChanged"/>
                    <TextBlock Text="°" Grid.Column="2" Style="{StaticResource LabelTextStyle}" Width="15"/>
                </Grid>
                
                <TextBlock Name="AngleHintText" Text="输入角度值，Enter确认" 
                           FontSize="9" Foreground="Gray" Margin="0,3,0,0" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- 复合输入模式（距离+角度） -->
        <Border Name="DistanceAngleInputPanel" Style="{StaticResource InputPanelStyle}" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="极坐标输入" FontWeight="Bold" FontSize="11" Margin="0,0,0,5" 
                           Foreground="#4A90E2" HorizontalAlignment="Center"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="距离:" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelTextStyle}" Width="35"/>
                    <TextBox Name="PolarDistanceTextBox" Grid.Row="0" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="80"
                             KeyDown="InputTextBox_KeyDown" TextChanged="PolarTextBox_TextChanged"/>
                    
                    <TextBlock Text="角度:" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelTextStyle}" Width="35"/>
                    <TextBox Name="PolarAngleTextBox" Grid.Row="1" Grid.Column="1" 
                             Style="{StaticResource InputTextBoxStyle}" Width="80"
                             KeyDown="InputTextBox_KeyDown" TextChanged="PolarTextBox_TextChanged"/>
                    <TextBlock Text="°" Grid.Row="1" Grid.Column="2" Style="{StaticResource LabelTextStyle}" Width="15"/>
                </Grid>
                
                <TextBlock Name="PolarHintText" Text="输入距离和角度，Tab切换，Enter确认" 
                           FontSize="9" Foreground="Gray" Margin="0,3,0,0" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- 信息显示面板 -->
        <Border Name="InfoDisplayPanel" Style="{StaticResource InputPanelStyle}" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Name="InfoTitleText" Text="当前信息" FontWeight="Bold" FontSize="11" 
                           Margin="0,0,0,5" Foreground="#4A90E2" HorizontalAlignment="Center"/>
                
                <StackPanel Name="InfoContentPanel">
                    <TextBlock Name="CurrentCoordinateText" Text="坐标: (0.000, 0.000)" 
                               FontSize="10" Margin="2"/>
                    <TextBlock Name="CurrentDistanceText" Text="距离: 0.000" 
                               FontSize="10" Margin="2" Visibility="Collapsed"/>
                    <TextBlock Name="CurrentAngleText" Text="角度: 0.000°" 
                               FontSize="10" Margin="2" Visibility="Collapsed"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
