using System;
using System.Numerics;
using SkiaSharp;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Graphics
{
    /// <summary>
    /// 基于SkiaSharp的专业图形渲染器
    /// 实现CAD软件标准的图形绘制功能
    /// </summary>
    public class SkiaGraphicsRenderer : IGraphicsRenderer
    {
        private readonly ViewBase _viewBase;
        private readonly SKCanvas _canvas;
        private readonly ICoordinateTransform _coordinateTransform;
        private int _renderDepth;

        public SkiaGraphicsRenderer(ViewBase viewBase, SKCanvas canvas)
        {
            _viewBase = viewBase ?? throw new ArgumentNullException(nameof(viewBase));
            _canvas = canvas ?? throw new ArgumentNullException(nameof(canvas));
            _coordinateTransform = new CoordinateTransform(viewBase);
            _renderDepth = 0;
        }

        public ICoordinateTransform CoordinateTransform => _coordinateTransform;

        #region 基础绘制方法

        public void DrawLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var start = TransformPoint(startPoint, space);
            var end = TransformPoint(endPoint, space);

            _canvas.DrawLine((float)start.X, (float)start.Y, (float)end.X, (float)end.Y, paint);
        }

        public void DrawCircle(Vector2 center, double radius, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadius = (float)_coordinateTransform.TransformDistance(radius, space, CoordinateSpace.Viewport);

            _canvas.DrawCircle((float)transformedCenter.X, (float)transformedCenter.Y, transformedRadius, paint);
        }

        public void DrawArc(Vector2 center, double radius, double startAngle, double sweepAngle, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadius = (float)_coordinateTransform.TransformDistance(radius, space, CoordinateSpace.Viewport);
            var transformedStartAngle = (float)_coordinateTransform.TransformAngle(startAngle, space, CoordinateSpace.Viewport);
            var transformedSweepAngle = (float)_coordinateTransform.TransformAngle(sweepAngle, space, CoordinateSpace.Viewport);

            var rect = new SKRect(
                (float)transformedCenter.X - transformedRadius,
                (float)transformedCenter.Y - transformedRadius,
                (float)transformedCenter.X + transformedRadius,
                (float)transformedCenter.Y + transformedRadius);

            _canvas.DrawArc(rect, transformedStartAngle, transformedSweepAngle, false, paint);
        }

        public void DrawRectangle(Vector2 position, double width, double height, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedPosition = TransformPoint(position, space);
            var transformedWidth = (float)_coordinateTransform.TransformDistance(width, space, CoordinateSpace.Viewport);
            var transformedHeight = (float)_coordinateTransform.TransformDistance(height, space, CoordinateSpace.Viewport);

            // 处理Y轴翻转的情况
            if (space == CoordinateSpace.Model)
            {
                transformedHeight = -transformedHeight;
            }

            _canvas.DrawRect((float)transformedPosition.X, (float)transformedPosition.Y, transformedWidth, transformedHeight, paint);
        }

        public void DrawEllipse(Vector2 center, double radiusX, double radiusY, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var transformedCenter = TransformPoint(center, space);
            var transformedRadiusX = (float)_coordinateTransform.TransformDistance(radiusX, space, CoordinateSpace.Viewport);
            var transformedRadiusY = (float)_coordinateTransform.TransformDistance(radiusY, space, CoordinateSpace.Viewport);

            var rect = new SKRect(
                (float)transformedCenter.X - transformedRadiusX,
                (float)transformedCenter.Y - transformedRadiusY,
                (float)transformedCenter.X + transformedRadiusX,
                (float)transformedCenter.Y + transformedRadiusY);

            _canvas.DrawOval(rect, paint);
        }

        public void DrawPolygon(Vector2[] points, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || points == null || points.Length < 3) return;

            using var path = new SKPath();
            var transformedPoints = TransformPoints(points, space);

            path.MoveTo(new SKPoint( (float)transformedPoints[0].X, (float)transformedPoints[0].Y));
            for (int i = 1; i < transformedPoints.Length; i++)
            {
                path.LineTo(new SKPoint((float)transformedPoints[i].X, (float)transformedPoints[i].Y));
            }
            path.Close();

            _canvas.DrawPath(path, paint);
        }

        public void DrawPath(SKPath path, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || path == null) return;

            if (space == CoordinateSpace.Viewport)
            {
                _canvas.DrawPath(path, paint);
            }
            else
            {
                // 对于其他坐标空间，需要变换路径
                using var transformedPath = new SKPath();
                var matrix = GetTransformMatrix(space);
                path.Transform(in matrix, transformedPath);
                _canvas.DrawPath(transformedPath, paint);
            }
        }

        public void DrawText(string text, Vector2 position, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || string.IsNullOrEmpty(text)) return;

            var transformedPosition = TransformPoint(position, space);
            _canvas.DrawText( text, (float)transformedPosition.X, (float)transformedPosition.Y, paint);
        }

        #endregion

        #region CAD专业功能

        public void DrawArrowLine(Vector2 startPoint, Vector2 endPoint, SKPaint paint, double arrowSize = 10, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            // 绘制主线
            DrawLine(startPoint, endPoint, paint, space);

            // 计算箭头
            var direction = Vector2.Normalize(endPoint - startPoint);
            var perpendicular = new Vector2(-direction.Y, direction.X);
            
            var transformedArrowSize = _coordinateTransform.TransformDistance(arrowSize, space, space);
            var arrowPoint1 = endPoint - direction * (float)transformedArrowSize + perpendicular * (float)(transformedArrowSize * 0.5);
            var arrowPoint2 = endPoint - direction * (float)transformedArrowSize - perpendicular * (float)(transformedArrowSize * 0.5);

            // 绘制箭头
            DrawLine(endPoint, arrowPoint1, paint, space);
            DrawLine(endPoint, arrowPoint2, paint, space);
        }

        public void DrawDimension(Vector2 startPoint, Vector2 endPoint, Vector2 textPosition, string text, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            // 绘制标注线
            DrawLine(startPoint, endPoint, paint, space);
            
            // 绘制箭头
            DrawArrowLine(startPoint, endPoint, paint, 5, space);
            
            // 绘制文本
            DrawText(text, textPosition, paint, space);
        }

        public void DrawArrow(Vector2 point, Vector2 direction, double size, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || size <= 0) return;

            var normalizedDirection = Vector2.Normalize(direction);
            var perpendicular = new Vector2(-normalizedDirection.Y, normalizedDirection.X);
            
            // 计算箭头的三个点
            var arrowLength = size * 0.8;
            var arrowWidth = size * 0.3;
            
            var arrowPoint1 = point - normalizedDirection * (float)arrowLength + perpendicular * (float)arrowWidth;
            var arrowPoint2 = point - normalizedDirection * (float)arrowLength - perpendicular * (float)arrowWidth;
            
            // 绘制箭头
            DrawLine(point, arrowPoint1, paint, space);
            DrawLine(point, arrowPoint2, paint, space);
            DrawLine(arrowPoint1, arrowPoint2, paint, space);
        }

        public void DrawExtensionLine(Vector2 startPoint, Vector2 endPoint, double offset, double extend, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            var direction = Vector2.Normalize(endPoint - startPoint);
            var offsetStart = startPoint + direction * (float)offset;
            var extendEnd = endPoint + direction * (float)extend;
            
            DrawLine(offsetStart, extendEnd, paint, space);
        }

        public void DrawGrid(double spacing, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || spacing <= 0) return;

            // 获取视口边界
            var viewBounds = GetViewBounds(space);
            
            // 绘制垂直线
            for (double x = Math.Floor(viewBounds.Left / spacing) * spacing; x <= viewBounds.Right; x += spacing)
            {
                DrawLine(new Vector2((float)x, viewBounds.Top), new Vector2((float)x, viewBounds.Bottom), paint, space);
            }
            
            // 绘制水平线
            for (double y = Math.Floor(viewBounds.Top / spacing) * spacing; y <= viewBounds.Bottom; y += spacing)
            {
                DrawLine(new Vector2(viewBounds.Left, (float)y), new Vector2(viewBounds.Right, (float)y), paint, space);
            }
        }

        public void DrawCoordinateAxis(Vector2 origin, double length, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null) return;

            // X轴
            DrawArrowLine(origin, origin + new Vector2((float)length, 0), paint, length * 0.1, space);
            
            // Y轴
            DrawArrowLine(origin, origin + new Vector2(0, (float)length), paint, length * 0.1, space);
            
            // 标签
            DrawText("X", origin + new Vector2((float)length * 1.1f, 0), paint, space);
            DrawText("Y", origin + new Vector2(0, (float)length * 1.1f), paint, space);
        }

        public void DrawSelectionHighlight(Vector2[] points, SKPaint paint, CoordinateSpace space = CoordinateSpace.Model)
        {
            if (paint == null || points == null || points.Length < 2) return;

            // 创建高亮效果的画笔
            using var highlightPaint = paint.Clone();
            highlightPaint.Color = highlightPaint.Color.WithAlpha(128); // 半透明
            highlightPaint.StrokeWidth = paint.StrokeWidth * 2; // 更粗的线条

            if (points.Length == 2)
            {
                // 矩形选择
                var width = points[1].X - points[0].X;
                var height = points[1].Y - points[0].Y;
                DrawRectangle(points[0], width, height, highlightPaint, space);
            }
            else
            {
                // 多边形选择
                DrawPolygon(points, highlightPaint, space);
            }
        }

        #endregion

        #region 渲染控制

        public void BeginRender()
        {
            _renderDepth++;
            if (_renderDepth == 1)
            {
                _canvas.Save();
            }
        }

        public void EndRender()
        {
            if (_renderDepth > 0)
            {
                _renderDepth--;
                if (_renderDepth == 0)
                {
                    _canvas.Restore();
                }
            }
        }

        public void PushMatrix(SKMatrix matrix)
        {
            _canvas.Save();
            _canvas.Concat(ref matrix);
        }

        public void PopMatrix()
        {
            _canvas.Restore();
        }

        public void SaveState()
        {
            _canvas.Save();
        }

        public void RestoreState()
        {
            _canvas.Restore();
        }

        public void SetClipRegion(Vector2 topLeft, Vector2 bottomRight, CoordinateSpace space = CoordinateSpace.Model)
        {
            var transformedTopLeft = TransformPoint(topLeft, space);
            var transformedBottomRight = TransformPoint(bottomRight, space);
            
            var clipRect = new SKRect(
                (float)Math.Min(transformedTopLeft.X, transformedBottomRight.X),
                (float)Math.Min(transformedTopLeft.Y, transformedBottomRight.Y),
                (float)Math.Max(transformedTopLeft.X, transformedBottomRight.X),
                (float)Math.Max(transformedTopLeft.Y, transformedBottomRight.Y));

            _canvas.ClipRect(clipRect);
        }

        #endregion

        #region 辅助方法

        private Vector2 TransformPoint(Vector2 point, CoordinateSpace space)
        {
            return _coordinateTransform.TransformPoint(point, space, CoordinateSpace.Viewport);
        }

        private Vector2[] TransformPoints(Vector2[] points, CoordinateSpace space)
        {
            var result = new Vector2[points.Length];
            for (int i = 0; i < points.Length; i++)
            {
                result[i] = TransformPoint(points[i], space);
            }
            return result;
        }

        private SKMatrix GetTransformMatrix(CoordinateSpace space)
        {
            return space switch
            {
                CoordinateSpace.Model => _coordinateTransform.GetViewMatrix(),
                CoordinateSpace.Screen => SKMatrix.CreateIdentity(),
                _ => SKMatrix.CreateIdentity()
            };
        }

        private SKRect GetViewBounds(CoordinateSpace space)
        {
            // 获取当前视口的边界
            var canvasInfo = _canvas.DeviceClipBounds;
            
            if (space == CoordinateSpace.Viewport)
            {
                return canvasInfo;
            }

            // 转换到指定坐标空间
            var topLeft = _coordinateTransform.TransformPoint(
                new Vector2(canvasInfo.Left, canvasInfo.Top), 
                CoordinateSpace.Viewport, space);
            var bottomRight = _coordinateTransform.TransformPoint(
                new Vector2(canvasInfo.Right, canvasInfo.Bottom), 
                CoordinateSpace.Viewport, space);

            return new SKRect((float)topLeft.X, (float)topLeft.Y, (float)bottomRight.X, (float)bottomRight.Y);
        }

        #endregion
    }
} 